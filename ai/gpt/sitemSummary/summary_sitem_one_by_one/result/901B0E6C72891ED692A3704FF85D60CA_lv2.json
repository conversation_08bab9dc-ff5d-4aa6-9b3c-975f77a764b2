{"guid": "901B0E6C72891ED692A3704FF85D60CA", "sitemId": "SI6: Logistics_General", "sitemTitle": "S4TWL - Retail Store Fiori App", "note": 2342914, "noteTitle": "2342914 - S4TWL - Retail Store Fiori App", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using the Fiori app for transfer stock. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, some table structures have changed. Thus, open transfer stock documents need to be closed prior to the conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes will not be affected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please, follow instructions of referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if the Fiori app for transfer stock is used. <br/><span 'times=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en;=\"\" lang=\"EN\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">This can be checked via transaction SE16N. Enter table TRF_DOC_HEAD and check whether the table exists or there are any entries in the table.</span></p>", "noteVersion": 3, "refer_note": [{"note": "2339756", "noteTitle": "2339756 - S4 PreChecks EA-RETAIL: Descriptions of checks for Retail Store Fiori app", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>For migrating to the target system S/4 HANA OP 1610, the class check is used. And the following message could return from the class execution.</p>\n<ul>\n<li>1. The check returns error message saying 'CLIENT: XXX, incompleted Transfer Stock data in staging tables cannot be migrated to S/4. Please finish the incompleted Transfer Stock tasks in Transfer Stock Fiori application.' </li>\n<li>2. The check returns error rmessage saying 'CLIENT: XXX, cannot clean up Transfer Stock data in staging tables before migrating to S/4. Please run the cleanup job to clean up the data in staging table.'</li>\n</ul>\n<p>For migrating to the target system S/4 HANA OP 1709, the simplified check is used. A few table checks are defined in transition DB and message could return by this tool when the pre-check step is executed.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, EA-RETAIL, Transfer Stock, SAP_EA_RETAIL_FIO, Retail Store</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Transfer Stock: In S4HANA, the primary key of the table TRF_DOC_DETAILS has been changed. Old data in the table cannot be used anymore in S4HANA. To avoid data inconsistence we suggest to delete the data in the following tables TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS in source system before the migration or in target system after migration.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>For migrating to the target system S/4 HANA OP 1610, please do the following on source system:</p>\n<ul>\n<li>For symptom 1: Use Transfer Stock Fiori application to complete the Open/Rejected transfer stock documents by either submit the transfers or delete them.</li>\n<li>For symptom 2: Use the function module - RETAIL_ST_TS_CLEANUP_STAGE_TAB to clean up the staging tables of Transfer Stock at ERP Retail system.</li>\n</ul>\n<p>Note: you can dalso use any other tools to directly delete all the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS instead of using the Fiori application or FM mentioned above.</p>\n<p>For migrating to the target system S/4 HANA OP 1709, please use the following steps on source system (before migration) or target system (after migration) to clean up the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS:</p>\n<ul>\n<li>Step 1: Use Transfer Stock Fiori application to complete the Open/Rejected transfer stock documents by either submit the transfers or delete them.</li>\n<li>Step 2: Use the function module - RETAIL_ST_TS_CLEANUP_STAGE_TAB to clean up the staging tables of Transfer Stock at ERP Retail system.</li>\n</ul>\n<p>Note: you can dalso use any other tools to directly delete all the entries in the following tables - TRF_DOC_HEAD, TRF_DOC_DETAILS, TRF_DOC_ERR_MSGS instead of using the Fiori application or FM mentioned above.</p>", "noteVersion": 2, "refer_note": [{"note": "2326521", "noteTitle": "2326521 - S4TC EA_RETAIL master check class for S/4 system transformation checks", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to migrate an existing SAP ERP for Retail system to an S/4 system. To check whether the prerequisites for the software component EA-RETAIL are met for an S/4 migration, some preparatory checks must be carried out in the existing SAP ERP for Retail system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4 precheck master check class EA-RETAIL</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Also implement all SAP Notes that contain the individual prechecks in your system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instructions in your system.</p>", "noteVersion": 13}]}], "activities": [{"Activity": "Business Operations", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Open transfer stock documents need to be closed prior to system conversion."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Customer Code needs to be adjusted as per change in functionality in SAP S4/HANA."}]}