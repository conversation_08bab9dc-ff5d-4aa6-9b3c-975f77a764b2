{"guid": "0050569455E21ED5B3E17678390BE09E", "sitemId": "SI6: SD_REBATES", "sitemTitle": "S4TWL - Optimization of SD Rebate Processing for TPM Customers", "note": 2267344, "noteTitle": "2267344 - S4TWL - Optimization of SD Rebate Processing for TPM Customers", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><span>Business Value</span></strong></strong></p>\n<p><span><span>Redundant index data, to determine relevant documents, was removed. This leads to a significantly r<span><span>educed database footprint</span></span>.<br/>Additional information can be found in note 2200691 and the documentation/notes for business function LOG_SD_REBATE_INDEX.</span></span></p>\n<p><strong>Description</strong></p>\n<p>Customers with an active CRM TPM or Trade Management license can continue using SD Rebate Processing, even though a successor is already being provided by Settlement Management (cp. simplification item SD Rebate Processing replaced by Settlement Management).</p>\n<p><strong>Business Process-Related information</strong></p>\n<p>For more information, see note <a href=\"/notes/2226380\" target=\"_blank\">2226380</a>.</p>\n<p>For these customers, the existing SD Rebate functionality has been optimized with regard to the database footprint. For this purpose, from a technical viewpoint, the rebate index table VBOX is not available.</p>\n<p>Licensed customers can optionally enable the creation of rebate agreements directly in sales via removal of the blacklist entries for VB01 (Note <a href=\"/notes/2249880\" target=\"_blank\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a>)<em>.</em> However, for the TPM process this is not necessary at all, since for this business process, rebate agreements are created by CRM TPM.<em> </em></p>\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\n<p>The SD Rebate solution is only provided in the current scope. Upcoming legal requirements and additional funcitionalities will <strong>only</strong> be covered in the Settlement Management solution.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>If the customer is using the extended SD rebate processing, a rebuild of the S469 content is required after the upgrade.</li>\n<li>The customer has to maintain new customizing settings.</li>\n</ul>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>This Transition Worklist Item is only relevant if the customer has an active Trade Promotion Management or Trade Management license.<br/></span></p>\n<p>The S/4HANA Readiness Check determines this Transition Worklist Item as relevant if SD Rebates have been used in the customer system (table KONA has entries with field ABTYP Equal to 'A'). <br/>But in fact only those customers are affected which have an active Trade Promotion or Trade Management license.</p>\n<p>The general changes for SD Rebates are described in note<br/><a href=\"/notes/2267377\" target=\"_blank\">2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management</a></p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: <a href=\"/notes/2200691\" target=\"_blank\">2200691</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 11, "refer_note": [{"note": "2267377", "noteTitle": "2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>The Settlement Management Solution provides a single point of entry to maintain and to administrate  contract-related conditions. Condition contract settlement allows to set up different rebate scenarios and settlement processes to fulfill your business requirements by customization. Flexible data sources, business selection criteria, settlement calendar and further features allow a streamlined contract entry and contract processing. Condition Contract Management offers an overview of actual business volume even for retroactively contracts and a rich functionality to control open settlements, business volume and accruals.</p>\n<p><strong>Description</strong></p>\n<p>In general, SD Rebate Processing is not available within SAP S/4HANA. The functional equivalent of SD Rebate Processing in SAP S/4HANA is Settlement Management.</p>\n<p>However, there is one exception: CRM TPM customers can still use SD Rebate Processing for their business process, but have to adapt to an SAP S/4HANA-optimized solution (cp. simplification item Optimization of SD Rebate Processing for TPM Customers, note 2267344).</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>In SAP S/4HANA, Settlement Management replaces SD Rebate Processing, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. New agreements can only be created based on condition contracts. Therefore, the corresponding transaction codes VBO1 resp. VB(D for the creation of rebate agreements is not available anymore.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>VBO1     Create Rebate Agreement<br/>VB(D      Extend Rebate Agreements</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>See the attached documents for more detailed information.</p>\n<p>Some countries like countries in Latin America require localization. For example Latin American authorities require invoicing with pre-assigned number through transaction IDCP. This functionality is not available as of now for settlement management documents. Please verify if settlement management solution fulfills the local requirements in the your specific countries and reach out to the SAP localization team in case you have questions.</p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p>Use SE16 to check table KONA: If table KONA contains records with ABTYP = ‘A’ then SD Rebate Processing is used.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2226380</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 7, "refer_note": [{"note": "2267344", "noteTitle": "2267344 - S4TWL - Optimization of SD Rebate Processing for TPM Customers", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><span>Business Value</span></strong></strong></p>\n<p><span><span>Redundant index data, to determine relevant documents, was removed. This leads to a significantly r<span><span>educed database footprint</span></span>.<br/>Additional information can be found in note 2200691 and the documentation/notes for business function LOG_SD_REBATE_INDEX.</span></span></p>\n<p><strong>Description</strong></p>\n<p>Customers with an active CRM TPM or Trade Management license can continue using SD Rebate Processing, even though a successor is already being provided by Settlement Management (cp. simplification item SD Rebate Processing replaced by Settlement Management).</p>\n<p><strong>Business Process-Related information</strong></p>\n<p>For more information, see note <a href=\"/notes/2226380\" target=\"_blank\">2226380</a>.</p>\n<p>For these customers, the existing SD Rebate functionality has been optimized with regard to the database footprint. For this purpose, from a technical viewpoint, the rebate index table VBOX is not available.</p>\n<p>Licensed customers can optionally enable the creation of rebate agreements directly in sales via removal of the blacklist entries for VB01 (Note <a href=\"/notes/2249880\" target=\"_blank\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a>)<em>.</em> However, for the TPM process this is not necessary at all, since for this business process, rebate agreements are created by CRM TPM.<em> </em></p>\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\n<p>The SD Rebate solution is only provided in the current scope. Upcoming legal requirements and additional funcitionalities will <strong>only</strong> be covered in the Settlement Management solution.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>If the customer is using the extended SD rebate processing, a rebuild of the S469 content is required after the upgrade.</li>\n<li>The customer has to maintain new customizing settings.</li>\n</ul>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span>This Transition Worklist Item is only relevant if the customer has an active Trade Promotion Management or Trade Management license.<br/></span></p>\n<p>The S/4HANA Readiness Check determines this Transition Worklist Item as relevant if SD Rebates have been used in the customer system (table KONA has entries with field ABTYP Equal to 'A'). <br/>But in fact only those customers are affected which have an active Trade Promotion or Trade Management license.</p>\n<p>The general changes for SD Rebates are described in note<br/><a href=\"/notes/2267377\" target=\"_blank\">2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management</a></p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: <a href=\"/notes/2200691\" target=\"_blank\">2200691</a></p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 11}]}, {"note": "2200691", "noteTitle": "2200691 - SD rebate: Simplifications in SAP S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are interested in the changes to the SD rebate in SAP S/4HANA.<br/>The following simplifications have been made:</p>\n<ul>\n<li>The functions of the business function 'LOG_SD_REBATE_INDEX: Determination of billing document without sales index' are always active.</li>\n<li>The functions of the enhanced rebate, previously activated via the enterprise function EA_ISE, are always active.</li>\n</ul>\n<p>This SAP Note answers common questions on this topic.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>VBOX, rebate index, LOG_SD_REBATE_INDEX, determination of billing document, TREB_DEF_MAP_IM, TREB_CUST_MAP_IM, enhanced rebate, S469</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade to SAP S/4HANA and use of existing SD rebate processing</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p class=\"SAPKWDITA-p\">Customers with an active TPM or Trade Management license can still use SD rebate processing even though a successor is already provided by Settlement Management. For more information, see SAP Note <a href=\"/notes/2226380\" target=\"_blank\">2226380</a>. For these customers, the existing SD rebate function was optimized in terms of the database footprint.</p>\n<p class=\"SAPKWDITA-p\">The use of SD rebate processing is limited to the time when maintenance for the CRM TPM or Trade Management solutions ends.</p>\n<p>The SD rebate solution is available only in its current scope.</p>\n<p>Further developments and adjustments in line with new legal requirements will be carried out only in Settlement Management.</p>\n<p>With SAP S/4HANA, data is no longer written to the index table VBOX. Instead of accessing this table, the standard SAP programs run a dynamic access to the application tables to determine the relevant billing documents.<br/><br/></p>\n<p>Required adjustments:</p>\n<ul>\n<li>The content of the table VBOX is deleted during the migration to SAP S/4 HANA; this table is no longer used.<br/><br/><br/><ul>\n<li>User-defined reports that read data from the table VBOX must be switched over. <br/>The function module SD_REBATES_GET_INVOICES_IM can be called to read the relevant billing documents instead of the table VBOX.<br/>The parameters of the function module correspond to the fields of the table VBOX plus the condition type (KSCHL) currently being processed.<br/><br/></li>\n</ul>\n</li>\n<li>Adjustment of functions in line with customer-specific logic<br/><br/>In pricing preparation, the system transfers the content of the document (saved in the application tables) to the pricing communication structure KOMG. Mapping defines this correlation.<br/>The standard mapping corresponds to the logic in an unmodified pricing preparation.<br/><br/></li>\n<ul>\n<li>If user exits, BAdIs, or modifications are used to fill the values of KOMG differently, customizing for customer-specific mapping (table TREB_CUST_MAP_IM) must be used to establish the correlation of KOMG with the original document data.<br/><br/></li>\n<li>If the customer-specific logics are too complex for direct mapping, the BAdI SD_REBATES_IM can be used.<br/><br/>For more information, see the documentation for the aforementioned BAdI or for the corresponding interface IF_EX_BADI_SD_REBATES_IM.</li>\n</ul>\n</ul>\n<p>The functions of the enhanced rebate are always active.</p>\n<p>Required adjustments:</p>\n<ul>\n<li>If the enhanced rebate, and thus the update of the table S469, was active before the migration, a one-time setup of the data of the table S469 with the report ENH_REBATE_S469RB is required due to technical changeovers.<br/>The content of the table S469 is deleted during the migration and the field VAKEY contained in the table is extended.<br/>For more information about the execution of the recompilation, see SAP Note <a href=\"/notes/1060629\" target=\"_blank\">1060629</a>.</li>\n</ul>\n<p> </p>\n<p> </p>\n<p> </p>", "noteVersion": 7, "refer_note": [{"note": "1060629", "noteTitle": "1060629 - SDS060RB/ENH_REBATE_S469RB:Reorg. of data relevant to rebate", "noteText": "<div class=\"mono\"><h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3><p>You can use report SDS060RB to reorganize information structure S060. You can call this report in Customizing for rebate processing under \"Simulate and Execute Reorganization of Statistical Data\" using transaction VFSN.<br/><br/>An incorrect execution of the report triggers incorrect sales volume, accrual and payment values. This note explains the functions and correct use of the report.<br/><br/>Carry out the same procedure for the report ENH_REBATE_S469RB to reorganize the structure S469.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3><p>S060, RUWRT, AUWRT, KAWRT_K VIS, rebate reorganization, SDS060RB, SDS060RC, VFSN<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some documentation is missing.<br/></p><h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3><p>You should generally only use report SDS060RB if there are errors in the statistics update. You should <b>not</b> schedule or execute the report regularly. You can use report SDS060RC instead to simulate the results of the update of an individual billing document.<br/><br/><br/><u>Functions of the report:</u><br/><br/>Report SDS060RB selects the billing documents determined and cumulates the values in information structure S060.<br/>If the values of the billing document have already been updated this triggers <b><b>duplicate</b></b>values or <b>repeatedly cumulated</b>values.<br/><br/>Therefore, an essential element of the report is the 'INITS060' function.<br/>If you start the report with this option, the system deletes all data (regardless of the selection criteria) from S060.<br/><br/>To reorganize the information structure correctly, the values must be initialized and all billing documents must be recalculated.<br/><br/><br/><u>Correct execution:</u><br/><br/>You can only use the report for a complete reorganization of statistical information structure S060.<br/><br/>To reorganize the information structure, you must execute the report for all documents exactly once. You can split report SDS060RB in intervals, as in partial runs. You must set the 'INITS060' indicator for the first partial run, which completely initializes the information structure. For subsequent partial runs you <b>cannot</b> set the indicator, as otherwise the data of the previous partial runs is deleted.<br/><br/>The safest thing to do is to execute an individual run of report SDS060RB without restrictions, with the 'INITS060' indicator set.<br/><br/><br/><u>Risks of an incorrect execution:</u></p> <ul><li>In partial runs where 'INITS060' is set for the first partial run, the system does not select all billing documents and data is missing for the billing documents that are not processed.</li></ul> <ul><li>In partial runs where 'INITS060' is set for the first partial run, the system repeatedly selects billing documents and this means there are multiple sales volume, accrual and payment values.</li></ul> <ul><li>In partial runs where 'INITS060' is set for all partial runs, data is missing for the partial runs prior to the last run.</li></ul> <ul><li>If you run the report without setting the 'INITS060' indicator, values are added and there are multiple sales volume, accrual and payment values.</li></ul> <p><br/></p></div>", "noteVersion": 6}]}, {"note": "2226380", "noteTitle": "2226380 - S/4 HANA: Deprecation of SD Rebate Processing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<div class=\"longtext\">\n<p>You are using an SAP ERP system and intend to perform the upgrade-like conversion to SAP S/4HANA.</p>\n<p>The custom code check shows customer objects that are affected by simplifications.</p>\n<p>SAP objects used by the customer objects have been changed in a syntactically incompatible way.</p>\n<p>The custom code check refers to SAP Note 2226380.</p>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4_TRANSFORMATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In general, the functionality SD Rebate Processing is not supported in SAP S/4HANA. The successor of SD Rebate Processing is Settlement Management.</p>\n<p>However, there is one exception: because the integration between CRM Trade Promotion Management (TPM) and Settlement Management has only been added in CRM 7.0 EhP4 SP05 and S/4HANA 1610 FP01, customer with an active TPM or Trade Management license can currently still use SD Rebate Processing. Customers with an active TPM or Trade Management license can still generate rebate agreements using the condition generation feature of CRM TPM. Licensed customers can optionally enable the creation of rebate agreements directly in sales via removal of the blacklist entries for VB01 (Note <a href=\"/notes/2249880\" target=\"_blank\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a><em>).</em></p>\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\n<p>For all other customers, the following is valid:</p>\n<p>SAP objects used by the customer objects are deprecated and shall not be used any more.</p>\n<p>Custom code does not comply with the scope and data structure of SAP S/4HANA.</p>\n<p>The usage of SD Rebate Processing for this case is limited to the maintenance period of the CRM TPM and Trade Management solutions.</p>\n<p>The SD Rebate solution is only provided in the current scope. Upcoming legal requirements and additional funcitionalities will <strong>only</strong> be covered in the Settlement Management solution.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>All usages of SAP objects in customer objects for which the custom code check refers to Note 2226380 will no longer work, and must be removed.</p>\n<p>For customers without an active TPM or Trade Management license, existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. New agreements can only be created based on condition contracts. Therefore, the following corresponding transaction codes for the creation of rebate agreements are only available for customers with an active TPM or Trade Management license:</p>\n<ul>\n<li>Create Rebate Agreement VBO1</li>\n<li>Extend Rebate Agreements VB(D</li>\n</ul>", "noteVersion": 7}], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Perform adjustments as described in SAP note 2200691."}, {"Activity": "Data migration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Run report ENH_REBATE_S469RB after technical conversion."}]}