{"guid": "0050569455E21ED5B3E17678391F009E", "sitemId": "SI_3_Logistics_QM", "sitemTitle": "S4TWL - QM WEB Workplace (MiniApps) based on ITS Services", "note": 2270129, "noteTitle": "2270129 - S4TWL - QM WEB Workplace (MiniApps) based on ITS Services", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IAC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Renovation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>SAP Internet Transaction Server (ITS) and mySAP Workplace are not available within SAP S/4HANA.  Therefore the MiniApps that are based on these techniques are not available within SAP S/4HANA.</p>\n<p><strong>Business Process related information</strong></p>\n<p>As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or the corresponding Web Dynpro applications, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"186\">\n<p><strong> Transaction not available in SAP S/4HANA on-premise edition 1511</strong></p>\n</td>\n<td valign=\"top\" width=\"569\">\n<p>IQS8WP               IQS8 - Call from Workplace/MiniApp</p>\n<p>IQS9WP               IQS9 - Call from Workplace/MiniApp</p>\n<p>QA32WP             QA32 - Call from Workplace/MiniApp</p>\n<p>QE09WP              Call QE09 from Workplace</p>\n<p>QPQA32              QM MiniApp Selection Variant</p>\n<p>QPQGC1              QM MiniApp Selection Variant</p>\n<p>QPQM10             QM MiniApp Selection Variant</p>\n<p>QPQM13             QM MiniApp Selection Variant</p>\n<p>QM10WP            QM10 - Call from Workplace/MiniApp</p>\n<p>QM13WP            QM13 - Call from Workplace/MiniApp</p>\n<p>QPIQS8 QM        MiniApp Selection Variant</p>\n<p>QPIQS9 QM        MiniApp Selection Variant</p>\n<p>WAO_QA32WP  QA32 -Call from Workplace/MiniApp</p>\n<p>WAO_QM10WP QM10 - Call from Workplace/MiniApp</p>\n<p>WAO_QM13WP QM13 - Call from Workplace/MiniApp</p>\n<p>WAO_QPQA32   QM        iView Selection Variant Insp.Lot</p>\n<p>WAO_QPQM10 QM        iView Selection Variant Notificat</p>\n<p>WAO_QPQM13 QM        iView Selection Variant Task</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Knowledge transfer to key and end users</p>", "noteVersion": 1, "refer_note": [{"note": "2207630", "noteTitle": "2207630 - Quality Management MiniApps are no longer available", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>The QM MiniApps are no longer available. The MiniApps were linked with the ITS services MA_QM_INSPLOT, MA_QM_NOTIF, MA_QM_NOTIFCOM, MA_QM_TASKS, and MA_QM_TASKSCOM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>IAC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>There is an obsolete Web technology in the ITS services.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Replace the call of the ITS service with calls of the relevant Web queries in BW or transactions in the back end.</p>", "noteVersion": 1}], "activities": [{"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "SAP Internet Transaction Server (ITS) and mySAP Workplace are not available within SAP S/4HANA. Therefore the MiniApps that are based on these techniques are not available within SAP S/4HANA.  As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or you could use the WebDynpro applications mentioned below, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5)."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}