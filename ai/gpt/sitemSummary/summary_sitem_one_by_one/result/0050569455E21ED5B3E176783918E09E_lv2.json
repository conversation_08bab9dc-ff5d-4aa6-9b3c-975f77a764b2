{"guid": "0050569455E21ED5B3E176783918E09E", "sitemId": "SI33: Logistics_PP", "sitemTitle": "S4TWL - Process Flow Scheduling (PP-PI)", "note": 2273079, "noteTitle": "2273079 - S4TWL - Process Flow Scheduling (PP-PI)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The process flow scheduler PFS was a scheduling tool for the process industries offered by SAP. Since SAP R/3 4.6 C, the process flow scheduler is replaced by SAP Advanced Planning and Optimization APO (see also SAP Note 217113 - PFS: Missing functions). The PFS interfaces is not available in SAP S/4HANA.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Use PPDS for detailed planning and scheduling of bottleneck resources. PPDS in part of SAP S/4HANA.</p>", "noteVersion": 1, "refer_note": [{"note": "2226966", "noteTitle": "2226966 - SAP S/4 HANA Simplification Item: External Interfaces in Production Planning", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p id=\"\"> In SAP S/4 HANA outdated interfaces to external systems in production planning are no longer supported. Following interfaces are not available in SAP S/4 HANA:</p>\n<ul>\n<li>Process-Flow-Scheduler: The process flow scheduler PFS was a scheduling tool for the process industries offered by SAP. It was replaced by SAP-SCM in the late 1990s.</li>\n<li>Lean planning and operations: Lean planning and operations LPO was an algorithm for leveling production based on lean principles. The algorithm was never productized.</li>\n<li>Icon: Icon was a response management solution. SAP's preferred solution for response management is IBP.</li>\n<li>Constraint-based planning: The constraint-based planning interface (CBP interface) was replaced by the POI interface in 2002. For details, see note 500526.</li>\n<li>KK2 and KK5 Interface: These interfaces used an outdated interface technology (flat file)</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\"> S/4 HANA, Simplification Item, PP</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Various interfaces to external systems have been deprecated.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p id=\"\"> Use the POI interface or the integration with Manufacturing Execution Systems instead.</p>", "noteVersion": 1, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "The Process-Flow-Scheduler (PFS) was an interim solution for the Process Industry with limited functionality. PPDS presents the SAP's startegic tool for configuring Industry specific scheduling scenarios, and provides enhanced feaures for the Process Industry such as Campaign Optimisation, Container Resource planning and can consider products shelf live. Consider to use PPDS for detailed planning and scheduling of bottleneck resources. Please, see also SAP Notes 217113 and 2273079."}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}