{"guid": "901B0E6D3ED11ED690BC431F5981E0C5", "sitemId": "SI18: PROC_RFQ Simplified Transaction", "sitemTitle": "S4TWL - RFQ Simplified Transaction", "note": 2332710, "noteTitle": "2332710 - S4TWL-RFQ Simplified Transaction", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP to SAP S/4HANA or an upgrade from a lower to a higher SAP S/4HANA release and are using the functionality described in this note. The following SAP S/4HANA Simplification Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4HANA,System Conversion, Upgrade, Request for Quotation, Supplier Quotation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The previous concept was based on requests for quotations (RFQs) that included a mandatory supplier field. In addition, the number of suppliers that could be entered for each RFQ was limited to one. This restriction doesn’t fit to multiple sourcing-related integration scenarios, such as Sourcing with SAP Ariba Sourcing.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>New concept enables the system to send RFQs, that were created in a Fiori app, to external sourcing platforms without mentioning exact supplier. On the sourcing platform, the registered suppliers can submit their quotations which are later available to the purchaser.</p>\n<p>Following IDOCS and transactions related to RFQ are not considered as the target architecture. This means that the functionality is currently available in SAP S/4HANA but it is not considered as future technology and a functional equivalent is not available.</p>\n<p>These IDocs are replaced with cXML messages:</p>\n<ul>\n<li>REQOTE (Basic IDoc type = ORDERS05) is the IDoc for RFQ</li>\n<li>QUOTES (Basic IDoc type = ORDERS05) is the IDoc for supplier quotation</li>\n</ul>\n<p>It is planned to introduce SOAP-based messages to further replace cXML messages for RFQ and supplier quotation.<span> </span></p>\n<p>The following transactions are deprecated in SAP S/4HANA:</p>\n<div class=\"table-responsive\"><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME41</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Create</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME42</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Change</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME43</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Display</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME44</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Maintain Supplement</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME45</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Release</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME47</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Maintain</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME48</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Display</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME49</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>Price Comparison List</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4B</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs by Requirement Tracking Number</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4C</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs by Material Group</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4L</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs by Vendor</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4M</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs by Material</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4N</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs by RFQ Number</p>\n</td>\n</tr>\n<tr>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"64\">\n<p>ME4S</p>\n</td>\n<td nowrap=\"nowrap\" valign=\"bottom\" width=\"309\">\n<p>RFQs per Collective Number</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Business Process Related Information</strong></p>\n<p>If you want to use functionality related to sourcing of goods and services, you can use the following apps:</p>\n<ul>\n<li>Manage RFQs (F2049)</li>\n<li>Monitor RFQ Items (F2425)</li>\n<li>Request for Quotation Types (F4149)</li>\n<li>Manage Supplier Quotations (F1991)</li>\n<li>Compare Supplier Quotations (F2324)</li>\n</ul>\n<p><span>Before you start to use Fiori apps mentioned above you need to close your open RFQs, that were created via old transactions, and set them to status \"Completed\".</span></p>\n<p><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><strong><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">Required and Recommended Action(s)</span></strong></span></span></span></span></span></span></p>\n<p><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 'times=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en-gb;=\"\" lang=\"EN-GB\" line-height:=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\"><span 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" en-gb;=\"\" en-us;=\"\" lang=\"EN-GB\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">You have to maintain the user roles.</span></span></span></span></span></span></span></span> The new Fiori apps are accompanied by the standard roles that contain relevant authorization objects. The relevant business roles are:</p>\n<ul>\n<li>SAP_BR_PURCHASER (Purchaser) for the apps:</li>\n</ul>\n<ul>\n<ul>\n<li>Manage RFQs (F2049)</li>\n<li>Monitor RFQ Items (F2425)</li>\n<li>Manage Supplier Quotations (F1991)</li>\n<li>Compare Supplier Quotations (F2324)</li>\n</ul>\n</ul>\n<ul>\n<li>SAP_BR_BUYER (Strategic Buyer) for the app:</li>\n</ul>\n<ul>\n<ul>\n<li>Request for Quotation Types (F4149)</li>\n</ul>\n</ul>", "noteVersion": 4, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Reconsider the UI Strategy"}, {"Activity": "User Training", "Phase": "Before or after conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Fiori Implementation", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": ""}, {"Activity": "Interface Adaption", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": ""}]}