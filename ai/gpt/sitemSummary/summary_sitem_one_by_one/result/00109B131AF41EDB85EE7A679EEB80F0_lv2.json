{"guid": "00109B131AF41EDB85EE7A679EEB80F0", "sitemId": "SI21: AS_ABAP_WORKFLOW_AUTHORIZATION_CHANGES", "sitemTitle": "ABAPTWL - Change of authorization checks SAP Business Workflow", "note": 2979517, "noteTitle": "2979517 - ABAPTWL - Change of authorization checks SAP Business Workflow", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>After an upgrade of your system you face authorization issues in reports or transactions of SAP Business Workflow. Especially this concerns helper reports or transactions like for example RS<PERSON>_MAINTAIN_USER_ATTR or RSWEQADMIN.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p><strong>Authorization objects:</strong></p>\n<p>S_WF_ADM, S_PROGRAM</p>\n<p><strong>Reports:</strong></p>\n<p>RHWFCOPL, LSWDDF32, LSWEQBROWSERF01, RHXWOSHW, RSLEI_TRC, RSSWW_MOVE_TO_HOT, RS<PERSON>_CONFIG_INITIATOR, RSWD_MAINTAIN_USER_ATTR, RSWD_REPLICATE_FROM_9999, RSWDACT0, RSWDACTIVATE, RSWDADH<PERSON>DEL, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>R8, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RS<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, RS<PERSON>VER9, <PERSON><PERSON><PERSON><PERSON><PERSON>, RS<PERSON><PERSON>_<PERSON>LIM_INCONSIST, RS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>LG, RSWEE<PERSON><PERSON>EFI, RSWEINST, RSWEINSTVMAINT, RSWELCFG, RSWELCFG2, RSWELOGA, RSWELOGD, RSWELOGS, RSWELTOGGLE, RSWEQADMIN, RSWEQDELETE, RSWEQMAINTAIN, RSWEQSRV, RSWEQSRV_ASYNC, RSWEQSRVDELETE, RSWEQSRVSCHEDULE, RSWF_ADD_OM_EXIT_CLASS, RSWF_ADM_SUSPEND, RSWF_ADM_SWWWIDH, RSWF_ALM_DELETE_MONITOR, RSWF_ANALYZE_RESUBMIT, RSWF_BAM_ADM, RSWF_CCMS_MONITOR_TEST, RSWF_CHECK_CONTAINER_STRUCTS, RSWF_CLEAR_SECMETHOD_MEMORY, RSWF_CNT_BOR_ELEM_REPLACE, RSWF_CNT_CHECK_STRUCTURE, RSWF_CNT_COMPARE, RSWF_CNT_DEL_LOC_STRUCTURE, RSWF_CNT_EDIT, RSWF_CNT_JOB_SHMEMUPD_DELETE, RSWF_CNT_JOB_SHMEMUPD_INSERT, RSWF_CNT_MAINTENANCE, RSWF_CNT_PREPARE_STRUCTURE, RSWF_CNT_TEST_CONVERSION_XML, RSWF_CREATE_WITEXT, RSWF_CRL_BUILDER, RSWF_DATABASE_UPGRADE, RSWF_DELETE_CONTAINER_ELEMENT, RSWF_DISPLAY_APPL_LOG, RSWF_FLEX_DEMO_CHECK, RSWF_FLEX_DEMO_CHECK_F01, RSWF_FLEX_DEMO_CHECK_O01, RSWF_FLEX_DEMO_CHECK_TOP, RSWF_FLEX_SWI2_FREQ, RSWF_FLEX_SWPINTRO, RSWF_FLEX_UTL_PROCESS_VIEW, RSWF_FLEX_UTL_PROCESS_VIEW_F01, RSWF_FLEX_UTL_PROCESS_VIEW_F02, RSWF_FLEX_UTL_PROCESS_VIEW_F03, RSWF_GET_FOOTPRINT, RSWF_LOG_DISPLAY, RSWF_MANAGE_WORKFLOW_DAEMON, RSWF_MIG_CNT_STRUCTS_INTERNAL, RSWF_MIG_CONTAINER_STRUCTS, RSWF_MIG_TOPWIID, RSWF_MON_ACTIVATED_DEADLINES, RSWF_MON_DATABASE_SIZE, RSWF_NOTIF_ACTIVATION, RSWF_OUTPUT_MANAGEMENT, RSWF_POWL_CHECK_QUERIES, RSWF_PUSH_NOTIF_EXECUTE_DEMO, RSWF_PUSH_NOTIFICATION_EXECUTE, RSWF_RESTART_WORKFLOWS, RSWF_SHOW_AGING_INCONSISTENCY, RSWF_SHOW_EXT_CONFIGURATION, RSWF_SHOW_SWW_USRATT, RSWF_SHOW_SWW_WIREGISTER, RSWF_SHOW_SWWLOGHIST, RSWF_SHOW_SWWLOGHIST_TOPLEVEL, RSWF_SHOW_SWWWIHEAD, RSWF_SHOW_SWWWIHEAD_TOPLEVEL, RSWF_SHOW_SWWWITEXT, RSWF_SYSTEM_ACTIONS, RSWF_SYSTEM_CLEANUP, RSWF_SYSTEM_DELAYED, RSWF_SYSTEM_SCHEDULER, RSWF_SYSTEM_TEMPORARY, RSWF_TCM_MIGR_CUST_CONF_XPRA, RSWF_TCM_MIGRATE_CUST_CONFIG, RSWF_TRC_BAM, RSWF_UNIT_CHCK_CONTAINER, RSWF_UNIT_CONFIGURATION, RSWF_UNIT_MONITORING, RSWF_UTL_ENABLE_DEFAULT_TRACE, RSWF_UTL_EXECUTE_ACTIONS, RSWF_WLC_WI_EVT_ADM, RSWF_WLC_WI_EVT_ADM_F01, RSWF_WLC_WI_EVT_ADM_F02, RSWF_XI_CACHE_RESET, RSWF_XI_REORG, RSWF_XI_SWI2, RSWF_XI_SWPC, RSWF_XI_SWPR, RSWF_XI_TEST_BULK, RSWF_XI_UNPROCESSED_MSGS, RSWF_XI_UNSENT_MSGS, RSWF_XI_UNSENT_MSGS_F01, RSWF_XI_WORKLOAD_TEST, RSWFADM_CHECK_SWWWIDH, RSWFEVTLOG, RSWFEVTLOGDEL, RSWFEVTLOGF01, RSWFSLSDHIN, RSWFSLSDLEX, RSWFSLSDLEX_INSERT, RSWFVMD, RSWH_DEMOINFO, RSWH_TEST_INBOX_TRANSLATION, RSWI_MIGRATE_OUTBOX, RSWI_REGENERATE_WI2OBJ, RSWI_WORKFLOW_DATABASE, RSWIADM2, RSWIDEAD_NEW, RSWIDIAG, RSWIDISP, RSWIDONE, RSWIEXEC, RSWIEXEL, RSWIEXET, RSWIFREQ_NEW, RSWILOAD, RSWIMEAN, RSWIODIA, RSWIOFIN, RSWIOINS, RSWIOTPD, RSWIRROR, RSWIUNIT_NEW, RSWIUSAG_NEW, RSWIW3DO, RSWIW3OD, RSWIW3OI, RSWIW3OT, RSWIWORK, RSWJ_PPF_PCOND, RSWL_INPLACE_INBOX_DEMO, SWL_INPLACE_INBOX_DEMO_PROFI, RSWL_SETUSERDEFAULT, RSWN_CHECK_DATA_VOLUME, RSWN_CHECK_DATA_VOLUME_F01, RSWN_CHECK_DATA_VOLUME_F02, RSWN_RUNTIME_DATA_REORG, RSWN_SET_APP_OBJ_KEY_HASH, RSWN_SET_RECIPIENT_HASH2, RSWNNOTIFDEL, RSWNNOTIFDEL_DELETE, RSWNNOTIFDEL_INSERT, RSWP_ADM_CREATE_CONFIG_PARAM, RSWP_CHANGE_MAX_NODES, RSWP_CHECK_CB_CONSISTENCY, RSWP_CONTINUE_WORKFLOWS_GRID, RSWP_RESTART_WORKFLOWS_GRID, RSWP_WFM_RUN_COMPATIBILITY, RSWR_TEST_MOBILE_API, RSWTDREQ, RSWTTR01, RSWTTR02, RSWTTR03, RSWTTR04, RSWTTR05, RSWTTR06, RSWUDFOL, RSWUDI00, RSWUDI01, RSWUDI02, RSWUDI03, RSWUDI04, RSWUDI05, RSWUDI06, RSWUDI07, RSWUDI08, RSWUDI09, RSWUDI10, RSWUESIM, RSWUMMES, RSWUOBJEX, RSWUOBJEXBO, RSWUOCHECK, RSWUODEL, RSWUSETT, RSWUTR01, RSWUVWIZBTE, RSWUVWIZCD, RSWUVWIZLIS, RSWW_ACTIVATE_MEMORY_DIFFTOOL, RSWW_DAAG_AGING_RUN, RSWW_DELETE_BINDEF, RSWW_DELETE_SWWRUNMETH, RSWW_DEST_SET_UNICODE, RSWW_DROP_OBSOLETE_TABLES, RSWW_EXEC_SUSPENDED_CALLBACK, RSWW_EXEC_SUSPENDED_CANCEL, RSWW_FILL_DEFGUID, RSWW_FILL_USER_ATTR, RSWW_MIG_FOOTPRINT, RSWW_REORG_SWWUSERWI, RSWW_RUNTIME_MONITORING, RSWW_STRAIGHTEN_SWW_REGISTER, RSWW_STRAIGTHEN_FINAL_WRKFLWS, RSWWARCL, RSWWCLEAR, RSWWCLEAR_DELETE, RSWWCLEAR_INSERT, RSWWCOND, RSWWCOND_DELETE, RSWWCOND_INSERT, RSWWCOND_MON, RSWWCOND_STRAIGHTEN_REGISTER, RSWWDELTAB, RSWWDHEX, RSWWDHEX_DEBUG, RSWWDHEX_DELETE, RSWWDHEX_EXTENDED, RSWWDHEX_INSERT, RSWWDHEX_INTERNAL, RSWWDHEX_XMB_INTERNAL, RSWWDHIN, RSWWERIN, RSWWERRE, RSWWERRE_DEBUG, RSWWERRE_DELETE, RSWWERRE_INSERT, RSWWHIDE, RSWWMONB_BACKGROUND_STARTED, RSWWRUNCNT, RSWWSTAT, RSWWWIDE, RSWWWIDE_DEP, RSWWWIDE_TOPLEVEL, RSWWWIM, RSWWWIM_DELETE, RSWWWIM_INSERT, RSWX_FORMABSENC_MONITOR, RSWXML_FIND, SWN_SELSEN_TEST, SWXFORMA, SWXFORMC, SWXFORMD, SWXFORMS, SWXFORMS_SHOW, SWXFORMS_TB, WDHEX_XMB_INTERNAL_DEADF01, WFUNIT_INTERNAL, WFUNIT0001, WFUNIT0002, WORKITEM_DEL</p>\n<p><strong>Transactions:</strong></p>\n<p>RSWF_EXECUTE_ACTIONS, RSWF_PUSH_NOT, RSWF_RESTART_WF, RSWF_SYSTEM_CLEANUP, RSWWCLEAR, RSWWCOND, RSWWDHEX, RSWWWIM, SWDD_ACTIVATE, SWDD_DISPLAY, SWDD_REPLICATE, SWDD_SCENARIO, SWDD_SCENARIO_DISP, SWDM, SWE3, SWE4, SWE5, SWEL, SWELS, SWEM, SWEQADM, SWEQDEL, SWF_CREATE_WITEXT, SWF_ENABLE_TRACE, SWF_EXECUTE_ACTIONS, SWF_FOOTPRINT, SWF_HELPERS_DISP, SWF_JOBREPO_SLG1_DSP, SWF_PROCESS_ADMIN, SWF_PROCESS_VIEW, SWF_SYSTEM_STATUS, SWFC, SWFVMD1, SWI1, SWI1_RULE, SWI11, SWI13, SWI2_ADM1, SWI2_DEAD, SWI2_DIAG, SWI2_DURA, SWI2_FREQ, SWI5, SWI6, SWIA, SWIADM1, SWIADM2, SWPC, SWPR, SWU0, SWU2, SWU3, SWU4, SWU5, SWU6, SWU7, SWU9, SWUD, SWW_MOVE_TO_HOT, SWWDHEX_DEBUG, SWWERRE_DEBUG</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Originally the concerned reports and transactions have been protected by an authorization check basing on the authorization group of the report. This check uses authorization object S_PROGRAM, which does not support checking the activity of a user action; that mean, no clear separation between display and change actions is possible.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>You use in your system helper reports or transactions from SAP Business Workflow.</p>\n<p><strong>Business-Process-Related Information</strong></p>\n<p>If you want to use the helper reports and transactions further, you have to change the authorizations of the user, which should be allowed to do so.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>From SAP_BASIS 756 onwards the authorization check by authorization object S_PROGRAM has been replaced by an authorization check on auhorization object S_WF_ADM. This new authorization object has two authorization fields:</p>\n<ul>\n<li>WF_GROUP</li>\n<li>ACTVT</li>\n</ul>\n<p><span>To a group all reports/transactions are assigned caring about the same topic. For example reports </span>RSWF_SHOW_SWW_WIREGISTER (Registered Actions for Work Items) and RSWEQADMIN (Event Queue Administration) are both assigned to group 'EVENT'.</p>\n<p><span>With the activity you can differentiate the actions, which are allowed to the user. For example by report RSWF_SHOW_SWW_WIREGISTER you can display the registered actions for work items, but you cannot change them. Therefore the display-activity is checked. But in report RSWEQADMIN several actions can be triggered, therefore it checks on the execute-activity.</span></p>\n<p><span>Your roles for the user needing these reports or transactions need to be adapted accordingly.</span></p>\n<p> </p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Technical System Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt your roles and authorizations as described in SAP note 2979517."}]}