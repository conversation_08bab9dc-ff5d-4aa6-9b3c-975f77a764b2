{"guid": "0050569455E21ED5B3E176783925809E", "sitemId": "SI1: Cross Industry - EDR Billing", "sitemTitle": "S4TWL - BRIM - Billing of Event Detail Records (EDR)", "note": 2271210, "noteTitle": "2271210 - S4TWL - BRIM - Billing of Event Detail Records (EDR)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>EDR Management is used to transfer event detail records (EDR) from an external system, manage the received information and prepare it for billing.</p>\n<p>The BRIM solution covers CRM, CI / FI-CA (ERP) and SAP Convergent Charging components/systems. It was developed to charge and bill single events like Toll Collects or downloads. The Event Detail Records solution was replaced by BIT BILLING in 2008.</p>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>No influence on business processes expected. All EDR customers are informed.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>To check if you use EDR Management, call the transaction SE16 and check for entries in the table DFKKBIBILLACC_H.</p>\n<p>Ensure that no fields contained in structure FKKBIINVBILL_I_DATA are used directly or indicretly in custom code and/or BRF(+)-Implementations to avoid short dumps.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": "Make people aware that the EDR is unavailable in SAP S/4HANA. The Event Detail Records (EDR) solution was replaced by BIT BILLING in 2008."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Adapt your Custom Code with regardas to the new solution"}]}