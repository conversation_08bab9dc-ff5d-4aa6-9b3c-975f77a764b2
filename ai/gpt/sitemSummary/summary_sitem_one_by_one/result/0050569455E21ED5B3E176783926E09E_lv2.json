{"guid": "0050569455E21ED5B3E176783926E09E", "sitemId": "SI3: Defense&Security", "sitemTitle": "S4TWL - Integration of DFPS with Investigative Case Management (ICM)", "note": 2273299, "noteTitle": "2273299 - S4TWL - Integration of DFPS with Investigative Case Management (ICM)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>ICM, Investigative Case Management, DFPS, Defense Security</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><span><strong>Description</strong></span></p>\n<p>The integration of DFPS with the CRM solution of Investigative Case Management is not available in SAP S/4HANA due to missing customer adoption in the SAP Business Suite. There is no functional equivalent available in SAP S/4HANA.</p>\n<p><span><strong>Business Process related information</strong> </span></p>\n<p><span>The features provided by the activation of Business Function DFPS_ICM_OF are not available in SAP S/4HANA, e.g.</span></p>\n<ul>\n<li>\n<div class=\"SAPXDPParagraph\">Necessary <em class=\"SAPXDPEmphasis\">technical prerequisites</em> for accessing <q class=\"SAPXDPScreenElement\" title=\"Object name\">Organizational Flexibility</q> functions from a CRM system for implementing the <q class=\"SAPXDPScreenElement\" title=\"Object name\">Investigative Case Management System</q>.</div>\n</li>\n<li>\n<div class=\"SAPXDPParagraph\">Display of the assignment of a <em class=\"SAPXDPEmphasis\">case ID</em> to an operation in the <q class=\"SAPXDPScreenElement\" title=\"Object name\">Structures Workbench</q> application.</div>\n</li>\n<li>\n<div class=\"SAPXDPParagraph\">Assignment of a position to a force element via the A/B 290 relationship to specify which position is responsible for an operation as the <em class=\"SAPXDPEmphasis\">operation planner</em>.</div>\n</li>\n</ul>\n<p><span><strong>Required and Recommended Action(s)</strong> </span></p>\n<p><span>Implement Business Scenarios and Business Processes based on the functionality available within SAP S/4HANA.</span></p>\n<p><strong><span>How to Determine Relevancy</span></strong></p>\n<p><span><span>Check in transaction SFW5, whether Business Function DFPS_ICM_OF is available and has been activated in your exsiting SAP Business Suite system. </span></span></p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "User Training", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Inform users about business process changes"}, {"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "The integration of DFPS with the SAP CRM solution of SAP Investigative Case Management is not available in SAP S/4HANA. Evaluate alternative business scenarios and business processes based on the functionality available within SAP S/4HANA and decide on implementation."}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Remove or adjust interfaces to the SAP CRM solution in SAP Investigative Case Management."}, {"Activity": "Implementation project required", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "Depending on business decision, design and implement alternative business scenarios and business processes based on the functionality available within SAP S/4HANA"}, {"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}, {"Activity": "Custom Code Adaption", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": ""}]}