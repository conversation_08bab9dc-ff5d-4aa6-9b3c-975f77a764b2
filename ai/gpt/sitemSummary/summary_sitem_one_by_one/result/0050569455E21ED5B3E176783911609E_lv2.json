{"guid": "0050569455E21ED5B3E176783911609E", "sitemId": "SI4: Logistics_MM-IM", "sitemTitle": "S4TWL - Material Valuation - Statistical moving average price", "note": 2267835, "noteTitle": "2267835 - S4TWL - Material Valuation - Statistical moving average price", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high-volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>\n<p>For goods movements changing the valuated inventory, the system uses exclusive locks to calculate a consistent change in inventory value in situations where concurrent transactions for the same material are ongoing. But, the need for setting exclusive locks is mainly driven by the inventory valuation method:</p>\n<ul>\n<li>An inventory valuation following the \"Standard Price\" method does (with a few exceptions) not require to set an exclusive lock. This is because the relationship between unit costs for material inventory (standard price) on the one hand, and inventory quantity and inventory value on the other hand, is kept constant during goods movements.</li>\n<li>Whereas an inventory valuation following the \"Moving Average Price\" method often requires to set an exclusive lock. This is because the relationship between unit costs for material inventory (moving average price) on the one hand, and inventory quantity and inventory value on the other hand, is often changing during goods movements.</li>\n</ul>\n<p>In SAP ERP, for materials with price control \"Standard\" the system calculates the inventory valuation following the \"Standard Price\" method mentioned above. Additionaly, it does an inventory valuation following the \"Moving Average Price\" method in parallel, the so-called \"statistical\" moving average price. Unfortunately, this \"statistical\" moving average price valuation requires exclusive locking as well.</p>\n<p>As mentioned at the beginning, exclusive locking limits the transactional data throughput, and therefore needs to be avoided to increase transactional data throughput significantly for scenarios with a high volume of transactions.</p>\n<p>If you do a system conversion to SAP S/4HANA on-premise edition, you have the <strong>option</strong> to change locking behavior during goods movements to increase transactional data throughput. As said, this is not mandatory for you, but nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements.</p>\n<p><strong>If you select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Materials with price control 'S': System will set (with a few exceptions) only a shared lock during goods movements.</li>\n<li>Materials with price control 'V': System will set an exclusive lock during goods movements in release SAP S/4HANA 1511. Beginning with release SAP S/4HANA 1610, system will set an exclusive lock during goods movements only in dedicated situations, for other situations it sets only a shared lock (see OSS note 2338387 for details).</li>\n<li>As described before, setting only a shared lock for materials with price control 'S' is in conflict with concept of calculating \"statistical\" moving average price, which would require an exclusive lock. Therefore, to gain the increase of transactional data throughput, <strong>system deactivates \"statistical\" moving average price and does not calculate it anymore</strong>.</li>\n<li>In addition, the following database table fields are affected by the deactivation of the \"statistical\" moving average price:</li>\n<ul>\n<li>In table MBEW, EBEW, OBEW, QBEW the fields SALKV and VERPR are not updated anymore for materials with price control “Standard”. The same applies to the history tables MBEWH, EBEWH, OBEWH and QBEWH.</li>\n<li>In table CKMLCR the fields PVPRS and SALKV are not updated anymore for materials with price control “Standard”.</li>\n<li>In table MLCR the fields SALKV, SALKV_OLD and PVPRS_OLD are not updated anymore for materials with price control “Standard”.</li>\n<li>If Actual Costing is used, the above mentioned fields will still be updated with the periodic unit price during the Actual Costing Closing Steps.</li>\n</ul>\n<li>As a consequence of not updating the above-mentioned fields, the \"statistical\" moving average price and the value based on the \"statistical\" moving average price are no longer available on the user interface. This concerns the following transaction codes: MM01, MM02, MM03, CKM3 and MR21.</li>\n<li>It is important to note that selecting this option <strong>can not be un-done</strong>.</li>\n</ul>\n<p><strong>If you do not select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Locking behaviour during goods movements is the same you know from SAP ERP. Means, exclusive locks are set during goods movements and transactional data throughput is limited.</li>\n<li>Materials with price control 'S': <strong>system still calculates \"statistical\" moving average price</strong> in parallel.</li>\n</ul>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>As its name implies, the \"statistical\" moving average price is purely statistical and does not have any impact on actual financials-relevant valuation.</p>\n<p>Without the \"statistical\" moving average price, an alternative way of valuation needs to be used in few scenarios. This concerns, for instance, Balance Sheet Valuation and Product Cost Planning, where selection/valuation variants must be checked and adapted.</p>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If within custom code the fields mentioned above under “Description” are evaluated for materials with price control “Standard”, then this usage should be removed. One possible reaction to this change could be to use instead of the V-Price (VERPR) either the S-Price (STPRS) or the V-Price (VERPR) depending on the configured price control for the respective material.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction code SE11 and to consider other techniques like transaction code CODE_SCANNER to find locations which SE11 cannot handle – such as dynamic programming or native SQL statements.</p>\n<p>Finally, if you like to select option to increase your transactional data throughput during goods movements and you accept the drawback that \"statistical\" moving average price gets deactivated, you can do the following:</p>\n<ul>\n<li>In SAP S/4HANA 1511 and SAP S/4HANA 1610 execute report SAPRCKM_NO_EXCLUSIVELY_LOCKING. As changes in customizing table TCURM are not recorded in a customizing request, you have to run report in each of your systems (customizing, development, production) separately.</li>\n<li>Beginning with SAP S/4HANA 1709 you can use IMG activity \"Set Material Lock for Goods Movements\" (SPRO -&gt; Materials Management -&gt; General Settings for Materials Management).</li>\n</ul>\n<p>As said, this <strong>is optional</strong> for converted systems running the S/4HANA on-premise edition.</p>\n<p><strong> </strong></p>\n<p><strong>Further Remarks</strong></p>\n<p>In the S/4HANA cloud edition or new installs of the SAP S/4HANA on-premise edition, the option to increase transactional data throughput during goods movements is mandatory and being set automatically. Deactivation of \"statistical\" moving average price is therefore also mandatory there.</p>", "noteVersion": 5, "refer_note": [{"note": "2338387", "noteTitle": "2338387 - S4TWL - Goods movements without exclusive locking by material valuation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>\n<p>Currently two different locking methods are available for goods movements:</p>\n<ul>\n<li>The first method locks all affected materials exclusively on their plant level during the entire document processing stage. This method offers the fastest document processing speed, since only a few locks need to be set and all required material data only needs to be read once for the whole process. The drawback of this method is a high lock collision probability for similar parallel postings. Moreover, this effect worsens with document size since every additional item extends the total exclusive locking duration and scope. This method is called “Exclusive block” in Customizing.</li>\n<li>The second method involves individual locking of material master and valuation data. Reduced exclusive locking times are used to minimize the collision probability between parallel postings. To achieve this, intended stock changes are communicated early in the process via the central enqueue server. All similar postings need to read these numbers from the enqueue server and take them into account along with the data read from the database. The drawback of this method is increased effort resulting in a longer total runtime for each document. This method is called “Late block” in Customizing.</li>\n</ul>\n<p>With SAP S/4HANA 1511 a new option to increase transactional data throughput for materials with Standard price control was introduced (for anonymous stock only). This option requires deactivation of the statistical moving average price and activation of “Late block”. With SAP S/4HANA 1610 this option also includes materials with Moving Average price control and all special stock types. The benefit of this option is that exclusive locking is no longer required for material valuation, which enables parallel material document processing. However, exclusive locking is still required if material master data is created or changed by a goods movement. Material master data is created by a goods movement if split valuation is used and a new batch has to be created, for instance. Changes of material master data concerns especially materials with Moving Average price control. When a goods movement changes the ratio between the stock value (SALK3) and the valuated stock quantity (LBKUM), the moving average price must be adjusted accordingly, which ultimately requires an exclusive lock until the V1 update. The quantity/value ratio is mainly changed by goods movements with an external value (for example, from the purchase order). However, if the change originates due to rounding differences only, the moving average price is not adjusted anymore. In addition, exclusive locking is still required, if SAP Note 1704534 (Deactivation of delivery costs for ML update) has been implemented.</p>\n<p>SAP S/4HANA for Oil &amp; Gas does also benefit from the new option starting with SAP S/4HANA 1709. For previous releases of SAP S/4HANA for Oil &amp; Gas the recommendation is to use “Exclusive block”.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The drawback of the new option described above is that temporary inconsistencies due to rounding differences may occur:</p>\n<ul>\n<li>Valuated stock quantity (LBKUM) = 0 and Stock Value (SALK3) &lt;&gt; 0</li>\n<li>Valuated stock quantity (LBKUM) &gt; 0 and Stock Value (SALK3) &lt; 0</li>\n<li>Valuated stock quantity (LBKUM) &lt; 0 and Stock Value (SALK3) &gt; 0</li>\n</ul>\n<p>In addition, the same inconsistencies can occur for value-only articles with VKSAL instead of LBKUM, if SAP for Retail is used. These inconsistencies are temporary because they are usually corrected by the next goods movement. Otherwise, transaction code MR23 (Adjustment of Inventory Value) is available to correct inconsistent stock values. You can find more information about the Adjustment of Inventory Value transaction in the SAP Help Portal.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>See SAP Note 2267835 for more information about the deactivation of statistical moving average price, which is the prerequisite to increase transactional data throughput as described above.</p>", "noteVersion": 4, "refer_note": [{"note": "2267835", "noteTitle": "2267835 - S4TWL - Material Valuation - Statistical moving average price", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>One major innovation of SAP S/4HANA in the area of MM-IM is the significant increase of transactional data throughput for high-volume transactions, especially goods movements. To enable this from a technical point of view, it is necessary for the application logic to avoid exclusive locking on the application layer.</p>\n<p>For goods movements changing the valuated inventory, the system uses exclusive locks to calculate a consistent change in inventory value in situations where concurrent transactions for the same material are ongoing. But, the need for setting exclusive locks is mainly driven by the inventory valuation method:</p>\n<ul>\n<li>An inventory valuation following the \"Standard Price\" method does (with a few exceptions) not require to set an exclusive lock. This is because the relationship between unit costs for material inventory (standard price) on the one hand, and inventory quantity and inventory value on the other hand, is kept constant during goods movements.</li>\n<li>Whereas an inventory valuation following the \"Moving Average Price\" method often requires to set an exclusive lock. This is because the relationship between unit costs for material inventory (moving average price) on the one hand, and inventory quantity and inventory value on the other hand, is often changing during goods movements.</li>\n</ul>\n<p>In SAP ERP, for materials with price control \"Standard\" the system calculates the inventory valuation following the \"Standard Price\" method mentioned above. Additionaly, it does an inventory valuation following the \"Moving Average Price\" method in parallel, the so-called \"statistical\" moving average price. Unfortunately, this \"statistical\" moving average price valuation requires exclusive locking as well.</p>\n<p>As mentioned at the beginning, exclusive locking limits the transactional data throughput, and therefore needs to be avoided to increase transactional data throughput significantly for scenarios with a high volume of transactions.</p>\n<p>If you do a system conversion to SAP S/4HANA on-premise edition, you have the <strong>option</strong> to change locking behavior during goods movements to increase transactional data throughput. As said, this is not mandatory for you, but nevertheless recommended in order to achieve a significant increase of transactional data throughput for goods movements.</p>\n<p><strong>If you select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Materials with price control 'S': System will set (with a few exceptions) only a shared lock during goods movements.</li>\n<li>Materials with price control 'V': System will set an exclusive lock during goods movements in release SAP S/4HANA 1511. Beginning with release SAP S/4HANA 1610, system will set an exclusive lock during goods movements only in dedicated situations, for other situations it sets only a shared lock (see OSS note 2338387 for details).</li>\n<li>As described before, setting only a shared lock for materials with price control 'S' is in conflict with concept of calculating \"statistical\" moving average price, which would require an exclusive lock. Therefore, to gain the increase of transactional data throughput, <strong>system deactivates \"statistical\" moving average price and does not calculate it anymore</strong>.</li>\n<li>In addition, the following database table fields are affected by the deactivation of the \"statistical\" moving average price:</li>\n<ul>\n<li>In table MBEW, EBEW, OBEW, QBEW the fields SALKV and VERPR are not updated anymore for materials with price control “Standard”. The same applies to the history tables MBEWH, EBEWH, OBEWH and QBEWH.</li>\n<li>In table CKMLCR the fields PVPRS and SALKV are not updated anymore for materials with price control “Standard”.</li>\n<li>In table MLCR the fields SALKV, SALKV_OLD and PVPRS_OLD are not updated anymore for materials with price control “Standard”.</li>\n<li>If Actual Costing is used, the above mentioned fields will still be updated with the periodic unit price during the Actual Costing Closing Steps.</li>\n</ul>\n<li>As a consequence of not updating the above-mentioned fields, the \"statistical\" moving average price and the value based on the \"statistical\" moving average price are no longer available on the user interface. This concerns the following transaction codes: MM01, MM02, MM03, CKM3 and MR21.</li>\n<li>It is important to note that selecting this option <strong>can not be un-done</strong>.</li>\n</ul>\n<p><strong>If you do not select option</strong> to increase your transactional data throughput during goods movements, this will have following effects:</p>\n<ul>\n<li>Locking behaviour during goods movements is the same you know from SAP ERP. Means, exclusive locks are set during goods movements and transactional data throughput is limited.</li>\n<li>Materials with price control 'S': <strong>system still calculates \"statistical\" moving average price</strong> in parallel.</li>\n</ul>\n<p> </p>\n<p><strong>Business Process related information</strong></p>\n<p>As its name implies, the \"statistical\" moving average price is purely statistical and does not have any impact on actual financials-relevant valuation.</p>\n<p>Without the \"statistical\" moving average price, an alternative way of valuation needs to be used in few scenarios. This concerns, for instance, Balance Sheet Valuation and Product Cost Planning, where selection/valuation variants must be checked and adapted.</p>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>If within custom code the fields mentioned above under “Description” are evaluated for materials with price control “Standard”, then this usage should be removed. One possible reaction to this change could be to use instead of the V-Price (VERPR) either the S-Price (STPRS) or the V-Price (VERPR) depending on the configured price control for the respective material.</p>\n<p>To identify such locations, it is required to make use of the where-used functionality of transaction code SE11 and to consider other techniques like transaction code CODE_SCANNER to find locations which SE11 cannot handle – such as dynamic programming or native SQL statements.</p>\n<p>Finally, if you like to select option to increase your transactional data throughput during goods movements and you accept the drawback that \"statistical\" moving average price gets deactivated, you can do the following:</p>\n<ul>\n<li>In SAP S/4HANA 1511 and SAP S/4HANA 1610 execute report SAPRCKM_NO_EXCLUSIVELY_LOCKING. As changes in customizing table TCURM are not recorded in a customizing request, you have to run report in each of your systems (customizing, development, production) separately.</li>\n<li>Beginning with SAP S/4HANA 1709 you can use IMG activity \"Set Material Lock for Goods Movements\" (SPRO -&gt; Materials Management -&gt; General Settings for Materials Management).</li>\n</ul>\n<p>As said, this <strong>is optional</strong> for converted systems running the S/4HANA on-premise edition.</p>\n<p><strong> </strong></p>\n<p><strong>Further Remarks</strong></p>\n<p>In the S/4HANA cloud edition or new installs of the SAP S/4HANA on-premise edition, the option to increase transactional data throughput during goods movements is mandatory and being set automatically. Deactivation of \"statistical\" moving average price is therefore also mandatory there.</p>", "noteVersion": 5}, {"note": "1985306", "noteTitle": "1985306 - Performance guide for goods movements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>With parallel goods movements, the system often issues message M3 024: \"Valuation data for material &amp; is locked by the user &amp;\" or M3 023: \"Plant data is locked by the user &amp;\", and you would like to find out more about what is behind these messages.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Late block, OMJI, MBEW, MARC, EM07W, EM07M, M3024, M3023, M3897, goods issue, post goods issue, PGI, VL02N, block error, goods movement, background processing, BAPI_GOODSMVT_CREATE, TCURM-MBEQU = 2, V_TCURM_ENQUEUE, BAPI_OUTB_DELIVERY_CONFIRM_DEC</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p class=\"p1\">Messages M3 024 and M3 023 are not program errors. Instead, they are informative notes for locks that are required from a business point of view in connection with a material for which different processes compete at the same time.</p>\n<p class=\"p1\">The purpose of the lock is to ensure the consistency of your stock quantities and stock values. Therefore, if one of the two messages mentioned above is issued, they are always justified and cannot be avoided. Otherwise, this would have an unwanted negative impact on your inventory valuation and subsequent postings in accounting.</p>\n<p class=\"p1\">Experience shows some customers have lots of untapped potential due to a lack of optimizations. This consulting note therefore gives you and your implementation partner guidance and tools to better understand the system behavior and to minimize the exclusive lock time for a material through customer-specific optimizations on the business process side or between the business process and the system side.</p>\n<p class=\"p1\">Your implementation partner or in-house consultant is responsible for optimizing or determining the ideal settings in accordance with your individual business processes</p>\n<p class=\"p1\">The task of SAP Development Support is to process errors in delivered standard software licensed by SAP. If you require support from SAP to optimize and align your business processes to the SAP software, especially in connection with points mentioned in this consulting note, contact your local SAP consulting organization.</p>\n<p class=\"p1\">For more information about contacting SAP, see the document <a href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf\" target=\"_blank\">\"The SAP Eco-System in a Nutshell</a>\".</p>\n<p class=\"p1\">Please understand that we cannot process incidents regarding the topics listed below as part of SAP Development Support in accordance with SAP Note 83020.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>This guide provides information on how the lock needed from a business point of view works in relation to the use of the late, exclusive lock (late lock) with goods movements, and identifies performance optimization opportunities, particularly with respect to goods issues.</p>\n<p><strong>1) Late lock for goods movements</strong></p>\n<p>You can use transaction OMJI or the Implementation Guide (SPRO), using the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Material Block for Goods Movements\", to carry this out.</p>\n<p>The late block with goods movements specifies that a material is not always blocked exclusively. It is blocked exclusively only when you actually want to save the data, in order to keep the block time as short as possible. The advantage of this process is that several users can enter goods movements simultaneously because when you enter a movement, the system only sets a shared block for the material.</p>\n<p>To begin with, you can leave the waiting time of the late block at the default value of 10 seconds. The waiting time for the late block specifies the number of blocking attempts permitted before the system rejects the goods movement with a block error. When the late block is activated, the system attempts to block the material master record data directly before the update starts. This is necessary in order to ensure that postings are consistent.</p>\n<p>If it is not possible to set the block because, for example, another user is currently carrying out an update process with the same material, the system waits for a second and then attempts to set the block again.</p>\n<p>A higher value for the waiting time means that the system rejects fewer good movements. However, the waiting time for the user may be longer.</p>\n<p>The late block is mapped on the central lock server via the lock objects EM07M for the quantities, EM07W for the values and EM07C for the batch valuation. In this case, the master tables (for example, MBEW, MARC) have a shared block only.</p>\n<p><strong>2) The late block for the material master</strong></p>\n<p>You can use transaction OMT0 or the Implementation Guide (SPRO), using the path \"Logistics - General -&gt; Material Master -&gt; Make Global Settings\", to set this block.</p>\n<p>If the indicator for the shared block is set in the material master, plant and valuation data (table MARC or MBEW) is not blocked exclusively in material master maintenance - this data is subject to a shared block. The advantage of this is that you can perform goods movements at the same time as material master maintenance, as long as you do not change any valuation-relevant or inventory management-relevant fields (for example, base unit of measure, batch management requirement indicator, valuation category or price unit) in material master maintenance.</p>\n<p>If you try to change a valuation-relevant or inventory management-relevant field, the system reacts as follows:</p>\n<ul>\n<li>It blocks the plant and valuation data in material master maintenance exclusively in order to prevent simultaneous goods movements.</li>\n<li>It reads the plant and/or valuation data from the database again because it is possible that it has changed due to a goods movement between the beginning of material master maintenance and changing the affected field.</li>\n<li>If a goods movement takes place or has taken place simultaneously or directly beforehand, the system does not perform the required change and issues a message. If this happens, try to change the data at a later time.</li>\n</ul>\n<p><br/><strong>3) The late block for invoice verification</strong></p>\n<p>You can use the Implementation Guide (SPRO), using the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Up Material Block for Invoice Verification\", to make the setting.</p>\n<p>In this step you determine the time when materials in invoice verification are blocked:</p>\n<ul>\n<li>During the assignment phase, the system blocks all the materials in the invoice. </li>\n<li>With simulation/posting, the system blocks and reads the materials whose stock needs to be changed.</li>\n<li>The system blocks and reads the materials whose stock needs to be changed only during posting.</li>\n</ul>\n<p>With direct posting to the material, the system blocks the specified material in all settings immediately. Likewise, if the material ledger is active, the material is blocked exclusively. If you use SAP S/4HANA, also refer to SAP Note 3044825.</p>\n<p>If you often post invoices parallel to goods movements, resulting in overlapping blocks, you can then set the block to come into effect with the simulation/posting only.</p>\n<p><strong>4) Price control and the effects on the late block</strong></p>\n<p>Use \"S\" price control for materials that you would like to post in parallel very often. During calculation of the values for the accounting document, it is always imperative from a business point of view to block the material (or the material assessment with the lock object EM07W) exclusively for a certain period. If the goods movement changes the ratio between the stock value (MBEW-SALK3) and the valuated stock (MBEW-LBKUM), the moving average price is adapted or changes have to be made to the previous period in a similar way, then an exclusive block is set until the V1 update is completed. If you use the \"S\" price control, the probability that the late, exclusive block is replaced by a shared block before the update begins is significantly higher than if you use moving average price control because the quantity/value ratio will probably not change. However, you must bear in mind that with \"S\" price control, the system valuates the material with the statistical moving average price in parallel as well. This means that, based on the statistical total value in alternative price control (MBEW-SALKV), the system creates a quantity/value ratio in relation to the quantity (MBEW-SALK3) in the same way. If this is influenced by the current update, it also results in the exclusive value block not being converted into the shared block before the update. In this way you definitely ensure that the statistical moving average prices are maintained correctly and checked periodically. Alternatively, SAP Note 139176 contains a report that you can use to set the statistical moving average price to the current standard price.</p>\n<p>You must also bear in mind that if, with a goods receipt, an external value (for example, from the purchase order) is specified that affects the quantity/value ratio of the statistical moving average price, the exclusive block is then compulsorily kept until the end of the V1 update program. Try to optimize your processes to ensure that, during peak times, you do not post goods receipts parallel to goods issue postings, in order to attain maximum goods issue performance.  </p>\n<p><strong>5) Performance optimization of customer-defined program source code</strong></p>\n<p>Ensure that, within the function modules MB_CREATE_GOODS_MOVEMENT and MB_POST_GOODS_MOVEMENT, you do not use any customer enhancements that cause an increase in the overall runtime (ABAP statements LOOP, SELECT). In addition to the exits &amp; BAdIs in inventory management, this also includes implicit/explicit enhancements and the output determination conditions.</p>\n<p>You can use transaction SE95 or the report SNIF to find activated exits/BAdIs. You can use the report MBFINDENHS to find enhancements (restriction to customer namespaces (for example, Z*) is advisable).</p>\n<p>Using a performance trace (transaction ST12 - see SAP Note 755977), you can also record the goods movement process and check all the activities between setting the exclusive value block (EEM07WE) and possibly converting it into a shared block for loss of performance.</p>\n<p><strong>6) To be borne in mind by SAP SCM APO with late block for goods movements</strong></p>\n<p>Bear in mind the application-specific settings for the integration with APO in the integration guide under \"Integration with Other SAP Components -&gt; Advanced Planning and Optimization -&gt; Application-Specific Settings and Enhancements -&gt; Settings and Enhancements for Stocks -&gt; Read Stock Values Before the Transfer to SAP APO\".</p>\n<p><strong>7) Optimization of the database update for the tables MBEW, MARD and MCHB</strong></p>\n<p>SAP Note 1737609 makes it possible to move the database lock for the update to the tables MBEW, MARD and MCHB in the update program to a later time. This is helpful if several processes on the database compete for blocking a data record/tuple of a table. In transaction OMJI or using the Implementation Guide (SPRO), use the path \"Materials Management -&gt; General Settings for Materials Management -&gt; Set Material Block for Goods Movements\" to activate the late database update. The system then performs the table updates only at the end of the update program, thus significantly reducing the total blocking time on the database for the data records involved, and therefore speeding up the parallel postings in the update program.</p>\n<p><strong>8) Individual fine tuning</strong></p>\n<p>If you have performed the above changes, it is advisable to use a mass test to determine the optimum waiting time/the number of blocking attempts from Point 1. There are no general recommendations for this because the value depends to a large extent on the type and constellation of the postings. Try to determine the best value in a realistic scenario by starting with a low value of 10 seconds and increasing it slowly until the terminations due to blocks decrease. In principle, you can increase the time to a maximum of 999 seconds. However, bear in mind that this significantly increases the time a dialog user waits until the block error, and that the runtime of the background job also increases.  You should therefore also adapt this value in line with the maximum resources available to you.</p>\n<p>If you would like to differentiate between the waiting time of a background user and a dialog user because, for example, the dialog user is to receive a block error after only 10 seconds but the background user is only to receive one after 180 seconds, you can use the BAdI in SAP Note 1840264 to achieve this. Also see the information in this SAP Note.</p>\n<p><strong>9) Special optimization with goods movements via delivery</strong></p>\n<p>If you post several parallel goods movements for a material in the same plant via several individual deliveries (transaction VL02N, VL32N or derivatives of them, such as BAPIs, IDocs), you can use the solution from SAP Note \"1776807 - Performance improvement for GI parallel processing\" to reduce the exclusive block time of the material. The SAP Note changes the processing sequence in the delivery update so that the decision to convert the exclusive value block (EM07W) to the shared block happens earlier which, in positive cases, results in the block being converted significantly earlier than before. One cause of long block times in a delivery could be, for example, an extremely excessive condition technique in delivery output determination. A performance trace, as described in Point 5, can provide information on this.</p>\n<p><strong>10) Blocks when using batches for goods issue</strong></p>\n<p>You are to post to the same batch in the event of parallel goods movements. Although the late block for goods movements has been activated (transaction OMJI), the system issues the message M3682 \"Batch &amp; of material &amp; is already locked by &amp;\". Batches are all exclusively blocked. SAP Note 1501121 provides a BAdI that you can use to control the shared lock for goods issue with batches. You may use the BAdI only if the business processes are coordinated in such a way that, during the goods movements, it is not possible to change batch data or classification data. This is because the shared block that is now set means that a simultaneous change cannot be prevented. The last save then applies.</p>\n<p>However, the transaction for the batch master (MSC2N) is an exception; this means that an exclusive block is still set that then also prevents a parallel goods movement.</p>\n<p>For transaction MIGO, you should additionally observe SAP Note 1523750, which is to be implemented in analogy to SAP Note 1501121 as the MIGO has an additional logic of its own for rejecting batch blocks.</p>\n<p><strong>11) Locks for Brazilian company codes</strong></p>\n<p>Historically, in Brazilian company codes, the plant segment (MARC) was locked exclusively when the nota fiscal was used, regardless of the system-wide customizing setting (see SAP Note 132193). SAP Note 3106950 removed this restriction. If error M3 897 \"The plant data of the material ... is locked by the user ...\" occurs despite the system-wide \"Late Lock\" setting in a Brazilian company code, you must implement SAP Note 3106950. <strong>You must always carry out the manual activity for explicit activation, regardless of the release/technical SAP Note implementation.</strong></p>\n<p><strong>12) Locks for goods movements in SAP S/4HANA</strong></p>\n<p><strong>Important:</strong> All the following SAP Notes are <strong>consulting notes</strong>. The instructions they contain <strong>must always be executed manually</strong>. Even if an SAP Note contains assignment to a support package, the manual activities always apply generally for all S/4HANA releases from On Premise edition 1610 or higher.</p>\n<p><strong>a) Posting without exclusive quantity/value lock</strong><br/>The lock logic in SAP S/4HANA has been completely redefined and, therefore, an option is available with SAP S/4HANA On Premise edition 1610 that enables no exclusive lock to be set with the late lock behavior. For more details, see SAP Notes 2319579 and 2267835. The new option for the late lock is activated by the report SAPRCKM_NO_EXCLUSIVELY_LOCKING if you have not done so yet.</p>\n<p><strong>b) Reduction of the runtime of a posting using pre-compaction</strong><br/>If you experience problems with the performance during the posting of goods movements, in particular with direct or indirect accesses to the tables MATDOC_EXTRACT or ACDOCA_M_EXTRACT, perform pre-compaction as described in SAP Notes 2246602 and 2342347 as a high priority. Pre-compaction is executed accordingly with the report FML_ACDOCA_M_EXTRACT_PRECOMP or NSDM_MTDCSA_PRECOMP.</p>\n<p><strong>13) Optimization of the individual business process</strong></p>\n<p class=\"p1\">Optimizing the alignment of your individual business process with the aforementioned system parameters requires extensive series of tests which you should define and execute together with your implementation partner or in-house consultant.</p>\n<p class=\"p1\">In general, we recommend that you keep the lock time of a material as short as possible. This is only possible if a single process takes up the lowest possible processing time. Ideally, you have already ensured this via the previous optimization steps. Otherwise, create a performance trace for your single process using transaction ST12 and try to identify and optimize the parts of the program with the highest runtime. This generally concerns message determination and/or customer-specific code (see point 5 or point 9). You can identify the message determination from the programs SAPLV61Z and/or SAPLV61A or from the condition tables in the trace. If these programs and customer-specific programs are no longer listed under the programs with the highest runtime, it can be assumed that the single process is optimized as far as possible.</p>\n<p class=\"p1\">The longer the process runtime of the single process, the longer the total lock time of a material automatically becomes and the less parallelization is possible. The quicker a posting process is ended, the faster the lock for a material is released again, and a parallel process can complete its posting.</p>\n<p class=\"p1\">Therefore, ideally no more than 250 posting lines should be included in one posting, even with posting processes optimized to the highest degree. Compare the process runtime per material document in your system with, for example: packages of 50, 125, or 250 lines and choose the ideal value according to your performance tests.</p>\n<p class=\"p1\">In general, though, no absolute recommendation can be made since this is always a customer-specific optimization process. Depending on the industry and individual requirements, each material has different attributes such as split valuation, batches, serial numbers, classification, and so on. In turn, each additional attribute contributes to the total runtime of the individual posting process.</p>\n<p><strong>14) Further support for process optimization</strong></p>\n<p class=\"p1\">If you have not yet reached a satisfactory result despite carefully processing the previous consulting and guidance, a holistic overall view of your system must be created. In addition to the points already listed in this SAP Note, our experience shows that the design of your system (sizing, operating system, basis settings) and other causes (database settings, configuration for table indexes or table buffers, release/patch level status, network communication, and so on) may also have a significant influence on the single process runtime, which in turn increases a material’s lock time.</p>\n<p class=\"p1\">As an Enterprise Support customer, you have the option of using additional specific services as part of the <a href=\"https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/continuous-quality-check-improvement-services.html\" target=\"_blank\">Continuous Quality Check &amp; Improvement Services</a> in order to have a corresponding check carried out. Examples include Business Process Improvement, Business Process Performance Optimization, EarlyWatch Check, or Technical Performance Optimization.</p>\n<p class=\"p1\">Alternatively, get in touch with your local contact person at SAP or read the document \"<a href=\"https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/incidents/sap-ecosystem.pdf\" target=\"_blank\">The SAP Eco-System in a Nutshell</a>\" for further support from SAP.</p>", "noteVersion": 9}]}, {"note": "2277568", "noteTitle": "2277568 - Activation of locking behavior \"no exclusively locking\" deprecates statistical moving average price", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Statistical moving average price is not available anymore, e.g. in valuation view of material master data, because new locking behavior \"no exclusively locking\" for goods movements is active (TCURM-MB_LOCK_MODE = '3').</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>To achieve a significant increase of transactional data throughput for goods movements the new locking behavior \"no exclusively locking\" for materials with price control \"Standard\" becomes obligatorily active</p>\n<ul>\n<li>for systems with S/4HANA cloud edition</li>\n<li>for systems with S/4HANA on-premise edition in case of new installation (\"green field approach\")</li>\n</ul>\n<p>In systems with S/4HANA on-premise edition that were built up with a data conversion e.g. from SAP ERP, the customer can decide on its own whether to activate the new locking behavior \"no exclusively locking\" or not. This new locking behavior can be activated with report SAPRCKM_NO_EXCLUSIVELY_LOCKING. Once activated this setting can not be deactivated anymore.</p>\n<p>The deactivation of the statistical moving average price is the consequence of the new locking behavior. This new locking behavior does not use exclusive locks, which is why the system is not able anymore to calculate a statistical moving average price. Therefore the activation of the new locking behavior leads to the deactivation of the statistical moving average price.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Please apply attached correction instruction to prevent activation of locking behaviour \"no exclusively locking\" by executing report SAPRCKM_OBLIGATORY_SETTINGS.</p>", "noteVersion": 2}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "The report SAPRCKM_NO_EXCLUSIVELY_LOCKING can be used to deactivate the statistical moving average price."}, {"Activity": "Business Decision", "Phase": "Before or during conversion project", "Condition": "Optional", "Additional_Information": "Decide to deactive statistical moving average price. It is optional in SAP S/4HANA if you convert from SAP ERP, however, it is recommended to achieve a significant increase of transactional data throughput for goods movements. It is important to note that the deactivation is not reversible."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If you decide to deactivate statistical moving average price, adjust your own coding respectively."}]}