{"guid": "901B0E6D3ED11ED7B8B214CA391CA0D0", "sitemId": "SI20: PROC_MM_IV_ILM", "sitemTitle": "S4TWL - Supplier Invoice New Archiving", "note": 2578291, "noteTitle": "2578291 - Simplification Item: Supplier Invoice Archiving", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Supplier Invoice Archiving does not fully support ILM Integration</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>With upgade to 1809 ILM Integration is fully supported.</p>\n<p>Please note: BAdI Implementations used in the old archiving reports (RM08RARC, RM08RADE) are no longer supported. The new archiving reports (RM08RW47, RM08RD47) use other BAdIs (ARC_MM_REBEL_WRITE, ARC_MM_REBEL_CHECK) which need to be newly implemented.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Business Decision", "Phase": "Before conversion project", "Condition": "Mandatory", "Additional_Information": "Supplier Invoice Archiving does not fully support ILM Integration (with upgade to 1809 ILM Integration is fully supported)"}, {"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Adapt the process due to changes in functionality"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Implement new BAdIs as indicated in SAP Note 2578291."}]}