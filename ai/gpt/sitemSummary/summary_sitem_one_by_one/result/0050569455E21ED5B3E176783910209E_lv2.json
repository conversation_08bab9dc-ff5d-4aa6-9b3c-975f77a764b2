{"guid": "0050569455E21ED5B3E176783910209E", "sitemId": "SI3: Logistics_ATP", "sitemTitle": "S4TWL - New fashion solution", "note": 2267749, "noteTitle": "2267749 - S4TWL - New fashion solution", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The new fashion solution comprising characteristic based ATP is not available in SAP S/4HANA 1511 or SAP S/4HANA 1610. Appropriate Business Function LOG_SEGMENTATION will be 'always off' within SAP S/4HANA, on-premise edition.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes which are based on ATP check for segmentation cannot be used in SAP S/4HANA 1511 or SAP S/4HANA 1610.</p>\n<p>Business processes which are based on the new fashion solution can't be used in SAP S/4HANA 1511 or SAP S/4HANA 1610.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Do not upgrade to SAP S/4HANA 1511 or SAP S/4HANA 1610 if you rely on stock segmentation.</p>\n<p>With SAP S/4HANA release 1709 and higher, the functionality of Segmentation is supported in combination with Advanced ATP (aATP) with an extended field length, please see the related note 2522971.</p>", "noteVersion": 3, "refer_note": [{"note": "2522971", "noteTitle": "2522971 - S4TWL - Segment Field Length Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a sytem conversion from SAP ERP 6.0 EHP7/EHP8 to SAP S/4HANA 1709 and you are using segmentation functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of segments has changed in SAP S/4HANA 1709. The field length has been increased from 16 to 40 characters.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, adjustments are made automatically.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation functionality is used.<br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with MARA-SGT_REL non initial.</p>", "noteVersion": 1, "refer_note": [{"note": "2471098", "noteTitle": "2471098 - Conversion of Default Segment Values Data", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709 (S4CORE 102), and you are using segmentation and default segment values in SAP Fashion Management.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Fashion Management, SAP ERP, Segmentation</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>In SAP Fashion Management, system was equipped with two ways to maintain default segments that can be consumed by different documents. Few applications use the data in the table SGT_CATDYN for the determination of default segment value. Few applications use the data in the tables SGT_SEGVAL and SGT_SEGVAL_STG. Both of them have different data models and data maintenance tools.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>In S/4HANA, single data model combines the functionality and advantages of both the data model and single data maintenance tool to handle the data maintenance. The data present in the above mentioned tables will be converted and stored in the tables SGT_DYNCAT and SGT_DYNVAL under a single data model. Applications will start determining the default segment by using the new data model.</p>\n<p>During the conversion, the following report is executed automatically R_SGT_DEFAULT_MIG.<br/>In case of problems during the conversion, you can execute these reports manually.</p>", "noteVersion": 3}]}]}