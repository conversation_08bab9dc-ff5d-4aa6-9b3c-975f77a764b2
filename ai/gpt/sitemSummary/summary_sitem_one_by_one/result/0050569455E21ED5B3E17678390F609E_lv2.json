{"guid": "0050569455E21ED5B3E17678390F609E", "sitemId": "SI15: PROC_FT", "sitemTitle": "S4TWL - Foreign Trade within SAP S/4HANA Procurement", "note": 2267740, "noteTitle": "2267740 - S4TWL - Foreign Trade within SAP S/4HANA Procurement", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Currently there are two software services for international trade transactions: Foreign Trade and SAP Global Trade Services (GTS). Foreign Trade is part of standard Materials Management (MM).</p>\n<p>SAP GTS is an external service that can be installed on a separate instance. With SAP S/4HANA on-premise edition 1511, the Foreign Trade solution will be not available anymore because SAP Global Trade Services (GTS) is the successor for the business requirement.</p>\n<p>For <em>Intrastat</em> a customer can leverage functionality within SAP S/4HANA, on-premise edition 1511.</p>\n<p>Additional functions for import and export management are available with SAP <span ar-sa;\"=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman',serif;=\"\" times=\"\">Global Trade </span>GTS.</p>\n<p><strong>Business Value</strong></p>\n<p>It is a strategic decision of SAP to use SAP Global Trade Services for international trade processes. Furthermore, selected international trade functionalities will be provided <span ar-sa;\"=\"\" calibri;=\"\" de;=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\" new=\"\" roman',serif;=\"\" times=\"\">with Foreign Trade in SAP S/4HANA.</span></p>\n<p><strong>Business Process related information</strong></p>\n<p>The customer needs to check whether 3rd party foreign trade systems are in use for foreign trade processes. Because of the replaced foreign trade functionality in S/4HANA, it is possible that 3rd party foreign trade system will need adjustments by the respective 3rd party Solution/Service Provider. However, SAP GTS can be connected to S/4HANA to run the respective foreign trade processes.</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>VX99     FT/Customs: General overview</p>\n<p>EN99     General Foreign Trade Processing</p>\n<p>VI09X    Change FT Data in Purchasing Doc.</p>\n<p>VI08X    Display FT data in purchasing doc.</p>\n<p>VI80X    Change FT Data in Goods Receipt</p>\n<p>VI79X    Display FT Data in Goods Receipt</p>\n<p>VI77X    Change FT Data in Outbound Delivery</p>\n<p>VI64X    Display FT Data in Outbound Delivery</p>\n<p>VI14X    Change FT Data in Billing Document</p>\n<p>VI10X    Display FT Data in Billing Document</p>\n<p>VIIM      FT: Op. Cockpit: Purchase order</p>\n<p>VIWE     FT: Op. Cockpit: Goods Receipt</p>\n<p>VIEX      FT: Journal Export Actual</p>\n<p>VE85      Change Statistical Value – Import</p>\n<p>VE86      Display Statistical Value – Import</p>\n<p>VE88      Change Statistical Value – Export</p>\n<p>VE89      Display Statistical Value – Export</p>\n<p>VE87      Change Stat.Value – Subcontracting</p>\n<p>VEI3       Display Stat.Value – Subcontracting</p>\n<p>OVE1     Commodity Code / Import code no.</p>\n<p>VXCZ     INTRASTAT: Form - Czech Republic</p>\n<p>VXHU    INTRASTAT: Form - Hungary</p>\n<p>VXPL     INTRASTAT: Form – Poland</p>\n<p>VXSK     INTRASTAT: Form – Slovakia</p>\n<p>VEBE     INTRASTAT: XML File - Belgium VECZ     INTRASTAT: File - Czech Republic</p>\n<p>VEHU    INTRASTAT: File – Hungary</p>\n<p>VEPL      Create INTRASTAT CUSDEC EDI PL</p>\n<p>VESK     Create INTRASTAT CUSDEC EDI SK</p>\n<p>VEIA      Create INTRASTAT CUSDEC EDI SE</p>\n<p>VEIB      Create INTRASTAT CUSDEC EDI PT</p>\n<p>VEID      Create INTRASTAT CUSDEC EDI LU</p>\n<p>VEI0       Create INTRASTAT CUSDEC EDI IE</p>\n<p>VEI7       Create INTRASTAT CUSDEC EDI GB</p>\n<p>VEI8       Create INTRASTAT CUSDEC EDI AT</p>\n<p>VEI9       Create INTRASTAT CUSDEC EDI ES</p>\n<p>VE02      INTRASTAT: Create Form – Germany</p>\n<p>VE03      INTRASTAT: Create File - Germany</p>\n<p>VE06      INTRASTAT: Paper Form – Belgien</p>\n<p>VE07      Create INTRASTAT Form for France</p>\n<p>VE08      Create INTRASTAT File for Italy</p>\n<p>VE09      Create INTRASTAT file for Belgium</p>\n<p>VE10      Create INTRASTAT file for Holland VE11      Create INTRASTAT file for Spain</p>\n<p>VE12      Create INTRASTAT form for Holland</p>\n<p>VE15      Create disk - INTRA/EXTRA/KOBRA/VAR</p>\n<p>VE16      Create INTRASTAT form for Austria</p>\n<p>VE17      Create INTRASTAT form for Sweden</p>\n<p>VE32      INTRASTAT: Paper Form – Ireland</p>\n<p>VE37      INTRASTAT: File - France</p>\n<p>VE33      INTRASTAT: Paper Form - U.K.</p>\n<p>VE42      INTRASTAT: File - Denmark</p>\n<p>VE45      INTRASTAT: Paper Form – Greece</p>\n<p>VE46      INTRASTAT: File - Finland</p>\n<p>VE95      Create INTRASTAT papers: Portugal</p>\n<p>VICZ      Create INTRASTAT CUSDEC EDI CZ</p>\n<p>VIIE        Create INTRASTAT XML IE</p>\n<p>VEFU     Foreign Trade: Add INTRASTAT Data</p>\n<p>VIMM   Decl. Recpts/Disptch Min. Oil Prod.</p>\n<p>VE96      EXTRASTAT Data Select.: Init. Screen</p>\n<p>VE04      EXTRASTAT: Data selection for export VE05      EXTRASTAT: Create File – Germany</p>\n<p>VE97      Create EXTRASTAT tape: Netherlands</p>\n<p>VEXU    Foreign Trade: Add EXTRASTAT Data</p>\n<p>VE13      KOBRA data selection: export Germany</p>\n<p>VE14      Create KOBRA file for Germany</p>\n<p>VE15      Create disk - INTRA/EXTRA/KOBRA/VAR</p>\n<p>VE21      VAR: Selection of bill. docs Switz.</p>\n<p>VE22      Create VAR form for Switzerland</p>\n<p>VE23      V.A.R.: File – Switzerland</p>\n<p>VE18      SED data selection for USA exporter</p>\n<p>VE25      SED: Selection: USA Carriers</p>\n<p>VE19      Create SED form for USA</p>\n<p>VE20      Create AERP file for USA</p>\n<p>VE27      HMF: Selection – USA</p>\n<p>VXJ1      MITI Declarations</p>\n<p>VXJ2      Declaration of ImportBill.Docs Japan</p>\n<p>VEIAI     INTRASTAT Archiving</p>\n<p>VEIAE    EXTRASTAT Archiving</p>\n<p>VE81      Check Report: General FT Data</p>\n<p>VE73      Goods Catalog: Create Document</p>\n<p>VE74      Goods Catalog: Create Diskette</p>\n<p>VXA5     Document. Payments: Print Monitoring</p>\n<p>VXA7     Documentary Payments: Simulation</p>\n<p>ENSV     Data Service</p>\n<p>VX01N  Create License</p>\n<p>VX02N  Change License</p>\n<p>VX03N  Display License</p>\n<p>VX22N  Change License Data</p>\n<p>VX23N  Display license data</p>\n<p>VE31      Blocked SD Documents</p>\n<p>VE30      Existing licenses</p>\n<p>VE29      Assigned Documents for Each License</p>\n<p>VE82      Check Report: Export Control Data</p>\n<p>VI83       Check Customer Master/Legal Control</p>\n<p>VX07     Simulation: License Check</p>\n<p>VX09     Simulation: Embargo Check</p>\n<p>VX08     Simulation: Boycott List Check</p>\n<p>VX16     BAFA diskette: Selection</p>\n<p>VX17     Create BAFA diskette</p>\n<p>VX10     Countries of Destination for License</p>\n<p>VX05     Customers for License</p>\n<p>VX06     Export Control Classes for License</p>\n<p>VE56      Check Export Control for Consistency</p>\n<p>VI84       Doc.Payments: Check Customer Master</p>\n<p>VX14N  Maintain Financial Document</p>\n<p>VX11N  Create Financial Document</p>\n<p>VX12N  Change Financial Document</p>\n<p>VX13N  Display Financial Document</p>\n<p>VI73N   Foreign Trade: Maintain Long-Term Vendor Declaration</p>\n<p>VI74N   FT: Display Vendor Declaration</p>\n<p>VI78       Foreign Trade: Countries</p>\n<p>VI54       Customs Approval Numbers</p>\n<p>VI55       Approval Number per Plant</p>\n<p>VED1     Print Parameters for Export Documents</p>\n<p>OVE1     Commodity Code / Import Code Number</p>\n<p>VPAR    Archiving Preference Logs</p>\n<p>VFAI      Archiving – INTRASTAT</p>\n<p>VFAE     Archiving – EXTRASTAT</p>\n<p>FTUS     Foreign Trade: Maintain User Data</p>\n<p>ENCO    Printing / Communication</p>\n<p>ENGK    Legal Control</p>\n<p>ENLO     Documentary payments</p>\n<p>ENPR     Preference Processing</p>\n<p>ENGR    Periodic Declarations</p>\n<p>ENSV     Foreign Trade Data Maintenance</p>\n<p>ENZD     Customs Objects: Documentation/Info.</p>\n<p>VOIM    Check Import Purchase Order</p>\n<p>VOWE   Check Goods Receipt</p>\n<p>VI09X    Change Import Purchase Order</p>\n<p>VI53X    Change Goods Receipt</p>\n<p>VOLI      Check Export Delivery</p>\n<p>VOEX    Check Export Billing Document</p>\n<p>VI77X    Change Export Delivery</p>\n<p>VI14X    Change Export Billing Document</p>\n<p>VI85       Check Report: Vendor Master</p>\n<p>VE81X   Check Report: General Foreign Trade Data</p>\n<p>VE82X   Check Report: Export Control Data</p>\n<p>VE83X   Check Report: Preference Data</p>\n<p>VI86       Cross-plant Check of Foreign Trade Data</p>\n<p>VI82X    Foreign Trade: Customer Master Consistency – General</p>\n<p>VI83X    Foreign Trade: Customer Master Consistency – Control</p>\n<p>VI84X    Foreign Trade: Customer Master Consistency - Letter of Credit</p>\n<p>VIE4       Periodic Declarations: Log of Incomplete Items</p>\n<p>VE24X   Foreign Trade: Information Commodity Code</p>\n<p>VI24X    Foreign Trade: Information Import Code Number</p>\n<p>FTIM     Journal: Purchase Orders</p>\n<p>FTGR     Journal: Goods Receipts</p>\n<p>FTIM     Import Order Analysis</p>\n<p>FTGR     Import Gds Receipt Analysis</p>\n<p>FTEX      Export Bill. Documents Journal</p>\n<p>FTEX      Exp.bill.doc.analysis</p>\n<p>VE30      Expiring Licenses</p>\n<p>VE29      Sales Orders for a License</p>\n<p>VXA2     Financial Documents: List</p>\n<p>VXA1     Financial Documents: SD Documents</p>\n<p>VI74N   Display Vendor Declaration</p>\n<p>CAAT     Foreign Trade: Call up FT Atrium</p>\n<p>VIUL      Foreign Trade: Data Upload</p>\n<p>VIBA      Send IDoc Output-AES-Initial Procg</p>\n<p>VIBC      Send IDoc Output-AES-Error in Procg</p>\n<p>VIBB      Send IDoc Output-AES-Repeat Procg</p>\n<p>VIB7      Send IDoc Output - Initial Procg</p>\n<p>VIB9      Send IDoc Output - Error in Procg</p>\n<p>VIB8      Send IDoc Output - Repeat Procg</p>\n<p>VIB4      Print Transaction: Initial Procg</p>\n<p>VIB6      Print Transaction: Error in Procg</p>\n<p>VIB5      Print Transaction: Repeat Procg</p>\n<p>VIB3      Foreign Trade Output Status</p>\n<p>VEII        SAPMSED8: Call IMPINV01</p>\n<p>VEIE       SAPMSED8: Call EXPINV02</p>\n<p>VX00     Preference Handling</p>\n<p>LEAN     Request long-term VendDecl. (vendor)</p>\n<p>LEMA    Dun long-term vendor decl. (vendor)</p>\n<p>VE67      Aggregate Vendor Declarations</p>\n<p>LEER      Create long-term VenDecl. (customer)</p>\n<p>VE55      Preference Determination: Individual</p>\n<p>VE54      Preference Determination: Collective</p>\n<p>VEB5     Calculate Assemblies Individually</p>\n<p>VEB6     Calculate Assemblies Collectively</p>\n<p>VEPR     Customs log</p>\n<p>VE91      Display Preference Values</p>\n<p>VE90      Change preference values</p>\n<p>VE83      Check Report: Preference Data</p>\n<p>VE66      Preference Procedure</p>\n<p>VE65      Preference Reg./Percentage Rates</p>\n<p>VE64      Commodity Code/Customs Areas</p>\n<p>VE94      Load Commodity Code for EU Countries</p>\n<p>VEM4    Merge: EU - Commodity Code</p>\n<p>VI94       Load Import Code Nos - EU Countries VIM4     Merge: EU - Import Code Number</p>\n<p>VA94     Load Commodity Codes for Japan</p>\n<p>VAM4   Merge: Japan - Commodity Code</p>\n<p>VP94     Load Import Code No. for Japan</p>\n<p>VPM4   Merge: Japan - Import Code Number</p>\n<p>VEU4     Load Commodity Code-Other Countries</p>\n<p>VEI4       Merge: Remaining Commodity Codes</p>\n<p>VIU4      Load Import Code No.-Other Countries</p>\n<p>VII4        Merge: Rest - Import Code Number</p>\n<p>VE24      Comm. Code Number Information  (old)</p>\n<p>VI24       Code Number Information – Import</p>\n<p>VI20       Display Customs Quota</p>\n<p>VI21       Display Pharmaceutical Products</p>\n<p>VI22       Display Customs Exemption</p>\n<p>VI23       Display Preferential Customs Duties</p>\n<p>VI19       Display Third-country Customs Duties</p>\n<p>VI18       Display Anti-dumping</p>\n<p>VXA3     Financial Documents: Blocked Docs VXA2     Existing Financial Documents</p>\n<p>VE68      Request Vendor Declarations</p>\n<p>VI75       Vendor Declarations - Dunning notice</p>\n<p>VE84      Monitoring: Purchasing Info Records</p>\n<p>VE83      Check Report: Preference Data</p>\n<p>VXSIM                  Simulate Import</p>\n<p>WZFT01               FT: Wizard - Declara. to Auth. (MM)</p>\n<p>WZFT02               FT: Wizard - Declara. to Auth. (SD)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p> </p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Analysis of all currently used foreign trade processes.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom Code related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2223144</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4, "refer_note": [{"note": "2223144", "noteTitle": "2223144 - S/4 HANA - Foreign Trade in SD/MM", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are using an SAP ERP system and intend to perform the upgrade-like conversion to SAP S/4HANA.</p>\n<p>You are using Foreign Trade functionality in the areas SD-FT or MM-FT.</p>\n<p>This functionality is no longer supported in SAP S/4HANA.</p>\n<p>Related Custom Code will no longer work.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Foreign Trade, SD-FT, MM-FT, GTS</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Upgrade-like conversion to SAP S/4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>It is a strategic decision of SAP to use SAP Global Trade Services for international trade processes. Furthermore selected international trade functionalities will be provided in SAP S/4HANA for international trade.</p>\n<p>Currently there are two software services for international trade transactions: Foreign Trade and SAP Global Trade Services(GTS). Foreign Trade is part of Sales and Distribution (SD) scope. <br/>SAP GTS is an external service that can be installed on an additional instance.</p>\n<p>In SAP S/4HANA, the Foreign Trade (SD-FT) functionality is not available anymore. Instead, SAP S/4HANA for international trade within SAP S/4HANA is the successor.</p>\n<p>With SAP S/4HANA 1709, letter of credit processing is provided by integration of SAP S/4HANA Sales with Trade Finance.<br/>For Intrastat, Legal Control and Classification with Commodity Codes customers can leverage SAP S/4HANA for international trade functions within SAP S/4HANA. <br/>For Preference Management, you can use functions based on SAP GTS. SAP GTS can be natively integrated with SAP S/4HANA. <br/><br/>For shipped releases compare the Feature Scope Description for SAP S/4HANA under<br/><a href=\"https://help.sap.com/s4hana\" target=\"_blank\">https://help.sap.com/s4hana</a> -&gt; Product Documentation -&gt; Feature Scope Description</p>\n<p>For future releases compare the Roadmap for SAP S/4HANA under <br/><a href=\"https://www.sap.com/products/roadmaps.html\" target=\"_blank\">https://www.sap.com/products/roadmaps.html</a>, <br/>section \"Lines of Business\" --&gt; \"LoB: Finance\".</p>\n<p><span>Additional functions for Import- and Export Management are available with SAP GTS.</span></p>\n<p>As long as this functionality is not yet available in SAP S/4HANA: Check whether usage of the solution SAP GTS could be relevant for you.</p>\n<p>All usages of SAP objects in customer objects for which the custom code check refers to Note 2223144 will no longer work, and must be removed.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>You need to analyze all foreign trade processes currently in use.</li>\n<li>You need to check whether third party Foreign Trade systems are in use for Foreign Trade processes.</li>\n<li>Due to the replaced Foreign Trade functionality in SAP S/4HANA, the respective third party solution/service provider may have to make adjustments to the third party Foreign Trade systems. However, you can connect SAP Global Trade Services (SAP GTS) to SAP S/4HANA to run the respective Foreign Trade processes.</li>\n<li>To migrate Legal Control data, check the documentation in KBA <a href=\"/notes/2869019\" target=\"_blank\">2869019</a>. <a href=\"https://help.sap.com/viewer/dfb1ceeb602c4a1dbde1171e3493209e/2020.002/en-US/36acaf2db9234e43bf49d930b46ef05a.html\" target=\"_blank\"><br/></a></li>\n<li>To migrate financial documents in Foreign Trade to Trade Finance, you carry out the migration steps available as Customizing activities (transaction SPRO, SAP Reference IMG) under:</li>\n<ul>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Preparations and Migration of Customizing--&gt;Preparations for Migration of Financial Documents to Trade Finance</li>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Data Migration--&gt;Migration of Financial Documents to Trade Finance and Complete Migration of Financial Documents to Trade Finance</li>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Activities after Migration--&gt;Manual Activities for Trade Finance</li>\n</ul>\n</ul>", "noteVersion": 7, "refer_note": [{"note": "2190420", "noteTitle": "2190420 - SAP S/4HANA: Recommendations for adaption of customer specific code", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a conversion to SAP S/4HANA or an upgrade to a higher SAP S/4HANA version and want to check in advance, if adaptions to your own ABAP coding are required in order to make it compatible with the target SAP S/4 HANA version.</p>\n<p>Or you are already on SAP S/4HANA and want to continously ensure that your new ABAP developments are compatible with SAP S/4HANA.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S/4 HANA, Custom Code Check</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Before looking into SAP S/4HANA custom code aspects it's important to understand, that the vast majority of custom code recommendations, best practices, activities and tools you might already know from SAP ERP are also applicable to SAP S/4HANA. Similarly, the majority of existing custom code on SAP ERP is compatible with SAP S/4HANA.</p>\n<p>So as far as custom code in SAP S/4HANA is concerned, there are indeed some differences on top of everything existing that you have to learn about and to adapt to. But these are rather small differences.</p>\n<p><span>The tools and processes that apply to custom code development on SAP ERP or any other ABAP based product are also applicable to SAP S/4HANA</span>. Including</p>\n<ul>\n<li>check for and remove obsolete custom code, unnecessary modifications and unnecessary clones of SAP objects, in order to keep your code base clean (housekeeping activities)</li>\n<li>run all code checks provided by SAP (e.g. functional correctness, performance, security etc.) on your custom code.</li>\n<li>do performance profiling and optimization (general as well as SAP HANA specific) of your custom code.</li>\n<li>define suitable test concepts and test your custom code. Including leveraging the possibilities of test automation.</li>\n<li>follow custom code best practices (ABAP, SQL in general and SAP HANA specific).</li>\n<li>do SPDD, SPAU, SPAU_ENH adjustments during lifecycle events.</li>\n</ul>\n<p><span>The recommended frequency and points in time for thes</span><span>e activities for SAP S/4HANA is the same as for SAP ERP and other ABAP based products</span>. Do the housekeeping and code checks</p>\n<ul>\n<li>on a regular basis, accompanying your development activities.</li>\n<li>with increased focus and intensity before major lifecycle events. Which in case of SAP S/4HANA means:</li>\n<ul>\n<li>Before the conversion to SAP S/4HANA</li>\n<li>Before an upgrade to a higher SAP S/4HANA release</li>\n</ul>\n</ul>\n<p><span>Most custom code developed on SAP ERP will run on SAP S/4HANA without or with only minor adaptions</span>. In order to achieve this, SAP S/4HANA includes various compatibility concepts and layers to minimize the custom code impact even in areas where bigger functional or architecture changes have taken place. One example for this are the so called \"compatibility views\" which provide backward compatibility in case of data model changes.</p>\n<p><span>On top of all this SAP provides SAP S/4HANA specific custom code checks</span>. These identify areas where custom code indeed needs to be adapted due to removed, replaced or changed functionality in SAP S/4HANA for areas where custom code compatibility measures are not feasible. These SAP S/4HANA specific custom code checks are fully integrated into the framework of the already existing code checks (ABAP Test Cockpit).</p>\n<p>Like in other static code checks, there are certain types of issues the SAP S/4HANA specific custom code checks cannot detect. E.g. dynamic usages which are only visible on runtime. Therefore even with the SAP S/4HANA specific custom code checks in place, it's still crucial to properly test your custom code on SAP S/4HANA.</p>\n<p>Also the SAP S/4HANA specific custom code checks can only determine on a technical level where custom code might need to be adapted to SAP S/4HANA. For some types of issues it depends in addition on the functional/business context in which the coding is used, if the coding actually needs to be adapted. To make this decision is beyond the capabilties of the SAP S/4HANA specific custom code checks and requires a final assessment of the findings by a developer. Therefore the SAP S/4HANA specific custom code checks might initially show more issues than actually have to be fixed.</p>\n<p>For a more detailed introduction to all these aspects, please refer to the following blog post:</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/\" target=\"_blank\">https://blogs.sap.com/2017/02/15/sap-s4hana-system-conversion-custom-code-adaptation-process/</a></li>\n</ul>\n<p>And to the following ressources on how to setup ABAP Test Cockpit for doing  SAP S/4HANA specific custom code checks</p>\n<ul>\n<li><a href=\"https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/\" target=\"_blank\">https://blogs.sap.com/2016/12/12/remote-code-analysis-in-atc-one-central-check-system-for-multiple-systems-on-various-releases/</a></li>\n<li>SAP note <a href=\"/notes/2436688\" target=\"_blank\">2436688</a></li>\n<li>SAP note <a href=\"/notes/2241080\" target=\"_blank\">2241080</a></li>\n</ul>", "noteVersion": 19}, {"note": "2267310", "noteTitle": "2267310 - S4TWL - Foreign Trade", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>There are currently two software services for international trade transactions: SAP ECC SD Foreign Trade (SD-FT) and SAP Global Trade Services (SAP GTS). <br/> Foreign Trade (SD-FT) is part of the standard SAP ECC Sales and Distribution (SD) scope. <br/> SAP GTS is an external service that can be installed on an additional instance.</p>\n<p>In SAP S/4HANA, the Foreign Trade (SD-FT) functionality is not available anymore. Instead, SAP S/4HANA for international trade within SAP S/4HANA is the successor.</p>\n<p>With SAP S/4HANA 1709, letter of credit processing is provided by integration of SAP S/4HANA Sales with Trade Finance.<br/> For Intrastat, Legal Control and Classification with Commodity Codes customers can leverage SAP S/4HANA for international trade functions within SAP S/4HANA.<br/>(Please note, that in the sales area Legal Control checks are currently only enabled for document types C - Sales Orders and G - Contract. For document type I - Free of Charge order and E - Scheduling Agreement this is planned to be available in one of the next releases.)<br/>For Preference Management, you can use functions based on SAP GTS. SAP GTS can be natively integrated with SAP S/4HANA.</p>\n<p><br/> For shipped releases compare the Feature Scope Description for SAP S/4HANA under<br/> <a href=\"https://help.sap.com/s4hana\" target=\"_blank\">https://help.sap.com/s4hana</a> -&gt; Product Documentation -&gt; Feature Scope Description</p>\n<p>For future releases compare the Roadmap for SAP S/4HANA under <br/> <span><a href=\"https://www.sap.com/products/roadmaps.html\" target=\"_blank\">https://www.sap.com/products/roadmaps.html</a></span>, <br/> section \"Lines of Business\" --&gt; \"LoB: Finance\".</p>\n<p>Additional functions for Import and Export Management are available with SAP GTS.</p>\n<p><strong>Business Value</strong></p>\n<ol>\n<li>Intrastat<ol>\n<li>The selection reports for Intrastat are simplified.</li>\n<li>Authorization check on company code level.</li>\n<li>Multiple users can work at the same time.</li>\n<li>Larger number of supported countries.</li>\n<li>Better user experience in processing Intrastat declarations, for example, mass update of item data is possible.</li>\n<li>Classification of services with Intrastat service codes for Italy is available (with S/4HANA for international trade 1610.</li>\n</ol></li>\n<li>Legal Control<ol>\n<li>Verification of relevant documents, like purchase orders, sales orders and outbound deliveries to ensure a proper compliance processes.</li>\n<li>Complete overview by using a Trade Compliance Document, which can consider multiple legal regulations, like Legal Control, Embargo and Watch List Screening regulations.</li>\n<li>Monitoring of compliance relevant documents, across different laws and rules.</li>\n<li>Possibility to maintain blocks and to create customer specific rules to determine blacklisting, whitelisting and/or license types in a specific order.</li>\n</ol></li>\n<li>Classification</li>\n<ol>\n<li>Optimized maintenance of the commodity code.</li>\n<ol>\n<li>Classify for multiple countries on cross-plant level.</li>\n<li>Time Dependent classification.</li>\n<li>One numbering scheme for multiple countries.</li>\n<li>Mass classification of multiple products in one step.</li>\n<li>Semi-automated upload of classification master data from external data providers.</li>\n<li>Synchronization for the commodity codes between \"SAP Global Trade Services\" (SAP GTS) and \"SAP S/4HANA for international trade\" without any manual interaction.</li>\n</ol>\n<li>Classification for Customs Tariff numbers also available.                                         </li>\n</ol>\n<li>Preference Management<ol>\n<li>Preference Management can be used via SAP GTS. For the relevant license topics please contact your Account Executive.</li>\n</ol></li>\n<li>Treasury<ol>\n<li>A complete solution for letter of credit processing through integration of Sales and Trade Finance of Treasury and Financial Risk Management in SAP S/4HANA.</li>\n</ol></li>\n</ol>\n<p>All User interfaces are build based on Fiori design principles.</p>\n<p><strong>Business Process-Related Information</strong></p>\n<p>The customer needs to check whether third-party foreign trade systems are in use for foreign trade processes. Because of the replaced foreign trade functionality in SAP S/4HANA, it is possible that the third-party foreign trade system will need to be adjusted by the respective third-party solution/service provider. However, SAP GTS can be connected to SAP S/4HANA to run the respective foreign trade processes.</p>\n<p> </p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"182\">\n<p>Transaction not available in SAP S/4HANA</p>\n</td>\n<td valign=\"top\" width=\"414\">\n<p>VX99     FT/Customs: General overview</p>\n<p>EN99     General Foreign Trade Processing</p>\n<p>VI09X    Change FT Data in Purchasing Doc.</p>\n<p>VI08X    Display FT data in purchasing doc.</p>\n<p>VI80X    Change FT Data in Goods Receipt</p>\n<p>VI79X    Display FT Data in Goods Receipt</p>\n<p>VI77X    Change FT Data in Outbound Delivery</p>\n<p>VI64X    Display FT Data in Outbound Delivery</p>\n<p>VI14X    Change FT Data in Billing Document</p>\n<p>VI10X    Display FT Data in Billing Document</p>\n<p>VIIM      FT: Op. Cockpit: Purchase order</p>\n<p>VIWE     FT: Op. Cockpit: Goods Receipt</p>\n<p>VIEX      FT: Journal Export Actual</p>\n<p>VE85      Change Statistical Value – Import</p>\n<p>VE86      Display Statistical Value – Import</p>\n<p>VE88      Change Statistical Value – Export</p>\n<p>VE89      Display Statistical Value – Export</p>\n<p>VE87      Change Stat.Value – Subcontracting</p>\n<p>VEI3       Display Stat.Value – Subcontracting</p>\n<p>OVE1     Commodity Code / Import code no.</p>\n<p>VXCZ     INTRASTAT: Form - Czech Republic</p>\n<p>VXHU    INTRASTAT: Form - Hungary</p>\n<p>VXPL     INTRASTAT: Form – Poland</p>\n<p>VXSK     INTRASTAT: Form – Slovakia</p>\n<p>VEBE     INTRASTAT: XML File - Belgium VECZ     INTRASTAT: File - Czech Republic</p>\n<p>VEHU    INTRASTAT: File – Hungary</p>\n<p>VEPL      Create INTRASTAT CUSDEC EDI PL</p>\n<p>VESK     Create INTRASTAT CUSDEC EDI SK</p>\n<p>VEIA      Create INTRASTAT CUSDEC EDI SE</p>\n<p>VEIB      Create INTRASTAT CUSDEC EDI PT</p>\n<p>VEID      Create INTRASTAT CUSDEC EDI LU</p>\n<p>VEI0       Create INTRASTAT CUSDEC EDI IE</p>\n<p>VEI7       Create INTRASTAT CUSDEC EDI GB</p>\n<p>VEI8       Create INTRASTAT CUSDEC EDI AT</p>\n<p>VEI9       Create INTRASTAT CUSDEC EDI ES</p>\n<p>VE02      INTRASTAT: Create Form – Germany</p>\n<p>VE03      INTRASTAT: Create File - Germany</p>\n<p>VE06      INTRASTAT: Paper Form – Belgien</p>\n<p>VE07      Create INTRASTAT Form for France</p>\n<p>VE08      Create INTRASTAT File for Italy</p>\n<p>VE09      Create INTRASTAT file for Belgium</p>\n<p>VE10      Create INTRASTAT file for Holland VE11      Create INTRASTAT file for Spain</p>\n<p>VE12      Create INTRASTAT form for Holland</p>\n<p>VE15      Create disk - INTRA/EXTRA/KOBRA/VAR</p>\n<p>VE16      Create INTRASTAT form for Austria</p>\n<p>VE17      Create INTRASTAT form for Sweden</p>\n<p>VE32      INTRASTAT: Paper Form – Ireland</p>\n<p>VE37      INTRASTAT: File - France</p>\n<p>VE33      INTRASTAT: Paper Form - U.K.</p>\n<p>VE42      INTRASTAT: File - Denmark</p>\n<p>VE45      INTRASTAT: Paper Form – Greece</p>\n<p>VE46      INTRASTAT: File - Finland</p>\n<p>VE95      Create INTRASTAT papers: Portugal</p>\n<p>VICZ      Create INTRASTAT CUSDEC EDI CZ</p>\n<p>VIIE        Create INTRASTAT XML IE</p>\n<p>VEFU     Foreign Trade: Add INTRASTAT Data</p>\n<p>VIMM   Decl. Recpts/Disptch Min. Oil Prod.</p>\n<p>VE96      EXTRASTAT Data Select.: Init. Screen</p>\n<p>VE04      EXTRASTAT: Data selection for export</p>\n<p>VE05      EXTRASTAT: Create File – Germany</p>\n<p>VE97      Create EXTRASTAT tape: Netherlands</p>\n<p>VEXU    Foreign Trade: Add EXTRASTAT Data</p>\n<p>VE13      KOBRA data selection: export Germany</p>\n<p>VE14      Create KOBRA file for Germany</p>\n<p>VE15      Create disk - INTRA/EXTRA/KOBRA/VAR</p>\n<p>VE21      VAR: Selection of bill. docs Switz.</p>\n<p>VE22      Create VAR form for Switzerland</p>\n<p>VE23      V.A.R.: File – Switzerland</p>\n<p>VE18      SED data selection for USA exporter</p>\n<p>VE25      SED: Selection: USA Carriers</p>\n<p>VE19      Create SED form for USA</p>\n<p>VE20      Create AERP file for USA</p>\n<p>VE27      HMF: Selection – USA</p>\n<p>VXJ1      MITI Declarations</p>\n<p>VXJ2      Declaration of ImportBill.Docs Japan</p>\n<p>VEIAI     INTRASTAT Archiving</p>\n<p>VEIAE    EXTRASTAT Archiving</p>\n<p>VE81      Check Report: General FT Data</p>\n<p>VE73      Goods Catalog: Create Document</p>\n<p>VE74      Goods Catalog: Create Diskette</p>\n<p>VXA5     Document. Payments: Print Monitoring</p>\n<p>VXA7     Documentary Payments: Simulation</p>\n<p>ENSV     Data Service</p>\n<p>VX01N  Create License</p>\n<p>VX02N  Change License</p>\n<p>VX03N  Display License</p>\n<p>VX22N  Change License Data</p>\n<p>VX23N  Display license data</p>\n<p>VE31      Blocked SD Documents</p>\n<p>VE30      Existing licenses</p>\n<p>VE29      Assigned Documents for Each License</p>\n<p>VE82      Check Report: Export Control Data</p>\n<p>VI83       Check Customer Master/Legal Control</p>\n<p>VX07     Simulation: License Check</p>\n<p>VX09     Simulation: Embargo Check</p>\n<p>VX08     Simulation: Boycott List Check</p>\n<p>VX16     BAFA diskette: Selection</p>\n<p>VX17     Create BAFA diskette</p>\n<p>VX10     Countries of Destination for License</p>\n<p>VX05     Customers for License</p>\n<p>VX06     Export Control Classes for License</p>\n<p>VE56      Check Export Control for Consistency</p>\n<p>VI84       Doc.Payments: Check Customer Master</p>\n<p>VX14N  Maintain Financial Document</p>\n<p>VX11N  Create Financial Document</p>\n<p>VX12N  Change Financial Document</p>\n<p>VX13N  Display Financial Document</p>\n<p>VI73N   Foreign Trade: Maintain Long-Term Vendor Declaration</p>\n<p>VI74N   FT: Display Vendor Declaration</p>\n<p>VI78       Foreign Trade: Countries</p>\n<p>VI54       Customs Approval Numbers</p>\n<p>VI55       Approval Number per Plant</p>\n<p>VI68      Control Commodity Code/Code Number</p>\n<p>VED1     Print Parameters for Export Documents</p>\n<p>OVE1     Commodity Code / Import Code Number</p>\n<p>VPAR    Archiving Preference Logs</p>\n<p>VFAI      Archiving – INTRASTAT</p>\n<p>VFAE     Archiving – EXTRASTAT</p>\n<p>FTUS     Foreign Trade: Maintain User Data</p>\n<p>ENCO    Printing / Communication</p>\n<p>ENGK    Legal Control</p>\n<p>ENLO     Documentary payments</p>\n<p>ENPA     Foreign Trade: Periodic Declarations</p>\n<p>ENPR     Preference Processing</p>\n<p>ENGR    Periodic Declarations</p>\n<p>ENSV     Foreign Trade Data Maintenance</p>\n<p>ENZD     Customs Objects: Documentation/Info.</p>\n<p>VOIM    Check Import Purchase Order</p>\n<p>VOWE   Check Goods Receipt</p>\n<p>VI09X    Change Import Purchase Order</p>\n<p>VI53X    Change Goods Receipt</p>\n<p>VOLI      Check Export Delivery</p>\n<p>VOEX    Check Export Billing Document</p>\n<p>VI77X    Change Export Delivery</p>\n<p>VI14X    Change Export Billing Document</p>\n<p>VI85       Check Report: Vendor Master</p>\n<p>VE81X   Check Report: General Foreign Trade Data</p>\n<p>VE82X   Check Report: Export Control Data</p>\n<p>VE83X   Check Report: Preference Data</p>\n<p>VI86       Cross-plant Check of Foreign Trade Data</p>\n<p>VI82X    Foreign Trade: Customer Master Consistency – General</p>\n<p>VI83X    Foreign Trade: Customer Master Consistency – Control</p>\n<p>VI84X    Foreign Trade: Customer Master Consistency - Letter of Credit</p>\n<p>VIE4       Periodic Declarations: Log of Incomplete Items</p>\n<p>VE24X   Foreign Trade: Information Commodity Code</p>\n<p>VI24X    Foreign Trade: Information Import Code Number</p>\n<p>FTIM     Journal: Purchase Orders</p>\n<p>FTGR     Journal: Goods Receipts</p>\n<p>FTIM     Import Order Analysis</p>\n<p>FTGR     Import Gds Receipt Analysis</p>\n<p>FTEX      Export Bill. Documents Journal</p>\n<p>FTEX      Exp.bill.doc.analysis</p>\n<p>VE30      Expiring Licenses</p>\n<p>VE29      Sales Orders for a License</p>\n<p>VXA2     Financial Documents: List</p>\n<p>VXA1     Financial Documents: SD Documents</p>\n<p>VI74N   Display Vendor Declaration</p>\n<p>CAAT     Foreign Trade: Call up FT Atrium</p>\n<p>VIUL      Foreign Trade: Data Upload</p>\n<p>VIBA      Send IDoc Output-AES-Initial Procg</p>\n<p>VIBC      Send IDoc Output-AES-Error in Procg</p>\n<p>VIBB      Send IDoc Output-AES-Repeat Procg</p>\n<p>VIB7      Send IDoc Output - Initial Procg</p>\n<p>VIB9      Send IDoc Output - Error in Procg</p>\n<p>VIB8      Send IDoc Output - Repeat Procg</p>\n<p>VIB4      Print Transaction: Initial Procg</p>\n<p>VIB6      Print Transaction: Error in Procg</p>\n<p>VIB5      Print Transaction: Repeat Procg</p>\n<p>VIB3      Foreign Trade Output Status</p>\n<p>VEII        SAPMSED8: Call IMPINV01</p>\n<p>VEIE       SAPMSED8: Call EXPINV02</p>\n<p>VX00     Preference Handling</p>\n<p>LEAN     Request long-term VendDecl. (vendor)</p>\n<p>LEMA    Dun long-term vendor decl. (vendor)</p>\n<p>VE67      Aggregate Vendor Declarations</p>\n<p>LEER      Create long-term VenDecl. (customer)</p>\n<p>VE55      Preference Determination: Individual</p>\n<p>VE54      Preference Determination: Collective</p>\n<p>VEB5     Calculate Assemblies Individually</p>\n<p>VEB6     Calculate Assemblies Collectively</p>\n<p>VEPR     Customs log</p>\n<p>VE91      Display Preference Values</p>\n<p>VE90      Change preference values</p>\n<p>VE83      Check Report: Preference Data</p>\n<p>VE66      Preference Procedure</p>\n<p>VE65      Preference Reg./Percentage Rates</p>\n<p>VE64      Commodity Code/Customs Areas</p>\n<p>VE94      Load Commodity Code for EU Countries</p>\n<p>VEM4    Merge: EU - Commodity Code</p>\n<p>VI94       Load Import Code Nos - EU Countries VIM4     Merge: EU - Import Code Number</p>\n<p>VA94     Load Commodity Codes for Japan</p>\n<p>VAM4   Merge: Japan - Commodity Code</p>\n<p>VP94     Load Import Code No. for Japan</p>\n<p>VPM4   Merge: Japan - Import Code Number</p>\n<p>VEU4     Load Commodity Code-Other Countries</p>\n<p>VEI4       Merge: Remaining Commodity Codes</p>\n<p>VIU4      Load Import Code No.-Other Countries</p>\n<p>VII4        Merge: Rest - Import Code Number</p>\n<p>VE24      Comm. Code Number Information  (old)</p>\n<p>VI24       Code Number Information – Import</p>\n<p>VI20       Display Customs Quota</p>\n<p>VI21       Display Pharmaceutical Products</p>\n<p>VI22       Display Customs Exemption</p>\n<p>VI23       Display Preferential Customs Duties</p>\n<p>VI19       Display Third-country Customs Duties</p>\n<p>VI18       Display Anti-dumping</p>\n<p>VXA3     Financial Documents: Blocked Docs VXA2     Existing Financial Documents</p>\n<p>VE68      Request Vendor Declarations</p>\n<p>VI75       Vendor Declarations - Dunning notice</p>\n<p>VE84      Monitoring: Purchasing Info Records</p>\n<p>VE83      Check Report: Preference Data</p>\n<p>VXSIM                  Simulate Import</p>\n<p>WZFT01               FT: Wizard - Declara. to Auth. (MM)</p>\n<p>WZFT02               FT: Wizard - Declara. to Auth. (SD)</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>To guarantee sales order payments with Trade Finance, the following transactions are available for processing letters of credit, standby letters of credit, and bank guarantees:</p>\n<ul>\n<li>FTR_CREATE (Create Financial Transaction)</li>\n<li>FTR_EDIT (Edit Financial Transaction)</li>\n<li>FTR_RCD (Trade Finance: Risk Check Decision Management)</li>\n<li>FTR_CHK_CONSTCY (Data Consistency check with S/4HANA Sales)</li>\n<li></li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<ul>\n<li>Analysis of all foreign trade processes that are currently in use.</li>\n<li>To migrate Legal Control data, check the documentation in KBA <a href=\"/notes/2869019\" target=\"_blank\">2869019</a>. </li>\n<li>To migrate financial documents in Foreign Trade to Trade Finance, you carry out the migration steps available as Customizing activities (transaction SPRO, SAP Reference IMG) under:</li>\n<ul>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Preparations and Migration of Customizing--&gt;Preparations for Migration of Financial Documents to Trade Finance</li>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Data Migration--&gt;Migration of Financial Documents to Trade Finance and Complete Migration of Financial Documents to Trade Finance</li>\n<li>Conversion of Accounting to SAP S/4HANA--&gt;Activities after Migration--&gt;Manual Activities for Trade Finance</li>\n</ul>\n</ul>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion pre-checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2205202</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Custom code-related information</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 2223144</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 16}, {"note": "2869019", "noteTitle": "2869019 - KBA: Data Migration of trade compliance data from ERP to S/4HANA", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This KBA includes information on the migration of trade compliance data from an SAP ERP system to an SAP S/4HANA system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Environment\">Environment</h3>\n<p><span>S/4HANA 1909 FPS1</span></p>\n<p><span>SAP S/4HANA</span></p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Resolution\">Resolution</h3>\n<p>The following trade compliance data can be migrated from an ERP to an S/4HANA by executing the reports below in your S/4HANA system:</p>\n<ol>\n<li><span>Control Classes (Export Control Classification Numbers)</span></li>\n<li>Control Groupings</li>\n<li>Assignments of Legal Control Classification to a Product</li>\n<li>Licenses and the assignment of Licenses to Logistic Documents</li>\n</ol>\n<p>To migrate the Control Classes, Control Groupings and the assignments of Legal Control Classifications to a product, you can use report <em>/SAPSLL/MIGRATE_LEGCON_CLS</em>.</p>\n<p><strong><br/>Control Classes</strong></p>\n<ul>\n<li>Before you can migrate Control Classes, make sure that you have defined a new legal regulation and assigned a Control Class numbering scheme to it. The numbering scheme must have an active numbering scheme content. The validity period for the control classes to be migrated is set from January 1st, 1900 to December 31st, 9999.</li>\n<li>The data from table <em>T606H</em> (Control Class definition) is migrated to table <em>/SAPSLL/CLSNR</em> and <em>T606V</em> (Description of Control Classes) to table <em>/SAPSLL/CLSNRT</em>.</li>\n<li>If there are already Control Classes existing in the system, only those are taken over that are not yet present.</li>\n</ul>\n<p><strong><br/>Control Groupings </strong></p>\n<ul>\n<li>Before you can migrate Control Groupings, make sure that you have defined a new legal regulation and activated Control Groupings for it.</li>\n<li>The data from table<em> T606B</em> (Control Grouping definition) is migrated to table <em>/SAPSLL/CCGR</em> and <em>T606U</em> (Description of Control Groupings) to table <em>/SAPSLL/CCGRT</em>.</li>\n</ul>\n<p><strong><br/>Assignment of Legal Control Classification to a Product</strong></p>\n<ul>\n<li>It is required that the settings for the legal regulation match, this means that if the legal regulation to be migrated has active control classes and control groupings, the new legal regulation must have the same settings. Otherwise, if - for example - control groupings are not active, the migration cannot be executed. In this case, no data will be taken over.</li>\n<li>If legal control classifications already exist for the new legal regulation, the migration skips them and migrates the others.</li>\n<li>Only unique legal control classifications can be migrated to the new data model. That means, if there are several departure countries for one legal regulation and product, the classification can only be migrated if all classifications for the different departure countries are the same.</li>\n<li>The data from table <em>MAEX</em> is migrated to table <em>/SAPSLL/MARLRG</em>.</li>\n<li>As an alternative you can migrate the data by using migration object: Product classification - Legal Control. Check the Migration Cockpit for further information.</li>\n</ul>\n<p class=\"p\">There is also an application log existing which will hold all information in regards to the migration of the above mentioned data. You can call up the log after the migration via transaction <em>SLG1</em>, Object <em>/SAPSLL/CLSMD</em>, Subobject <em>/SAPSLL/LEGCON_MIGR.</em></p>\n<p class=\"p\"><br/><strong>Licenses and the assignment of Licenses to Logistic Documents</strong></p>\n<p class=\"p\">Licenses and the assignment of licenses to logistic documents can be migrated by using migration report <em>/SAPSLL/MIGRATE_LEG_CON_LIC </em>(see SAP Note - <a href=\"/notes/2864003\" target=\"_blank\">2864003</a>).</p>\n<ul>\n<li>Before you can execute this report, you have to maintain the mapping in transaction <em>SM30</em> for maintenance view <em>/SAPSLL/V_TLGLTM</em> between the old legal regulation/license type and the new legal regulation/license type. The attributes for the legal regulation and license type must be consistent.</li>\n<li>Only licenses in status active, created, or requested are relevant for the migration.</li>\n<ul>\n<li>Table <em>EMBK</em> (License header) is migrated to <em>/SAPSLL/LCLIC</em></li>\n<li>Table <em>T606X</em> (License countries) is migrated to <em>/SAPSLL/LCLICL</em></li>\n<li>Table <em>T606Y</em> (License control classes) is migrated to <em>/SAPSLL/LCLICE</em></li>\n<li>Table <em>T606Z</em> (License partners) is migrated to <em>/SAPSLL/LCLICP<br/><br/></em></li>\n</ul>\n<li>To migrate the assignment of license to logistic documents as well, trade compliance must be active for the sales document type, and legal control must be active for the relevant company codes. For every license to be migrated, the migration creates trade compliance documents for every logistic document that has this license assigned to it. If an outbound delivery already exists, the system also creates a trade compliance document if trade compliance is also active for outbound delivery documents. <strong>But:</strong> Outbound Deliveries, which have been created without a sales order as pre-decessor, and have a license assigned, are not migrated. Reason is, that in such deliveries the legal control check is only simulated and not persisted.</li>\n<li><strong>Note: </strong>If the migration is executed before trade compliance was activated for sales orders and outbound deliveries, the migration can be executed again. Then only the logistic documents are migrated, but the licenses are not.</li>\n</ul>\n<p>A table is written for all the licenses that have already been migrated: <em>/SAPSLL/LICR3N</em>. It contains the assignment of old license to new license. The data from table <em>VBEX</em> (assignments of licenses to a document) are migrated to <em>/SAPSLL/CLDLR and /SAPSLL/CLDLA</em>.</p>\n<p>An application log is also available. You can call up the log after the migration via transaction <em>SLG1</em>, Object <em>/SAPSLL/TRD_CPL</em>, Subobject <em>/SAPSLL/CON_LIC_MIGR.</em></p>\n<p><strong>FAQ's for report <em>/SAPSLL/MIGRATE_LEG_CON_LIC</em>:</strong></p>\n<p><strong>Questions:</strong></p>\n<p>Q1. Under what circumstances the license migration will happen in General?<br/>Q2. What will happen for the order with \"0\" quantity?<br/>Q3. Rejection lines? Will they be migrated?<br/>Q4. Expired license - Does the migration program consider to update the expired licenses, when we are migrating sales order which carries order lines with expired licenses ( at source).<br/>Q5. Completed order Line - Does the license for this completed line also get migrated with a new number based on /SAPSLL/LICR3M.<br/>Q6. Archived Line Items - We for example have order with two line items. First line is completely delivered and Invoiced and archived in source system. Second line item is still open for future delivery. What will happen in this case for the first line item?<br/>Q7. How do I handle the error 'official license number &amp;1 ambiguous; only one instance migrated' (error message - /SAPSLL/TC_LMGM039) during migration?</p>\n<p><strong>Answers:<br/><br/></strong></p>\n<p>A1. All licenses with status (EMBK~GENST) space (created), A (requested) and C ( active) are migrated. Beside that the mapping of license types and legal regulation must be done.<br/>A2. For scheduling lines with a required order quantity of zero the trade compliance document creation is skipped.<br/>A3. For rejected line items, compliance document items are created. In this case the legal regulation status reason in table /SAPSLL/CLDLR is set to value ‘I’ - Item Rejected.<br/>A4. Only licenses in status created, requested or active are migrated. The validity period of the license is not checked.<br/>A5. All active licenses are migrated. If the line item is already completed, a trade compliance document is created and the license is assged to the item. This is necessary to build the remaining quantity/amount in case of depreciation.<br/>A6. The migration only takes line items into account that are not yet archived.<br/>A7. After running the migration, rename the official license number. Afterwards, run the migration again so that the license can be migrated. Please also refer to the long text of this error.</p>\n<ul></ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"See Also\">See Also</h3>\n<p>SAP Note: <a href=\"/notes/2864003\" target=\"_blank\">********</a> - Migration of SD-FT licenses to SAP S/4HANA for international trade<br/>SAP Note: <a href=\"/notes/3332536\" target=\"_blank\">3332536</a> - Bug in report /SAPSLL/MIGRATE_LEG_CON_LIC<br/>SAP Note: <a href=\"/notes/3135685\" target=\"_blank\">3135685</a> - Issue in migration report /SAPSLL/MIGRATE_LEG_CON_LIC<br/>SAP Note: <a href=\"/notes/2961068\" target=\"_blank\">2961068</a> - License is migrated despite inconsistent data<br/>SAP Note: <a href=\"/notes/3340063\" target=\"_blank\">3340063</a> - Insufficient performance in migration of licenses<br/>SAP Note: <a href=\"/notes/3073520\" target=\"_blank\">3073520</a> - Check area for trade regulations is not filled during migration of licenses<br/>SAP Note: <a href=\"/notes/3259532\" target=\"_blank\">3259532</a> - Incorrect license assignments after migration of licenses from SD-FT to SAP S/4HANA for international trade<br/>SAP Note: <a href=\"/notes/3153056\" target=\"_blank\">3153056</a> - Migration of license assignments creates too few Trade Compliance documents</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Keywords\">Keywords</h3>\n<p>License assignment, license, migration, Datenmigration, Kontrollstamm, Kontrollstämme, Genehmigung, Zuordnung, Legal Control, Gesetzliche Kontrolle, /SAPSLL/TC_LMGM039</p>", "noteVersion": 12}]}], "activities": [{"Activity": "Landscape Redesign", "Phase": "Before conversion project", "Condition": "Conditional", "Additional_Information": "Foreign trade functionality in S/4HANA is replaced. SAP GTS can be connected to S/4HANA to run the respective foreign trade processes"}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "Interface Adaption", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "It is possible that 3rd party foreign trade system will need adjustments by the respective 3rd party Solution/Service Provider."}, {"Activity": "User Training", "Phase": "During or after conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}