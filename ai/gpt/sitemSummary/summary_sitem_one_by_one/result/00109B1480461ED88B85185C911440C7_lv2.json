{"guid": "00109B1480461ED88B85185C911440C7", "sitemId": "SI59: Logistics_General", "sitemTitle": "S4TWL - Fashion changes in 1709 FPS02", "note": 2627238, "noteTitle": "2627238 - S4TWL - Fashion changes in 1709 FPS02", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using fashion related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>Certain fashion functionalities have changed in SAP S/4HANA for fashion for vertical business, when compared to SAP Fashion Management. Part of this Business Impact Note, we address two topics that are unrelated to each other.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>1) The following transactions are obsolete prior to SAP S/4HANA 1709 FPS02, and are available starting w/ SAP S/4HANA 1709 FPS02:</p>\n<ul>\n<li>FSH_QDP Quantity Distribution Profile</li>\n<li>FSH_PCW Production Control Workbench</li>\n<li>In addition, the number range of object FSH_MPLND (was in SAP Fashion Management) has been substituted by number range object FSH_MPLO. The number range intervals of object FSH_MPLO need to be maintained accordingly (transaction SPRO -&gt; Logistics - General -&gt; Fashion Management -&gt; Production Planning and Control -&gt; Define Number Range for Master Planned Order; or directly via transaction SNRO, enter FSH_MPLO for the object name). </li>\n</ul>\n<p>2) The following transactions are obsolete prior to SAP S/4HANA 1909, and are available starting w/ SAP S/4HANA 1909:</p>\n<ul>\n<li>FSH_PG_CTCT Maintain Condition Table</li>\n<li>FSH_PG_CTFC Maintain Field Catalog</li>\n<li>FSH_PG_CT_ACC_SEQ Maintenance of Access Sequences</li>\n<li>FSH_PG_CT_COND_TYPE Maintain Condition Types in POG</li>\n<li>FSH_PG_CT_DET_PROC Determination procedure maintenance</li>\n<li>FSH_PG_CT_MGROUP Maintenance Group</li>\n<li>FSH_POGD Delivery Date Determination</li>\n<li>FSH_POGG Group Type Determination</li>\n<li>FSH_POGO Order Type Determination</li>\n<li>FSH_POGT PO Generation Tool</li>\n<li>FSH_POGTWB Purchase Order Workbench</li>\n<li>FSH_POHD Header Data Determination </li>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if fashion processes are used.</p>\n<p>For 1) this is the case if at least one of the following transaction is used: FSH_QDP, FSH_PCW.</p>\n<p>For 2) this is the case if there are entries in table FSH_POG. You can check this via transaction SE16.</p>", "noteVersion": 6, "refer_note": [{"note": "2516743", "noteTitle": "2516743 - S4TWL - Fashion Functionality", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using fashion related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>Certain fashion functionalities have changed or are not available anymore in SAP S/4HANA for fashion for vertical business, when compared to SAP Fashion Management.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>For changed functionlity, please see referenced notes for:</p>\n<ul>\n<li>Segmentation</li>\n<li>Segment Field Length Extension</li>\n<li>Fashion Season Conversion</li>\n<li>Allocation Run Master Data</li>\n<li>Allocation Run</li>\n<li>Season in Inventory Management </li>\n</ul>\n<p>The following functionality has changed:</p>\n<ul>\n<li>Display Stock/Requirements Situation: Transaction FSH_MD04 is not available in SAP S/4HANA anymore. There is a new transaction MD04P providing relevant functionality.</li>\n<li>Mass Data Change for Sales Documents and Stock Transport Orders: Transactions FSH_MASS_SD/FSH_MASS_SDN are not available anymore. Functionality is provided via repricing functionality in SD and MM (e.g. VA05, MEI1), and Advanced ATP transactions.</li>\n<li>Split and Change Purchase Orders: Transaction FSH_SPLITPO is not available anymore. Functionality is provided via transaction FSH_PWB (Purchase Order Mass Maintenance Workbench).</li>\n<li>Process for Subcontracting will not be supported by Supply Assignment (Allocation Run, ARun).</li>\n<li>Stock Protection is not considered in ATP overview display in sales order processing (it is considered in calculation and thus correct).</li>\n<li>Multi Ship-to Order (MSO)</li>\n<li>Fiori apps for Buy Planner</li>\n<ul>\n<li>Plan Procurement for Buy-Planner (PLAN_PROCURE),</li>\n<li><span class=\"sapMTextMaxLine\" id=\"__xmlview7--headerInfo-titleText-inner\">Purchase Requisition Processing for Buy-Planner (PR_PROCESS)</span></li>\n</ul>\n<li>are not available anymore.</li>\n<li>Purchase Order generation rules.</li>\n<li>Snapshot functionality in procurement and sales is not available anymore.</li>\n</ul>\n<p>The following transactions are obsolete:</p>\n<ul>\n<li>FSH_ARUNITA Insight to Action</li>\n<li>FSH_CFMH Hierarchy Transfer for a Gen. Art.</li>\n<li>FSH_CIF_BATCH_CL Default Class for Batches at Plant</li>\n<li>FSH_CIF_BTCHCHR Batch Characteristics</li>\n<li>FSH_DROP_REQ Drop Open Requirements</li>\n<li>FSH_ITAE Insight to Action -Config exceptions</li>\n<li>FSH_ITAH Insight to action - drilldown configuration</li>\n<li>FSH_MASS_SD Mass change transaction</li>\n<li>FSH_MD04 Generic Materials Stock/Requirements List</li>\n<li>FSH_PROD Create product number for GTS transfer</li>\n<li>FSH_SPLITPO Split Purchase Order</li>\n<li>FSH_CO09 Generic Article CO09</li>\n</ul>\n<p>The following transactions are obsolete prior to SAP S/4HANA 1709 FPS02, and are available starting w/ SAP S/4HANA 1709 FPS02 (see also note 2627238):</p>\n<ul>\n<li>FSH_QDP Quantity Distribution Profile</li>\n<li>FSH_PCW Production Control Workbench</li>\n<li>In addition, the number range of object FSH_MPLND (was in SAP Fashion Management) has been substituted by number range object FSH_MPLO. The number range intervals of object FSH_MPLO need to be maintained accordingly (transaction SPRO -&gt; Logistics - General -&gt; Fashion Management -&gt; Production Planning and Control -&gt; Define Number Range for Master Planned Order; or directly via transaction SNRO, enter FSH_MPLO for the object name). <em><br/></em></li>\n</ul>\n<p>The following transactions are obsolete prior to SAP S/4HANA 1809, and are available starting w/ SAP S/4HANA 1809:</p>\n<ul>\n<li>FSH_PFM Maintain PFM Rules</li>\n<li>FSH_PSM Maintain PSM Rules</li>\n</ul>\n<p>Integraion scenarios with SAP Customer Activity Repository 3.0</p>\n<ul>\n<li>Please note, that currently integration scenarios with SAP Customer Activity Repository 3.0 (CARAB 2.0) and consuming applications are not supported. SAP plans to provide a note to enbale such an integration scenario.</li>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p>Please see also referenced Release Information Note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if fashion processes are used. This is the case if at least one of the following transaction is used: FSH_ARUNITA, FSH_CFMH, FSH_CIF_BATCH_CL, FSH_CIF_BTCHCHR, FSH_CO09, FSH_COD, FSH_COD_GR_UPD, FSH_DROP_REQ, FSH_ITAE, FSH_ITAH, FSH_MASS_SD, FSH_MD04, FSH_PCW, FSH_PFM, FSH_PROD, FSH_PSM, FSH_QDP, FSH_SPLITPO.</p>", "noteVersion": 28, "refer_note": [{"note": "2566648", "noteTitle": "2566648 - Delete FSH_CO09 user settings", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>Unable to delete user settings for transaction FSH_CO09</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>FMS, Fashion Managment Solution, FSH_ATP_SETTING, FSH_CO09</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The issue is happening because of program error</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>Implement the correction instruction attached to the note and then use Report FSH_ATP_SETTING_DELETE to delete user setting of transaction FSH_CO09.</p>", "noteVersion": 1}, {"note": "2532764", "noteTitle": "2532764 - SAP S/4HANA 1709 Feature Package Stack 00: Additional Release Information for Retail and Fashion", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>This note contains information and references to notes from Retail and Fashion, for applying Feature Package Stack 00 (09/2017) of product version SAP S/4HANA 1709.</p>\n<p><strong><strong>Note</strong>:</strong> This SAP note is subject to change. Check this note for changes on a regular basis. All important changes made after release of Feature Package Stack 00 is documented in section \"Changes made after Release of Feature Package Stack 00\".</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S/4HANA 1709 Feature Package Stack 00, Business function ISR_RETAILSYSTEM, FASHION, LOG_SEGMENTATION</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You are using Retail and/Or Fashion and you want to get additional information about Feature Package Stack 00 of product version SAP S/4HANA 1709.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Notes (as of 10th October 2017) to be applied on top of <strong>Feature Package Stack 00 </strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"5\" cellspacing=\"5\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>SAP Note</strong></td>\n<td><strong>Description</strong></td>\n<td><strong>Software Component</strong></td>\n<td><strong>Manual Activities required</strong></td>\n</tr>\n<tr>\n<td><a href=\"/notes/2520808 \" target=\"_blank\">2520808</a></td>\n<td>Incorrect origin of the attribute in rule maintenance</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2524560\" target=\"_blank\">2524560</a></td>\n<td>Missing relevant authorization objects check in 'PSST Assignment and Monitor' application</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2545245\" target=\"_blank\">2545245</a></td>\n<td>Program terminates while creating a PSST rule using Transaction RFM_PSST_GRA</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2526811 \" target=\"_blank\">2526811 </a></td>\n<td>Error in the article master when you use the material ledger</td>\n<td>\n<p>S4CORE</p>\n</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2518048\" target=\"_blank\">2518048</a></td>\n<td>FSH_CONVERT_SPO does not select Grouped Purchase Order</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2122891\" target=\"_blank\">2122891</a></td>\n<td>Migration of characteristics and characteristic values, adjustment of tables MARA and AUSP after migration</td>\n<td>\n<p>S4CORE</p>\n</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2328046\" target=\"_blank\">2328046</a></td>\n<td>Quota Scale and Seasonal Procurement Migration in S/4HANA</td>\n<td>S4CORE</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2525411\" target=\"_blank\">2525411</a></td>\n<td>TCode \"ARUN_ITARD\" dump for toolbar handling</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2514123\" target=\"_blank\">2514123</a></p>\n</td>\n<td>Season determination in Sales Order</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2517387\" target=\"_blank\">2517387</a></p>\n</td>\n<td>A-run ITA : Over assignment issue</td>\n<td>S4CORE</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2517388\" target=\"_blank\">2517388</a></td>\n<td>Immediate Assignment issue for STO</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2522787\" target=\"_blank\">2522787</a></td>\n<td>Supply Assignment issue for Sales Orders in ARun Insight to Action</td>\n<td>S4CORE</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2522787\" target=\"_blank\">2531263</a></td>\n<td>Syntax error in program \"SAPMC29L\" during execution of transactions CS15, CSD5, CSC5</td>\n<td>S4CORE</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2522787\" target=\"_blank\">2523930</a></td>\n<td>Sales Order change triggers season redetermination</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2522787\" target=\"_blank\">2518075</a></td>\n<td>Incorrect data conversion of articles' season data during migration of the system to S4CORE 102 release</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2522787\" target=\"_blank\">2515840 </a></td>\n<td>ATC check and Performance improvement in Quantity Distribution Profile ( Method GET_DO_RESET_PROFILES of class CL_FSH_PP_BD_PROFILE )</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2512602\" target=\"_blank\">2512602</a></td>\n<td>Stock Segment Value is Cleared Off in MIGO Transaction for Generic Articles Relevant for Segmentation</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2511965\" target=\"_blank\">2511965</a></td>\n<td>Performance Problems: Season Redetermination</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2509715 \" target=\"_blank\">2509715</a> </td>\n<td>Hide ATP Checking Rule at Sales Document Type</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td>\n<p><a href=\"/notes/2507477\" target=\"_blank\">2507477</a></p>\n</td>\n<td>Seasonal Workbench performace issue -error messages Sales View</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2506224\" target=\"_blank\">2506224</a></td>\n<td>Performance Problems: Season Redetermination</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2503686\" target=\"_blank\">2503686</a></td>\n<td>Short dump on 'Basic Data 2' tab of article master transactions</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2502317\" target=\"_blank\">2502317</a></td>\n<td>Exceptions are not caught for FM 'HELP_VALUES_GET_NO_DD_NAME'</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Following notes are added on 18th September 2017. <strong> </strong></strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"5\" cellspacing=\"5\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td><strong>SAP Note</strong></td>\n<td><strong>Description</strong></td>\n<td><strong>Software Component</strong></td>\n<td><strong>Manual Activities required</strong></td>\n</tr>\n<tr>\n<td><a href=\"/notes/2533465 \" target=\"_blank\">2533465 </a></td>\n<td>Deprecated functionality related to Season Active in Inventory Management is available in S/4 1709</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n<tr>\n<td><a href=\"/notes/2532273\" target=\"_blank\">2532273</a></td>\n<td>Season determination does not happen for segment non-relevant article and plant combination</td>\n<td>S4CORE</td>\n<td>No</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 4}, {"note": "2467506", "noteTitle": "2467506 - S4TWL -  Order Allocation Run (ARun) in Fashion Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using Order Allocation Run (ARun) in SAP Fashion Management. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Order Allocation Run, ARun, Fashion Management, Retail, Supply Assignment</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>There are differences between Order Allocation Run in SAP Fashion Management and Order Allocation Run in SAP S/4HANA (which is now part of Supply Assignment).</p>\n<p><strong>Business Process related information</strong></p>\n<p>Salient features of Supply Assignment (ARun) in SAP S/4HANA are as follows:</p>\n<ul>\n<li>Supply Assignment is now an SAP S/4HANA core solution</li>\n<li>Supply Assignment and Back Order Processing (BOP) are merged into one common solution based on Advanced ATP</li>\n<li>FIORI based executions of actual and simulated assignments</li>\n<li>FIORI based result monitor and explanation tool for the assignments</li>\n<li>Supply Assignments of requirements for sales orders and stock transport orders</li>\n<li>Supply Assignment is not mandatory and can be switched on/off at article/site level</li>\n<li>Assignment of batches is optional and can be decided on article/site level</li>\n<li>Release procedure is decoupled from Supply Assignment execution run and can run independent</li>\n<li>Simplified Release Rule Determination procedure</li>\n<li>Insight to action – Supply Assignment workbench to check exceptions and acting on them</li>\n</ul>\n<p>Order Allocation Run features not available in SAP S/4HANA (that were available in SAP Fashion Management):</p>\n<ul>\n<li>Order Allocation Run customizing settings</li>\n<li>Contract call off assignments and reference handling</li>\n<li>Customer BADIs throughout Order Allocation Run process</li>\n<li>Order Allocation Run workbench / Order Allocation Run workbench batch report</li>\n<li>Release and grouping strategy and determination table generation</li>\n<li>Assignment and reference handling between Multiple-Ship-to-Order (MSO) main orders and exploded orders</li>\n<li>Consistency check report for Order Allocation Run results (because of simplified data model)</li>\n<li>Assignments of Order Allocation Run to Make-to-Order, Purchase-to-Order, and subcontracting components</li>\n<li>Order Allocation Run related transactions are not available in SAP S/4HANA </li>\n</ul>\n<p>Data model simplification:</p>\n<ul>\n<li>The design of Order Allocation Run in SAP S/4HANA is different compared to SAP Fashion Management. In SAP S/4HANA the Order Allocation Run does not store allocations in the tables FSH_BDBS and FSH_PREVIEW anymore, and those tables are not refered to by the Order Allocation Run.</li>\n<li>The allocations in FSH_BDBS and FSH_PREVIEW in SAP Fashion Management will not be migrated to the new table in SAP S/4HANA.</li>\n<li>As a consequence, the Order Allocation Run has to be set up and executed again after system conversion.</li>\n<li>Due to changed design in SAP S/4 HANA, the fields FSH_RALLOC_QTY and FSH_SALLOC_QTY in standard tables would not be updated anymore by Supply Assignment.</li>\n</ul>\n<p>In SAP S/4HANA, the following transactions are not available:</p>\n<ul>\n<li>ARUN</li>\n<li>ARUNNO</li>\n<li>ARUNP</li>\n<li>ARUNWB</li>\n<li>ARUNWB_BATCH</li>\n<li>ARUN_BATCH</li>\n<li>ARUN_CUST_TEMP_GRP</li>\n<li>ARUN_PROI</li>\n<li>ARUN_VARIANT</li>\n</ul>\n<p>Note - In order to use the Supply Assignment in SAP S/4HANA, the business functions SUPPLY_ASSIGNMENT_01 has to be activated. For retail fashion management specific functionalities of Supply Assignment, business function SUPPLY_ASSIGNMENT_RETAIL_01 has to be activated.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Adjust the respective business processes accordingly.</p>\n<p><strong><strong>How to Determine Relevancy</strong></strong></p>\n<p>This Simplification Item is relevant if Order Allocation Run functionality is used.<br/>This can be checked via transaction SE16N. Enter table FSH_BDBS and check whether there are entries, or whether there are entries in table FSH_PREVIEW.</p>", "noteVersion": 10}, {"note": "2481829", "noteTitle": "2481829 - S4TWL - Fashion Season Conversion (SAP ERP to SAP S/4HANA 1709)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP (SAP Fashion Management or SAP Merchandising) to SAP S/4HANA 1709, and you are using season functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of season has changed from SAP ERP to SAP S/4HANA 1709.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>SAP Fashion Management: During the conversion, the following report is executed automatically R_FSH_S4_SEASONS_MAT_MIG_XPRA.<br/>In case of problems during the conversion, you can execute this report manually.</p>\n<p>SAP Merchandising: During the conversion, the following reports are executed automatically: R_FSH_S4_SEASONS_MD_MIG followed by R_FSH_S4_SEASONS_MAT_MIG_XPRA.<br/>In case of problems during the conversion, you can execute these reports manually.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if season functionality is used.<br/>SAP Fashion Management: This can be checked via transaction SE16N. Enter table FSH_SEASONS_MAT and check whether there are entries.<br/>SAP Merchandising: This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field MARA-SAISO (Season) not initial.</p>", "noteVersion": 4}, {"note": "2590824", "noteTitle": "2590824 - Post upgrade clean up of obsolete objects generated by POGT", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>While running the pre-check report during the conversion from EHP8 FOR SAP ERP 6.0 SP08 (or higher) to SAP S/4HANA, you get errors as follows:</p>\n<p>\"Check item S57/S59: Logistics_General\"</p>\n<p>Highest consistency check return code: 7</p>\n<p>Check Sub-ID:\"POGT\", return code = 7</p>\n<p>Post Migration, POGT(Purchase Order Generation Tool) is obsolete.</p>\n<p>POGT generated objects needs to be cleaned up. Please refer SAP Note 2590824.\"</p>\n<p>Also, for SUM 2.0 SP02 and lower you get activation errors for certain generated DDIC objects (in $TMP package) that are generated by condition technique.</p>\n<p>These objects are generated by the POGT (Purchase Order Generation Tool) application that uses condition application = PGT and usage = PT.</p>\n<p>List of these messages of the following type are available in the attachment ACTUPG.pdf:</p>\n<p>   3 EDT012XActivate table \"/1CN/WORKING_SET_E_D_PGT\"<br/>   1EEDT242 Field \"FSH_POG_AUART\": Component type or domain used not active or does not exist</p>\n<p>After the conversion, you will also get condition type Maintenance Group related messages in LONGPOST.LOG. List of these message of the following type are available in attachment LONGPOST.pdf:</p>\n<p>    A2PE/SAPCND/CUSTOMIZING 020 Maintenance group \"PGT_OT\" is inconsistent, as it does not contain any items</p>\n<p>Please note that the exact messages (or message text) will depend on the system.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>POGT, PGT, Condition Technique, Fashion migration, FSH_POG_AUART, Clean up</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Note 2591835 is a prerequisite note containing the DDIC changes required for the report. POGT functionality is not supported in SAP S/4HANA. Please refer the simplification note 2516743. If POGT is present in the EHP8 source system, the generated objects (in $TMP) are copied to the SAP S/4HANA target system. These obsolete objects needs to be cleaned up.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The obsolete objects need to be cleaned up post conversion, by implementing this note to get the cleanup program RFM_CLEANUP_POGT_OBJECTS. Run this program (using SA38 transaction) in conversion systems. This program needs to be executed in all systems (dev, test, prod) where conversion is run.</p>\n<p>Include the Note implementation (the cleanup program) into your SPAU request. You will then have the cleanup program available in the follow up systems of the system landscape, ready for execution after the conversion.</p>", "noteVersion": 9}, {"note": "2535093", "noteTitle": "2535093 - S4TWL - Season Active in Inventory Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP ERP (SAP Fashion Management or SAP Merchandising) to SAP S/4HANA, and you are using season based inventory management. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>In SAP S/4HANA functionality to assign a season to inventory stock is not available anymore.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Visibility to which season a specific stock is assigned to is not available anymore.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During conversion from SAP Fashion Management to SAP S/4HANA the respective flag in the article master (level article-site) is cleared. Stock information as such is unchanged, just the assignment to a season is cleared.<br/>You need to adjust your business processes accordingly.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if season functionality is used in inventory management.<br/>SAP Fashion Management: This can be checked via transaction SE16N. Enter table MARC and check whether there are entries with FSH_SEAIM set to ‘X’</p>", "noteVersion": 2}, {"note": "2481891", "noteTitle": "2481891 - S4TWL - Season Conversion (SAP S/4HANA 1610 to 1709)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system upgrade from SAP S/4HANA 1610 to SAP S/4HANA 1709, and you are using season functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of season has changed from SAP S/4HANA 1610 to SAP S/4HANA 1709.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, the following report is executed automatically R_FSH_S4_SEASONS_MAT_MIG_XPRA.<br/>In case of problems during the conversion, you can execute this report manually.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if season functionality is used.<br/>This can be checked via transaction SE16N. Enter table FSH_SEASONS_MAT and check whether there are entries.</p>", "noteVersion": 2}, {"note": "2522971", "noteTitle": "2522971 - S4TWL - Segment Field Length Extension", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a sytem conversion from SAP ERP 6.0 EHP7/EHP8 to SAP S/4HANA 1709 and you are using segmentation functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>The data model of segments has changed in SAP S/4HANA 1709. The field length has been increased from 16 to 40 characters.</p>\n<p><strong>Business Process related information</strong></p>\n<p>This data model change does not have any business impact.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, adjustments are made automatically.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation functionality is used.<br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with MARA-SGT_REL non initial.</p>", "noteVersion": 1}, {"note": "2465556", "noteTitle": "2465556 - S4TWL - Master Data for Order Allocation Run (ARun) in Fashion Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion from SAP Fashion Management to SAP S/4HANA, and you are using Order Allocation Run functionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>There are some differences in master data for Order Allocation Run in SAP S/4HANA fashion for vertical business compared to SAP Fashion Management.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP Fashion Management it was possible to maitain both the distribution profile and the Allocation Run relevance via integrated article maintenance (transaction MM41, MM42).</p>\n<p>In SAP S/4HANA either the distribution profile, or Allocation Run/Supply Assignment relevancy can be mainatiend, as both functionality exclude each other.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, the adjustments are executed automatically. The relevant XCLA class is CL_XCLA_MARC.<br/>The class clears the MARC-FPRFM entries, for those MARC entries where there is a non-initial MARC-FSH_MG_ARUN_REQ.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if Order Allocation Run functionality is used.<br/>This can be checked via transaction SE16N. Enter table MARC and check whether there are entries where MARC-FPRFM and MARC-FSH_MG_ARUN_REQ both are not initial.</p>", "noteVersion": 2}, {"note": "2465612", "noteTitle": "2465612 - S4TWL - Segmentation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1709, and you are using segmentation related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Segmentation, Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>In SAP S/4HANA segmentation is available, but has some diiferences compared to SAP ERP which need to be considered.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>The differences in segmentaion between SAP ERP and SAP S/4HANA are the following:</p>\n<ul>\n<li>In SAP S/4HANA segmentation strategies 1:n and n:m are not available anymore.</li>\n<li>Stock protection in SAP ERP is implemented via segmentation. In SAP S/4HANA stock protection can be achieved via product allocation (PAL).</li>\n<li>Segmentation is considered only in the advanced ATP (aATP). The classic ATP is not supported anymore.</li>\n<li>In SAP S/4HANA, the default segmentation can be maitained via a specific transaction (SGT_DEFAULT).</li>\n<li>In SAP S/4HANA transactions to configure segmentation structures and strategies (SGTS, SGTC) are now available in the application (vs. customizing in SAP ERP).</li>\n<li>In SAP S/4HANA MRP related article/material master data on segment level are not available anymore.</li>\n<li>In SAP S/4HANA segmentation maintenance in the article/material master has been simplified compared to SAP ERP (e.g. automatic fixing of the segmentation strategy during article and listing maintenance).</li>\n<li>In SAP S/4HANA the transaction LS24 doesn’t support  the “Refresh” button anymore</li>\n<li>PRC planning level (SGT_PRPL) and Consecutive Number for Segmentation of Equal Status (SGT_CNES) fields are not supported anymore</li>\n<li>Safety stock is supported only at Default stock segment level (vs stock segment level in SAP ERP)</li>\n<li>Segmentation is considered only in MRP live (MD01N). The classic MRP is not supported. </li>\n<li>In SAP S/4HANA segmentation is not supported in the following applications:</li>\n<ul>\n<li>Quality Management (QM)</li>\n<li>Production Planning - Detailed Scheduling (PP-DS)</li>\n<li>Process Order</li>\n<li>Demand-Driven MRP (DDMRP)</li>\n<li>Predictive MRP (pMRP)</li>\n<li>Segmentation structure and strategy are not considered in EWM</li>\n<li>Spare Parts Planning (SPP) </li>\n</ul>\n<li>In SAP S/4HANA, the following fields are not available:</li>\n<ul>\n<li>Discrete Batch Number (SGT_CHINT)</li>\n<li>Stock Protection Indicator (SGT_STK_PRT)  </li>\n<li>Consumption Priority (SGT_PRCM)  </li>\n<li>ATP/MRP Status for Material and Segment (SGT_MRP_ATP_STATUS)  </li>\n<li>Sort Stock based on Segment (SGT_MRPSI)  </li>\n<li>Date from which the plant-specific material status is valid (SGT_MMSTD)</li>\n</ul>\n<li>In SAP S/4HANA, the following transactions are not available:</li>\n<ul>\n<li>SGT_CONVERT_MATERIAL</li>\n<li>SGT_MAINTAIN_VALT</li>\n<li>SGT_MASS_EXTD_MAT</li>\n</ul>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Part of the conversion from SAP ERP to SAP S/4HANA, article need to be resasigned from \"old\" to \"new\" segmentation, please see referenced note.</p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if segmentation is used. <br/>This can be checked via transaction SE16N. Enter table <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text36-__list6-0\">SGT_COVS_T </span>and check whether there are entries.<br/>There is also a pre-check class <span class=\"sapMText sapMTextMaxWidth sapUiSelectable sapUiTinyMarginTopBottom tdbCheckListFont\" id=\"__text39-__list7-0\">CLS4SIC_SGT_MASTER_DATA, please see attached note.</span></p>", "noteVersion": 12}, {"note": "2468952", "noteTitle": "2468952 - S4TWL - Fashion Contract (Requirement Relevant Contracts) in SAP Fashion Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to an SAP S/4HANA release prior to 1709 FPS02, and you are using Fashion Contracts (requirement relevant contracts) in SAP Fashion Management. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP Fashion Management, Fashion Contract, Requirement Relevant Contract</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong><strong><strong>Description</strong></strong></strong></strong></p>\n<p>Fashion contracts are not available prior to SAP S/4HANA 1709 FPS02.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP Fashion Management it is possible to characterise contracts as fashion contracts. In the processes of requirements planning (MRP), ATP, and Order Allocation Run those fashion contracts are treated accordingly. In SAP S/4HANA fashion contracts are available only starting with release S/4HANA 1709 FPS02.</p>\n<p><strong><strong><strong><strong><strong>Required and Recommended Action(s)</strong></strong></strong></strong></strong></p>\n<p>If you are using contracts and contract release orders in SAP Fashion Management solution, and wamt to convert to S/4HANA to a release befor 1709 FPS02 you need to perform the below mentioned actions:</p>\n<ul>\n<li>You must close the existing open contract release orders referencing fashion contracts (e.g. transaction VA02).</li>\n<li>You must close the existing open fashion contract documents (e.g. transaction VA42) so that additional requirements existing for these documents would not be considered in SAP S/4HANA system.</li>\n</ul>\n<p>In SAP S/4HANA prior to 1709 FPS02 you need to use standard sales orders only to realize order to cash (O2C) lifecycle.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if Fashion Contract functionality is used.<br/>This can be checked via transaction SE16N. Enter table VBAK and check whether there are entries where VBTYP = 'G' and FSH_CQ_CHECK = 'X'.</p>", "noteVersion": 3}, {"note": "2727960", "noteTitle": "2727960 - S4TWL - Fashion Purchase Order Generation Tool", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using fashion related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>Certain fashion functionalities are not available anymore in SAP S/4HANA for fashion for vertical business, when compared to SAP Fashion Management.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>The following transactions are obsolete prior to SAP S/4HANA 1809 FPS02. It is planned to make them available starting w/ SAP S/4HANA 1809 FPS02:</p>\n<ul>\n<li>FSH_PG_CTCT Maintain Condition Table</li>\n<li>FSH_PG_CTFC Maintain Field Catalog</li>\n<li>FSH_PG_CT_ACC_SEQ Maintenance of Access Sequences</li>\n<li>FSH_PG_CT_COND_TYPE Maintain Condition Types in POG</li>\n<li>FSH_PG_CT_DET_PROC Determination procedure maintenance</li>\n<li>FSH_PG_CT_MGROUP Maintenance Group</li>\n<li>FSH_POGD Delivery Date Determination</li>\n<li>FSH_POGG Group Type Determination</li>\n<li>FSH_POGO Order Type Determination</li>\n<li>FSH_POGT PO Generation Tool</li>\n<li>FSH_POGTWB Purchase Order Workbench</li>\n<li>FSH_POHD Header Data Determination </li>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p>Information was stored in t<span #333333;=\"\" arial',sans-serif;=\"\" color:=\"\" en;\"=\"\" lang=\"EN\" mso-ansi-language:=\"\">able FSH_POG. This table is not available in SAP S/4HANA anymore. If these data are needed for future reference, a back-up of the table entries need to be taken. For clean-up report, please see referenced not 2590824.</span></p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if Purchase Order Generation Tool is used. This is the case if there are entries in tbale FSH_POG<span #333333;=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" en;=\"\" lang=\"EN\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">. This can be checked via transaction SE16N.</span><span>Enter table </span><span #333333;=\"\" 107%;=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" en;=\"\" lang=\"EN\" line-height:=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">FSH_POG a</span><span>nd check whether there are entries.</span></p>", "noteVersion": 1}, {"note": "2627238", "noteTitle": "2627238 - S4TWL - Fashion changes in 1709 FPS02", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using fashion related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Fashion, Retail.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong><strong>Description</strong></strong></p>\n<p>Certain fashion functionalities have changed in SAP S/4HANA for fashion for vertical business, when compared to SAP Fashion Management. Part of this Business Impact Note, we address two topics that are unrelated to each other.</p>\n<p><strong><strong><strong><strong>Business Process related information</strong></strong></strong></strong></p>\n<p>1) The following transactions are obsolete prior to SAP S/4HANA 1709 FPS02, and are available starting w/ SAP S/4HANA 1709 FPS02:</p>\n<ul>\n<li>FSH_QDP Quantity Distribution Profile</li>\n<li>FSH_PCW Production Control Workbench</li>\n<li>In addition, the number range of object FSH_MPLND (was in SAP Fashion Management) has been substituted by number range object FSH_MPLO. The number range intervals of object FSH_MPLO need to be maintained accordingly (transaction SPRO -&gt; Logistics - General -&gt; Fashion Management -&gt; Production Planning and Control -&gt; Define Number Range for Master Planned Order; or directly via transaction SNRO, enter FSH_MPLO for the object name). </li>\n</ul>\n<p>2) The following transactions are obsolete prior to SAP S/4HANA 1909, and are available starting w/ SAP S/4HANA 1909:</p>\n<ul>\n<li>FSH_PG_CTCT Maintain Condition Table</li>\n<li>FSH_PG_CTFC Maintain Field Catalog</li>\n<li>FSH_PG_CT_ACC_SEQ Maintenance of Access Sequences</li>\n<li>FSH_PG_CT_COND_TYPE Maintain Condition Types in POG</li>\n<li>FSH_PG_CT_DET_PROC Determination procedure maintenance</li>\n<li>FSH_PG_CT_MGROUP Maintenance Group</li>\n<li>FSH_POGD Delivery Date Determination</li>\n<li>FSH_POGG Group Type Determination</li>\n<li>FSH_POGO Order Type Determination</li>\n<li>FSH_POGT PO Generation Tool</li>\n<li>FSH_POGTWB Purchase Order Workbench</li>\n<li>FSH_POHD Header Data Determination </li>\n</ul>\n<p><strong><strong><strong><strong>Required and Recommended Action</strong></strong></strong></strong></p>\n<p>Implemented business processes need to be adjusted according to the changes listed above.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if fashion processes are used.</p>\n<p>For 1) this is the case if at least one of the following transaction is used: FSH_QDP, FSH_PCW.</p>\n<p>For 2) this is the case if there are entries in table FSH_POG. You can check this via transaction SE16.</p>", "noteVersion": 6}, {"note": "2385984", "noteTitle": "2385984 - S4TWL - SAP S/4HANA Retail for Merchandise Management, SAP S/4HANA for Fashion and Vertical Business - Simplification Items", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, and in SAP S/4HANA for fashion and vertical business several functional areas have changed, or are not available anymore, or are not stratgic anymore.</p>\n<p><strong>Business Process related information</strong></p>\n<p>See details in the respective referenced notes.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>See details in the respective referenced notes.</p>", "noteVersion": 3}]}], "activities": [{"Activity": "Process Design / Blueprint", "Phase": "Before or during conversion project", "Condition": "Mandatory", "Additional_Information": "Implemented business processes need to be adjusted according to the changes listed in SAP Note 2627238"}, {"Activity": "Data cleanup / archiving", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Run cleanup program RFM_CLEANUP_POGT_OBJECTS to remove obsolete POGT program artifacts"}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Users meust be trained in new process designed for Fashion functionalities"}, {"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "The number range intervals of object FSH_MPLO need to be maintained"}]}