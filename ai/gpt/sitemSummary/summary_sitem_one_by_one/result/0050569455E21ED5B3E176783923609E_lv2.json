{"guid": "0050569455E21ED5B3E176783923609E", "sitemId": "SI6: FIN_TRM", "sitemTitle": "S4TWL - Exposure Management 1.0", "note": 2340804, "noteTitle": "2340804 - S4TWL - Exposure Management 1.0 within Treasury and Risk Management", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are planning a system conversion to SAP S/4HANA. The following SAP S/4HANA transition worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>S4TC, S/4 Transition</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p>The <em>Exposure Management 1.0</em> is no longer the target architecture and has been disabled technically. <em>Exposure Management 2.0</em> is the more flexible and comprehensive successor application. There is no standard migration tool from <em>Exposure Management 1.0</em> to <em>Exposure Management </em><em>2.0</em> though. You have to set up <em>Exposure Management 2.0</em> and transfer the data into the new application just in the same way as if you had used another software system before and wanted to migrate to the SAP solution.</p>\n<p>The basic functionality of the<em> Exposure Management 2.0</em> is available with the business functions <em>TRM, Hedge and Exposure Management, New Financial Product</em> (FIN_TRM_LR_FI_AN_2) deliverd with <em>SAP ERP 6.0 enhancement package 4. </em></p>\n<p>There are several other business functions, for example, <em>TRM, Enhancements in Exposure Management 2.0</em> (FIN_TRM_LR_FI_AN_4) delivered with <em>SAP ERP 6.0 enhancement package 7 </em>. These business functions are always on in SAP S/4HANA.</p>\n<p>Customers have to implement the <em>Exposure Management 2.0</em> <strong>before</strong> they go to SAP S/4HANA.</p>\n<ul>\n<li>If you are not using <em>Exposure Management 1.0</em> and you do not plan to use <em>Exposure Management 2.0</em> you can upgrade to SAP S/4HANA without any difficulties.</li>\n<li>If you are using <em>Exposure Management 1.0</em> and upgrade to SAP S/4HANA from a release in which <em>Exposure Management 2.0</em> is technically available, SAP recommends that you set up <em>Exposure Management 2.0</em> and create necessary customizing before upgrading to SAP S/4HANA. However it is possible to upgrade to SAP S/4HANA without setting up <em>Exposure Management 2.0</em>, but in that case you cannot use <em>Exposure Management 2.0</em> in SAP S/4HANA until customizing for the <em>Exposure Management 2.0</em> is available (which is an own project and needs time).</li>\n<li>If you are using <em>Exposure Management 1.0</em> and upgrade to SAP S/4HANA from an older release in which <em>Exposure Management 2.0</em> is technically not available, SAP recommends that you first upgrade to a release in which <em>Exposure Management 2.0</em> is available and set up the <em>Exposure Management 2.0</em> including necessary customizing creation, before upgrading to SAP S/4HANA. However it is possible to upgrade to SAP S/4HANA without setting up <em>Exposure Management 2.0</em>, but in that case you cannot use <em>Exposure Management 2.0</em> in SAP S/4HANA until customizing for the <em>Exposure Management 2.0</em> is available (which is an own project and needs time).</li>\n</ul>\n<p>Read the following information in case the Pre-Upgrade-Check in your system issues a message for the check ID  SI6_EXP_MGT_10 \"Exposure Management 1.0\": This check issues a warning if it finds entries in the database which means that exposures have been entered in at least one client in the system. The warning message is for your information, no futher mandatory action is required.</p>\n<p>Read the following information in case the custom code analysis has detected customer coding related to this transition worklist item: SAP objects which are used by the detected customer code are deprecated and shall not be used any more. The customer code will not work any more.</p>", "noteVersion": 1, "refer_note": [], "activities": [{"Activity": "Customizing / Configuration", "Phase": "During conversion project", "Condition": "Conditional", "Additional_Information": "If in your EhP4 for SAP ERP 6.0 the new functionality is available then implement to the new functionality in your EhP4 for SAP ERP 6.0."}, {"Activity": "Software Upgrade / Maintenance", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "If in your SAP ERP 6.0 (EhP<4) the new functionality is not available then upgrade your EhP version and then implement to the new functionality."}, {"Activity": "Process Design / Blueprint", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "If in SAP ERP 6.0 (EhP<4) new functionality is not available you could also implement the new functionality in SAP S/4HANA as a small project."}, {"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}, {"Activity": "User Training", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": ""}]}