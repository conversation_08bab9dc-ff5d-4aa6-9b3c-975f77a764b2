{"guid": "00109B131AF41EDA909E358613B9E0E2", "sitemId": "SI65: Logistics_General", "sitemTitle": "S4TWL - Vendor Characteristic Values", "note": 2885547, "noteTitle": "2885547 - S4TWL - Vendor Characteristic Values", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Via classification system it is possible to assigen characteristic values to articles.<br/>Vendor Characteristic Values are used to maintain the descriptions of a characteristic value a supplier uses, in case this description deviates from the description you are using. E.g. you use \"red\" for a characteristic value whereas the supplier uses \"fire engine red\".</p>\n<p>In ECC it was possible to maintain the vendor characteristic values either in vendor maintenance (transactions MK0x, XK0x) or in purchasing view of the article maintenance (tramsacations MM4x). Because of the Business Partner harmonization in S/4HANA, it is not possible anymore to maintain the values via supplier maintenance. It is still possible to maitain the values via article maintenance.</p>\n<p>In addition it is possible to achieve a simliar via Characteristic Value Conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes are not affected besides the maintenance as such.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Use maintenance capabilities of article maintenance or switch to Characteristic Value Conversion.</p>\n<p>This does not affect your custom code.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is potentially relevant if Vendor Characteristic Values are used.<br/>This can be checked via transaction SE16N. Enter table WYT2 and check whether there are any entries.</p>", "noteVersion": 2, "refer_note": [{"note": "2386003", "noteTitle": "2386003 - S4TWL - Changed Retail Functionality", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, there are changes in several functional areas.</p>\n<p><strong>Business Process related information</strong></p>\n<p>See details in the respective referenced notes.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>See details in the respective referenced notes.</p>", "noteVersion": 1, "refer_note": [{"note": "2365503", "noteTitle": "2365503 - S4TWL - Retail Revaluation", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>Data model changes in Material Ledger and Accounting (see note #2337368) have affected the way, retail revaluations are posted. When you are using the stock valuation at sales price, as this is usual in retail systems, this note describes the changes, that may be relevant for custom coding.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Revaluations affecting margins are posting financial documents</strong></p>\n<p>Function Module STOCK_SALES_PRICE_REVALUATION, as well as the FORMS purchase_price_calculate and purchase_prepare_price_data have been changed, so that they are posting financial documents at the end. In SAP ERP this was the case for the revaluation not affecting margins only. The financial posting leads to entries in tables ACDOCA and ACDOCA_M_EXTRACT which are used to aggregate the MBEW-VKSAL field. (See changes in Material Ledger note #2337368).</p>\n<p><strong>Revaluations not affecting margins are updating stock values at cost in alternative material ledger currencies</strong></p>\n<p>When a revaluation leads to changes in the stock value at purchasing price, this change is now posted in all material ledger currencies. This is normally the case, when you use a revaluation profile \"not affecting margins\". This change is implemented in FORM add_material_ledger_currencies in include LWBW1F09.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if sales price valuation, or value-only articles are used.<br/>This can be checked via transaction SE16N. <br/>Sales price valuation: Enter table T001K and check whether there are entries with field XVKBW (Sales price valuation active) not equal blank.<br/>Value-only article: Enter table T023W and check whether there are entries with field WWGPA (Material group material) not equal blank. If this is the case, that is a good indicator that value-only articles are used.</p>", "noteVersion": 2}, {"note": "2365665", "noteTitle": "2365665 - S4TWL- Retail Season Conversion ((SAP ERP to SAP S/4HANA 1610)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>Season, Fashion Season, Retail</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>You have to activate busines function ISR_RETAILSYSTEM</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA certain functionality is not supported anymore, among others the Retail Season (e.g. customizing tables T6WSP, TWSAI).</p>\n<p>With the conversion, the following new season fields are introduced:</p>\n<ul>\n<li>SEASON YEAR</li>\n<li>SEASON</li>\n<li>COLLECTION</li>\n<li>THEME</li>\n</ul>\n<p>The old season fields in the article master on the Basic Data (table MARA) are not used anymore.  The new season fields will be maintained on Basic Data 2 (table FSH_SEASONS_MAT).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The season will not be defined in the customizing anymore, instead there is the Season Workbench (transaction FSH_SWB) available as a application transaction.</p>\n<p>Part of the conversion from SAP ERP to SAP S/4HANA 1610, article need to be resasigned from \"old\" to \"new\" season.</p>\n<p>With the conversion, the following tables are filled automatically:</p>\n<ul>\n<li>Assignment season to article</li>\n<ul>\n<li>FSH_SEASONS_MAT </li>\n</ul>\n<li>Season definition</li>\n<ul>\n<li>FSH_SEASONS</li>\n<li>FSH_SEASONS_T</li>\n<li>FSH_SD_PERIODS</li>\n<li>FSH_MM_PERIODS</li>\n<li>FSH_COLLECTIONS</li>\n<li>FSH_COLLECTION_T<strong> </strong></li>\n</ul>\n</ul>\n<p>Note: The season fields are no longer maintained in table MARA. All relevant season information is now maintained in table FSH_SEASONS_MAT.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>During the conversion, the following reports are executed automatically:</p>\n<ul>\n<li>R_FSH_S4_SEASONS_MD_MIG to convert the season Customizing data</li>\n<li><span>R_FSH_S4_SEASONS_MAT_MIG_XPRA </span>(XPRA) to convert the season assignment to article</li>\n</ul>\n<p>In case of problems during the conversion, you can execute these reports manually.</p>\n<p>Note that <span><span>R_FSH_S4_SEASONS_MAT_MIG_XPRA</span></span> is a cross-client enabled report, whereas R_FSH_S4_SEASONS_MD_MIG has to be executed in each client required.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if retail season is used. <br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field SAISO (Season) not equal blank.</p>\n<p> </p>", "noteVersion": 5}, {"note": "2417861", "noteTitle": "2417861 - S4TWL - Omichannel Promotion Pricing", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Omichannel promotion pricing includes pricing information for the effective sales price in sales orders based on information from the central omnichannel promotion pricing solution.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Omichannel promotion pricing is planned to be provided in an upcoming SAP S/4HANA release. In addition this functionality has been downported to Enhancement Package 8 Support Package Stack 6 of SAP ERP 6.0, and Enhancement Package 7 Support Package Stack 14 of SAP ERP 6.0.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This note is only relevant if Business Function ISR_RETAIL_OPP is active.</p>", "noteVersion": 2}, {"note": "2339010", "noteTitle": "2339010 - S4TWL - Generic Article Harmonization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You want to convert your SAP ERP for Retail/Fashion system to an SAP S/4HANA system and you want to use existing generic articles in SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p>SAP S4HANA</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>The way of storing characteristic valuations of variants of a generic article and a configurable material were different.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA, generic article harmonization with configurable materials becomes available. With that a generic article is assigned to a configuration class and the variant-creating characteristics for that generic article are taken from that configuration class. The valuation of the variant-creating characteristics of the variants is stored within the configuration storage (IBase). For this reason, the existing generic articles, the variant-creating characteristics and the variants need to be migrated during the upgrade.</p>\n<p><strong>Business Process related information</strong></p>\n<p>As the variant-creating characteristics are no longer linked to a merchandise category or characteristics profile, but to configuration classes, a re-classification of the merchandise category/characteristics profile of a generic article is no longer restricted by the assignment of variant-creating characteristics to merchandise categories/characteristics profiles.</p>\n<p>As a consequence of the data model change the following functionality is not available anymore:</p>\n<ul>\n<li>Assigning a single article to a generic article via report MMASSIGNMAT, see also attached note 2772474</li>\n<li>Transforming a variant of a generic article to a single article via report MMUNHINGEVAR</li>\n<li>Characteristic value change of a variant of a generic article via report MMCHANGEVAR</li>\n<li>Convert Material Master data to Article Master data via report RMMMMPOI (transaction WRST); starting with SAP S/4HANA 1909 report RFM_CONV_MATERIAL_TO_ARTICLE is available which has similar functionality, see note 2843173.</li>\n<li>Conversion of a single article to a generic article with logistical variants via report MMLOGMETRANSFORM</li>\n</ul>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>The complete data migration process is divided into three phases: manual started pre-migration activities, automated migration processes during downtime, and manual started post-migration activities.</p>\n<p>The 1st phase includes the required manual execution of Pre-Migration reports in the SAP ERP for Retail system in all clients. Please implement the SAP Note 2331707 to get the pre-migration reports.</p>\n<p>The 2nd phase includes the automatic execution of another migration report which will be executed by the SUM tool during the downtime.</p>\n<p>The 3rd and the last phase includes the recommended manual execution of a Post-Migration report in the converted SAP S/4HANA system in all clients. Please refer to SAP Note 2350650 for more details.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if generic articles and variants are used. <br/>This can be checked via transaction SE16N. Enter table MARA and check whether there are entries with field ATTYP (Material Category) 01 (generic article) or 02 (variant).</p>", "noteVersion": 8}, {"note": "2930396", "noteTitle": "2930396 - S4TWL - Screen Sequences for Article Maintenance and Area Menus", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are converting an existing SAP ERP system to SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business. The following SAP S/4HANA Transition Worklist Item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Screen sequences allow you to define which screens are available in article maintenance (transactions MM41, MM42, and MM43). Furthermore, you can define in which sequence the screens appear and which subscreens a screen comprises. Screen sequences are defined in Customizing.</p>\n<p>SAP delivers predefined screen sequences that can be used out of the box or can be adjusted or can be used as a copy-from template.</p>\n<p>In SAP ERP, the screen sequences delivered were 23/33 for Retail and FS for Fashion. These screen sequences are still available in SAP S/4HANA; however, to support data model changes for season, supply assignment, and segmentation, new screen sequences have been created. Screen sequence 24 for Retail and screen sequence F4 for Fashion.</p>\n<p>Furthermore, SAP delivers area menus comprising relevant retail and fashion transactions in a structured way. In SAP ERP, area menu W10M contains merchandise management transactions and area menu. W10T contains merchandise management transactions plus transactions from other areas, such as Finance and Human Resources. In SAP S/4HANA, area menu W10M is still available and has been adjusted to reflect the simplifications and innovations available in SAP S/4HANA. Area menu W10T is no longer available; in its place, W10U has been introduced.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes are not affected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In case you are using seasons, segmentation, or supply assignment function, switch from screen sequence 23 to 24, or 33/FS to F4 respectively. If you are using custom screen sequences, adjust them accordingly. For more details check the attached files.</p>\n<p>In case you are using area menu W10T in SAP ERP, in SAP S/4HANA, use area menu W10U.</p>\n<p>Custom code is not affected.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if articles are maintained using transactions MM41, MM42, MM43.</p>", "noteVersion": 7}, {"note": "2340264", "noteTitle": "2340264 - S4TWL - Global Data Synchronization (GDS)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using GDS funcionality. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA GDS only works if the latest Open Catalog Interface (either enterprise service or IDoc PRICECATALOGUE) is used. IDoc PRICAT is not supported anymore. <br/>Also transactions PRICAT* (PRICAT, PRICATCUS1, PRICATCUS2, PRICATCUS3, PRICATCUS6, PR<PERSON>ATCUS7, <PERSON><PERSON><PERSON><PERSON>O<PERSON>, PR<PERSON><PERSON><PERSON><PERSON>GOUT) are not supported anymore. Transactions W_SYNC or W_PRICAT_MAINTAIN need to be used.</p>\n<p><strong>Business Process related information</strong></p>\n<p>It is required that the latest Open Catalog Interface is used.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Execute process as described in referenced note 2326511.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if GDS is used. <br/>This is the case if transactions PRICAT*, W_SYNC, W_PRICAT_MAINTAIN are used.</p>", "noteVersion": 2}, {"note": "2339317", "noteTitle": "2339317 - S4TWL - Retail Business Function Harmonization", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using some Retail related busines applications. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA the structure of the  Retail Business Functions was reorganized and simplified so that most of the Retail functions and  features can be switched on by the Enterprise Business Function  ISR_RETAILSYSTEM..</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes will not be affected directly. For details see referenced note.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>For details see referenced note <span class=\"urTxtStd\">2304479 and <span class=\"urTxtStd\">2303155.</span></span></p>\n<p><span class=\"urTxtStd\"><span class=\"urTxtStd\">In case you have activated one of the following Business Functions, but don't use the functionality, please, create an incident, and we will release the respective pilot note for you:<br/><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">- ISR_RETAIL_INDIA_LOC<br/>- <span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" en-us;=\"\" lang=\"EN-US\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">ISR_RET_PERISH_PROCUREMENT<br/>- ISR_RETAIL_MAP_IF<br/>- ISR_RETAIL_MAP_IF2<br/>- ISR_RETAIL_INSTORE_FPD<br/><span ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" calibri;=\"\" color:=\"\" de;=\"\" en-us;=\"\" minor-latin;=\"\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" mso-fareast-theme-font:=\"\">- ISR_RETAIL_RMA<br/>- ISR_RETAIL_RMA_2<br/>- ISR_RETAIL_RMA_3 </span></span></span></span></span></p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if you have activated the Business Function Set ISR_RETAIL. This can be checked via transaction SFW5.</p>", "noteVersion": 2}, {"note": "2339008", "noteTitle": "2339008 - S4T<PERSON>L - Business Partner for Sites", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using sites in your source system. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>With S/4HANA 1610 the Business Partner harmonization becomes available for the site. Sites can be migrated during the upgrade so they fulfill the new Business Partner paradigm in S/4HANA (every customer/supplier master record must have a Business Partner as leading entity; this was not possible on ERP for customers/vendors assigned to retail sites).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The Business Partner will be the leading object.</p>\n<p>Certain functionality of department store/shop concept is not available SAP S/4HANA. Transactions WRFCATEGORYSHOP, WRFCLOSESHOPS, WRFDELIVERYSITESHOPS, WRF<PERSON>GIONSHOPS, <PERSON><PERSON><PERSON><PERSON>CA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WRF_ARC_DEPSHOP, WRF_TCHAIN_MIGR, WSHOPMIGR are not available.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Follow instructions in the referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if retail sites are used. <br/>Relevance of this note can be checked via transaction SE16N. Enter table T001W and check whether there are entries with field VLFKZ (Plant Category) not equal blank.</p>\n<p>For functionality not availble anymore, please check whether transactions WRFCATEGORYSHOP, WRFCLOSESHOPS, WRFDELIVERYSITESHOPS, WRFREGIONSHOPS, WRFSHOPCAT, WRFSHOPDEP, WRF_ARC_DEPSHOP, WRF_TCHAIN_MIGR, WSHOPMIGR are used.</p>", "noteVersion": 4}, {"note": "2267742", "noteTitle": "2267742 - S4TWL - Automatic Document Adjustment", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the following differences to the process of automatic document adjustment apply:</p>\n<ol>\n<li>The direct entry function is activated per default for all document categories to simplify the creation of worklist entries.</li>\n<li>The field length of the Material Number data element (MATNR) is 40 characters. As a consequence, the field length of data element 'VAKEY' is enhanced and the database tables WIND and S111 have also been adjusted.</li>\n</ol>\n<p><strong>Business Process related information</strong></p>\n<ol>\n<li>In SAP S/4HANA, the indicator for direct entry is set now automatically during the update process of conditions. This means that:</li>\n</ol>\n<ul>\n<li>When a user saves a condition change - including the creation of a new condition - an entry is made in the worklist (table WIND) for all document categories with valid Customizing settings.</li>\n</ul>\n<p>For more information, please see SAP Note 519685 (Conversion of the procedure for Worklist creation).</p>\n<p>Note also that change pointers do not have to be updated for message type CONDBI if direct entries are activated. With the conversion to the 'direct entry' procedure, you can deactivate the <strong><span>update of change pointers</span></strong> if you do not use it for other purposes</p>\n<p>2. Rebuilding business data volume (tables WIND and S111)</p>\n<ul>\n<li>SAP recommends that you process all open worklist entries completely before conversion to SAP S/4HANA. In this case, no activities for automatic document adjustment are required after the system conversion process</li>\n<li>If the worklist cannot be completed in the SAP ERP system, you can proceed with the procedure described in section 'Required and Recommended Actions on Start Release.</li>\n</ul>\n<p><strong><br/>Required and Recommended Action(s)</strong></p>\n<p>If in the ERP system the worklist could not be completely processed, a rebuilding of the worklist for automatic document adjustment is required in SAP S/4HANA, on-premise edition 1511 -&gt; please follow in this case the following procedure:</p>\n<ul>\n<li>Delete the content for automatic document adjustment of table S111 ( Report RMEBEIN6 (transaction MEI6)) and</li>\n<li>Rebuild the table entries for S111 for the used document categories by using the appropriate setup reports (see below). Make sure that the filter selection for rebuilding the S111 entries is meaningful  by selecting the appropriate organizational criteria ( time, org data, docnr, ...) [Rebuild is in SAP S/4HANA]</li>\n</ul>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td>\n<p><strong>Doc. category </strong></p>\n</td>\n<td>\n<p><strong>Description</strong></p>\n</td>\n<td>\n<p><strong>Report for rebuilding  S111 entries</strong></p>\n</td>\n</tr>\n<tr>\n<td>\n<p>01</p>\n</td>\n<td>\n<p>Purchase Order</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>02</p>\n</td>\n<td>\n<p>Scheduling Agreement</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>10</p>\n</td>\n<td>\n<p>Sales Price Calculation</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>11</p>\n</td>\n<td>\n<p>Sales Price Calculation Index</p>\n</td>\n<td>\n<p>RWVKP012</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>20</p>\n</td>\n<td>\n<p>Customer billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>30</p>\n</td>\n<td>\n<p>Vendor billing document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>31</p>\n</td>\n<td>\n<p>Expense document</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>32</p>\n</td>\n<td>\n<p>Vendor settlement document</p>\n</td>\n<td>\n<p>RMEBEIV3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>35</p>\n</td>\n<td>\n<p>Remuneration list</p>\n</td>\n<td>\n<p>RMEBEIL3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>40</p>\n</td>\n<td>\n<p>Vendor settlement request</p>\n</td>\n<td>\n<p>RMEBEIZ3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>50</p>\n</td>\n<td>\n<p>Assortment List</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>55</p>\n</td>\n<td>\n<p>POS-Outbound</p>\n</td>\n<td>\n<p>RMEBEIN3</p>\n</td>\n</tr>\n<tr>\n<td>\n<p>60</p>\n</td>\n<td>\n<p>Customer settlement</p>\n</td>\n<td>\n<p>RMEBEIK3</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p>If you want to process multiple applications in one step (subsequent settlement and automatic document adjustment), you can also use report RMCENEUA.</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2192984, 2215169</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3}, {"note": "2451873", "noteTitle": "2451873 - S4TWL - Retail Store Fiori App - Order Product (F&R Enhancements)", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using the Retail Store Fiori App for Order Products (F0752) with F&amp;R enhancements. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>There are enhancements to the basic retail store Fiori app Order Products for the integration of F&amp;R Replenishment Orders (RPO) and the display of additional F&amp;R forecast information (SAP Notes <a href=\"/notes/0002397064\" target=\"_blank\">2397064</a>/<a href=\"/notes/0002393342\" target=\"_blank\">2393342</a>).</p>\n<p><strong>Business Process related information</strong></p>\n<p>The enhancement is provided starting with SAP S/4HANA 1709.</p>\n<p>In addition this functionality has been downported to Enhancement Package 8 (Support Package Stack 5) and Enhancement Package 7 (Support Package Stack 14) of SAP ERP 6.0. The basic app Order Products without the F&amp;R enhancements continues to be available in SAP S/4HANA 1610.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This note is only relevant if order document type \"F&amp;R Purchase Order\" ('003') is selected in the relevant basic settings in customizing. You can check this, e.g. via transaction SE16, table RTST_C_OP_BASICS, whether there are entries with value \"003\" for field ORDER_DOCUMENT_TYPE.</p>", "noteVersion": 4}, {"note": "2436143", "noteTitle": "2436143 - S4TWL - Retail Store Fiori App - Transfer Products", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA 1610, and you are using the Retail Store Fiori App for Transfer Products (F2449). The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>The retail store Fiori App Transfer Products allows to create a product transfer.</p>\n<p><strong>Business Process related information</strong></p>\n<p>The retail store Fiori app Transfer Product is provided starting with SAP S/4HANA 1709.</p>\n<p>In addition this functionality has been downported to Enhancement Package 8 Support Package Stack 6 of SAP ERP 6.0.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>This note and the related checks shall inform you about this fact and in case you are actively using this functionality on SAP ERP 6.0 prevent a system conversion to older SAP S/4HANA releases in order to prevent a loss of features or data.</p>\n<p>In this case a system conversion is only possible to the SAP S/4HANA release which contains this functionality (or higher).</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>Transfer Products: This note is only relevant if there are entries in table RTST_TP_TRANSFER. You can check this via transaction SE16.</p>", "noteVersion": 2}, {"note": "2267415", "noteTitle": "2267415 - S4TWL - Subsequent Settlement - Vendor Rebate Arrangements", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Business Value</strong></p>\n<p>Condition Contract Management is superior to Subsequent Settlement. It offers the following advantages:</p>\n<ul>\n<li>Central, standardized solution for the administration of supplier and customer conditions</li>\n<li>Transparency which documents are relevant and related to each other</li>\n<li>Detailed overview of settlements</li>\n<li>Flexible settlement calendar</li>\n<li>Support of various data sources to allow modelling of flexible scenarios (including, but not limited to sales-related rebates, scanback rebates, customer funds, purchase-related-rebates)</li>\n<li>Fully integrated with the Order-to-Cash and the Procure-to-Pay Processes</li>\n<li>Designed for high performance</li>\n<li>Architected to benefit from SAP HANA</li>\n</ul>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, the Subsequent Settlement (MM-PUR-VM-SET) application is replaced by the new Contract Settlement (LO-GT-CHB) application. For this reason, the functional scope of subsequent settlement has been restricted, that is, in SAP S/4HANA, it is no longer possible to:</p>\n<ul>\n<li>Create new rebate arrangements using transaction MEB1 or</li>\n<li>Extend existing rebate arrangements using transactions MEBV / MEB7 / MEBH</li>\n</ul>\n<p>Additionally, as a consequence of the material field length extension in the SAP S/4HANA landscape, the structure of table S111 was adjusted. This can have some impact if the business volume data has to be rebuilt in the SAP S/4HANA system.</p>\n<p><strong>Business Process related information</strong></p>\n<p>In SAP S/4HANA, Contract Settlement replaces Subsequent Settlement, which means that existing rebate agreements can only be processed up until the end of the validity date of the agreement and must then be closed by a final settlement. Afterwards new agreements can only be created based on condition contracts.</p>\n<p>Furthermore, if recompilation of the business volume data in SAP S/4HANA, on-premise edition 1511 is required, the index table S111 has to be rebuilt as described in SAP Note 73214 (Subseq.settl.: Retrospec.compltn/recompltn of busin.vol.data).</p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"229\">\n<p>Transaction not available in SAP S/4HANA on-premise edition 1511</p>\n</td>\n<td valign=\"top\" width=\"366\">\n<p>MEB1; MEB7; MEBV, MEBH</p>\n</td>\n</tr>\n</tbody>\n</table></div>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>In SAP S/4HANA there are no direct activities required for continuing the business process for subsequent settlement, with the only exception that the agreements have to be closed after the end of the validity period.</p>\n<p>If a recompilation of the business volume data is required, the table S111 has to be rebuilt with the report RMEBEIN3 as described in note 73214</p>\n<p><strong>Related SAP Notes</strong></p>\n<div class=\"table-responsive\"><table border=\"1\" cellpadding=\"0\" cellspacing=\"0\" class=\"table table-bordered table-striped col-resizeable\">\n<tbody>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Conversion Pre-Checks</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Notes: 2194923, 2215220</p>\n</td>\n</tr>\n<tr>\n<td valign=\"top\" width=\"302\">\n<p>Related SAP Notes</p>\n</td>\n<td valign=\"top\" width=\"302\">\n<p>SAP Note: 73214</p>\n</td>\n</tr>\n</tbody>\n</table></div>", "noteVersion": 3}, {"note": "2385984", "noteTitle": "2385984 - S4TWL - SAP S/4HANA Retail for Merchandise Management, SAP S/4HANA for Fashion and Vertical Business - Simplification Items", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA Retail for merchandise management, and in SAP S/4HANA for fashion and vertical business several functional areas have changed, or are not available anymore, or are not stratgic anymore.</p>\n<p><strong>Business Process related information</strong></p>\n<p>See details in the respective referenced notes.</p>\n<p><strong>Required and Recommended Action</strong></p>\n<p>See details in the respective referenced notes.</p>", "noteVersion": 3}, {"note": "2342914", "noteTitle": "2342914 - S4TWL - Retail Store Fiori App", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA, and you are using the Fiori app for transfer stock. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In SAP S/4HANA, some table structures have changed. Thus, open transfer stock documents need to be closed prior to the conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes will not be affected.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Please, follow instructions of referenced note.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is relevant if the Fiori app for transfer stock is used. <br/><span 'times=\"\" ar-sa;\"=\"\" arial',sans-serif;=\"\" black;=\"\" color:=\"\" de;=\"\" en;=\"\" lang=\"EN\" mso-ansi-language:=\"\" mso-bidi-language:=\"\" mso-fareast-font-family:=\"\" mso-fareast-language:=\"\" new=\"\" roman';=\"\">This can be checked via transaction SE16N. Enter table TRF_DOC_HEAD and check whether the table exists or there are any entries in the table.</span></p>", "noteVersion": 3}, {"note": "2885547", "noteTitle": "2885547 - S4TWL - Vendor Characteristic Values", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system conversion to SAP S/4HANA Retail for merchandise management or SAP S/4HANA for fashion and vertical business. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>Via classification system it is possible to assigen characteristic values to articles.<br/>Vendor Characteristic Values are used to maintain the descriptions of a characteristic value a supplier uses, in case this description deviates from the description you are using. E.g. you use \"red\" for a characteristic value whereas the supplier uses \"fire engine red\".</p>\n<p>In ECC it was possible to maintain the vendor characteristic values either in vendor maintenance (transactions MK0x, XK0x) or in purchasing view of the article maintenance (tramsacations MM4x). Because of the Business Partner harmonization in S/4HANA, it is not possible anymore to maintain the values via supplier maintenance. It is still possible to maitain the values via article maintenance.</p>\n<p>In addition it is possible to achieve a simliar via Characteristic Value Conversion.</p>\n<p><strong>Business Process related information</strong></p>\n<p>Business processes are not affected besides the maintenance as such.</p>\n<p><strong>Required and Recommended Action(s)</strong></p>\n<p>Use maintenance capabilities of article maintenance or switch to Characteristic Value Conversion.</p>\n<p>This does not affect your custom code.</p>\n<p><strong>How to Determine Relevancy</strong></p>\n<p>This Simplification Item is potentially relevant if Vendor Characteristic Values are used.<br/>This can be checked via transaction SE16N. Enter table WYT2 and check whether there are any entries.</p>", "noteVersion": 2}]}], "activities": [{"Activity": "Customizing / Configuration", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you currently maintain vendor characteristic values via vendor maintenance you should adapt your authorization concept to the alternatives mentioned in SAP note 2885547."}, {"Activity": "User Training", "Phase": "Before or during conversion project", "Condition": "Conditional", "Additional_Information": "If you currently maintain vendor characteristic values via vendor maintenance you should train your users for one of alternatives mentioned in SAP note 2885547."}]}