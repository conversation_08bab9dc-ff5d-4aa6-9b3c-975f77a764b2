{"guid": "00109B13199E1EDB9CDAC68A041DA105", "sitemId": "SI12: FIN_MISC_ML", "sitemTitle": "S4TWL - Material Ledger: deprecated table for currency and valuation types", "note": 3017994, "noteTitle": "3017994 - S4TWL -Material Ledger: deprecated table for currency and valuation types", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are doing a system upgrade or conversion to SAP S/4HANA 2021. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>\n<div class=\"sapUiVltCell sapuiVltCell\">\n<div class=\"sapMFlexBox sapMVBox sapMFlexBoxJustifyStart sapMFlexBoxAlignItemsStretch sapMFlexBoxWrapNoWrap sapMFlexBoxAlignContentStretch sapMFlexBoxBGTransparent\" id=\"__vbox0-__layout4-0\">\n<div class=\"sapMFlexItemAlignAuto sapMFlexBoxBGTransparent sapMFlexItem\" id=\"__data392\">\n<div class=\"myNNFV2Text sapUiSelectable\" id=\"__html0-__layout4-0\">\n<p>The custom code check shows customer objects affected by simplifications.</p>\n<p>SAP objects used by the customer objects have become obsolete.</p>\n</div>\n</div>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<p id=\"\">Worklist</p>\n<p>CKMLCT</p>\n<p>Currency Types and Valuation Types in a Valuation Area</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<div class=\"sapMFlexItemAlignAuto sapMFlexBoxBGTransparent sapMFlexItem\" id=\"__data395\">\n<p>Renovation</p>\n<p>SAP table CKMLCT (Currency Types and Valuation Types in a Valuation Area) has become obsolete with SAP S/4HANA 2021.</p>\n</div>\n<div class=\"sapMFlexItemAlignAuto sapMFlexBoxBGTransparent sapMFlexItem\" id=\"__data396\">\n<div class=\"myNNFV2Text sapUiSelectable\" id=\"__html0-__layout4-2\">\n<p>This note contains detailed descriptions about how to adopt customer objects to the data model change.</p>\n</div>\n</div>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Required and Recommended Action(s)</strong><span><br/></span></p>\n<p><span><strong><br/></strong>Please run the ATC custom code check for simplification item S4TWL - Material Ledger: deprecated table for currency and valuation types.</span></p>\n<p><span>If the custom code check returns affected objects, please replace all usages of table CKMLCT with view FMLV_CKMLCT. The view FMLV_CKMLCT has the same structure like table CKMLCT. </span></p>\n<p><span>With S/4HANA the posting amounts of Material Ledger and General Ledger are always in sync. Therefore the fields ADJUST_FLAG (Currency Type Reconc), ADJUST_VERS (SAP Release), ADJUST_DATE (Reconcil. Date) and ADJUST_USER ( User Who Performed the Reconciliation) are not needed anymore. The view FMLV_CKMLCT returns initial values for this fields.</span></p>", "noteVersion": 2, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "After conversion project", "Condition": "Conditional", "Additional_Information": "If custom coding for CKMLCT exists"}]}