{"guid": "0090FAE68D2C1EECBADEF4EF0CB28CCF", "sitemId": "SI14: Insurance_FS-RI", "sitemTitle": "S4TWL - FS-RI - Interface adaptation in the claim", "note": 3210235, "noteTitle": "3210235 - Transition to SAP S/4HANA - FS-RI - interface adjustment in loss", "noteText": "<h3 class=\"section\" data-toc-skip=\"\" id=\"Symptom\">Symptom</h3>\n<p>You are performing a system conversion to SAP S/4HANA 2022.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Other Terms\">Other Terms</h3>\n<ul>\n<li>Loss</li>\n</ul>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\n<p>This SAP Note is relevant if your source release for the system conversion is an ERP release or an SAP S/4HANA release up to and including SAP S/4 HANA 2021.</p>\n<p>It is not relevant if your source release is SAP S/4HANA Finance.</p>\n<h3 class=\"section\" data-toc-skip=\"\" id=\"Solution\">Solution</h3>\n<p><strong>Description</strong></p>\n<p>In the Release SAP S/4HANA 2022, the following methods are deleted and no longer available:</p>\n<ul>\n<li>Class /MSG/CL_VRP_LIB_BO_CLAIM_MOV:</li>\n<ul>\n<li>CHECK_CLAIMHD_ALLOC_CLAIM: No replacement</li>\n<li>CREATE_NEW_CL_DECISION: The method has been integrated into the method COPY_OCLAIM_ASS of the class /MSG/CL_VRP_LIB_BO_CLAIMASS.</li>\n</ul>\n</ul>\n<p>In release SAP S/4HANA 2022, the following methods are renamed:</p>\n<ul>\n<li>Class /MSG/CL_VRP_LIB_BO_CLAIM_DATA:</li>\n<ul>\n<li>SET_OCLAIMHD_FROM_MCLAIMHD is renamed to ASSIGN_MCLAIMHD_TO_OCLAIMHD</li>\n<li>CHK_FIELD_CHANGE is renamed to RESET_CLAIM_HEADER_FIELDS</li>\n</ul>\n<li>Class /MSG/CL_VRP_LIB_BO_CLAIM_ASS:</li>\n<ul>\n<li>CREAT_OCLAIM_ASS is renamed to CREATE_OCLAIM_ASS</li>\n</ul>\n</ul>\n<p>In Release SAP S/4HANA 2022, the following method parameters of the class /MSG/CL_VRP_LIB_BO_CLAIM_DATA are adjusted:</p>\n<ul>\n<li>Method ASSIGN_MCLAIMHD_TO_OCLAIMHD (formerly: SET_OCLAIMHD_FROM_MCLAIMHD):</li>\n<ul>\n<li>IMPORTING parameters (deleted):</li>\n<ul>\n<li>IR_OINSPER</li>\n<li>IR_MINSPER</li>\n<li>IF_CHANGE</li>\n<li>IF_NULLVAL</li>\n<li>IT_NULLVAL</li>\n<li>IF_NEW_CLAIMHD</li>\n</ul>\n<li>EXPORTING parameters (deleted):</li>\n<ul>\n<li>EF_CLAIM_DATE_IDENT</li>\n<li>EO_MCLAIMHD</li>\n<li>EF_UPDATED_CLAIM_HD</li>\n</ul>\n<li>IMPORTING parameters (replaced):</li>\n<ul>\n<li>IR_OPOL replaced by IO_OPOL</li>\n<li>IR_OCLAIM replaced by IO_OCLAIM</li>\n<li>IR_MCLAIM replaced by IO_MCLAIM</li>\n<li>IR_PARENT_MESSAGE replaced by IO_PARENT_MESSAGE</li>\n</ul>\n<li>CHANGING parameters (replaced):</li>\n<ul>\n<li>CS_ALLOC (structure) is deleted. The following structure fields are replaced by parameters:</li>\n<ul>\n<li>CS_ALLOC-MR_OLIFE replaced by IMPORTING parameter IO_OLIFE</li>\n<li>CS_ALLOC-MF_NEW_CLAIM_HD replaced by EXPORTING parameter EF_NEW_CLAIM_HD</li>\n<li>CS_ALLOC-MR_OCLAIMHD replaced by EXPORTING parameter EO_OCLAIMHD</li>\n</ul>\n</ul>\n<li>IMPORTING parameters (new):</li>\n<ul>\n<li>IT_OCLAIMHD (table with all operational claim headers of the current life)</li>\n<li>IO_MCLAIMHD (claim header of movement)</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method RESET_CLAIM_HEADER_FIELDS (previously: CHK_FIELD_CHANGE):</li>\n<ul>\n<li>IMPORTING parameters (deleted):</li>\n<ul>\n<li>IR_OPOL</li>\n<li>IR_OINSPER</li>\n<li>IR_MINSPER</li>\n<li>IR_OCLAIM</li>\n<li>IR_MCLAIM</li>\n</ul>\n<li>EXPORTING parameters (deleted):</li>\n<ul>\n<li>EF_ASS_EXISTS</li>\n<li>EF_FOLLOWING_CLAIM_MOV</li>\n<li>ER_OCLAIMDEC</li>\n<li>ER_OCLAIMASS</li>\n</ul>\n<li>IMPORTING parameter (replaced):</li>\n<ul>\n<li>IR_PARENT_MESSAGE replaced by IO_PARENT_MESSAGE</li>\n</ul>\n<li>EXPORTING parameter (changed):</li>\n<ul>\n<li>EF_CHANGE changed to RETURNING parameter RF_CHANGE_ALLOWED</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method PROCESS_NEW_CLAIM</li>\n<ul>\n<li>IMPORTING parameters (deleted):</li>\n<ul>\n<li>IF_NO_ASSESSMENT</li>\n<li>IT_CHECK</li>\n</ul>\n<li>IMPORTING parameters (replaced):</li>\n<ul>\n<li>IR_INSPER replaced by IO_OINSPER</li>\n<li>IR_POLCOMP replaced by IO_OPOLCOMP</li>\n<li>IR_MPOLCOMP replaced by IO_MPOLCOMP</li>\n<li>IR_POL replaced by IO_OPOL</li>\n<li>IR_CLAIMHD replaced by IO_OCLAIMHD</li>\n<li>IR_MCLAIM replaced by IO_MCLAIM</li>\n<li>IR_PARENT_MESSAGE replaced by IO_PARENT_MESSAGE</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method CALC_CLAIMSHR_AMOUNTS</li>\n<ul>\n<li>IMPORTING parameter (replaced)</li>\n<ul>\n<li>IR_POL replaced by IO_OPOL</li>\n</ul>\n<li>CHANGING parameter (replaced)</li>\n<ul>\n<li>CO_CLAIM is replaced by IMPORTING parameter IO_OCLAIM.</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method CALC_CLAIM_FROM_CLAIMSHR</li>\n<ul>\n<li>IMPORTING parameter (replaced)</li>\n<ul>\n<li>IR_POL replaced by IO_OPOL</li>\n</ul>\n<li>EXPORTING parameter (deleted)</li>\n<ul>\n<li>EF_CLAIM_CALCULATED</li>\n</ul>\n<li>CHANGING parameter (replaced)</li>\n<ul>\n<li>CS_CLAIM_CH is replaced by IMPORTING parameter IO_OCLAIM</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method SET_CL_PROC_STATUS_IN_PROC</li>\n<ul>\n<li>EXPORTING parameter (deleted)</li>\n<ul>\n<li>EF_CLAIM_PROCESSED</li>\n</ul>\n<li>CHANGING parameter (replaced)</li>\n<ul>\n<li>CS_CLAIM is replaced by IMPORTING parameter IO_OCLAIM</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method DET_AMOUNT_ADMITTED</li>\n<ul>\n<li>IMPORTING parameter (replaced)</li>\n<ul>\n<li>IR_POL replaced by IO_OPOL</li>\n<li>IR_PARENT_MESSAGE replaced by IO_PARENT_MESSAGE</li>\n</ul>\n<li>CHANGING parameter (deleted)</li>\n<ul>\n<li>CF_CLAIM_PROCESSED</li>\n</ul>\n<li>CHANGING parameter (replaced)</li>\n<ul>\n<li>CS_CLAIM is replaced by IMPORTING parameter IO_OCLAIM</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method DET_CLAIM_AMOUNTS</li>\n<ul>\n<li>CHANGING parameter (replaced)</li>\n<ul>\n<li>CS_CLAIM is replaced by IMPORTING parameter IO_OCLAIM.</li>\n</ul>\n</ul>\n</ul>\n<p><span>In Release SAP S/4HANA 2022, the following method parameters of the class /MSG/CL_VRP_LIB_BO_CLAIM_ASS are adjusted:</span></p>\n<ul>\n<li>Method CREATE_OCLAIM_ASS (previously: CREAT_OCLAIM_ASS)</li>\n<ul>\n<li>IMPORTING parameter (replaced)</li>\n<ul>\n<li>IR_OCLAIM replaced by IO_OCLAIM</li>\n<li>IR_OPOL replaced by IO_OPOL</li>\n</ul>\n<li>IMPORTING parameters (deleted)</li>\n<ul>\n<li>IR_OCLAIMDEC</li>\n<li>IR_OCLAIMHD</li>\n<li>IF_CLAIMDEC_EXISTS</li>\n</ul>\n<li>EXPORTING parameter (replaced)</li>\n<ul>\n<li>EO_OCLAIMASS is replaced by RETURNING parameter RO_OCLAIMASS.</li>\n</ul>\n</ul>\n</ul>\n<ul>\n<li>Method REASS_OCLAIM_ASS</li>\n<ul>\n<li>IMPORTING parameters (replaced)</li>\n<ul>\n<li>IR_OCLAIM replaced by IO_OCLAIM</li>\n<li>IR_OPOL replaced by IO_OPOL</li>\n<li>IT_OCLAIMDEC replaced by IO_OCLAIMDEC</li>\n<li>IR_OCLAIMHD replaced by IO_OCLAIMHD</li>\n</ul>\n<li>IMPORTING parameters (deleted)</li>\n<ul>\n<li>IF_CLAIMDEC_EXISTS</li>\n</ul>\n</ul>\n</ul>\n<p><strong>Information related to the business process</strong></p>\n<p>The impact on business processes depends on how you use the deleted objects.</p>\n<p><strong>Required action(s)</strong></p>\n<p>Check whether you use the deleted objects specified in the object list and in this SAP Note. If so, you must replace the deleted objects views.</p>\n<p>To do this, replace the objects as described in the SAP Note text. If there is no replacement, you will have to implement a replacement yourself if necessary.</p>", "noteVersion": 3, "refer_note": [], "activities": [{"Activity": "Custom Code Adaption", "Phase": "During conversion project", "Condition": "Mandatory", "Additional_Information": "Obsolete Objects are not available in SAP S/4HANA 2022. Check via ATC if the objects included in the piece list are used in custom code. If yes, you must adapt the custom code. If a replacement for the deleted object is provided, it can be used in the custom code."}, {"Activity": "New developments", "Phase": "During conversion project", "Condition": "Optional", "Additional_Information": "Obsolete Objects are not available in SAP S/4HANA 2022. Check via ATC if the objects included in the piece list are used in custom code. If a replacement for the deleted object isn't provided and needed, you need to implement an own solution."}]}