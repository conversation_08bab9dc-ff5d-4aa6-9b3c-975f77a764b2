import logging
import json
import csv
import datetime
import time
import re

from summarize_util import get_completion, get_summary_gpt_4o, get_completion_with_claude, get_completion_with_gemini
from request_utils import get_sitem_master, read_json_file_from_repo
# from evaluateSummaryWith4Llms import evaluate_summary_gpt4o
from prompts import SYSTEM_MSG, SCORE_PROMPT, GIVE_IMPROVED_SUMMARY_SYSTEM_PROMPT, GIVE_IMPROVED_SUMMARY_USER_PROMPT
from concurrent.futures import ThreadPoolExecutor, as_completed

results = []
failed_guids = []
file_count = 1
existing_list = []

def process_item(item, new_branch=None):
    sitem_guid = item['guid']

    # 如果 GUID 已存在于 existing_sitem_guid_list 中，跳过处理，用于处理中断后的继续处理
    if sitem_guid in existing_list:
        print(f"Skipping existing sitem_guid: {sitem_guid}")
        return None, None

    if new_branch:
        item_info = read_json_file_from_repo(sitem_guid, new_branch)
    else:
        item_info = read_json_file_from_repo(sitem_guid)
    if not item_info:
        return None, sitem_guid
    
    msg = json.dumps(item_info, indent=4)
    initial_summary = get_completion_with_gemini(msg, sitem_guid)  # 4o get_summary_gpt_4o claude get_completion_with_claude gemini get_completion_with_gemini
    
    attempts = 0
    total_score = 0
    best_score = 0
    best_summary = initial_summary

    while total_score <= 360 and attempts < 5:
        attempts += 1
        context = {
            'Document': item_info,
            'Summary': initial_summary
        }
        parse_attempts = 0
        while parse_attempts < 3:
            score_and_feedback = get_completion_with_gemini(SCORE_PROMPT.render(context), sitem_guid, SYSTEM_MSG, 0)  # 4o get_completion claude get_completion_with_claude gemini get_completion_with_gemini
            try:
                score_and_feedback = remove_json_markdown_blocks(score_and_feedback)
                score_and_feedback_json = json.loads(score_and_feedback)
                break
            except json.JSONDecodeError:
                parse_attempts += 1
                print(f"Failed to parse JSON for sitem_guid {sitem_guid}, attempt {parse_attempts} of 3.")
                if parse_attempts == 3:
                    print(f"Failed to parse JSON for sitem_guid {sitem_guid} after 3 attempts.")
                    return None, sitem_guid
        
        total_score = sum(details['score'] for details in score_and_feedback_json.values())
        print(f"Total score: {total_score}")

        if total_score > 360:
            best_score = total_score
            best_summary = initial_summary
            print("Total score over 360, summary fulfilled the requirement.")
            break
        else:
            improvement_feedbacks = "\n\n".join(
                f"{key.capitalize()}: {feedback['improvementFeedback']}"
                for key, feedback in score_and_feedback_json.items()
            )
            improvementContext = {
                'SourceDocument': item_info,
                'InitialSummary': initial_summary,
                'ImprovementFeedbacks': improvement_feedbacks
            }
            improvedSummary = get_completion_with_gemini(  # 4o get_completion claude get_completion_with_claude gemini get_completion_with_gemini
                GIVE_IMPROVED_SUMMARY_USER_PROMPT.render(improvementContext), 
                sitem_guid,
                GIVE_IMPROVED_SUMMARY_SYSTEM_PROMPT
            )
            initial_summary = improvedSummary
            if total_score > best_score:
                best_score = total_score
                best_summary = initial_summary
    
    print(f"Final summary after {attempts} attempts, the score is {best_score}, sitem_guid is {sitem_guid}")
    return {'guid': sitem_guid, 'summary': best_summary, 'score': best_score}, None

def save_results_to_csv(results, file_count):
    # 获取当前时间
    current_time = datetime.datetime.now()
    # 格式化时间戳
    timestamp = current_time.strftime("%Y%m%d_%H%M%S")
    filename = f'results_part_{file_count}_{timestamp}.csv'
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['guid', 'summary', 'score']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for result in results:
            writer.writerow(result)
    print(f"Saved {len(results)} results to {filename}")

# Gemini返回的结果中包含了JSON代码块，需要将其移除
def remove_json_markdown_blocks(text):
    # Regular expression to match a JSON code block
    pattern = r"```json\s*(\{.*?\})\s*```"
    
    # Use re.DOTALL to match across multiple lines
    match = re.search(pattern, text, re.DOTALL)
    
    if match:
        # Extract JSON content without the code block markers
        json_content = match.group(1)
        return json_content
    else:
        # Return the original text if no JSON code block is found
        return text

def get_continuous_improved_item_summary(updated_items, new_branch, max_workers=5):
    summaries = {}
    local_failed_guids = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {executor.submit(process_item, item, new_branch): item['guid'] for item in updated_items}
        for future in as_completed(futures):
            result, failed_guid = future.result()
            if result:
                if result['summary']:
                    summaries[result['guid']] = result['summary']
                else:
                    local_failed_guids.append(result['guid'])
            if failed_guid:
                local_failed_guids.append(failed_guid)
    if local_failed_guids:
        print(f"Failed to process guids: {local_failed_guids}")                
    return summaries, local_failed_guids

if __name__ == '__main__':
    sitem_master = get_sitem_master()

    if sitem_master is None:
        logging.error("Failed to get sitem master")
        exit(1)

    sitem_list = sitem_master['sitemList']


    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(process_item, item) for item in sitem_list]
        for future in as_completed(futures):
            result, failed_guid = future.result()
            if result:
                results.append(result)
                print(f"Processed {len(results)} items")
                if len(results) == 50:
                    save_results_to_csv(results, file_count)
                    results = []
                    file_count += 1
            if failed_guid:
                failed_guids.append(failed_guid)

    if results:
        save_results_to_csv(results, file_count)

    print(f"Sitem guids which failed to get score: {failed_guids}")

