<h3>SI01: Logistics_MOC - Simplifications in MOC</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2616203">2616203 - S4TWL - Simplifications in Management of Change</a></strong></p>
<p><strong>Description:</strong></p>
<p>The following features of Management of Change (MOC) are <strong>not</strong> available in SAP S/4HANA:</p>
<ul>
<li>Enterprise service to create change requests</li>
<li>Web Dynpro application "Change Request - Quick Entry"</li>
<li>MOC Home Page</li>
<li>"Ask an Expert" function for change requests</li>
<li>Determination of people based on business partner hierarchy (see SAP Note 2409939)</li>
<li>Integration of related objects from remote systems</li>
<li>Operational data provisioning to analyze change requests and activities</li>
</ul>
<p>The following features of MOC have been changed in SAP S/4HANA:</p>
<ul>
<li>The worklist (POWL) for change requests and activities has been replaced by SAP Fiori apps.</li>
<li>The business partner integration has been adjusted to the SAP S/4HANA business user concept (see SAP Note 2570961). Business User Management supports only business partners of category <em>Person</em> (BUT000-TYPE = 1).</li>
<li>The way of adding activities to change requests or activities has been simplified. Only adding activities from templates is supported.</li>
</ul>
<p>The following related object types are supported for MOC on the local SAP S/4HANA system:</p>
<ul>
<li>Batch (BAT)</li>
<li>Change Master (ECN)</li>
<li>Customer (CUS)</li>
<li>Document (DOC)</li>
<li>Equipment (EQ)</li>
<li>Functional Location (FL)</li>
<li>Internal Order (IO)</li>
<li>Material (MAT)</li>
<li>Outbound Delivery (DLV)</li>
<li>Outbound Delivery Item (DLI)</li>
<li>Plant (PLA)</li>
<li>Plant Maintenance Notification (PMN)</li>
<li>Plant Maintenance Order (PMO)</li>
<li>Purchase Order (PO)</li>
<li>Purchase Order Item (POI)</li>
<li>Risk Assessment Request in EHS (RAR)</li>
<li>Vendor (VEN)</li>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>To create change requests, you can use the "Change Request" Web Dynpro application instead of the enterprise service and the Web Dynpro application "Change Request - Quick Entry". It can be accessed in creation mode with parameters to predefine certain attributes like change request type or leading object.</p>
<p>To access change requests and activities, you can use the new Fiori apps "Manage Change Requests" and "Manage Change Activities" instead of the "MOC Home Page".</p>
<p>The "Ask an Expert" function has not been replaced by an equivalent.</p>
<p>The process for determination of people has not changed, although adjustments in configuration may be required.</p>
<p>Related objects have to be available on the local SAP S/4HANA system. To be compatible with existing and planned features in MOC on SAP S/4HANA, you must <strong>not</strong> add the following:</p>
<ul>
<li>Related objects from remote systems.</li>
<li>Other related object types than the listed above.</li>
</ul>
<p>The operational data provisioning has not been replaced by an equivalent.</p>
<p>The worklist (POWL) to manage change requests and activities has been replaced by the following SAP Fiori apps:</p>
<ul>
<li>Manage Change Requests</li>
<li>Manage Change Activities</li>
</ul>
<p>The way of handling business users in change requests and activities is the same as it was for business partners. Adjustments in configuration may be required.</p>
<p>The previous versions of MOC have offered two options to add activities: "Add Manually" and "Add from Template". In SAP S/4HANA, you can add activities only from templates.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>If the enterprise service or the Web Dynpro application "Change Request - Quick Entry" has been used, use the Web Dynpro application “Change Request” instead.</p>
<p>Check in Customizing under <em>Cross-Application Components &gt; Management of Change &gt; Define Change Request Type</em> which reference objects (related objects) you are using in MOC. Check in Customizing under <em>Cross-Application Components &gt; Management of Change &gt; Settings for Reference Objects</em> if a remote access has been configured for these reference objects. Ensure that you only use reference objects without remote access in MOC.</p>
<p>Ensure the correct and complete setup of the business user management. For more information, see SAP Note 2570961.</p>
<p>Check setup for people determination:</p>
<ul>
<li>In Customizing under <em>Management of Change</em> > <em>Define Partner Role </em>ensure that none of the active entries contains <em>Business Partner Hierarchy </em>as <em>Position Origin.</em></li>
<li>In <em>Basic Determination of People</em> (transactions <em>/MOC/CRBP</em> and <em>/MOC/ACTBP</em>) and <em>Additional Determination of People using BRFplus</em> ensure that active entries:
<ul>
<li>Only contain business users as <em>Business Partner</em>.</li>
<li>Do not contain <em>Positions</em> from the <em>Business Partner Hierarchy</em>.</li>
</ul>
</li>
</ul>
<p>If users of MOC have used "Add Manually" to add activities of certain activity types, define equivalent activity templates for these activity types.</p>
<p>Implement new Fiori apps "Manage Change Requests" and "Manage Change Activities"</p>
<p>Inform users about changed or new UI.</p>
<p>Check and adjust the customizing for SAP Management of Change.</p>