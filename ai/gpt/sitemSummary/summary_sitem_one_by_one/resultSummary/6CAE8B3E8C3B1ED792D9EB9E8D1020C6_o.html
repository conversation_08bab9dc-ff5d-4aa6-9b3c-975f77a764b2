<h3>BW139: DataSources (Hierarchy)</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2480284">2480284 - BW4SL - Hierarchy DataSources</a></strong></p>
<p><strong>Description:</strong></p>
<p>Loading hierarchy DataSources via Operational Data Provisioning (ODP) is not available in a SAP NetWeaver 7.0x to 7.3x Source System. This affects the following ODP Provider Contexts:</p>
<p>ODP-BW (Hierarchies from InfoObjects in SAP BW Source Systems) in SAP BW as Source System for releases in between SAP BW 7.0x and SAP BW 7.3x</p>
<p>ODP-SAPI (Hierarchy DataSources / Extractors in any SAP Source System) in an SAP Source System (ERP, CRM, SCM, etc.) that only uses the ODP Data Replication API 1.0 or the ODP Replication API 2.0 in conjunction with PI_BASIS 7.30:</p>
<ul>
    <ul>
        <li>Information on the availability of the ODP Replication API 1.0: See SAP Note <a href="https://launchpad.support.sap.com/#/notes/1521883">1521883</a></li>
        <li>Information on the availability of the ODP Replication API 2.0: See SAP Note <a href="https://launchpad.support.sap.com/#/notes/1931427">1931427</a></li>
    </ul>
</ul>
<p><strong>Business Process Impact:</strong></p>
<p>This limitation affects the ability to extract hierarchies using ODP from specified SAP BW and other SAP source systems. Until the mentioned SAP Notes are implemented, hierarchies cannot be loaded using ODP in systems running SAP NetWeaver versions less than 7.3 or similar configurations.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>For ODP-BW (InfoObjects Hierarchies from SAP BW as Source System):
    <ul>
        <li>For SAP BW versions >= 7.0x but < 7.3x: Implement SAP Note <a href="https://launchpad.support.sap.com/#/notes/2418250">2418250</a> on both source and target systems.</li>
        <li>For SAP BW 7.3x: Implement SAP Note <a href="https://launchpad.support.sap.com/#/notes/2469120">2469120</a> on the source system.</li>
    </ul>

    For ODP-SAPI (Hierarchy DataSources/Extractors from any SAP Source System):
    <ul>
        <li>For systems using ODP Data Replication API 1.0: Implement SAP Note <a href="https://launchpad.support.sap.com/#/notes/2418250">2418250</a> on both source and target systems, following manual pre-steps from SAP Note <a href="https://launchpad.support.sap.com/#/notes/2768527">2768527</a>.</li>
        <li>For systems using ODP Replication API 2.0 and PI_BASIS 7.30: Implement SAP Note <a href="https://launchpad.support.sap.com/#/notes/2469120">2469120</a> on the source system.</li>
    </ul>
</p>
<p>If the above limitations apply and required SAP Notes are not implemented, alternative methods may be used for SAP BW versions >= 7.3x with some workarounds as described in the SAP Note 2480284.</p>
<p><strong>Reference Notes:</strong>
<ul>
    <li><a href="https://launchpad.support.sap.com/#/notes/1521883">1521883 - ODP Data Replication API 1.0</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/1931427">1931427 - ODP Data Replication API 2.0</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2418250">2418250 - Extraction of hierarchies via ODP in NetWeaver < 7.30</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2440562">2440562 - ODP: Hierarchy extraction</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2768527">2768527 - ODP API 1.5 Hierarchie Support UDO</a></li>
    <li><a href="https://launchpad.support.sap.com/#/notes/2469120">2469120 - ODP & Hierarchies in PI_BASIS 7.30 / BW 7.3x</a></li>
</ul>
</p>