<h3>SI10: Utilities_CVI</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2344100">2344100 - 2344100 - S4TWL - CVI Integration for SAP ISU</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S/4HANA, the central business partner approach (CVI) is mandatory. Customer and vendor master record creation, maintenance, and display are only possible via the Business Partner functionality (Transaction 'BP'). Consequently, the former concept of SAP IS-U Business Partner and SAP SD Customer integration in SAP ERP has been substituted by SAP Customer Vendor Integration (CVI) in SAP S/4HANA. If the Customer/Vendor Integration is not in place, the system will be declined for the transition.</p>
<p><strong>Business Process Impact:</strong></p>
<p>No influence on business processes expected.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In addition to following the required steps in Note 2265093, execute the following reports provided by Note 2354282:</p>
<table border="1" cellpadding="3" cellspacing="0" class="table table-bordered table-striped col-resizeable">
    <thead>
        <tr>
            <th>Report Name</th>
            <th>Purpose</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>REU_CVI_CUST_LINK</td>
            <td>Establishes a link between the business partner and the customer as required by the standard Customer Vendor Integration (CVI) solution.</td>
        </tr>
        <tr>
            <td>REU_CVI_EKUN_TO_BUT0ID</td>
            <td>Converts the ISU-specific identification fields from the table EKUN to the table BUT0ID in the system.</td>
        </tr>
    </tbody>
</table>
<p>If the aforementioned reports are not run, the system master check <strong>S4TC</strong> will fail. CVI Direction 'Business Partner-to-Customer/Vendor' must not be activated in the source ECC system prior to the S/4HANA conversion. It must be activated only post-conversion in the target SAP S/4HANA system. Early activation of CVI Direction 'Customer/Vendor-to-Business Partner' in the ECC system is not recommended unless synchronized via the BADI CVI_CUSTOM_MAPPER as per Note 1830886.</p>
<p>For more information about CVI-related authorization objects, refer to SAP Note 2671328.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2354282">2354282 - S4 PreChecks IS-UT: Reports for the conversion for transition of the SAP Utilities Industry Solution to S/4 HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2671328">2671328 - SAP S/4HANA CVI related authorization objects for creating/updating business partner</a>, <a href="https://launchpad.support.sap.com/#/notes/2456502">2456502 - S4TC IS-UT Master Check for S/4 System Conversion Checks (New Simplification Item Checks)</a>, <a href="https://launchpad.support.sap.com/#/notes/2364509">2364509 - Report REU_CVI_CUST_LINK does not process all records</a>, <a href="https://launchpad.support.sap.com/#/notes/2792765">2792765 - FAQ: IS-UT upgrade S4</a>, <a href="https://launchpad.support.sap.com/#/notes/2202282">2202282 - S4TC IS-UT Master Check for S/4 System Conversion Checks</a>, <a href="https://launchpad.support.sap.com/#/notes/1830886">1830886 - BP_CVI: Mapping the last name and first name for CVI synchronization</a>