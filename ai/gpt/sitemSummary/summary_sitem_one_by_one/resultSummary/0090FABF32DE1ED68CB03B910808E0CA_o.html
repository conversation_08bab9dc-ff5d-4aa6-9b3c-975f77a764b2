<h3>SI9: Oil_Inconsistency-reports-deprecation</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2328561">2328561 - Inconsistency Reports in HPM</a></strong></p>
<p><strong>Description:</strong></p>
<p>In SAP S4HANA 1610 there is a change in MM-IM data model. Data is moved from existing MM-IM tables to new tables MATDOC and MATDOCOIL. Due to this change the old inconsistency check reports for aggregate and history tables are now deprecated. The change details regarding the new data model specify that no data is stored in the old tables like MKPF, MSEG, MSEGO1, and MSEGO2. Instead, master data is stored in Aggregate tables and transactional data is calculated on the fly from MATDOC and MATDOCOIL using redirect CDS views.</p>
<p><strong>Business Process Impact:</strong></p>
<p>As per the old data model, the data was maintained in the Aggregate/History tables for both Core and Oil & Gas, and there was a necessity to check and correct any inconsistencies between the Core and Oil tables. Since the new data model eliminates data storage in the Aggregate/History tables, there is no longer the requirement to correct inconsistencies between the Core and Oil counterpart tables. Thus, the reports for inconsistency check in the Oil Aggregate/History tables are no longer available.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>The following actions are required during or after the conversion project:
<ul>
<li><strong>User Training:</strong> Train usage of the new consistency check report.</li>
<li><strong>Business Operations:</strong> Switch to new consistency check report ROIBSCAN_MISSING_O1O2 in daily operations.</li>
<li><strong>Custom Code Adaption:</strong> Remove any references to deprecated ABAP objects in your custom code.</li>
</ul>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2599569">2599569 - Data Inconsistency Between Oil and Core Tables</a></p>