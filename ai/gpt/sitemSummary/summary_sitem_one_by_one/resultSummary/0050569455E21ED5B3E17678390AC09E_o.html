<h3>S13: MasterData</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2267297">2267297 - S4TWL - SRM Product Master</a></strong></p>
<p><strong>Description:</strong></p>
<p>The functional scope of SAP Supplier Relationship Management (SAP SRM) will gradually be made available within SAP S/4HANA. The related functions will become part of the procurement solution of SAP S/4HANA. Technically, the procurement solution within SAP S/4HANA is not a successor to the SAP SRM components. Therefore, the SAP SRM software components and SAP S/4HANA cannot be installed on the same system. Accordingly, the SRM Product Master is not available in SAP S/4HANA, on-premise edition 1511.</p>
<p><strong>Business Process Impact:</strong></p>
<p>There is no influence on the business processes, as the SRM product master was not used in ERP functionality.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>In SAP S/4HANA the COMM* tables (COMM_PRODUCT, COMM_HIERARCHY) are no longer available. It is recommended that users archive the ABAP Basis COMM* product master tables and proceed with the upgrade. In a non-productive client, use reports COM_PRODUCT_DELETE_ALL to delete products and COM_HIERARCHY_DELETE_ALL to delete hierarchies. For productive clients, set the deletion indicator using transaction COMMPR01 and archive the products using transaction SARA, then delete the hierarchies using COMM_HIERARCHY. For SRM-OneClient deployment, the users should be aware that the SRM product master data is not accessible after S/4 conversion.</p>
