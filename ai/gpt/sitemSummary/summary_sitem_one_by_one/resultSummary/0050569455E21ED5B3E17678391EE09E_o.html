<h3>SI_2_Logistics_QM</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270126">2270126 - S4TWL - ITS services in QM</a></strong></p>
<p><strong>Description:</strong></p>
<p>SAP Internet Transaction Server (ITS) is considered to be an old technique. Therefore the ITS services for the Internet Application Components (IAC) in QM are not available within SAP S/4HANA, on-premise edition 1511.</p>
<p><strong>Business Process Impact:</strong></p>
<p>As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or you could use the WebDynpro applications mentioned below, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5).</p>
<p>a) Results Recoding: WebDynpro application QI_RECORD_RESULTS_ETI_APPL</p>
<p>b) Quality Notifications: WebDynpro application QIMT_NOTIFICATION_APP</p>
<p>c) Certificates: No WebDynpro application available. However, existing QM transactions are still available, such as QC20, QC21</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Knowledge transfer to key and end users</p>
<p>Check if the services are used via transaction SICF. Hierarchy Type = "Service", Service Name e.g. "QC40" and execute. Click on node e.g. "QC40" and check via context menu (mouse-click right) if service is active. Do the same for all services in the table.</p>
<p>Custom Code Adaption during conversion project (Mandatory)</p>
<p>Customizing / Configuration during conversion project (Conditional): In case WebDynpro applications will be used as replacement, then PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5) need to be configured, and respective authorizations</p>
<p>User Training during conversion project (Mandatory)</p>
<p>Technical System Configuration during conversion project (Mandatory): ITS services in QM - Internet Application Components (IAC) in QM are not available within SAP S/4HANA. As alternatives, you could use the corresponding QM transactions in SAP GUI for HTML or you could use the WebDynpro applications mentioned below, which are part of the PFCG role for NWBC Quality Inspector (SAP_SR_QUALITY_INSPECT_5).</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2207278">2207278 - ITS services for quality certificate, results recording, and quality notification removed</a></p>