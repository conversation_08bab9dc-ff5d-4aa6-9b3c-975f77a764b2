<h3>BW204: Key Date Derivations</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2443281">2443281 - BW4SL - Currency Translations, Quantity Conversions, Key Date Derivations</a></strong></p>
<p><strong>Description:</strong></p>
<p>Currency translation types, unit conversion types, or key date derivations that are using a field/InfoObject from an InfoSet in their definition (name contains three underscores), are not available in SAP BW/4HANA (since InfoSets are not available). Unit conversion types that are based on a classic DataStore object are not available in SAP BW/4HANA.</p>
<p><strong>Business Process Impact:</strong></p>
<p>The unavailability of InfoSets in SAP BW/4HANA impacts currency translation, unit conversion, and key date derivation processes that rely on InfoSets. This requires either manual adaptation or deletion of these processes to align with SAP BW/4HANA's structure. Additionally, unit conversion types based on classic DataStore objects need to be converted to advanced DataStore objects using Transfer Tools.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>1. Manually adapt or delete currency translation types, unit conversion types, and key date derivations based on InfoSets.<br>2. Convert unit conversion types based on classic DataStore objects to advanced DataStore objects using Transfer Tools.<br>3. Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are supported or can be converted to SAP BW/4HANA.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2421930">2421930 - Simplification List for SAP BW/4HANA</a>, <a href="https://launchpad.support.sap.com/#/notes/2347382">2347382 - SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)</a>, <a href="https://launchpad.support.sap.com/#/notes/2383530">2383530 - Conversion from SAP BW to SAP BW/4HANA</a></p>