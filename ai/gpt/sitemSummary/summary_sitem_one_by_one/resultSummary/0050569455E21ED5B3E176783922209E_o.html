<h3>SI4: FIN_CO</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2270411">2270411 - S4TWL - General Cost Objects and Cost Object Hierarchies</a></strong></p>
<p><strong>Description:</strong></p>
<p>You are doing a system conversion to SAP S/4HANA, on-premise edition. The following SAP S/4HANA Transition Worklist item is applicable in this case.</p>
<p><strong>Business Process Impact:</strong></p>
<p>General Cost Objects are not available within SAP S/4HANA, on-premise edition 1511 (consider using internal orders or product cost collectors instead). The associated functions were previously part of the menu Controlling &gt; Product Cost Controlling &gt; Cost Object Controlling &gt; Intangible Goods and Services. Note that a general cost object is not the same as a CO object which can refer to orders, WBS elements or any item to which costs can be assigned.</p>
<p>Cost Object Hierarchies are not available within SAP S/4HANA, on-premise edition 1511 (consider using summarization hierarchies for aggregation of costs on manufacturing orders and distribution of usage variances function to distribute inventory differences and activity differences instead). The associated functions were previously part of the menu Controlling &gt; Product Cost Controlling &gt; Cost Object Controlling &gt; Product Cost by Period.</p>
<p>Where the cost object hierarchy is being used to aggregate costs collected on the manufacturing orders, it is recommended that an order hierarchy be used instead. These hierarchies are created by using transaction KKR0 and are aggregated at runtime by using transaction KKBC_HOE_H. Note that this transaction behaves like transaction KKBC_HOE but does not require a data collection run to pre-aggregate the data for each hierarchy node.</p>
<p>Where the cost object hierarchy is used to collect costs at an aggregate level and then distribute them to the associated manufacturing orders, it is recommended that transaction CKMDUVMAT be used to distribute material usage variances and CKMDUVACT to distribute activity usage variances.</p>
<p>Analyze reporting processes to determine whether cost objects are used as an account assignment and understand the nature of the costs posted to the cost object nodes. Use distribution of usage variances function as an alternative for cost distribution.</p>
<p>Analyze reporting processes to determine whether cost objects are used to aggregate costs and understand the nature of the reporting hierarchy (representation of profit centers, cost centers, responsibility areas) and determine alternative ways of reporting.</p>
<p>Custom Code to update cost object to CO tables will still run. Contact SAP for pilot note to release functions for short term use.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Do not activate Cost Objects in controlling area. Do not build a data model that requires use of general cost objects or cost object hierarchies.</p>
<p>Investigate alternative solutions outlined above if you are using cost objects or cost object hierarchies as you move to SAP S/4HANA.</p>
<p>Adjust roles and authorizations to remove deprecated transactions.</p>
<p><strong>User Training:</strong> Remove mentioned transactions from existing roles and train users to work with alternative transactions.</p>
<p><strong>Process Design / Blueprint:</strong> Investigate alternative solutions outlined in this SAP Note if you are using cost objects or cost object hierarchies.</p>