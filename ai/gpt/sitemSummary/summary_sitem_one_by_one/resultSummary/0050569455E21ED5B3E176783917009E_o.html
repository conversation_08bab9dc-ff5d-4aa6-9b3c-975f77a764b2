<h3>SI18: Logistics_PP</h3>
<p><strong>Business Impact Note: <a href="https://launchpad.support.sap.com/#/notes/2268116">2268116 - S4TWL - ABAP-List-Based PI-Sheets</a></strong></p>
<p><strong>Description:</strong></p>
<p>Starting with R/3 4.6 C, the previously used ABAP-list-based process instruction sheets have been replaced by browser-based PI sheets. The functionality of the ABAP-list-based PI sheets has been partly deactivated and underlying code is only partially available in SAP S/4HANA on-premise. ABAP-list-based process instruction sheets will be carved out fully in a future release. You can use browser-based PI sheets in SAP S/4HANA, on-premise edition for the time being. New capabilities for PI sheets will be offered as part of SAP Digital Manufacturing Cloud as the functionality of control recipes and process instruction in S/4HANA is not considered as future technology.</p>
<p><strong>Business Process Impact:</strong></p>
<p>Customers that used ABAP-list-based PI sheets temporarily have to switch to browser-based PI sheets: The corresponding control recipe destinations of type 1 (ABAP-list-based PI sheets) have to be replaced with control recipe destinations of type 4 (browser-based PI sheets). This mostly affects the used master recipes and process orders. Browser-based PI sheets offer better support for business processes. No effects on business processes are expected. Transactions CO56 and CO58 have been deactivated in SAP S/4HANA, on-premise edition.</p>
<p><strong>Required and Recommended Actions:</strong></p>
<p>Start exchanging control recipe destinations of type 1 (ABAP-list-based PI sheets) with control recipes of type 4 (Browser-based PI sheets). Create and send test control recipes for the exchanged control recipe destinations. Check the browser-based PI sheets that are generated from the test control recipes.</p>
<ul>
<li>Use XStep-based process instructions instead of characteristic-based process instructions</li>
<li>Replace generation scopes and filters that are defined in customizing (of the control recipe destination) with XStep-based generation scopes and valuation symbols</li>
</ul>
<p>For mid- to long-term planning consider new capabilities for PI sheets in SAP Digital Manufacturing Cloud.</p>
<p><strong>Reference Notes:</strong> <a href="https://launchpad.support.sap.com/#/notes/2270233">2270233 - S4TWL - Control Recipes/Instructions</a>, <a href="https://launchpad.support.sap.com/#/notes/397504">397504 - Support for ABAP list-based PI sheet</a></p>