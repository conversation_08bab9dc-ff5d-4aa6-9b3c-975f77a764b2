SAP Note 2582387 provides information on the support for the Edge Legacy Browser by the SAP Web Client User Interface Framework (WEBCUIF). Below is a summary of key points from the note:

- This note confirms official support for the Edge Legacy Browser on Windows 10 for WEBCUIF version 747, S4102, and subsequent releases.
- It specifies that this support does not extend to application-specific code within the WEBCUIF layer.
- Users encountering graphics issues with older flash technologies on Edge Legacy should refer to SAP Note 2475426 for a solution.
- Edge Chromium does not require the corrections in this note; users should see SAP Note 1678064 for Edge Chromium support.
- The note provides reasons for this support as being due to changes in Microsoft's browser technology.
- With the discontinuation of IE11 support by Microsoft, SAP advises using modern browsers such as Chrome, Edge (Chromium), Safari, or Firefox.
- End of support for the Edge Legacy desktop app was on March 9, 2021.
- The note includes a warning about known memory leaks with Edge Legacy and IE11 browsers and references several Microsoft web pages reporting these issues.
- SAP Note 2050322 is suggested for instructions on clearing the cache before testing after the installation of the correction instructions.

Additionally, for accessibility concerns, SAP Note 2911434 should be referred to.

Overall, this SAP Note provides guidance on browser support related to WEBCUIF, highlights the limitations and considerations for using Edge Legacy, and provides cross-references for additional support and troubleshooting resources.