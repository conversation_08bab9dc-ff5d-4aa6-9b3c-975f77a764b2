SAP Note 2895412 addresses an issue with the migration object "FI - Accounts payable open item" or "FI – Accounts receivable open item" in SAP S/4HANA versions 1809 and 1909. When attempting to load A/P open item or A/R open item data using the SAP S/4HANA migration cockpit, users may encounter an error that says "Vendor/Customer has no bank details with indicator xxxx," even though the bank details are correctly maintained in business partner master data.

The cause of the issue is that the conversion rule for the "Partner Bank Type" (BVTYP) field is applying a "CONVERSION_EXIT_ALPHA_INPUT" function, which adds leading zeros to the value (thus, '01' becomes '0001').

The solution depends on the release and service pack level:
- For SAP S/4HANA 1809, from SP00 to SP03, it's recommended to implement TCI Note 2891675.
- For SAP S/4HANA 1909, from SP00 to SP01, implementing TCI Note 2891712 is the advised solution.

Alternatively, users can modify their generated Z project to exclude the "CONVERSION_EXIT_ALPHA_INPUT" from the conversion rule CVT_BVTYP_CUST (for customers) and CVT_BVTYP_SUPL (for suppliers) using the tool LTMOM (Legacy Transfer Migration Object Modeler).