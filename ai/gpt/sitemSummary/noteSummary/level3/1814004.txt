SAP Note 1814004 addresses an issue with the migration tool that involves the transfer rules from an export DataSource of an InfoProvider (like a DataStore Object, or DSO) to a 3.x customer-specific InfoSource. The problem described involves two points:

1. When using the migration tool, a transformation that is based on transfer rules and uses an export DataSource as its source is erroneously duplicated.
2. The migration tool fails to migrate the export DataSource, resulting in an error message in the log.

The correction attached to the note changes the tool's behavior in the following ways:

1. The source of the copied transformation will be directly from the InfoProvider itself (e.g., DSO) instead of the export DataSource.
2. The export DataSource is ignored so that no error message is issued regarding its migration.

For obsolete 3.x InfoSources and transfer rules, the note advises that they must be manually deleted as the migration tool does not support their deletion.

The solution provided in this note involves importing specific Support Packages depending on the SAP NetWeaver BW version:
- For SAP NetWeaver BW 7.30: Import Support Package 10 (SAPKW73010) which is detailed in SAP Note 1810084.
- For SAP NetWeaver BW 7.31 (Enhancement Package 3): Import Support Package 08 (SAPKW73108) which is described in SAP Note 1813987.
- For SAP NetWeaver BW 7.40: Import Support Package 03 (SAPKW74003) as outlined in SAP Note 1818593.

In urgent cases, the correction instructions from the SAP Note can be applied as an advance correction. Users are advised to read SAP Note 875986 before using transaction SNOTE.

Furthermore, it is noted that the above-mentioned SAP Notes may be available before the Support Package release and may be labeled as "Preliminary version."