SAP Note 960062 addresses an issue with Data Transfer Processes (DTP) that are configured to transfer delta data only once into Operational Data Store (ODS). The symptom described is that these delta requests are being checked for completeness in the ODS, which isn't always necessary and can be omitted for this delta method due to a program error.

Summary of the SAP Note 960062:

- **Issue**: There's an unnecessary check for gaps and completeness of delta requests in ODS when using DTP that transfers delta only once.
- **Terms related to the issue**: DTP, request, delta, transfer only once.
- **Cause**: The issue arises from a programming error.
- **Affected SAP Release**: This issue affects BW 7.0 systems.
- **Solution**: To fix this issue, SAP recommends importing Support Package 09 for BW 7.0 (identified by patch number SAPKW70009) into the affected BW system. Additional details about the Support Package will be available in SAP Note 914303, titled "SAPBWNews BW 7.0 SP09."
- **Availability of the solution**: The solution is to be applied as per the release of SAPBWNews BW 7.0 SP09; this related note might be available even before the Support Package's release, labeled as a "Preliminary version."