SAP Note 947385 addresses a specific enhancement in the SAP Real Estate Management (RE Classic) module concerning the calculation of surcharges and reductions within representative lists of rents. Here's a summary of the note:

**Symptom:**
Previously, if surcharges and reductions were maintained within the representative list of rents without reference to a fixtures and fittings characteristic, they were ignored in RE Classic.

**Enhancement Details:**
1. After implementing this note, you are able to input the maximum number of surcharges and deductions, as well as the maximum area price, in Customizing. These values are now supported during the calculation process.
2. A priority scheduling system is introduced for using surcharges and deductions as follows:
   a) If an area price is defined, it is used.
   b) If no area price is defined but a percentage price is, the percentage price is used.
   c) If neither an area price nor a percentage price is defined, but an absolute price is available, then the absolute price is used.
3. The fields for Maximum number and Maximum area price become irrelevant if the surcharges and deductions are maintained with reference to a fixtures and fittings characteristic.

**Reference Terms:**
RECACUST, representative list of rents, characteristic categories, surcharges, reductions.

**Reason for Enhancement:**
The note specifies that this is a program enhancement designed to improve the module's functionality.

**Solution:**
To benefit from this enhancement, it is necessary to implement the correction instructions provided with the note into your SAP system. 

This update will impact users who manage surcharges and reductions within the representative lists of rents in RE Classic and should provide more flexibility and accuracy in rent calculations.