SAP Note 1685280 addresses the issue where users were unable to convert semantically partitioned objects (SPOs) or their partitions to the SAP HANA-optimized data format using transaction RSMIGRHANADB, which previously only supported conversion for standard InfoCubes and DataStore objects (DSOs).

The Note specifically applies to systems where SAP HANA is the underlying database platform.

Here's a summarized solution provided by the Note:

1. Transaction RSMIGRHANADB now includes support for converting both InfoCube-based and DataStore-based SPOs for SAP HANA optimization.
2. The process of converting SPOs encompasses several steps, including checking the SPO for conversion eligibility, saving, locking, converting the partitions, and then unlocking the SPO.
3. The locking process differs between InfoCube-based and DSO-based SPOs, affecting how partitions are handled depending on whether or not they contain data.
4. Conversion logs can be viewed using transaction SLG1 with the object "<PERSON><PERSON><PERSON>" and subobject "REFACTORING".
5. Any issues during the conversion process can be identified and resolved based on the status information stored in the table RSDRI_HDB_SPO, which keeps track of the conversion's progress and failures.
6. External activation of the SPO is not possible while it is locked, indicated by an error message RSDRI_HDB 261. The SPO can only be activated normally after the SAP HANA conversion is completed successfully.

Additionally, the note provides references to two Support Packages that include necessary updates for SAP NetWeaver BW systems (versions 7.30 and 7.31) and indicates that preliminary versions of the SAP Notes may be available before the full release of the Support Packages.

Users with urgent needs can implement the correction instructions as an advance correction after reading SAP Note 875986, which offers guidelines on using transaction SNOTE for this purpose.