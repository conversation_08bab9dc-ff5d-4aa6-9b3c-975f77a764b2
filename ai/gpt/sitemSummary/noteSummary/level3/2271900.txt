SAP Note 2271900 addresses the process of analyzing custom code in the context of an S/4HANA conversion using the Custom Code Migration Worklist (transaction SYCM) and SAP Code Inspector.

Summary:

- Users are using transaction SYCM to check custom code against the Simplification Database and encountering a high number of issues, particularly related to the enhancement of the length of the material number (referenced in SAP Notes 2215424 and 2215852).
- The note explains the desire to automatically generate an SAP Code Inspector check variant based on data from the Simplification Database, which would include checks for field extension support and search DB operations.
- Prerequisites include implementing SAP Notes 2135878 (if SFIN 2.0 is not installed) and SAP Note 2232062 for SAP_BASIS 7.50 SP 0 or 1 systems.
- For systems based on SAP_BASIS 7.51 or higher, the note indicates that transaction SYCM is obsolete. Instead, it suggests using the "S/4HANA Readiness" ATC checks with variants S4HANA_READINESS or S4HANA_READINESS_REMOTE for analyzing development objects.
- For systems based on SAP_BASIS 7.50, a Code Inspector check variant can be generated using the report SYCM_CREATE_CI_CHECK_VARIANT, which considers data from the Simplification Database. It also recommends the use of the remote code inspector check "Field Length Extension" to reduce findings related to material number enhancement.
- Users are instructed to implement the note and follow the manual post-instruction steps provided.
- Finally, the note includes an attached how-to guide titled "Code Inspector in the Context of an S/4HANA Migration" for further guidance.

The note suggests that for optimal results, the use of S/4HANA custom code checks based on SAP_BASIS 7.51 or higher is preferred over the Custom Code Migration Worklist and Code Inspector checks based on SAP_BASIS 7.50.