SAP Note 1823106 addresses a specific problem where using the same RFC connection for multiple successive calls via the BAPI_DSOURCE_SEND* interface to BW (Business Warehouse) results in an incorrect data record count (target values / sent).

The root cause of this issue is identified as a programming error.

The solution provided is to import the corresponding support package for various SAP NetWeaver BW versions, which contains the fix for this problem. The note outlines the required support package numbers for different BW versions as follows:

- For SAP NetWeaver BW 7.00, import Support Package 31 (SAPKW70031), with further information detailed in SAP Note 1782745.
- For SAP NetWeaver BW 7.01, import Support Package 14 (SAPKW70114), with further information detailed in SAP Note 1794836.
- For SAP NetWeaver BW 7.02, import Support Package 14 (SAPKW70214), with further information detailed in SAP Note 1800952.
- For SAP NetWeaver BW 7.11, import Support Package 12 (SAPKW71112), with further information detailed in SAP Note 1797080.
- For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010), with further information detailed in SAP Note 1810084.
- For SAP NetWeaver BW 7.31, import Support Package 08 (SAPKW73108), with further information detailed in SAP Note 1813987.
- For SAP NetWeaver BW 7.40, import Support Package 03 (SAPKW74003), with further information detailed in SAP Note 1818593.

For urgent cases, the note suggests implementing correction instructions as an advance correction.

It also advises reading SAP Note 875986 for information about transaction SNOTE, which facilitates the application of support package notes.

Lastly, the Note mentions that the related SAP Notes detailing the support packages may already be available in a preliminary version before the official release of the support package. The preliminary version will be indicated by the words "Preliminary version" in the short text of the SAP Note.