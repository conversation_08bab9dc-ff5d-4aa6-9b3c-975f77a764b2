SAP Note 187344 addresses an issue where users experience a short dump with the error message "Function module 'ENQUEUE_ESSERPNODE' not found" when attempting to insert a report into the report tree. Additionally, when trying to activate the lock object ESSERPNODE in the ABAP dictionary, the error "Total length of lock argument for table SREPOVARI longer than 150" is displayed, and the lock object is not activated.

This problem stems from the constraint in SAP Release 4.5 that restricts the length of lock arguments to 150 characters, a limit exceeded by lock object ESSERPNODE.

The note provides a solution for the issue as follows: 
- For SAP Release 4.5B, the lock object has been corrected.
- For Release 4.5A, a prerelease version of the lock object ESSERPNODE is available on sapServ3.

The note includes instructions to import the transport request (consisting of files K000163.K4D and R000163 found in directory /general/R3server/abap/note.0187344) by following the steps outlined in SAP Note 13719.

Users who encounter this specific error should implement the provided solution to correct the lock object and prevent the associated short dump when working with the report tree in the affected SAP releases.