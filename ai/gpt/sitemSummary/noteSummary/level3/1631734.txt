SAP Note 1631734 outlines how to set up Windows Active Directory (AD) plugin and Single Sign-On (SSO) for SAP BusinessObjects Business Intelligence (BI) version 4.0. However, the note emphasizes that the approach described is outdated by approximately five years and could cause problems if used today. Instead, the note strongly recommends using Knowledge Base Article (KBA) 2629070, which contains updated guidance that should be compatible with newer versions of BI (4.1 and 4.2) and environments including Windows 10, Chrome browsers, and Active Directory environments using AES encryption and SSL.

The outdated SAP Note discusses various configurations and prerequisites for setting up AD and SSO, including service account roles, creating service principal names, configuring AD plugin in the SAP Central Management Console (CMC), starting the server under a service account, and configuring manual AD authentication.

The resolution for setting up manual AD login and SSO is detailed in the attached document "Configuring Active Directory Manual Authentication and SSO for BI4.pdf," which covers the following sections:

1. Planning your Service Account Configuration
2. Creating and preparing the service account
3. Configure the AD Plugin Page in the CMC and map in AD groups
4. Steps to start the SIA/CMS under the service account
5. Configuring Manual AD authentication to Java Application Servers
6. Configuring BI Launch Pad and CMC for manual AD logon
7. Configuring Active Directory Single Sign-On
8. Additional information and best practice settings

The note also mentions that some environments using Windows AD 2003 or BI 4.0, which are either not recommended or out of support, might still find this guide useful, though other settings may be found in different guides.

It acknowledges other related KBAs for additional configurations such as SSL, SSO to databases, web services SSO, and browser setups for kerberos SSO. In addition, it urges users to comment and rate the article for feedback and directs them to SAP Questions and Answers or the authentication guided answers for further help or solutions.

The keywords section mentions various errors and issues that can arise during setup and provides further descriptions regarding AD Single Sign-On configurations and troubleshooting.