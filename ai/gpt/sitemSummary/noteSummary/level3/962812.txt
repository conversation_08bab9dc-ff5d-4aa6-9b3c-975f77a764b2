SAP Note: 962812 - Deleting an authorization assignment does not work

Summary:

Symptom:
Users sometimes encounter issues where they are unable to save changes in the assignment maintenance of analysis authorizations for users.

Other Terms:
The issue is relevant to transactions such as RSECADMIN (the Analysis Authorization Maintenance) and RSU01 (User Maintenance), specifically regarding authorization assignment.

Reason:
The issue occurs under specific circumstances. For instance, when a user changes the user's authorization, attempts to save it, deletes the assignment, and attempts to save again. After restarting, the assignment reappears. This indicates that the error is persistent.

Solution:
The solution provided is to import Support Package 09 for SAP NetWeaver 2004s Business Intelligence (BI) into the BI system. Detailed information about this Support Package can be found in Note 0914303 with the short text "SAPBINews BI 7.0 SP09," which is to be released for customers. For immediate resolution, correction instructions can be used if the Support Package is not yet available. It should be noted that the mentioned notes might be in a "preliminary version" state if the Support Package has not been released.