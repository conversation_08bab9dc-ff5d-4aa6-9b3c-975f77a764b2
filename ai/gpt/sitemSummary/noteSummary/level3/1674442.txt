SAP Note 1674442 addresses the issue of unsatisfactory data throughput when data is extracted from an SAP source system using the Operational Data Provider (ODP) API with an ETL tool such as SAP BusinessObjects DataServices. The poor performance is due primarily to how data is handled in the method _DATA_TO_XTABLE of the class CL_RODPS_REPLICATION, specifically during the execution of the RFC module RODPS_REPL_SOURCE_FETCH.

The root cause of the issue is identified in the note as the system's inefficient method of transferring the transport structure. It converts each field of the extracted data record into a character-type field individually, rather than using the MOVE-CORRESPONDING statement to transfer entire data records together.

To solve this performance problem, the note recommends importing the relevant PI_BASIS Support Package for your SAP system release. If the support package is not applicable, users are advised to use the Note Assistant with transaction SNOTE to implement the advanced correction provided with this note.

Keywords and issues related to this note include ETL, timeouts, runtime errors such as TIME_OUT or RFC_ABAP_RUNTIME_FAILURE, and ‘time limit exceeded’ exceptions. The note could also contain workarounds, references to other notes, or links to support packages aimed at resolving the problem.