SAP Note 949859 addresses an issue where an administrator activates a user to record traces, but the process to be traced terminates with a "DB_READ_PROBLEMS" short dump. The cause of this problem is a program error where the system incorrectly accesses a table that the user activated for tracing doesn't necessarily have an entry in.

The resolution provided in the note is specific to SAP NetWeaver 2004s BI. Users facing this issue should import Support Package 09 for SAP NetWeaver 2004s BI, which can be identified by the patch number BI Patch 09 or SAPKW70009. Details about this Support Package can be found in SAP Note 0914303, titled "SAPBINews BI 7.0 Support Package 09," once it has been released for customers.

In urgent situations, correction instructions are provided as an interim solution. Additionally, the note mentions that the relevant notes may already be available prior to the official release of the Support Package, but will contain the wording "preliminary version" in their short text during that period.