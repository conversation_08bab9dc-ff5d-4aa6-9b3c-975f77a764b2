The SAP Note 1718793 addresses an issue with the G/L (General Ledger) accounts object type within the context of segment reorganization in SAP. The problem is that the object type is only set for Generation and Transfer posting activities. As a result, secondary level objects, which are essential for proper functioning, are not being generated. This leads to issues when trying to close a reorganization plan.

The issue occurs due to an error in the definition of the BC set (Business Configuration Set). For users to be affected by this issue, they must be on Enhancement Package 4 (EhP4) for SAP ERP, and the business function FIN_GL_REORG_SEG, which relates to financials general ledger reorganization for segments, must be activated.

The note provides a solution in the form of manual correction instructions that users need to implement to resolve the error. This typically would involve steps that adjust the BC set so that G/L account object types become relevant for the full scope of desired activities, not just Generation and Transfer postings. These changes would ensure that second-level objects are generated correctly, enabling the successful closure of reorganization plans.