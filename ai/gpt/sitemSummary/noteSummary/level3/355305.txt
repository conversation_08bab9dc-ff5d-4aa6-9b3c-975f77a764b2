The SAP Note 355305 addresses an issue where users are unable to transfer the TCURN table to the BW (Business Warehouse) system. This problem results in error messages RSW_RATE_GET_BASIC-02 or RSW_RATE_GET_BASIC-01 during the process of currency translation.

Relevant terms for this issue include SBIC, global settings, exchange rates, currencies, currency translation, and the program SBIC_CURRENCIES_TRANSFER.

The source of this issue is identified as a program error.

To resolve this problem, the following solutions are recommended:

1. For the BW system:
   Users should import Support Package 10 for BW 2.0B, SAPKW20B10, into their BW system. The availability of this Support Package is contingent upon the release of Note 213330 with the short text "SAPBWNews for BW 2.0B Support Package 10." Before its official release, the Note 213330 may have the term "preliminary version" in its short text.

2. For the OLTP (R/3) system:
   Users should import the respective Basis Support Package into their source system based on different releases, as follows:
   - For Release 4.6A: Support Package 29
   - For Release 4.6B: Support Package 21
   - For Release 4.6C: Support Package 11
   - For Release 4.6D: Support Package 04
   Alternatively, users can import a specific transport (Y9AK005167) from the SAPSERVE3 server, under the directory path \home\general\R3server\abap\note.0355305.

By following these steps, the problem preventing the transfer of table TCURN into the BW system should be resolved, thus eliminating the associated error messages during currency translation.