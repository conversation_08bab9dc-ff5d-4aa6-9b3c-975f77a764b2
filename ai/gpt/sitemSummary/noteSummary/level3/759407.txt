SAP Note 759407 addresses an issue where the EU_INIT background job is repeatedly starting and running in parallel, which leads to deadlocks on the database involving tables like WBCROSSI, CROSS, and WBCROSSGT. This unwanted behavior occurs without the user's initiation.

The problem is caused by a program error or incorrect job scheduling. EU_INIT, which either runs program <PERSON>PR<PERSON><PERSON> or SAPRSEUB, is based on creating a complete where-used list index in the system, having a significantly long runtime. It is generally needed only in development systems and is supposed to run only once.

The solution provided in the note involves implementing advance corrections or a hot package to resolve the issue. If the recurring EU_INIT job is not required, you can stop it by running program <PERSON><PERSON>SEUB_STOP or ZSAPRSEUB_STOP based on the relevant SAP Basis release level:

- For releases 6.10 to 6.40, create a customer-specific program ZSAPRSEUB_STOP with source code from correction instruction 875591.
- For 7.0x releases, use the Note Assistant to implement correction instruction 838097, which provides the report SAPRSEUB_STOP.
- From Release 7.10 onwards, SAPRSEUB_STOP is included in the standard system.

Executing one of these programs once will prevent the system from rescheduling the EU_INIT job after it is terminated or stopped. EU_INIT should not be active when you execute this program.

It is also mentioned that the solution is valid for productive systems where the EU_INIT job was unintentionally started, and a run was not necessary. After applying the correction, the job's restart mechanism will be deactivated until a user manually restarts it. However, users should be aware that suppressing the EU_INIT job will stop setting up indexes for the where-used list, potentially affecting the correct functionality of the where-used list in the system.