SAP Note 656651 addresses an issue with retroactive accounting in SAP payroll processing where retroactive differences in various wage types are inconsistently added into specific wage types during the in-period, leading to discrepancies and confusion in subsequent recalculations.

Summary of the Issue:
- Wage types such as /104, /105, and /115 are incorrectly added to wage type /102 in the in-period when retroactive changes occur.
- Similarly, wage types /131, /132, and /133 are incorrectly added to wage type /130.
- Upon recalculation, differences may be calculated differently, creating multiple difference wage types and potentially causing endless discrepancies in future recalculations.
- This inconsistency can cause misunderstandings and make tracking and accounting for payroll differences challenging.

Solution:
The SAP Note provides a solution that can be implemented via a Support Package or using an attachment provided in the note via SAPSERV.
- Corrections to schemas such as ARB0, ARSS, and ARIM must be made, matching them to the standard schema.
- New rules named ARDV, ARDT, ARDS, ARTV, ARTT, ARTS, ARR1, ARR2, ARR3, ARR5, ARR6, and ARR7 have been created and must be assigned to appropriate subschemas.
- New wage types /3R2, /3R3, /3R4, /3R5, /3R6, and /3R7 have been created to properly track and register the adjustment of wage types /102 and /130 due to differences from another wage type.
- The solution stipulates how adjustments should be made during retrocalculation and normal processing to ensure the accurate transfer and application of differences between wagetypes.

Additional References:
- For information on using the SAPCAR utility to extract files, see SAP Note 212876.
- For more details on sapservs, see SAP Note 13719.
- For guidance on attachments, see SAP Note 480031.

Users are advised to follow the detailed instructions provided within the note to implement the solution and rectify the payroll calculation errors regarding Social Security (SSO) and taxes (TAX).