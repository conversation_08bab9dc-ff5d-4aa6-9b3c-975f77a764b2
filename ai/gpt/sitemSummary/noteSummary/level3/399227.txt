SAP Note 399227 provides guidance for importing APO Support Package 11 (SAPKY30A11) for APO Release 3.0A. It includes corrections that were issued between March 6, 2001, and April 6, 2001, addressing various areas such as SAPGUI upgrade, kernel updates, OCX updates, COM routines, optimizer corrections, and others.

Key points from the note are:

1. **Upgrade Information for SAPGUI**: SAP recommends upgrading to Frontend Release 4.6D as maintenance for 4.6C ended on December 31, 2000.

2. **Transaction SPAM Procedure**: It is recommended to import multiple APO support packages together in one queue, starting from Support Package 9.

3. **Maintenance Strategy for SAPGUI**: Import the most current 46D kernel patch, and see the referenced notes for importing GUI patches and handling errors in field input processing.

4. **Prerequisites**: APO Support Package 11 requires that all previous support packages up to SAPKY30A10 have been imported for a complete installation/upgrade as of May 15, 2000. Additional prerequisites include Basis Support Package 16 for 4.6C and BW Support Package 13 for 2.0B.

5. **Installation Steps**: 

   - Update the SPAM manager to the latest version.
   - Download and import APO Support Package SAPKY30A11 from SAPNet.
   - Update the OCX files and ensure to read related notes for liveCache preparation and updates.
   - No new liveCache version is provided for SP11, but kernel patch >= 579 for Release 4.6D is required. 
   - Download and import the COM routine files from the sapservX server.
   - Import optimizer corrections from the sapservX server.

6. **Manual Implementation of Additional Notes**: Implement the additional notes manually, which include notes related to user authorizations, RFC dialog users, and Demand Planning in APO with BW patches.

The note concludes with the available languages for APO Support Package 11 and specifies that liveCache should not be initialized for this Support Package.

Please note that this summary is based on the provided content and does not include any additional or updated information that may exist after my knowledge cutoff date.