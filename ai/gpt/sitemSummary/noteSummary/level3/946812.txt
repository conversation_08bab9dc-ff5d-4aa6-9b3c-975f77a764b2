SAP Note 946812 addresses an issue with the input help functionality when creating a characteristic in SAP, particularly when using the "Customer implementation" option to read master data. The main points of the note are as follows:

Symptom:
- When setting up a characteristic that reads master data through the "Customer implementation," the system asks for the name of the "master data read class."
- The input help for this field incorrectly lists all existing classes, rather than just the relevant ones, due to a program error.

Other Terms:
- This note is relevant to the Characteristic maintenance transaction (RSD1) and classes that work with the IF_RSMD_RS_ACCESS interface.

Solution:
- The note provides a correction that filters the input help to show only the classes that implement the IF_RSMD_RS_ACCESS interface, which are the relevant ones for this purpose.
- Users should import Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or SAPKW70009) to apply this correction.
- The Support Package will be detailed in Note 0914303 titled "SAPBINews BI 7.0 Support Package 09," which should be consulted once released.

Additional Information:
- The support package containing the fix is released under Note 0914303, even though that note might still indicate it's a "preliminary version."
- If the issue is urgent, users are advised that correction instructions are available prior to the release of the Support Package.

Overall, Note 946812 provides a solution to a specific program error related to characteristic creation in SAP NetWeaver BI, by ensuring only relevant master data read classes are displayed in the input help.