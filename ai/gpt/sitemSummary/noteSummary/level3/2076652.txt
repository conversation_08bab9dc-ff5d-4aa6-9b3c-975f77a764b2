SAP Note 2076652 pertains to the adjustments required for customer-specific programs to support SAP Data Aging with regards to financial (FI) documents in SAP Simple Finance. The note is relevant for organizations that have custom developments in SAP ERP 6.0 and now want to utilize SAP Simple Finance's data aging capabilities.

Key points from the note:
- Custom programs must be adjusted to account for changes in the access to FI totals and archived data because of the introduction of data aging.
- Specific FI totals tables (GLT0, FAGLFLEXT, KNC1, KNC3, LFC1, LFC3) have been replaced by views that compute totals, including data in the "cold" database area which is not selected by default.
- Developers must implement controls to access this cold data when needed, which involves specifying the data's "temperature" based on the selection conditions.

The note describes a step-by-step process for making the necessary adjustments:
1. Identify affected code segments using the Code Inspector (transaction SCI).
2. Determine the data temperature for selection conditions using class CL_FINS_FI_AGING_UTILITY.
3. Set the data temperature using either the class CL_ABAP_SESSION_TEMPERATURE for the main program or CL_ABAP_STACK_TEMPERATURE for routines.

For archive access adjustments:
- Aging replaces archiving in SAP Simple Finance, but support for accessing archived files remains.
- Developers must adjust their code to access the cold data in the database where archives were previously accessed.
- Depending on the situation, you may adjust the database SELECT statements or archive access method to include cold data access.

The note provides guidance on using specific classes and methods to calculate and set the temperature for accessing cold data. It suggests adjustments to both the database accesses and the archive access methodologies to ensure that legacy custom code functions correctly with the new data aging strategy implemented in SAP Simple Finance.