SAP Note 2804474 serves as the Release Information Note (RIN) for SAP S/4HANA 1909, specifically addressing the integration and usage of Revenue Accounting and Reporting (RAR). It encompasses guidelines and references to additional SAP Notes that relate to the RAR functionality, which has been a standard part of SAP S/4HANA since the 1809 release. The note targets users who plan to use RAR with S/4HANA 1909, offering details on various subjects such as important changes introduced in previous versions, technical system landscapes, and activation procedures.

Some key points covered in this SAP Note include:

1. The seamless integration of RAR, previously an add-on, into SAP S/4HANA 1809 and later versions.
2. Support package notes from RAR 1.3 Support Package 09 are available in SAP S/4HANA 1909.
3. References to other important related SAP Notes, including a comprehensive list with SAP component-wise categorization of notes that should be read prior to implementing SAP RAR in S/4HANA 1909.
4. Details on the changes made to contract and performance obligation IDs data types from NUMC to CHAR to optimize performance.
5. Upgrade guidance, indicating a direct upgrade from RAR 1.2 is unsupported, while upgrades from RAR 1.3 follow a standard process without specific RAR-related procedures.
6. Instructions on regenerating RAI classes post-upgrade if new fields were added.
7. An introduction to the Optimized Contract Management, starting with S/4HANA 1909, functioning alongside Classic Contract Management.
8. A high-level comparison table between the classic and optimized contract management features and system behavior changes, along with functionalities that are currently unsupported in the optimized version.
9. Recommendations to accelerate Silent Data Migration by implementing SAP Note 2842212 before upgrading to SAP S/4HANA 2020.

Additionally, SAP Note 2804474 provides insight into the transition from ECC with SD Revenue Recognition to S/4HANA, highlighting the process of migrating open SD Revenue Recognition processes to SAP RAR 1.3 in ECC before upgrading to S/4HANA. It emphasizes that a direct migration from SD Revenue Recognition to SAP RAR is only possible in ECC source systems due to the dependency on specific ECC programs for data consistency and migration.

For users of the new Optimized Contract Management in SAP S/4HANA 1909, the note lists a set of new FIORI apps designed to work exclusively with this management system. It specifies how to activate and maintain OData services for FIORI apps and outlines general setups for financial applications.

Finally, it concludes by providing links to application help documentation for both RAR features in S/4HANA 1909 and optimizes contract management.

The note emphasizes the importance of regular checks for updates and changes to ensure accurate implementation and transition to RAR features within S/4HANA 1909.