SAP Note 1097758 addresses an issue where users encounter an error when trying to maintain infotype texts via a batch input session that is started through an ITS-service. The error message displayed is "No batch input data for screen SAPLHRPAD00TEXTEDITOR 0100". This problem occurs because the system inappropriately calls the webgui text editor, which is not designed to work in batch input mode.

The note identifies this behavior as a program error and provides a solution. To resolve the issue, users need to ensure that the 'old' text editor is called during batch input mode, as the 'old' text editor is compatible with batch inputs, unlike the webgui editor. 

The recommended action is to implement the corresponding HR support package to fix the issue. For those who need an immediate solution, SAP has provided correction instructions attached to the note which can be implemented in advance of the support package. 

Other terms related to this issue that might be searched for or referenced include FP50ME02, EDITOR-CALL, F9, ITXEX, and HR_CALL_TEXTEDITOR.