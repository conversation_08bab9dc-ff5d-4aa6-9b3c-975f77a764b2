SAP Note 923190 describes an issue where an incorrect "CREATE TABLE" statement is generated when creating a remote table in StorHouse. This issue specifically affects preview customers of the nearline storage prototype for StorHouse.

The error triggers if a characteristic of the InfoProvider, which should be a string of numbers (NUMC data type), has more than 14 numbers. The cause of this error is that the column definition in the "CREATE TABLE" statement is cut off after 72 characters due to an excessively long default value, which is based on the data type from BW.

To resolve the issue, customers are advised to implement an advance correction or import the provided transport QB8K903617 from the ZIP archive into their BW system. For more detailed information, they are referred to SAP Note 13719.