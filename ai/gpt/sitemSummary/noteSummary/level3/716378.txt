SAP Note 716378 addresses an issue encountered during the Unicode upgrade when certain libraries are missing, causing an error when starting the `PREPARE` command from the upgrade master CD/DVD.

Summary of the SAP Note:

Symptom:
- Error messages regarding missing ICU (International Components for Unicode) libraries or unresolved symbols (like "u16_set_trace") when initiating the `PREPARE` command as part of an upgrade process.

Reason:
- During a Unicode upgrade, specific Unicode-related libraries are required by the upgrade program.
- For systems up to Release 6.40, if the kernel is at version 6.20, it contains an older version of the ICU libraries (Version 20) which can trigger the error because the R3up (upgrade tool) looks for Version 26.
- For the second error message, this is due to the need for a newer version of "libsapu16.so" than what is present in the system.

Solution:
- Replace the "libsapu16.so" in the kernel directory with the latest version from the SAP Service Marketplace if you encounter unresolved symbols or related error messages.
- If ICU library versions are incorrect (relevant for releases up to 6.40), the required ICU libraries can be found in the SAPEXE.SAR archive on the kernel CD/DVD.
  - Instructions are provided for unpacking these libraries to a temporary directory and then copying them into the kernel directory without overwriting existing files. Note that library naming conventions can vary by operating system, and one must adjust commands accordingly.
- Users should then execute the `PREPARE` command again. The newer libraries will not be used by the older 6.20 kernel, so they won't affect the current system's kernel.
- On UNIX and Windows, it might be required to shut down the SAP system before replacing "libsapu16.so" if the operating system doesn't allow replacing libraries while in use.
- On Windows 32-bit systems specifically, if an R3up.exe patch is imported as per Note 663258, users should manually delete certain DLL files from the %WINDOWS% directory after the upgrade, as the `PREPARE` command copies them there.

Recommendation:
- Follow the stated procedures for replacing the appropriate libraries to resolve the error and continue with the Unicode upgrade process.