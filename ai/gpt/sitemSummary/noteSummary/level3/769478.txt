SAP Note 769478 outlines the procedures for establishing a remote connection to an XI (Exchange Infrastructure) customer system for troubleshooting purposes by SAP Support. The symptoms indicating the need for this support include scenarios where direct aid from SAP is required.

The note lists different methods and associated SAP Notes for setting up the remote connection:

1. ABAP connection for XI runtime:
   - R/3 Support

2. For XI tools error analysis (repository and directory), the following connection options are provided:
   - Windows terminal server connection (Note 605795)
   - T.120 NetMeeting connection (Note 356635)
   - pcANYWHERE connection (Note 100740)
   - Citrix MetaFrame (Note 701588)
   - Direct connections to the tools as of XI 3.0 (Note 800267)

3. If XI tools are not needed, an HTTP connection may be sufficient (Note 592085).

4. For UNIX servers, a Telnet connection may be considered (Note 37001).

Additionally, the note references storing logon data in the message's secure area (Note 508140) and advises reading the composite SAP note (Note 35010) for a comprehensive overview of service connections. The note also suggests visiting the web page http://service.sap.com/access-support for further information.

This SAP Note provides several alternatives and references to detailed support documents to assist in setting up the appropriate remote connection for SAP Support to diagnose and troubleshoot issues in XI customer systems.