SAP Note 1751367 addresses an issue where the total amount of receivables or payables is not fully reassigned, despite the relevant accounts not being cleared at the time of the account assignment change. This issue pertains to profit center and segment reorganization processes.

Key points from this note:

1. **Symptom**: Users notice that after reassigning receivables and payables, the system fails to reassign the entire amount for certain accounts, even though these accounts were open (not cleared).

2. **Other Terms**: The issue relates to reorganization ("Reorg") processes, specifically profit center reorganization and segment reorganization.

3. **Reason**: The note identifies the cause of the problem as being related to a program error within the system.

4. **Solution**: The note advises that if a reorganization plan is currently open, users should first reassign all balances for receivables and payables. Following this, they should implement the correction instructions provided by the note to prevent the error from happening in the future. To address the amounts that have already been incompletely reassigned, the note recommends contacting SAP Support for assistance.

In summary, the note provides guidance on how to handle incomplete reassignment of accounts in the context of profit center and segment reorganizations due to a program error and includes preventative measures as well as a directive to seek further support for correction.