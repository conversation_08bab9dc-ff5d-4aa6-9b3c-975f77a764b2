SAP Note 2441895 addresses an issue where, when using the Data Transfer Process (DTP) to load data from a Hierarchy DataSource in SAP BW, more languages than expected are loaded without the standard language selection filter being applied.

The note identifies this behavior as a program error and provides a solution in the form of support package updates for different SAP BW versions.

Summarized solutions per SAP BW version:
- For SAP BW 7.40, implement Support Package 18 (SAPKW74018). Details of this package will be described in SAP Note 2445563 titled "SAPBWNews 7.40 BW ABAP SP18".
- For SAP BW 7.50, implement Support Package 8 (SAPK-75008INSAPBW), with details in SAP Note 2419099, "SAPBWNews 7.50 BW ABAP SP8".
- For SAP BW 7.51, implement Support Package 3 (SAPK-75103INSAPBW), with details in SAP Note 2424895, "SAPBWNews 7.51 BW ABAP SP3".
- For SAP BW/4HANA 1.0, implement Support Package 4 (SAPK-10004INDW4CORE), with details in SAP Note 2429588, "SAPBWNews SAP BW/4HANA 1.0 SP04".

In urgent cases, there are correction instructions available. However, before proceeding with the corrections, users should check SAP Notes 1668882 and 2248091 related to the transaction SNOTE.

Additionally, it’s mentioned that after applying the correction, the DTP must be activated once.

The note also mentions that it might be available before the support packages are released, indicated by the term "preliminary version" in the short text.