SAP Note 3007220 addresses the known restrictions for the on-premise release of the next generation Just-In-Time Supply to Production (JIT S2P) for SAP S/4HANA 2020. The note summarizes the issues and provides solutions for the restrictions identified.

Summary:
- The note discusses the limitations related to action control and BOM usage in the JIT S2P component.
- For action control, the standard JIT S2P action control (S2P_ACTRL) is not automatically available across all clients after deployment. It requires manual customizing from client 000 to other testing clients. The note suggests copying customizing nodes such as "Define External Status and Internal Processing Status (JIT)", "Define JIT Actions", and "Define JIT Action Control" from the SPRO path given towards Logistics Execution and Just-In-Time Processing (Next Generation).
- For BOM usage, the BOM usage type "J" specific to JIT S2P is also not present in all clients by default. This too requires manual customizing from client 000 to testing clients. The customizing node "Define BOM Usages" must be copied to the respective testing clients as provided in the SPRO path under Logistics Execution and JIT Supply to Production.

The note emphasizes that these changes are not automatically propagated across clients and need manual intervention to ensure that the relevant settings for JIT S2P are correctly set up across different clients in the SAP system. It also warns that the contents of the note are subject to change.