SAP Note 131702 addresses an issue specific to users who have upgraded to SAP BW Release 1.2B. The note provides a solution for a problem where not all fields of the SD transfer structures S260 to S263 are being updated after the upgrade.

Summary of the SAP Note:

- **Symptom:** Post-upgrade to BW Release 1.2B, certain fields within transfer structures S260, S261, S262, and S263 are not updated. This note is relevant only for users of BW Release 1.2B and should be applied after the BW Add-Ons have been imported.
  
- **Other Terms Referenced:** The note mentions specific extractors (RMCSS260, RMCSS261, RMC<PERSON>262, RMCSS263) and related field counters (ANZAU, ANZAUPOS, ANZLI, ANZLIPOS, ANZFK, ANZFKPOS).

- **Reason and Prerequisites:** The main reason for the issue is the extension of the SD transfer structures and their extractors in BW 1.2B. While the structural extensions were included with the BW Add-On import, the extractor extensions were omitted.

- **Solution:** Implement the correction instructions provided in the note from SAPSERV3. This involves downloading certain objects listed in the note from the directory `/general/R3server/abap/note.0131702` on SAPSERV3 using the respective transport request XRSK002460. The objects to be imported are <PERSON>MC<PERSON><PERSON>001, RMCDW002, RMCDW003, RMCDW004, RMCSS260, RMCSS261, RMCSS262, and RMCSS263.

After importing the objects, it's recommended to access them using Transaction SE38 so they attain 'modified' status. This prevents them from being overwritten during a release upgrade, ensuring any subsequent system update will highlight them in the user's comparison list.

The note also advises users to ensure that any changes made according to SAP Note 301410 should not be lost when implementing this note's recommendations.