SAP Note 946992 addresses an issue where a transaction load into an InfoCube sporadically fails with the error "Master data/text of characteristic XXX already deleted (RSDMD 138)". The characteristic XXX is a part of the InfoCube being loaded.

The error occurs because during the load process, the system checks for an entry in the table RSMASD for each characteristic and datapackage. If an entry exists, the system attempts to acquire an exclusive lock on the characteristic. If a master data deletion for a characteristic was previously aborted, the entry may remain in the RSMASD table. Subsequent transaction loads will find this entry and attempt to get an exclusive lock, which can fail if another user or process holds the lock, resulting in the RSDMD 138 error.

The solution provided in the note is twofold:
1. Manually remove the entry from the RSMASD table after ensuring no master data deletion processes for the same characteristic are currently running. A custom report can be written to delete the entry for the specific characteristic from the RSMASD table.
2. Apply code corrections through a Support Package that introduces a waiting period when requesting an exclusive lock. If the lock is not available, the system will attempt to obtain it multiple times before the loading of the datapackage fails with the RSDMD 138 error.

The note specifies the required Support Packages for different SAP BW releases that contain the fix:

- For BW3.0B, import Support Package 32 (SAPKW30B32), detailed in note 0914949.
- For BW3.1C, import Support Package 26 (SAPKW31026), detailed in note 0935962.
- For BW3.50, import Support Package 18 (SAPKW35018), detailed in note 0928661.
- For BW7.0, import Support Package 09 (SAPKW70009), detailed in note 0914303.

The note also mentions that it might be available before the mentioned Support Packages are released, in which case the short text will include "preliminary version".