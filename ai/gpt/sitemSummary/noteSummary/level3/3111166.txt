This SAP note 3111166 addresses an issue encountered in the SAP S/4HANA Migration Cockpit when using the "FI - Historical balance" (SIF_HIST_BALANCE) migration object during parallel data transfer jobs, resulting in a lock error (Message no. MC601). 

The symptom is that when using either transaction LTMC or the Fiori app "Migrate Your Data" for data migration to SAP S/4HANA, and utilizing the "FI - Historical balance" object with more than one data transfer job, the error message "Object requested is currently locked by user <username>" appears.

The cause of this issue is that the API used for this migration object is not designed to handle more than one data transfer job, leading to lock issues with journal entry number assignment.

The resolution involves setting the maximum number of data transfer jobs for the "FI - Historical balance" object to 1. This will avoid the locking issues during migration. The note provides specific instructions on how to adjust this setting for different SAP S/4HANA versions using both LTMC and the "Migrate Your Data" Fiori app. 

Additionally, SAP notes that with SAP S/4HANA 2022 and SAP S/4HANA 2208 Cloud, a new Historical Balance object (SIF_FI_HIST_BAL2) is delivered. This new object has a different sender structure that should prevent the error from occurring.

The note also lists relevant keywords and the affected SAP S/4HANA environment.