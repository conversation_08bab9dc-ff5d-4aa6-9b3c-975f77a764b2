SAP Note 2270387 outlines the modifications to the data structure of Asset Accounting when migrating from the classic version to the new version in SAP S/4HANA. The key points summarized from this note are as follows:

1. New Asset Accounting requires the use of new General Ledger Accounting in SAP S/4HANA. If classic General Ledger Accounting was in use prior to conversion, it is automatically activated with the conversion to SAP S/4HANA.

2. There are changes to the storage of actual and planned asset data. Actual data previously stored in ANEP, ANEA, ANLP, ANLC, and values from ANEK are now saved in table ACDOCA and associated with BKPF and BSEG tables for new Asset Accounting. Statistical and planned data, previously in ANLP and ANLC, are relocated to FAAT_DOC_IT and FAAT_PLAN_VALUES, respectively.

3. The traditional Asset Accounting tables (ANEA, ANEP, ANEK, ANLC, ANLP) are no longer directly updated in SAP S/4HANA. Instead, compatibility views are provided to ensure existing reports and custom developments can still access required data from the new structure. These views serve as an interface to replicate the data format of the original tables and are automatically used when selections from the outdated tables are made.

4. The note provides a detailed mapping of the compatibility views and the original table access. For each table like ANEA, ANEP, ANEK, ANLC, ANLP, etc., there is a corresponding DDL Source and DDIC View for the compatibility view, as well as another set for access to the original table content.

5. Furthermore, selections from certain existing DDIC views (e.g., V_ANEPK, ANEKPV, V_ANLSUM_1) are diverted to corresponding new compatibility views to ensure continuity of old report and development functionalities.

6. Specific limitations when using selections from ANLP are mentioned, with a reference to SAP Note 2297425 for more details.

This note is essential for businesses during the process of migrating to SAP S/4HANA's new Asset Accounting and should be consulted to understand the implications for existing asset reports and custom developments.