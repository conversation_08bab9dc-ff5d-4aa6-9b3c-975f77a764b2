SAP Note 1479743 addresses an issue where users encounter error message FI 026 while attempting to open or close periods in transaction OB52. The error message states: "No controlling area has been assigned to company code &."

The issue occurs when the changed variant includes a company code that has not been assigned a controlling area. This situation may arise following a reorganization where the periodic opening and closing of accounts for affected company codes have changed frequency.

The system performs a check to ascertain if the opening or closing of periods is permissible for all the company codes within the variant. This requires deriving the controlling area for the company codes. If a company code in the variant lacks a linked controlling area, the system produces the stated error message.

The solution provided in the note indicates that company codes without an assigned controlling area should not be impacted by the reorganization and thus the system should not check the opening or closing periods for such codes.

To resolve this error, the note includes correction instructions that users should implement. The correction will adjust the system's behavior to ensure that it no longer performs the unnecessary check for company codes without controlling areas when opening or closing periods.