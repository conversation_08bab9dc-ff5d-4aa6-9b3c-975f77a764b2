SAP Note 179482 addresses an issue that arises after upgrading from SAP Release 3.0/3.1 to Release 4.0/4.5, specifically related to volume-based rebate agreements. The symptom outlined is that an error may occur during currency translation when changing or displaying rebate agreements, resulting in inconsistencies between the currencies/values in 'agreement - condition - document'.

The note identifies problems in the SDBONTO2 report, as well as potential issues with the sequence of processing the required conversion reports, as possible reasons for this error. These conversion reports include SDBONTO1, SDBONTO2, and SDS060RB.

To resolve this issue, the note offers a solution that involves using a report called ZZFIXIT2, which can identify incorrect documents and reset their values, allowing for data enhancement to be carried out again with the correct sequence of reports. It is suggested to run ZZFIXIT2 in TESTMODE initially to assess the affected data. 

The necessary steps for the conversion to be repeated are:

1. Run SDBONTO1 to enhance KONP data.
2. Run SDBONTO2 to update KONV data.
3. Run SDS060RB to organize the statistics S060, ensuring that the INITS060 parameter is set.

The note also includes a caution that if any billing documents have already been created in the new Release 4.x before executing report <PERSON>BONTO1, report <PERSON><PERSON><PERSON><PERSON><PERSON>2 must be called up and then the aforementioned steps (1-3) should be performed to correct the rebate processing.

Additional keywords for this issue include “Rebate processing” and “VK300”.