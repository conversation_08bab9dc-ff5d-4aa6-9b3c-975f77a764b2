SAP Note 2940842 addresses an issue encountered during the "Restart Workflow Processes" step of the R_EHFND_WFF_RESTART_PROCESSES report. The problem arises due to incomplete data within existing workflow processes, leading these processes to be unnecessarily selected. This can cause the step to take an extended period of time, potentially resulting in short dumps such as "Out of memory" or "SQL statement selection condition."

The cause of the issue is identified as a program error, and users are advised to apply the correction instructions provided in the note to fix the problem. This solution is intended to prevent the "Restart Workflow Processes" step from ending in a short dump.

For users who prefer to apply a complete package, the note also references the relevant Support Packages containing the official correction. To ensure the fix is effective, it is necessary to follow the correction instructions that maintain the validity of the correction within those packages.