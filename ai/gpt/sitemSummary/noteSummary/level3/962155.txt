SAP Note 962155 addresses an issue that occurs when creating a transformation in SAP NetWeaver 2004s Business Intelligence (BI). The symptom of the problem is that the field for the source unit remains empty even though the unit exists as a source field in the rule definition.

The underlying cause of this issue is identified as a program error, more specifically, an error in the mapping process of the source unit within the transformation.

To resolve the issue, SAP provides a solution that involves importing Support Package 09 for SAP NetWeaver 2004s BI, designated by the label SAPKW700009. Customers need to wait for the release of the support package, which is further detailed in SAP Note 0914303 titled "SAPBINews BI 7.0 Support Package 09.”

In cases where a resolution is urgent and cannot wait for the support package's release, correction instructions are provided. The related SAP Notes, including the one describing the support package, may be available to customers before the official release of the support packages. If encountered pre-release, the notes may still contain the phrase "Preliminary version" in their short text.