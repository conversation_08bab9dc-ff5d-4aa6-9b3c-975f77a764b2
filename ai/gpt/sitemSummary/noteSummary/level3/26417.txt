SAP Note 26417 provides information on the hardware and software requirements for SAP GUI (Graphical User Interface) for Windows. It lists the system specifications needed to run SAP GUI effectively, depending on the themes used. The note also includes important related SAP Notes that should be referred to for additional guidance on supported platforms and maintenance.

Key points from the note are:

- SAP GUI is compatible with Microsoft networks and requires a Microsoft-compatible network for installation.
- Monitor size and screen resolution are guided by the aim to avoid scrollbars, assuming the use of default font sizes and DPI scaling.
- Different SAP GUI themes require different levels of color depth and resolution. Themes like "Classic" can work with lower requirements, while "Belize" and "Quartz" demand higher specs.
- As newer technologies and themes (Quartz, SAP Signature, Corbu, Blue Crystal, and Belize) are introduced, additional resources, such as memory, may be needed due to improved rendering technologies.
- Central processing unit (CPU) and memory recommendations vary by the theme used and whether SAP GUI is run in conjunction with other applications.
- A new 64-bit version of SAP GUI is available from release 8.00, which comes with modest increases in memory and disk space usage but can improve CPU utilization and performance.
- Hard disk requirements for SAP GUI range from a minimum of 110 MB to potentially 550 MB, excluding additional components like SAP Business Client.
- Up to and including release 7.60, SAP GUI required Microsoft Internet Explorer, but from release 7.70, an Edge (using Chromium) based control can be used instead.
- SAP GUI may have dependencies on Microsoft Office for certain components like the SAP Desktop Office Integration.
- External programs and documents on the client PC can be executed by SAP GUI through ABAP methods, but issues with these programs should be addressed with the program vendor.

Lastly, the note recommends that the minimum requirements listed may not be sufficient for productive usage and encourages thorough evaluation of requirements especially in conjunction with other applications or in terminal server environments. It also suggests checking with operating system and office product vendors for their requirements and recommendations.