SAP Note 755977 provides documentation and guidance for using transaction ST12, also known as "Single transaction analysis" for SAP EarlyWatch/GoingLive. It describes how to utilize the ST12 ABAP Trace for performance analysis during SAP Service Sessions, such as SAP GoingLive Check or Solution Management Optimization Services.

The note explains that ST12 integrates ABAP and performance traces (including SQL, Enqueue, and RFC traces from ST05) with improved functionality and convenience for tracing and analyzing performance hotspots in ABAP/CPU bound issues, as well as DB bound issues. It highlights that ST12 is similar to a combination of the standard ABAP and SQL trace transactions SE30 and ST05, with some enhancements.

ST12 enables traces for another user, across all servers and retains the results in a centralized database manner for ease of analysis and sharing. It offers three levels of aggregation (calling position, full, and per modularization unit) and provides deep insights into gross and net times of ABAP statements and modularization unit calls.

This note also details the availability of ST12 as of basis release 4.6B and requires a certain version of the addon ST-A/PI for full functionality. It provides a future outlook, including plans to include SQL/performance trace handling, statistical records, and the SQLR transaction functionality into ST12.

The note educates beginners on the basics of ABAP trace, compares ST12 with SE30, and guides on different tracing scenarios, trace collection, administration, and evaluation, including context trace across RFCs. It suggests that ABAP trace with ST12 should be used for performance hotspot analysis, modification and user exit identification, call hierarchy issues, and for searching localized technical optimization potential.

Finally, the note addresses troubleshooting related to ST12 and references additional resources such as blogs and other SAP notes for a comprehensive understanding of ST12 transaction analysis.