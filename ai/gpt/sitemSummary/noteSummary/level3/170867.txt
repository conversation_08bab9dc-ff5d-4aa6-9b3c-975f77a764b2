SAP Note 170867 addresses issues related to batch input sessions for view cluster maintenance dialogs that were created in versions of SAP before Release 4.6 and are no longer executable after an upgrade to Release 4.6 or higher.

**Symptom:**
- Batch input sessions made before Release 4.6 for view cluster maintenance dialogs do not work in Release 4.6 due to the introduction of TreeControl for navigation.
- The TreeControl used for navigation is not available in the recorded batch input session.

**Other Terms:**
- The note mentions terms such as SM34 (transaction for view cluster maintenance), complex table maintenance, background input, and screens.

**Reason:**
- Prior to Release 4.6, navigation in view cluster maintenance dialogs was performed using a subscreen included in the maintenance screens. In Release 4.6 and later, this navigation was replaced with a TreeControl.
- Because controls are generally not compatible with batch input, the old function codes for navigation are no longer compatible with the new release.

**Solution:**
There are two scenarios:

1. If the subscreen used for navigation before Release 4.6 is still present on the maintenance screens, batch input sessions created prior to Release 4.6 will work without modification. Navigation in the batch input recording is done through the subscreen, not the TreeControl.

2. If the subscreen for navigation was removed from the maintenance screens after the upgrade, batch input sessions from before Release 4.6 will need adjustment. Navigation in batch input recording should be performed using the menu by following 'Table view' --> 'Other view' and selecting the necessary navigation level in the 'Structure overview' dialog box.

Users can verify whether the subscreen for navigation is still available by initiating the transaction in recording mode for batch input. If the navigation area is visible in the usual place (upper third of the screen) from before Release 4.6, then the subscreen is still included on the maintenance screen, and the pre-4.6 batch input sessions should still be operational under Release 4.6.