SAP Note 1691147 addresses an issue encountered during integration with meter data unification and synchronization (MDUS) systems in which the allocation between the device and its location is missing, resulting in difficulties in mapping the MDUS data. This information is also not transferred for device information records, which is essential for service-oriented architectures (SOA), particularly in environments using SAP's Process Integration (PI) or Exchange Infrastructure (XI).

The root cause identified for this issue is that the necessary fields for conveying this information were not initially included in the system interfaces.

To resolve the problem, the solution provided in this note involves adding a new node called 'LogicalLocation' to the business object 'UtilitiesDevice'. The node allows for multiple entries (having cardinality 0..n) and contains three fields:
1. StartDate
2. EndDate
3. LogicalInstallationPointID, which correlates to the logical device number in the backend system.

This improved information is available in the following services:
- UtilitiesDeviceERPSmartMeterLocationNotification_Out (from Enhancement Package 5 onwards)
- UtilitiesDeviceERPSmartMeterLocationBulkNotification_Out (from Enhancement Package 6 onwards)

These services can be used for creating, removing, and changing device information records by implementing the methods provided by the Business Add-In ISU_AMI_DEV_INFO_RECORD (example implementation class CL_EX_ISU_AMI_DEV_INFO_RECORD).

Additionally, the service for replicating device data, UtilitiesDeviceERPSmartMeterReplicationBulkRequest_Out, has been enhanced starting from Enhancement Package 5.

It should be noted that enhancing the required elements as per this SAP Note is optional and the enhancements are included with the specified Support Package.