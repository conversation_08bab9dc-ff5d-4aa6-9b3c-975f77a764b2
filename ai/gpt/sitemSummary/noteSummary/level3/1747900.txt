The SAP Note 1747900 addresses a specific issue where the runtime error ASSERTION_FAILED is encountered when generating or reassigning object lists within a reorganization plan. This error occurs specifically in the method GET_PLAN_RESTRICTIONS_P of the class CL_FAGL_R_OBJLIST.

Key aspects of this note include:

- **Symptom**: Users experience a runtime error while working with reorganization plans.
- **Other Terms**: Related key phrases include GET_OBJECT_INFO, parameter passing, parameter transfer, and reference, which are terms associated with the technical aspects of the error.
- **Reason and Prerequisites**: The note indicates that the root cause of the issue is a program error.
- **Solution**: The note advises implementing corrections to resolve the problem. However, the details of the corrections are not provided in the given summary.

Please note that SAP Notes like this one typically provide more specific guidance, such as a step-by-step guide to applying the corrective actions or links to additional resources or support packages. Since this information is not included here, users should look up the full note for detailed instructions on addressing the error.