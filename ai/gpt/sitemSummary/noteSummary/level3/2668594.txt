SAP Note 2668594 addresses an issue in SAP S/4HANA 1809 FPS00 where the 'Deficit Cash Pool' app is not displaying pools that have been created with the Fiori app 'Manage Cash Pools.'

The note states that there is a limitation in the initial Feature Pack Stack release of SAP S/4HANA 1809, where the 'Deficit Cash Pool' app does not yet support the new cash pool design. Consequently, users who have upgraded from SAP S/4HANA 1709 or 1610 might notice that while pools defined using the 'Manage Bank Accounts - Bank Hierarchy View' app in their earlier system versions are displayed, the pools created with the newer 'Manage Cash Pools' app are not.

The solution provided is to apply SAP Note 2707215, which likely contains corrections or workarounds to address the issue. Alternatively, the note recommends upgrading the system to SAP S/4HANA 1809 FPS01, the subsequent feature pack stack or support package level, which should include a fix for this problem.