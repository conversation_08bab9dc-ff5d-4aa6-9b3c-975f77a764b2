SAP Note 955668 addresses a performance issue encountered when executing an MDX command with the WITH MEMBER clause in the context of a query with a large number of empty cells. The note identifies that the execution of such queries can take an excessive amount of time to complete, which is a problem for performance optimization.

The solution provided in the note is to import specific support packages for different SAP BW (Business Warehouse) versions which contain the necessary performance improvements. The required support packages are as follows:

- For SAP BW 3.0B, the note instructs to import Support Package 32 (BW 3.0B Patch 32 or SAPKW30B32). Further details are given in Note 914949 which describes Support Package 32 in more detail.
  
- For SAP BW 3.10 Content, Support Package 26 (BW 3.10 Patch 26 or SAPKW31026) needs to be imported, with additional information available in Note 935962.

- For SAP BW 3.50, Support Package 18 (BW 3.50 Patch 18 or SAPKW35018) should be imported, with Note 928661 providing more details.

- For SAP BW 7.0, the note suggests importing Support Package 09 (BW 7.0 Patch 09 or SAPKW70009), with additional information in Note 0914303.

In urgent cases where immediate remedy is necessary, the note also provides detailed manual correction instructions which involve inserting an EMPTY TYPE RS_BOOL attribute into the PRIVATE SECTION of the CL_RSR_MDX_OLAP_REQUEST class manually for BW releases 3.0B, 3.10, and 3.50.

The notes mentioned above (914949, 935962, 928661, 0914303) may be available before the full release of the Support Packages. In such cases, the short text of the notes will indicate "Preliminary version" to reflect that they are made available in advance to provide information to customers before the official release of the patches.