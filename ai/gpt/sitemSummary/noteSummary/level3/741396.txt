SAP Note 741396 addresses an issue where the posting of a return delivery for a stock transport order using movement type 122 results in incorrect quantity updates in Business Warehouse (BW). The core of the problem is an incorrect determination of the debit/credit indicator, which leads to duplicate quantity entries, with both plus and minus indicators.

Key terms related to this note include:
- SHKZG (Debit/Credit Indicator)
- ZU_ABGANG (Goods Receipt/Goods Issue Indicator)
- LMCRSF02, XMCMSEG-BWSWITCH (Program and fields related to update)
- 2LIS_03_BF (BW Data Source)
- FUELL<PERSON>_ZUABGANG_MSEG, BWART 122/123, OLI1BW, T156-XSTBW (Related functions and movement types)
- XSTOR, ROCANCEL, MCEX_UPDATE_03 (Correction-related terms)

The prerequisite for this error is specifically during the return or reversal of a return (with movement types 122 or 123 respectively).

The solution provided by this note is to implement the necessary program corrections to rectify the issue. The note likely contains the specific technical details of these corrections, which must be applied by the SAP support team or the responsible technical personnel familiar with ABAP programming and SAP system updates.

Users experiencing this problem should follow the instructions provided in the note to apply the program corrections and resolve the error in quantity updating during stock transport order returns.