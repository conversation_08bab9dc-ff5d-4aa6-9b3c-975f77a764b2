SAP Note 1639580 describes the process of installing the add-on LOCINUTL 606 on SAP ERP 6.0 for enhancement packages 6, 7, or 8, along with the prerequisites and installation procedure for NetWeaver 7.31.

Key points of the note:
- Transaction SAINT is used for add-on installation or delta upgrade.
- LOCINUTL 606 is an add-on that once installed cannot be uninstalled.
- It's essential to check for the latest version of the note before installation.
- Relevant release requirements include SAP ERP 6.0 with EHP 6, 7, or 8.
- Minimum SPAM version 25 is required before installation.
- Installation steps on SAP Service Marketplace are outlined for obtaining and loading the necessary packages into the system.
- Specific support package requirements must be met for SAP_BASIS and IS-UT components.
- The LOCINUTL 606 add-on does not contain modifications and can be installed regardless of any additional support packages previously imported.
- Industry Solution IS-UT is required, and the relevant Business Function Set must be activated.
- Instructions for preparing the installation include making the add-on package available and properly loading it into the system.
- For the installation, transaction SAINT is used, and the note provides a password required during the installation process.
- There are no known errors at the time of the note's issue.
- Language support includes English, and no additional language packages are required.
- Finally, the note mentions that LOCINUTL 606 has been integrated into the core as of SAP S/4HANA 1610 (IS-UT 801).

The note also includes the history of changes, including release dates on EHP7 and EHP8, and specifies that certain SAP Notes should be obtained before installation, such as those for add-on conditions and problems with transaction SAINT. Additionally, it provides a guideline on required components and support packages, and additional information on installation, such as the CD material number for add-on installation (51041942).

It's crucial for users to follow the outlined prerequisites and installation process carefully to ensure successful implementation of the LOCINUTL 606 add-on.