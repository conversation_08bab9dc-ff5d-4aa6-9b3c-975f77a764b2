SAP Note 2972163 addresses an issue within the Foundation for EHS (Environmental Health and Safety) Management component, specifically in the Regulatory List module. The symptom described in the note is that the EDIT button does not work correctly when editing a revision of a regulatory list. When users select substances from the list of contained substances, the EDIT function is not being enabled or disabled as appropriate for the selection. Moreover, when multiple substances are selected, the EDIT function only opens the first one, regardless of the number chosen.

The issue is identified as a program error, and SAP recommends referring to the correction instructions for details on the validity of the corrections.

The solution provided involves implementing corrections that are included in specific Support Packages, whose details are listed in the 'Support Packages' section of the note. As an alternative, users can manually implement the correction instructions attached to the note. After the solution is applied, the EDIT function is supposed to be active exclusively when a single entry from the list of contained substances is selected.