SAP Note 853535 addresses an issue where, upon logging into an SAP System using an Asian language such as Japanese, users may encounter incorrectly displayed icons. Instead of the intended icon, users see the icon definition text, which might appear as "@6C\Q@" or just "@". Additionally, the icon definition and the associated quick info text could display vertically or spread across multiple rows.

Some of the key terms mentioned in the note include Icon, SAP GUI for the Java environment (JavaGui), Internet Transaction Server (ITS), SAP Integrated ITS, and terms related to SAP modules such as PR05 (Travel Management: Travel expenses) and CATW (Cross-Application Time Sheet in Web Dynpro). ALV (ABAP List Viewer) is also mentioned, which is a common component where icons are widely used.

The root cause of the issue as identified in the note is a program error that occurs when the SAP system is parsing the icon definition.

The note describes the solution to the problem which requires updating specific components to the following versions or higher:
- SAP GUI for Java to version 6.40 revision 2
- SAP ITS to 6.20 patch level 19
- SAP kernel 6.40 to patch number 80 and SAP kernel 7.00 to patch number 18 for ITS Plugin (Integrated ITS).

Applying these patches or updating to these versions of the SAP components is expected to correct the error and properly display the icons in Asian language sessions.