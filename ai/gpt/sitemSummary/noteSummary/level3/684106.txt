SAP Note 684106 addresses an issue where 32-bit programs starting from SAP Release 6.40 or 64-bit programs from SAP NetWeaver 7.1 and higher cannot be started because certain Microsoft runtime DLLs are missing. This problem is relevant to users who encounter errors due to the absence of DLLs such as msvcr71.dll, msvcp71.dll, mfc71.dll, mfc71u.dll, msvcr80.dll, and dbghelp.dll.

Reason for the Issue:
The problem stems from the use of the Visual Studio .NET 2003 compiler for Windows x86 (32-bit) in SAP NetWeaver '04 (6.40) and SAP NetWeaver 2004s (7.0), and the Visual Studio 2005 compiler in subsequent releases for all Windows platforms. These runtime DLLs are typically installed with the SAP installation tool SAPinst, but they may be missing if only individual programs are used from versions 6.40 or higher.

Solution:
To resolve the issue, the note provides a few steps:

1. For SAP Releases 6.40 and 7.0, an archive called R3DLLINST.ZIP is supplied containing C-runtime 7.1 DLLs. Users must unpack and execute "R3DLLINS.EXE" in the subdirectory NTPATCH.

2. For SAP Releases 4.6D EX2, Web AS 6.40 EX2, 7.01, and 7.10 and higher, users should download the Microsoft Visual C++ 2005 SP1 Redistributable Package (vcredist_<platform>.exe for C-runtime 8.0) and run it. The vcredist installation packages can also be found on the SAP installation master DVDs.

This note includes instructions to download the redistributable package from Microsoft's security bulletin page, select the appropriate file based on the system type, and proceed with the installation.

Known Issues:
There is a known handle leak with the _popen call on x86 platforms for SAP Releases 6.40 or 7.0, which is addressed with a Microsoft KB article patch included in the provided attachment.

Concerning dbghelp.dll, which is not part of the C-Runtime Library but is also available in the attachment, the note advises it should be pre-included in newer Windows operating systems. It is installed automatically by SAPinst for SAP server products and only needs to be manually installed or updated if the version contained in the note is more recent than the one existing on the user's system.

Lastly, the note warns of precautions to be taken when replacing dbghelp.dll, especially related to the "Windows System File Protection" feature. It also instructs users to install the dbghelp.dll twice on 64-bit Windows platforms (once in the \Windows\System32 directory and once in the \windows\syswow64 directory).

The note includes a disclaimer that it is a machine-translated document and may contain inaccuracies, along with links to provide feedback and access the original document.