SAP Note 1828879 addresses an issue where the system produces error messages for navigation attributes on a VirtualProvider (VirtProv) based on an SAP HANA model that are not mapped. This is caused by the system executing syntax checks for these unmapped navigation attributes.

To resolve the issue, the SAP Note provides solutions for three different versions of SAP NetWeaver BW:

1. For SAP NetWeaver BW 7.30, users should import Support Package 10 (SAPKW73010), details of which are described in SAP Note 1810084 titled "SAPBWNews NW 7.30 BW ABAP SP10."

2. For SAP NetWeaver BW 7.31 (also known as SAP NW BW 7.0 Enhancement Package 3), users should import Support Package 08 (SAPKW73108), with further information available in SAP Note 1813987 with the short text "SAPBWNews NW BW 7.31/7.03 ABAP SP8."

3. For SAP NetWeaver BW 7.40, the required action is to import Support Package 03 (SAPKW74003), which is elaborated in SAP Note 1818593, "SAPBWNews NW BW 7.4 ABAP SP03."

The note also mentions that in urgent cases, correction instructions can be implemented as an advance correction. It advises users to first read SAP Note 875986 for information about using transaction SNOTE.

Furthermore, it mentions that the information about the said solutions may already be available before the Support Packages are released, which may be indicated by the words "Preliminary version" in the short text of the SAP Note.