SAP Note 2943035 addresses the procedures for migrating historical financial balances to SAP S/4HANA and SAP S/4HANA Cloud systems using the SAP S/4HANA Migration Cockpit.

**Key Takeaways from the Note:**

1. **Historical Balance Migration:**
   Historical financial transactions, which are typically needed for audit and internal reporting purposes, can be migrated into the SAP S/4HANA system.

2. **Supported Versions and Options:**
   - For SAP S/4HANA on-premise (LTMC), migration of historical financial transactions is only supported from release 1909 onwards using the migration object "FI - Historical balance (restricted)".
   - For SAP S/4HANA Cloud, the migration object "FI - Historical balance" is used for this purpose.

3. **Restrictions:**
   - For SAP S/4HANA (S4CORE) release prior to 1909, historical financial transaction migration isn't supported, including subledgers detail.
   - The migration object has limitations on the currency fields it can handle: document/transactional balance currency, company code currency, and group currency for on-premise and these plus freely defined currency for Cloud.

4. **Mapping Instructions:**
   - **Next Period Movement (NPM)**: Balances are uploaded at the end of the posting period and reversed in the subsequent period.
   - **Balance Carry Forward (BCF)**: Posts the initial cumulative balance in the first period and subsequent movements at the end of each following period.

5. **SAP S/4HANA Cloud Specifics:**
   - Historical balances migration serves primarily for reporting needs.
   - Transactions are not visible post-migration; only balances are facilitated for reporting.

6. **Alternative Approaches:**
   - If the migration object does not meet specific requirements, historical balances can be migrated to "dummy" G/L accounts and assigned to Financial Statement Versions (FSV) for internal reporting.

7. **Useful SAP Fiori Apps:**
   - Apps are available for managing G/L account master data, maintaining FSV, managing cost centers, managing journal entries, and reporting.

8. **Q&A Section:**
   - Provides answers to common questions regarding the use of the migration object, visibility of the migrated data in reports and tables, the necessity to open past periods, handling of fixed assets migration, and possibility to reverse incorrect migrations.

9. **Keywords:**
   - Keywords related to this migration concern SAP S/4HANA Migration Cockpit, historical financial data, LTMC, FI_HIST_BAL, and related object identifiers.

In summary, SAP Note 2943035 furnishes guidelines, restrictions, and valuable information for customers undertaking the migration of historical financial balances to SAP S/4HANA and SAP S/4HANA Cloud. It clarifies that this functionality is supported from SAP S/4HANA (on-premise) release 1909 onwards and offers detailed mapping instructions and alternative solutions for reporting historical financial information in the new system.