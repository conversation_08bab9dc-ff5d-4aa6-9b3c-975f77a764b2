SAP Note 400898 addresses an issue where reports used for recompiling business volume data and income do not allow selection by rebate arrangement type, due to a program error. The affected reports are <PERSON><PERSON><PERSON><PERSON>07, <PERSON><PERSON><PERSON><PERSON>08, <PERSON><PERSON><PERSON><PERSON>37, and <PERSON><PERSON><PERSON><PERSON><PERSON>, relating to subsequent settlement, volume-based rebate, and recompilation of business volume data and income.

The solution provided by the SAP Note involves both implementing repair instructions or importing a Support Package to rectify the program error and making specific changes to function module interfaces and reports selection texts for different SAP releases (3.1I and 4.0B).

The required changes include extending the interface of the function module MM_ARRANG_READ with new tables parameters like T_BOART and IDENT2 for Release 3.1I and T_BOART for Release 4.0B for allowing arrangement type selection. For Release 3.1I, it also includes marking the table parameter T_T6B1T of function module MM_BOART_READ_ALL as optional.

In addition, the note instructs to create specific selection texts and text elements (S_BOART, S_IDENT1, S_IDENT2, and their respective text elements 040, 041, 042) for the affected reports. Furthermore, the note suggests the creation of error messages MN661 and <PERSON>N663 for Release 3.1I, and MN661 for Release 4.0B to address missing settlement documents and inconsistencies on the database, respectively, which are identified as internal errors.