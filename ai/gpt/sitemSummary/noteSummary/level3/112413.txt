SAP Note 112413 provides guidance on managing units of measure during subsequent settlement in purchasing. Here is a summarized version of the note:

**Symptom:**
The note offers basic information on how units of measure should be handled within the subsequent settlement process in purchasing.

**Other Terms:**
The terms covered in this note include subsequent settlement in purchasing and units of measure.

**Reason and Prerequisites:**
- Units of measure are all the units assigned to a material or an article, such as base or alternative units of measure.
- Conversions between different units of measure usually require a material/article reference.
- Units like weight (gross/net), volume, and points are associated with dimensions and can be converted without material/article reference, but only one point unit is supported programmatically.

**Key Considerations:**
- Careful consideration is required for units of measure in condition records for subsequent settlements that do not refer to material/article, to prevent issues with price determination, business volume updating, or settlement.
- Business volume data is typically maintained at the most detailed level, which is the plant, without referencing the material/article.
- It's advised not to use volume, weight, and points as units of measure. Instead, use reference magnitudes/calculation rules maintained in the material/article master.
- For condition records related to business volumes from external service management, invoicing plans, or purchase orders and scheduling agreements without a unit of order, it's recommended to avoid using units of measure, including weight, volume, and points.
- From Release 4.5A, the same applies to condition records used cross-document.
- Up to three different kinds of units of measure can be referred to in subsequent settlement agreements.

**Scaling and Condition Basis:**
- Scale basis (for period and main condition record): a quantity that is not separately adjustable.
- Main condition record condition basis: a quantity, represented by a calculation rule.
- Period condition records condition basis: a quantity that can be changed in the arrangement maintenance based on a period calculation rule.

**Customizing and Data Update:**
- Settings for these are managed in Customizing of the condition type.
- Updated business volume data is summarized in the Logistics Information System, distinguishing between units of measure, volumes, weights, and points.
- Key figures with units of measure generally cannot be summarized at higher levels due to the absence of material data. But weight, volume, and points can be summarized.
- For volume, weight, and points, dedicated fields exist for storing business volumes. For quantities considered with performance and the database table size in mind, two fields are available.
- Problems can occur if the business volume needs conversion via settlement material, especially if the source document's material is unknown. This issue primarily affects Release 3.0.

**Solution:**
Not necessary.

This note essentially cautions users on the complexities involved in handling units of measure during the subsequent settlement and provides best practices to avoid common pitfalls that could arise due to improper handling of these units within the SAP system.