SAP Note 104838 is a collective note for SAP Release 3.1I, specifically addressing various errors found within the Shop Floor Information System (SFIS).

Symptom: Users of the SFIS are experiencing different errors.

Reason: The errors are attributed to program errors within the SFIS. There are no modifications made in the SFIS programs or modification notes implemented that would cause these errors.

Solution: To resolve the known problems in the SFIS (except for exceptions listed in the note), users are instructed to import specific transports from a directory on a Sapserv computer and execute some manual corrections, which should be initially tested in a non-production system. This note does not address cross-application errors of the Logistics Information System (LIS).

Users must run program RMCFNE22 in the background before operation analysis. Additionally, the note provides a list of other notes with issues not covered by the collective note, which need to be manually maintained:
- Note 92453, which deals with no or incorrect LIS data for orders in repetitive manufacturing.
- Note 101381, concerning a rare case where existing units might be deleted during goods issue backflush in repetitive manufacturing with user-defined information structures.
- Note 78940, associated with various errors when copying standard information structures S021, S022, S023, and S024.
- Note 79258, where adjustments are needed for correct representation in work center analysis.

There's an important caution to first implement Note 119466 ("EMS: Incorrect lines in planned/actual comparison") before taking action described in this collective note.

The collective note includes transports for the following objects:
- Function modules: MCF_STATISTICS_LIS, and others.
- Programs: FMCF1F01, FMCF2F01, and additional ones listed.
- Program texts for RMCFNEUA.
- Structures and tables: MCFKENNZ, MCKALK, and more.
- Function groups such as MCF4.
- Table contents for T804A, TMC2, and various other TMC tables.

This note guides users through resolving SFIS issues by importing the appropriate transports and making any necessary manual adjustments, while also considering other exceptions and dependencies.