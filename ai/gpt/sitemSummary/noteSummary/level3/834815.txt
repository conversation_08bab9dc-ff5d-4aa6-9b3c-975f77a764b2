SAP Note 834815 addresses the tasks required when upgrading to SAP ECC 6.00 for customers using FI-CA (Financial Accounting - Contract Accounts Receivable and Payable) integrated with Funds Management (FM).

**Symptom:**
When upgrading to SAP ECC 6.00, certain switches related to FI-CA and FM integration are automatically activated. This may lead to issues with FM table fields being duplicated in document tables.

**Other Terms:**
A list of terms and table names are associated with the upgrade and the integrations: HHM, FP-FM, IS-PS-CA, ACT_<REL>, ACT_700, DT963, alongside several DFKK-related table names.

**Reason and Prerequisites:**
The note mentions that during the upgrade, fields such as STAKZ and XANZA recommended in Note 773065 may cause activation errors due to duplication. These must be addressed before the upgrade.

**Solution:**
*Part A: Steps to Execute Before Upgrade*

- Create SI includes (SI_FKKFMOPK and SI_FKKFMOP) for FM table fields by copying from CI includes (CI_FKKFMOPK and CI_FKKFMOP).
- Activate the SI includes even if they trigger a duplication warning.
- Delete the original CI includes, while making sure that the new SI includes contain the FM fields.
- Use transaction SE11 to activate several tables and structures ensuring the correct includes are present and active.
- Important to follow steps precisely to avoid data loss, and it is noted that this process does not convert tables but merely transfers fields from one include to another.

*Part B: Steps to Execute If Activation Errors Occur During Upgrade*

- Delete the problematic CI includes if activation errors occur.
- You may ignore certain activation errors post deletion as specified.
- Further activation errors not related to the original problem should not be ignored.
- Delete runtime objects for the deleted CI includes and regenerate them for certain objects.
- After the upgrade, run the report FMGL_CHANGE_APPL_IN_LEDGER as described in Note 840783.

The note emphasizes the care that should be taken during the upgrade regarding the SI and CI includes to prevent activation errors and data loss. It also provides a clear course of action if such activation issues occur despite the precautions.