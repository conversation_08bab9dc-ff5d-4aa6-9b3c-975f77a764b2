SAP Note 1364960 provides instructions for improvements to the "Goals" functionality within the SAP Performance Management module. The improvements addressed in this note pertain to performance enhancements, better usability, and improved error handling.

This note specifies enhancements for managing Performance Management, Corporate Goals, Core Values, Team Goals, and other goal-related functions.

The prerequisites for applying this note involve previously implementing SAP Note 1332333. Once that prerequisite is met, users are instructed to:

1. Import the SAPCAR file attached to the note.
2. Perform several manual pre-implementation steps such as:
   - Modifying the structure 'HAP_S_WD_PMP_NAV_ITEM' by adding a 'DELETE_TOOLTIP' field.
   - Creating a new function group 'HRHAP_GOALS' in the mentioned package.

3. Execute post-implementation manual steps which include:
   - Creating a new BAdI (Business Add-In) implementation for 'HRHAP00_GOAL_PERIOD' in transaction SE19.
   - Adding specific class names and activating the new implementations.

The note details that after applying the support package indicated (or later versions) and the correction instructions provided, the user will benefit from the enhancements made to the SAP Performance Management Goals functionality. It is important to follow the sequence of instructions carefully to ensure proper implementation of the improvements detailed in the note.