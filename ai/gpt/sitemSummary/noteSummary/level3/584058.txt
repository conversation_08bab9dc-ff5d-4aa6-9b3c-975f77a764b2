SAP Note 584058 addresses the requirements for service charge settlement (SCS) according to Czech law within SAP R/3 Real Estate Release 4.6C or 4.70.

**Symptom:**
Users need guidance on how to post receivables or credits from service charge settlements to tenants, determining tax codes via condition types, regardless of whether lease-outs are with or without output tax.

**Prerequisites for Czech SCS:**
1. The company code does not opt, meaning:
   - The input tax is fully deductible from the Tax Office.
   - All lease-outs are valued as opted.
2. For each lease-out, a condition type must be created corresponding to the output tax code used for receivables postings.
3. An output tax code must be assigned per condition type.
4. Service charge keys with the same output tax code must be linked to the same condition type. Multiple condition types can be settled with the same output tax code.

**Solution:**
To comply with Czech law for SCS, the following actions are required:

1. Implement the provided program corrections.
2. In transaction SE38 (ABAP Editor), update text elements and text symbols with specific references to Czech regulations.
3. In transaction SE11 (ABAP Dictionary), create the database table TIVTAXSCS_CZ to specify the relationship between tax codes for SCS receivables and condition types. This table must be set up with certain properties, such as delivery class C, data class APPL2, a size category of 0, and be fully buffered.

The solution includes technical steps to correctly configure the SAP system to handle SCS according to Czech tax laws, including manual updates to ABAP text symbols and creating and activating a new database table. A maintenance view for customer-specific configurations may be delivered in a future support package.