SAP Note 3120781 is a central correction note for November 2021, targeting specific content issues encountered with the SAP S/4HANA Data Migration feature for the SAP S/4HANA 2020 version, especially for service pack levels SP00 to SP03. The affected migration scenarios include the use of both files and staging tables within the Legacy Transfer Migration Cockpit (LTMC).

The problems addressed by this note pertain to two technical objects: SIF_ROUTING and SIF_TR_IR_INSTR. For the SIF_ROUTING migration object, the issue causes the data migration process for 'Routing' to become stuck during the preparation step. The second issue, related to SIF_TR_IR_INSTR, prevents the processing of the S_MAINFLOW table for the 'TRM - Interest Rate Instrument'.

Both issues are resolved by implementing a Transport-based Correction Instruction (TCI) included with this note. The resolution automatically updates the related objects of SAP-delivered content. However, the notes clarify that if these objects have been modified or copied by the user, the corrections provided by the TCI will not apply to those altered objects.

Users are directed to specific SAP Notes for each technical object where detailed descriptions of the issues are available:
- SAP Note 3119149 for SIF_ROUTING
- SAP Note 3119151 for SIF_TR_IR_INSTR

Furthermore, users can refer to SAP Knowledge Base Article (KBA) 2543372 for guidance on how to implement a TCI. The note ultimately aims to patch content issues without impacting user-modified or user-copied data migration objects.