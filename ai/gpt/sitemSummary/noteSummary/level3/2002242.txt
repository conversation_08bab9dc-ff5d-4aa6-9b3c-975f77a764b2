SAP Note 2002242 provides a resolution to an issue where error message FAGL_REORGANIZATION 566 is encountered during the account assignment change of receivables and payables. This error highlights a discrepancy between PRCTR (Profit Center) and SEGMENT values in the tables FAGL_SPLINFO and FAGL_R_SPL.

Summary of the SAP Note:

Symptom: While changing account assignments of receivables and payables, the system generates an error message FAGL_REORGANIZATION 566 indicating a mismatch in PRCTR/SEGMENT in the related system tables.

Other Terms: The note mentions terms like Profit Center Reorganization, Segment Reorganization, and Reorg.

Reason: The issue might occur due to several factors, including:
- Not all SAP Notes have been implemented (reference to SAP Note 1910342).
- Changes in the customizing of document splitting post-activation.
- Manual assignment of customer/vendor line items.

Solution: The following steps are recommended to resolve the issue:
1. Update your system with the attached program corrections or via the relevant Support Package.
2. Verify that all associated SAP Notes, except for composite SAP Note 1471153, are implemented in the system. If certain notes are missing, they should be added, especially if they are essential for the initial generation of AP/AR objects. If notes are implemented after the generation, reset the test environment fully.
3. If error message 566 persists after proper implementation, raise a customer message with the FI-GL-REO-GL component. SAP Support may identify a special case, such as manual PRCTR entry in an invoice. If confirmed, set the message FAGL_REORGANIZATION 567 as a warning using transaction OBA5. Doing this allows manual reassignment of affected documents by bypassing the error message.

The note provides detailed instructions on how to change the message control settings for message number 567 using transaction OBA5, specifying that it should be set to a warning both online and in batch input, with the standard being an error.

This note is for those who have a detailed understanding of SAP financial modules, particularly those involved with profit center and segment management, and are capable of implementing SAP Notes and working with SAP Support to resolve complex issues.