SAP Note 1840072 addresses an issue in the BEx (Business Explorer) Web role menu where the URLs are restricted to a length of 255 characters. When users select a BW (Business Warehouse) role menu entry that contains a long URL, such as web links to BW queries or Web Application Designer (WAD) templates, the URL can be truncated at 255 characters, leading to incomplete loading of the content, especially if the URL includes additional parameters.

Affected areas include BW role menus, favorites, BW queries, and BW templates. The underlying cause of the issue is identified as a program error. 

The solution provided in the Note is to apply the mentioned Support Packages or Patches for various SAP NetWeaver BW versions, such as:

- For SAP NetWeaver BW 7.00, apply Support Package 31 (SAPKW70031).
- For SAP NetWeaver BW 7.01 (EHP 1), apply Support Package 14 (SAPKW70114).
- And so on, with corresponding Support Packages for BW 7.02, 7.11, 7.30, 7.31, and 7.40.

These Support Packages are detailed in related SAP Notes (1782745, 1794836, and others as listed), which, upon release, will describe the Support Packages more thoroughly.

In urgent cases where immediate resolution is necessary, manual corrections can be implemented via transaction SNOTE. This includes adding the component "URL_EXT" of type "AGR_URL2" to the structure RSBB_NODES using the ABAP Dictionary (transaction SE11).

Additional information highlighted in this Note:

- The correction applies to BEx Web role menu item starting with version 2 of this SAP Note.
- Implementation of the correction via SNOTE on releases based on 7.30 or higher may encounter issues due to missing versions of the affected coding. Investigations into a solution for this are underway.

Users are also advised to reference SAP Note 2097382 if they encounter problems with variables not being replaced properly. Before using the correction instructions, users should check SAP Note 875986, which provides information about using transaction SNOTE.

Lastly, it's important to note that while the correction instructions might be available before the official release of the Support Package, the short text may still indicate a "preliminary version".