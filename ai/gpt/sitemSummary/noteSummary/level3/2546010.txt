SAP Note 2546010 addresses an issue concerning the function module RFC_CVI_EI_INBOUND_MAIN, which is utilized in various SAP environments for creating or updating Business Partner master records. It is reported that the function module does not create or update all the expected fields, and some fields are not even available for use.

The note clarifies that RFC_CVI_EI_INBOUND_MAIN is not the recommended method for creating or updating Business Partners because it does not cover all features. Furthermore, it is not an officially released SAP function module but was originally intended only for internal purposes. This is supported by references to other SAP Notes (109533, 1909989, 2527926, and 2506041) which provide further context on the matter.

The resolution suggested is to use the officially supported methods for creating or updating Business Partners, as detailed in SAP Note 2417298, titled 'Creation of Business Partner with Customer and Supplier Roles'.

Keywords associated with this SAP Note are SAP Note numbers 2417298, 2506041, 109533, 1909989, 2527926, as well as terms like RFC_CVI_EI_INBOUND_MAIN, BP, Business Partner, and Master Data. These keywords help users searching for resolutions to similar issues.