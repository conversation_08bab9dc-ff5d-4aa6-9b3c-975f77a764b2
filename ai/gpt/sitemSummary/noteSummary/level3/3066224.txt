SAP Note 3066224 addresses issues with the Data Migration content provided for SAP S/4HANA 1909, particularly for users who have not modified the pre-delivered content and are experiencing errors when transferring data either using files or using staging tables in the SAP S/4HANA Migration Cockpit.

The note summarises the symptoms as general issues with the data migration content, and it specifies certain technical components related to financial data, such as general ledger (G/L) accounts and open item management for accounts payable and receivable, which may be experiencing errors.

It is applicable to systems using the SAP S/4HANA 1909 release, from Support Package 00 to Support Package 04.

The provided solution is a Transport-based Correction Instruction (TCI) that addresses specific issues listed in a table within the note, with each issue linked to a corresponding detailed SAP Note. The TCI will automatically update the generated migration objects.

The key points addressed by the TCI include:
- The "Clearing Specific to Ledger Groups" field being migrated to an incorrect target field.
- Missing data for the line item currency '10' in the document migration related to accounts payable and receivable open item migration objects.
- Erroneous addition of a leading zero due to a conversion exit in the transaction type field during the balance migration process.

Users are reminded that if they have modified the original data migration objects provided by SAP, the corrections from this TCI will not apply. In such cases, or for further guidance, users are advised to refer to KBA 2543372 on how to implement a Transport-based Correction Instruction.