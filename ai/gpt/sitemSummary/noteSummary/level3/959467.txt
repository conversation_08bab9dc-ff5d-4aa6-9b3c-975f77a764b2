The SAP Note 959467 addresses an issue with the incorrect display of date and time key figures in an SAP BW (Business Warehouse) system. The problem arises when a key figure that is of DATE or TIME type is recalculated using the 'Calculate Results As...Average' function, causing it to lose its date or time property.

The implications of the issue are categorically outlined:

1. For key figures of the date type (MWKZ = 'D'), the result should again be of the date type for both averages across all values and averages across all non-zero values.

2. For key figures of the time type (MWKZ = 'T'), the issue extends to cover three scenarios: averages across all values, averages across all non-zero values, and standard deviation. The result should, in each case, maintain its time type property.

3. For key figures of the integer type (MWKZ = 'C'), certain calculations now show results with 3 decimal places, including averages across all values and non-zero values, and when calculating single values as such averages.

4. For nondimensional key figures (MWKZ = 'F'), all averages were adjusted to have at least 3 decimal places, provided no scaling is active. It is important to note that this specific change was reverted with SAP Note 995517.

To resolve this issue, customers are instructed to import the appropriate support packages for the different versions of SAP BW they might be running. The specific support packages mentioned are:

- For BW 3.0B: Import Support Package 32 (SAPKW30B32).
- For BW 3.10 Content: Import Support Package 26 (SAPKW31026).
- For BW 3.50: Import Support Package 18 (SAPKW35018).
- For BW 7.0: Import Support Package 09 (SAPKW70009).

Additionally, the note refers to other SAP Notes for detailed information concerning each mentioned Support Package. Users are alerted that these other notes may already be available even before the Support Packages are formally released, indicated by the phrase "Preliminary version" in their short text.

To summarize, SAP Note 959467 details a bug where calculated averages of date and time key figures would lose their proper format, instructions are given to update to specific support packages to correct this anomaly, and a reversion of changes for nondimensional key figures through another note is outlined.