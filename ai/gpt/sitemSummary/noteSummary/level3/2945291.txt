SAP Note 2945291 addresses an issue encountered during the Bill of Materials (BOM) Transfer process within the Product Compliance functionality of SAP. Specifically, when a sub BOM contains multiple BOM positions for the same material assigned to different alternative groups, the BOM transfer results in a single aggregated entry for that material, rather than displaying separate entries for each group.

Key points from this note are as follows:

- **Symptom:** When transferring a BOM that has sub BOMs with the same material listed multiple times under different alternative groups, only one instance of the material is displayed in the product structure with a combined quantity, rather than maintaining separate line items.

- **Other Terms:** Referenced terms include EHPRC_CPB02, EHPRC_CPB02PV, EHPRC_CPM_BOMBOS 028, and EHPRC_CPM_BOMBOS 114.

- **Cause:** The issue arises because of a program error.

- **Prerequisites:** Details regarding prerequisites are found in the corresponding correction instructions.

- **Solution:** Users should implement the provided SAP Note or import the relevant Support Packages or Feature Package Stacks for the SAP EHS Management component extension or SAP S/4HANA to fix this error.

Additionally:
- When the same material is listed more than once for another alternative group within the same sub BOM, an error message stops the BOM Transfer, indicating that the BOM has not been updated.
- If the same material occurs multiple times within the same alternative group, a warning is issued, and the quantities of the occurrences are aggregated.
- The correction provided within this note affects the default implementations of certain user exit functions. Users need to adjust their custom versions based on these correction instructions. The specific user exits affected are EHPRC_CP_BB20_UNITCON_PERC_WGT, EHPRC_CP_BB20_UNITCON_MAT_UOM2, and EHPRC_CP_BB20_SUBBOM_DOUBL_CHK.

To summarize, SAP Note 2945291 provides a solution to a problem in Product Compliance BOM Transfer where materials within different alternative groups are not shown correctly, and advises users on how to implement this correction and adjust their systems accordingly.