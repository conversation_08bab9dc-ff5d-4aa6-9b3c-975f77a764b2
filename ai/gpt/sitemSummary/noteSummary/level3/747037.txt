SAP Note 747037 addresses an issue in the handling of before and after images within Operational Data Store (ODS) objects which are now known as DataStore Objects (DSOs) in SAP BW. The symptom noted is that before images are unexpectedly converted to after images in the data target (ODS object or DataStore Object).

The reason for this symptom is that when before and after images—which pertain to the changes made to the data—are processed, fields that should be updated by overwriting are being initialized instead. Additive fields are calculated correctly, but overwriting fields aren't maintaining their expected values.

The solution provided in the note has two parts:
A) Ensure that before and after images are transferred together in the same data package, with the same key, to prevent initial values in the before images from overwriting the values in the after images.
B) If before and after images for a source key are split across different data packages, the unwanted initialization may occur.

For the 3.x data flows that use delta-enabled SAP extractors, case (A) is applicable. For case (B), the note specifically mentions delta extraction and DataStore to DataStore loading scenarios using Direct Transfer Process (DTP) and references additional details in SAP Note 1358780.

To prevent the unwanted initialization when updating before and after images with different keys, or to avoid the effects described in (B), the note suggests converting all before images to after images in the start routine of the update rule or the transformation. This can be done by setting the field 0RECORDMODE from 'X' for deletion to ' ' for insert or update, which maintains the value that needs to be overwritten without it being initialized.