SAP Note 1834480 addresses the functionality of displaying dependencies of version variables within the BICS Design Time interface of SAP NetWeaver BW.

Summary:

- **Symptom**: The note introduces the ability to view the dependencies of version variables in the BICS Design Time interface.
- **Reason and Prerequisites**: The enhancement is characterized as a 'Feature'.
- **Solution**: The note provides a solution where it adds a reference about related version variables to the hierarchies.

For users to benefit from this function, the following steps must be taken:

1. For SAP NetWeaver BW 7.30, import Support Package 10 (SAPKW73010) after SAP Note 1810084 (which describes the Support Package in more detail) is released for customers.

2. For SAP NetWeaver BW 7.31 (also known as SAP NW BW7.0 EHP 3), import Support Package 8 (SAPKW73108) following the release of SAP Note 1813987 that also provides a detailed description of the Support Package.

The SAP Note mentions that in cases where immediate resolution is required, users can implement the correction instructions as an advance correction. However, users must first read SAP Note 875986, which provides details on using transaction SNOTE.

Additionally, the referenced SAP Notes (1810084 and 1813987) may be available prior to the release of the Support Packages. In such cases, those notes will be marked with the term "Preliminary version" in the short text.