SAP Note 1006116 addresses the issue of workload statistics data migration following an upgrade to NetWeaver2004s (Basis Release 7.00). The problem described is that after the system upgrade, historical workload statistics cannot be displayed in transaction ST03N due to the introduction of a new and incompatible workload statistics collector.

Key Points of the Note:

1. Incompatibility: The new workload statistics collector in NetWeaver2004s is incompatible with data from earlier versions (4.6C, 6.20, or 6.40).

2. Prerequisites: The issue arises after upgrading to NetWeaver2004s from one of the specified older releases. For details on preparing the data for upgrade, reference is made to SAP Note 1005238.

3. Solution:
   - Data must be saved before the upgrade and then converted and imported into the new data structure post-upgrade.
   - The note provides the SWNCMIGRATION1 report as a correction instruction, which needs to be created manually and executed post-upgrade to convert the data (instructions on creation and assignment to packages SWNC_COLLECTOR or SWNC_COLL are provided based on the release).
   - A procedure detailed in a series of steps guides users through deactivation and reactivation of the SWNCREORG report, executing the migration report, checking data, adjusting retention times, and post-migration checks and cleanup.
   - Instance names in ST03N will receive a prefix to distinguish old data, reflecting the release identification before the upgrade (e.g., "46C_abc123_XYZ_42").

4. Additional Notes:
   - The configuration of the TCOLL table should be verified using either SAP Note 966309 for Release 7.0x or 966631 for Release 7.1x.

The note ultimately provides a clear method for ensuring users can continue to access and use workload statistics following an upgrade, which is crucial for large SAP systems where historical performance data is critical for analysis and troubleshooting.