SAP Note 2160045 explains an issue and its solution related to customer fields added to tables COEP or BSEG that are missing in the ACDOCA table after an upgrade to SAP S/4HANA Finance (also known as Simple Finance or sFIN).

Summary of the SAP Note:

- Symptom: After upgrading to S/4HANA Finance, custom fields added to COEP or BSEG via Append Structure or modification are missing in table ACDOCA. Additionally, if fields are appended directly to ACDOCA, a syntax error may occur in class ZCL_FINS_MIG_UJ_HDB_GENERATED during migration.

- Other Terms: The note references error terms such as "AMDP_NATIVE_DBCALL_FAILED", "CX_AMDP_NATIVE_DBCALL_FAILED" and the SQL code 1306 related to the issue.

- Solution: The note recommends that coding block extension fields in the Include Structure CI_COBL will be automatically included in ACDOCA and are considered during data migration, which is the preferred method for extending the unified journal in S/4HANA. This support is available from S/4HANA Finance 1605 and S/4HANA 1511 onwards.

  Additionally, SAP suggests using the Extension Include "INCL_EEW_ACDOC" for customer fields, which has been available since sFIN 1503. Directly appending structures to ACDOCA is not supported and will lead to syntax errors. If fields need to be migrated from COEP or BSEG that cannot be moved to CI_COBL, they should be appended to the INCL_EEW_ACDOC structure. The migration program will then automatically consider these fields during the line item migration.

  If encountering syntax errors in ZCL_FINS_MIG_UJ_HDB_GENERATED, this class can be re-generated by running the report FINS_MIG_UJ_MAPPING_GENERATE after creating the appropriate Append Structure to INCL_EEW_ACDOC.

In summary, SAP Note 2160045 provides guidance on handling custom fields during an upgrade to S/4HANA Finance to ensure that they are correctly included in the ACDOCA table, and informs about the correct approach to extend the table for a successful data migration.