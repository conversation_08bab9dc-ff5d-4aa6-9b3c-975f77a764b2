SAP Note 2727544 addresses an issue in the Incident Management component where the description of the "Injury/Illness Type" in the value help does not match the customized entry for that classification in the Incident Management application's "People" overview within the "Injury/Illnesses" subtab.

The root of the problem is that the customizing activity for Injury/Illness Types did not enforce a check for the uniqueness of the "Injury/Illness Type" field, resulting in possible duplicate entries that cause inconsistent data display in incident records.

To resolve this, the note provides a solution which involves introducing a validation check to prevent future duplicates and requires the manual correction of any existing duplicates by assigning new codes to them. The note specifies that this correction will not affect existing customizing view entries, only providing a check going forward.

It's also mentioned that for the user to know the validity of the corrections, they should refer to the correction instructions. The note then directs users to the "Support Packages" section for packages containing the necessary corrections or, alternatively, provides correction instructions that can be implemented manually.