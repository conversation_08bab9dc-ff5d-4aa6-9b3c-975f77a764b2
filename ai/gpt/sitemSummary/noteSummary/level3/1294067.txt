SAP Note 1294067 provides guidance on the activation of enhancements for Service Parts Management (SPM) within the SAP system. It addresses confusion about when to activate a specific switch for SPM and its effects. The switch in question is intended to control the program flow for SPM processes, particularly when integrated with Extended Warehouse Management (EWM) systems.

Key points of the note:

1. **Symptom**: Users find the activity "Activate Enhancements for Service Parts Management" in the IMG and are unsure about when to perform this activation and the resulting impacts.

2. **Reason**: The confusion stems from missing documentation regarding the activation switch.

3. **Solution**: While EWM-specific processes can now often be controlled without activating the SPM switch due to several corrections (referenced by Notes 976716, 990782, and 989611), there are still specific SPM processes requiring the switch:
   - Redirection of scheduling agreement releases in the goods receipt process.
   - SPM-specific calculation of the cumulative delivered quantities.

   If these special processes are not required, and only an EWM system is used for warehouse management, as of certain support packages and in specified releases (6.02, 6.03, and 6.04), activation of the SPM switch is no longer needed.

4. **CRM Integration**: If a CRM system is integrated with ERP, the SPM switch should not be activated. Instead, activate the process under "Logistics Execution -> Service Parts Management (SPM) -> Integrate SPM with other Components -> Activate Processes Using SAP CRM". This supports processes such as sales order management in CRM, third-party order processing, and complaints and returns with CRM sales orders, available as of SAPKH60010 and in the specified releases.

5. **Switch Activation Timing**: It is recommended to activate the SPM switch just before the creation of the first document if the SPM processes are required. The switch doesn't affect system configuration and can be deactivated later if SPM processes are no longer used.

6. **Constraints**: The note advises consulting an SAP consultant before implementing SPM, EWM, or direct delivery scenarios with CRM to ensure there are no constraints on planned business processes.

In summary, this SAP note clarifies when and why to use the activation switch for SPM enhancements and outlines the scenarios for required or optional use, along with guidance on CRM integration. It also highlights consultation as a critical step before implementing complex scenarios involving SPM and CRM.