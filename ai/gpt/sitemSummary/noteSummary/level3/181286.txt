SAP Note 181286 addresses the issue where it is not possible to exclude individual tablespaces from the RUNSTATS jobs generated by the "Update statistics for all R/3 objects" or "RUNSTATS on obj. needing new statistics" jobs in the DBA planning calendar (DB13) for DB2/390 databases.

The provided solution requires that the user first imports specific transports that are detailed in SAP Note 101217 and follows the sequence described therein. The note also urges users to regularly check SAP Note 101217 for updates or enhancements to keep their system up-to-date.

The transports to be imported depend on the user's R/3 release, and these are outlined as follows:
- For Release 3.1I, the transport is KDGK000107.
- For Release 4.0B, it is D5IK000089.
- For Release 4.5B, it is KDIK000121.
- For Release 4.6A and beyond, the functions are already included in the delivery.

Once the appropriate transport is imported, a table named DB2N<PERSON><PERSON> is provided. This table can be maintained using transaction SE16 or SPUFI. The RUNSTATS_ALL jobs and RUNSTATS_ALERT jobs generated in transaction DB13 will now explicitly exclude tablespaces listed in the DB2NORUN table, allowing users to exclude certain tables like VBHDR, VBMOD, and <PERSON>B<PERSON><PERSON> from the RUNSTATS jobs, assuming they reside in separate tablespaces. This ability to exclude tablespaces is further referenced in SAP Note 116698.

In summary, the note provides a solution to exclude certain tablespaces from automatic RUNSTATS jobs in a DB2/390 database environment through the import of specific transports and the maintenance of the DB2NORUN table.