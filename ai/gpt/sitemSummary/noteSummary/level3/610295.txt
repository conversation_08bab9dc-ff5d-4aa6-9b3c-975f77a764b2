SAP Note 610295 addresses an error that occurs during the upgrade to R/3 Enterprise, where the activation of views H_TMFK, V_TB2BT, and V_TB2BU fails. This problem is due to the dependency of the GlobalTrade add-on on the core SAP system. Specifically, the issue arises because the views in question are part of the Global Trade Management application but rely on tables in the core system. The error occurred after a table in the core system was altered in Support Package 7, which resulted in the views becoming incorrect.

The solution provided in this note is that if the Global Trade Management add-on is not being used, the error messages related to the views can be safely ignored. The underlying issue was resolved in Support Package 7 for the Global Trade add-on. It's noted that systems affected by this problem would have core system Support Package 7 or higher installed but do not have the corresponding Support Package update for the Global Trade add-on. The note concludes by reassuring users that if they do not utilize the views from the add-on, the errors will not impact their system.