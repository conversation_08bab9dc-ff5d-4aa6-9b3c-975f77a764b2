SAP Note 951393 addresses an issue where users of BEx Query Designer 3.x or BEx Query Designer 2004s within a 2004s SAP system receive undetailed error messages from the transport system. These error messages simply state that the operation has failed without providing further information, as opposed to the plaintext error messages usually delivered in the 3.x system.

Key points from the note:

Symptom:
- Users experience a lack of detailed error messages when using BEx Query Designer in a 2004s SAP system.

Reason:
- The problem is due to a program error within the SAP software.

Solution:
- The note advises importing Support Package 09 for SAP NetWeaver 2004s BI (BI Patch 09 or SAPKW70009) into the BI system.
- This Support Package will resolve the issue, and the detailed contents of the package can be found in SAP Note 0914303 titled "SAPBINews BI 7.0 Support Package 09".
- The note suggests that even though the details about the Support Package may be available before the official release (marked with "preliminary version"), the Support Package itself will only be useful once officially released.

Other Terms:
- The note lists keywords related to the issue: Query, error, transport, message, available, incorrectly reported.

In summary, SAP Note 951393 provides a solution for missing detailed error messages in BEx Query Designer by recommending the installation of a specific support package, and directs users to an accompanying note for more details on that package.