SAP Note 1403396 addresses a specific requirement for businesses operating within Hungary that use SAP's IS-U (Industry-Specific Solution for Utilities) system. The Hungarian Finance Ministry regulations – specifically, regulations 24/1995 and 41/2009, as well as statements 4007634802/2009 and 4007640807/2009 from the Hungarian Tax Office – mandate that an invoicing program must be able to distinguish between original and additional (copy) printings of an invoice. More importantly, it must ensure that the original copy of an invoice can be printed only once.

The keywords mentioned are related to copy numbering, invoice numbering, and the original and copy designations (in Hungarian: eredeti for original, and másolat for copy) which are important for compliance reasons.

To fulfil these requirements, SAP provides local enhancements in the Hungary version of the CEEISUT (Central and Eastern Europe Industry Solution Utilities) add-on, which is designed to handle utilities processes specific to Central and Eastern European countries. These enhancements include a function for copy numbering that is part of versions 4.72-6.04 of the CEEISUT add-on.

The note lists the Application Object Parts (AOPs) required for different CEEISUT add-on versions to get this copy numbering functionality:

- For CEEISUT 4.72, the function is available from AOP 24.
- For CEEISUT 6.00, it is available from AOP 17.
- For CEEISUT 6.04, it is available from AOP 05.

The note also instructs users to see the attached documentation for details on the manual steps necessary after implementing the mentioned AOPs. If businesses need this functionality for IS-U/CCS version 4.64, they must create a customer message on the component XX-CSC-HU-IS-U.

In summary, SAP Note 1403396 provides guidance on how SAP IS-U's Hungarian users can comply with local invoicing regulations by implementing specific enhancements through AOPs to distinguish original invoices from copies and restrict the printing of the original invoice to one instance only.