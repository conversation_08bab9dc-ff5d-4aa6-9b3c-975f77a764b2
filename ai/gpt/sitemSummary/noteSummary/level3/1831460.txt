SAP Note 1831460 details adjustments needed for using the SQL Monitor with the runtime monitor (RTM) in productive environments. This note is relevant for SAP Basis Releases from 700 to 731.

The changes described in this note require importing a specified Support Package or implementing correction instructions that are provided. It references SAP Note 1806015, indicating that corrections from that note should be applied first if the SAP release is affected.

Additionally, the note provides optional steps if there's a need to dynamically deactivate the SQL Monitor at the kernel level. To achieve this, the creation of a new profile parameter 'abap/sqlm/main_switch' is outlined. These changes cannot be applied through SNOTE (SAP Note tool) and include instructions for using transaction RZ11 to create the parameter. In case the parameter is not recognized by the kernel, SAP Note 1833999 should be consulted for the relevant kernel patch.

The profile parameter should have the specified attributes, such as being applicable to ABAP, allowing changes, valid across all operating systems, dynamically switchable, and with the special character string "on,off" as possible values.

In summary, SAP Note 1831460 provides solutions for adjustments in using the SQL Monitor with RTM and advises on creating a profile parameter for dynamic deactivation of the SQL Monitor at the kernel level, complementing the information provided in other connected notes.