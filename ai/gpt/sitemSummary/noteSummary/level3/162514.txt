SAP Note 162514 addresses an issue specific to the SAP IS-Oil solution. The note warns that it should only be applied to systems with the IS-Oil component installed, as applying it to non-IS-Oil systems could cause serious damage.

Symptom:
The issue pertains to the incorrect determination of the Mode of Transport in output determination. The system is incorrectly retrieving the Mode of Transport value from the order header when it should be obtaining it from the order item due to relevance checks being performed at the item level.

Other Terms:
Related terms include TAS-Interface, VA41/42 (transaction codes), Output Determination, and TAS Relevance.

Reason and Prerequisites:
The prerequisite for the issue is that the Mode of Transport is incorrectly being sourced from the order header instead of the order item.

Solution:
The solution involves downloading and importing Servicepack 3. The transport files, along with the object list for this transport, can be found on specified SAP servers. The note cautions users to only apply this transport to IS-OIL systems and refers to Note 47531 for specific instructions regarding IS-Oil systems. Additionally, Note 13719 is mentioned for guidance on importing corrections to customer systems. Before installing, users are directed to reference OSS Notes 98642 and/or 98876 for the proper sequence of installation.

In summary, SAP Note 162514 provides a remedy for a Mode of Transport determination issue within the SAP IS-Oil sector. It includes detailed instructions for applying the fix, while emphasizing the importance of following the correct sequence and applying it only to appropriate IS-Oil systems.