SAP Note 1835574 addresses an issue with MDX queries in SAP NetWeaver Business Warehouse (BW) where incorrect data or no data is displayed when the IIF function is used within a calculated measure. The problem occurs under these conditions:

1. A calculated measure used in the MDX statement includes the IIF function.
2. The "true" condition in the IIF function returns a tuple with only one measure, not the default measure.
3. The "false" condition returns a tuple that contains a member from an MDX dimension other than the one used in the "true" branch, and the member from the "false" condition is not displayed.

This issue is identified as a program error and has been observed only internally at SAP.

The solution provided in this note involves importing specific Support Packages for various SAP NetWeaver BW versions, namely BW 7.00, BW 7.01, BW 7.02, BW 7.11, BW 7.30, BW 7.31, and BW 7.40. Each solution suggests importing a different Support Package for each version, and references to additional SAP Notes with detailed information about these Support Packages are provided. These referenced notes also describe the relevant Support Packages.

Additionally, if there's an urgent need to correct this issue, there's a possibility to implement advance correction instructions. Before doing this, one must read SAP Note 875986 regarding transaction SNOTE, which is used for implementing SAP Notes directly in the system.

Furthermore, although the SAP Notes detailing the Support Packages may already be available to customers, their short texts might still indicate "Preliminary version" until the Support Package is officially released.