SAP Note 2368983 addresses an issue where the field RHOART, which stands for "origin object type," is not being populated for all line items in the ACDOCA table, which is the universal journal in S/4HANA. The reason for this problem is identified as a function module that is responsible for deriving the origin object type only being active when a particular business function is active.

The solution provided in the note is the implementation of a correction that ensures the RHOART field is always filled in the universal journal. It is important to note that this solution applies only to postings made within S/4HANA and does not address records that were created during the migration process from ERP to S/4HANA. For such records, the RHOART field is filled only if there was a custom field ZZRHOART in the FAGLFLEXA table of ERP that contained the relevant data.

The SAP Note does not reference any other notes, meaning that the information contained within this note is self-contained and sufficient to address the specified issue regarding field RHOART.