SAP Note 2270318 addresses a scenario where a system is being converted to SAP S/4HANA and involves the transitioning of SEM Banking functionality.

The note clarifies that SEM Banking is no longer included in SAP S/4HANA. Much of the SEM Banking functionality has been superseded by the Bank Analyzer or other applications, but some functions lack direct replacements.

Technical disablement in SAP S/4HANA for SEM Banking has been implemented by:
- Disabling the SEM Banking menu and IMG (hidden from the interface).
- Ensuring central SEM Banking transactions terminate with an error ("A-message").
- Prohibiting the execution of SEM Banking transactions.

Business process related information specifies that SEM Banking applications like "Datapool," "Profit Analyzer," "Risk Analyzer," and "Strategy Analyzer" will not be part of the SAP S/4HANA offering and all related transaction codes for these applications are not available.

The recommended actions for users of SEM Banking are:
- To seek alternatives for SEM Banking in SAP S/4HANA, potentially by adopting the functionality of the Bank Analyzer or by maintaining a separate ERP installation with SEM Banking.
- To archive or delete obsolete SEM Banking data before migrating to SAP S/4HANA in order to avoid cluttering the new system with unnecessary data, after disabling SEM Banking.

Relevancy is determined by the presence of an active risk management area or an active Banking operating concern in the system.

The note also references SAP Note 2211665 for more information about custom code-related information.

Furthermore, the note refers to several other SAP Notes (2805619, 2661159, 2484134, 2328754, 2211665, 2144579, and 2059467) for release information on "SAP for Banking" in the context of various SAP S/4HANA editions. These references provide detailed compatibility and integration limitations of "SAP for Banking" applications and solutions with different editions of SAP S/4HANA.