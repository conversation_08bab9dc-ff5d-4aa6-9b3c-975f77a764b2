Summary of SAP Note 1655212 - No new time interval during reorg for assets not posted to:

Symptom:
- When executing a profit center reorganization in SAP, the system fails to create a new time interval for assets that have not been posted to on the date of reorganization.
- Adding a new time interval to an asset that has not been posted to results in the following: the new interval shows the correct, updated profit center, but the existing interval also incorrectly shows this new profit center. Attempting to change this using transaction AS02 causes it to terminate with an error message AIST 009, after which the error can no longer be corrected.

Other Terms Referenced:
- PRCTR (Profit Center), ASSET_ORG_ASSIGNMENT_CHANGE, T093_BSN_FUNC-SEGMENT_REPORT, FIN_GL_REORG_1

Reason:
- The issue is caused by a program error within the system.

Solution:
- The note instructs users to implement the corrections as described in the attached correction instructions to resolve this issue. Once applied, assets that were previously not posted will also receive a new time interval with the newly assigned profit center, as expected.

References:
- The note references SAP Note 1627018, a composite note for segment reorganization, which does not directly solve the issue but guides users to check the "Related Notes" section for a list of SAP Notes addressing various aspects of segment reorganization.
- It also references SAP Note 1471153, a composite note for profit center and Funds Management reorganization, which compiles all related SAP Notes on the topic, especially for users who have activated the business functions FIN_GL_REORG_1 or PSM_FM_REASSIGN and are using the new General Ledger functionalities.

This SAP Note 1655212 describes a specific issue related to profit center reorganization and provides a clear solution that involves applying provided corrections to overcome the identified program error.