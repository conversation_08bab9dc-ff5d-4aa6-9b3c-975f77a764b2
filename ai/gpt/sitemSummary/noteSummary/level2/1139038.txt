SAP Note 1139038 addresses an issue with the class-based signature tool in SAP_ABA, where there is no standard application to selectively choose and display log entries recorded during the digital signature process. This absence makes it difficult to trace the signature process timeline.

Key points from the note:

- **Symptom**: While the system records entries in the application log during the signature process, there is no standard way to display these logs selectively, hindering the tracking of the signature process.
  
- **Other Terms**: Keywords related to this note include Digital signature, signature tool, log display, application log, DSAL, CJ, DSIGAL, DS_LOG_DISPLAY.
  
- **Reason and Prerequisites**: The log display for the signature process is usually part of the SAP_APPL layer, but the signature tool operates within the SAP_ABA layer, where no such display is available.
  
- **Solution**:
  - For SAP NetWeaver 7.00, the solution is to import the relevant Support Package. However, if the user chooses to manually implement corrections against SAP's recommendation, they must follow the pre-implementation and post-implementation steps specified in the note.
    - For releases higher than NetWeaver 7.00, implement the corrections provided in SAP Note 1438530.
    - The implementation guide for including the signature tool is detailed in the PDF file attached to SAP Note 700495, valid for all releases.

This note is part of a series of related notes that collectively address issues related to the digital signature process in SAP applications. Other referenced notes provide further guidance and solutions for implementing digital signatures, addressing custom BAdI implementations for modifying log displays, and rectifying dumps occurring during the display of digital signature comments.