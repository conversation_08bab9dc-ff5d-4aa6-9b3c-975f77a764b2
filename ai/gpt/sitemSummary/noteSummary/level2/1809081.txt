SAP Note 1809081 - Force local update

Summary:

The SAP Note 1809081 addresses an issue where clients are not able to enforce a global local update using a profile parameter. Normally, updates in SAP systems are executed asynchronously, but it is possible to make them execute immediately (locally) using the ABAP statement `SET UPDATE TASK LOCAL`.

In cases where a systemwide local update is needed, the profile parameter `abap/force_local_update_task` is intended to be used. By setting this parameter to '1', the system is expected to treat all update tasks as if they were executed with the local setting, thereby enforcing the local update across the entire system.

The solution provided in this note is to apply the latest kernel patch which includes the added support for the profile parameter `abap/force_local_update_task`. Users need to check the SAP Service Marketplace to see if the patch is available for their platform and apply it following the instructions in SAP Note 19466.

The note includes a word of caution about the implications of enforcing local updates, such as potential changes in transaction semantics and extended database lock durations. These consequences are especially critical if changes to the database content are made in the last dialog step before triggering the update, using Open SQL commands like INSERT, MODIFY, UPDATE, or DELETE, which is contrary to the standard SAP transaction concept.

To update to the minimum required patch level, users can consult the “SP Patch Level” section within the note for further information. After the relevant Support Package is imported, it is possible to set the profile parameter dynamically.

Lastly, two referenced notes are mentioned for additional context:

1. SAP Note 1833947 describes updates and features for SAP Stack Kernel 721 (EXT) Patch Level 100, including the introduction of the `abap/force_local_update_task` parameter, which is relevant to the issue at hand.

2. SAP Note 1728283 provides general information about the improvements and enhancements available with SAP Kernel 721, offering context on the kernel environment where the parameter `abap/force_local_update_task` would be utilized.

This note is important for system administrators who need to implement local updates systemwide and must be aware of the associated risks and necessary patch levels to enable this feature.