SAP Note 116272 outlines the replacement of the conventional invoice verification transaction MR01 with the logistics invoice verification transaction MR1M, effective as of SAP Release 4.5B. The transition to MR1M began with Release 3.0 and has seen enhancements in subsequent releases, with significant improvements by Release 4.5B and completion expected in Release 4.6C.

This note highlights the advancements of logistics invoice verification, which now offers better ease of operation, no limitations on the number of purchase orders, delivery notes, and invoice line items, cross-company code postings, and a wide range of other functionalities (listed as points 1 to 31). Notably, it now allows for enhanced allocation, settlement of both blanket purchase orders and invoicing plans, advanced tax handling, and support for material ledger and transfer prices.

However, the note also acknowledges existing disadvantages of logistics invoice verification that were still present as of the release of the note, such as the limited capability for direct postings to G/L accounts and assets, lack of support for one-time vendors (until Release 4.6), and various other restrictions (points 1 to 13). SAP encourages users to provide practical examples of functions available in conventional invoice verification to assist in further improving MR1M.

The note also informs that starting with Release 4.6C, maintenance and support for conventional invoice verification (and related functions like Transactions MRHR, MRHG, MR03, etc.) will be completely discontinued, emphasizing the importance of transitioning to MR1M and its related transactions listed in the note to ensure continuity of support and functionality.

In summary, SAP Note 116272 details the enhanced capabilities and remaining limitations of the logistics invoice verification process as the successor to the conventional MR01 transaction, and it marks a significant shift in SAP's maintenance strategy toward logistics invoice verification for future enhancements.