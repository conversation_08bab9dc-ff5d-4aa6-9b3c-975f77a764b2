SAP Note 1849498 provides guidance for reconverting SAP HANA-optimized DataStore objects (DSOs) back to classic DataStore objects, following the recommendations of SAP Note 1849497. This reconversion is recommended because once SAP Note 1849497 is applied, new SAP HANA-optimized DataStore objects can no longer be created, and standard DSOs will perform similarly.

The reconversion process varies depending on whether the DSO contains data:

1. If the DSO does not contain data:
   - Open the modeling of the DataStore.
   - Deactivate the "Optimized for SAP HANA" option.
   - Reactivate the DataStore object.

2. If the DSO contains data:
   - Run transaction SE38.
   - Execute the report "RSDRI_RECONVERT_DATASTORE".
   - Select the DataStores to be converted.
   - Choose whether to retain the change log after conversion.
   - Execute the report.

It is important to carry out these steps in all connected systems—development, quality, and production—to avoid issues during the transport of the objects.

The prerequisites for this reconversion include using SAP HANA Support Package Stack 5 (SP5) with a revision level of 57 or higher.

The note also provides solutions for different versions of SAP NetWeaver BW:
- For SAP NetWeaver BW 7.30, import Support Package 10.
- For SAP NetWeaver BW 7.31, import Support Package 9.
- For SAP NetWeaver BW 7.40, import Support Package 4.

Before applying these support packages, one must refer to the respective SAP Notes for each version mentioned above for more details. In urgent cases, correction instructions may be implemented in advance, and users should consult SAP Note 1668882 for guidance on using transaction SNOTE.

SAP Notes 1849497 and 1849498 work in conjunction, with 1849497 detailing the optimization of standard DSOs for SAP HANA and 1849498 describing the steps for reconverting HANA-optimized DSOs to classic format for consistency and to utilize the improved performance without special modifications.