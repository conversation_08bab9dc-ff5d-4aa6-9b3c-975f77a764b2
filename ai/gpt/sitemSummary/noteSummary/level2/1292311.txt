SAP Note 1292311 addresses issues with Data Transfer Processes (DTP) in SAP NetWeaver BI that are connected to transformations created from migration. There are two main symptoms highlighted:

1. DTP requests termination due to runtime errors `TSV_NEW_PAGE_ALLOCATION_FAILED` or `TSV_TNEW_BLOCKS_NO_ROLL_MEMORY`.
2. Data-based errors lead to DTP requests being terminated with error `RSBK 257`, and detailed error messages that should help identify the cause of the issue are lost.

The root cause of these problems is identified in the note as a program error.

The note provides the following solutions:

- Users with transformation programs that originated from migration can apply corrections using tools discussed in SAP Note 1052648 and SAP Note 1369395. Specifically, using the remigration process or other provided tools can help automatically correct the problematic source code.
- The note includes a tool named `RS_ABAPSOURCE_COMPARE`, which helps to compare the transformation program's source code before and after the remigration to detect if manual adjustments are necessary. This requires copying the old transformation program with the ID beginning with "GP*" to a new version with "ZZ_GP*".

Regarding the software version and necessary patches, the note instructs the import of various Support Packages that are specific to different SAP NetWeaver BI versions. The availability of these Support Packages and additional details are described in other related SAP Notes.

Moreover, if there's an urgent case, users have the option to implement the correction instructions as an advance correction before the Support Packages become available. For this, they should also refer to SAP Note 875986 to understand how to use transaction SNOTE.

Lastly, the note mentions that "Preliminary version" texts could appear before the official release of Support Packages to give customers beforehand information.