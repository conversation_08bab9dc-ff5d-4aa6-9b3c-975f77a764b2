SAP Note 1866467 addresses an issue in the reorganization of profit centers where assets that should be included in an object list are not shown if they are exclusively assigned to a service order (specifically order type 30) as a second-level object. The asset in question in this scenario is indicated as a cost order in its master record.

Key points from this SAP Note include:

- **Symptom**: Failure to include certain assets in the object list during the reorganization of profit centers due to their exclusive assignment to a service order.
- **Other Terms**: The note uses the terms PRCTR (profit center) and CAUFN (generic term for a service order).
- **Reason and Prerequisites**: The root of the problem is identified as a lack of functionality.
- **Solution**: To remedy the issue, the note prescribes specific corrections described in the attached correction instructions. These corrections involve adding new nodes (O30-FA, O30-FA-POA, O30-FA-POA-AP, O30-FA-AP, and O30-FA-AR) and adjusting the derivation hierarchy. Users can either follow manual activities or import the Support Package provided to implement these changes. However, users are warned that existing reorganization plans will not automatically incorporate these changes and will continue using the previously valid derivation hierarchy. Therefore, if someone wishes to use the updated derivation hierarchy, they must create a new reorganization plan and, if necessary, discard the old one.

Additionally, SAP Note 1866467 refers to SAP Note 1471153, which is a composite note that serves as a central reference point for issues and solutions related to the reorganization of profit centers and Funds Management within the new General Ledger functionality. Note 1471153 also discusses related SAP Notes that can be identified using specific prefixes related to the type of reorganization and provides advice on implementation, related conditions, and license fees.