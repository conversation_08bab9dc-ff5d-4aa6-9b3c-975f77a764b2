SAP Note 3044825 addresses an issue encountered in the "Enter Incoming Invoice" transaction (MIRO) when the material ledger is active. Users receive an error message M3 024, stating that the "Valuation data for material & is locked by the user &," when attempting to post an invoice or goods receipt for a material that is already being processed by another user.

The cause for this issue is that the system is exclusively locking the table MBEW for every incoming invoice related to a material purchase order when the material ledger is active. However, this level of locking is not always necessary.

The note provides a solution which involves corrections delivered in specified support packages. Users are advised to implement the relevant correction instructions for an advance solution. Once implemented, the system will perform a more detailed check to determine if locking the material is necessary, thus potentially reducing unnecessary locking conflicts.

There are no references to other documents within this Note.