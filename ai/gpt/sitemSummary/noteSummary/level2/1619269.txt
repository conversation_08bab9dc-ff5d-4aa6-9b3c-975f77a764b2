SAP Note 1619269 addresses an issue in transfer posting processes where balances are saved without an account and/or company code, thus preventing their transfer. The symptom described indicates that although object type implementations determine and transfer balances to the transfer posting program, the system currently lacks a security check to ensure both the account and company code accompany the saved balance.

Key Details:
- **Symptom**: Balances determined for transfer by object type implementations are saved but cannot be transferred if done so without an accompanying account and/or company code. The system does not check for these details before the balance is saved.
- **Other Terms**: The note mentions terms like profit center reorganization, segment reorganization, New General Ledger Accounting, and reassignment that are related to the context of the issue.
- **Reason**: The root cause of this issue is identified as a program error.
- **Solution**: It is recommended to implement the correction instructions attached to the note to resolve the issue.

References within this note:
- SAP Note 1627018: This is a composite note dealing with segment reorganization, serving as a guide for users to find relevant information on segment reorganization within the related notes section. It warns users to check with their SAP Account Executive about additional licensing fees.
- SAP Note 1471153: This composite note relates to profit center and Funds Management reorganization. It provides users with a central reference for all related SAP Notes on these reorganizations and instructs users to check with their SAP Account Executive regarding licensing fees for profit center reorganization.

In summary, SAP Note 1619269 documents a software error and provides correction instructions to address an issue where balances could be saved without necessary information, hampering subsequent transfer posting processes within SAP systems.