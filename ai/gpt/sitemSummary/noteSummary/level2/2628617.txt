SAP Note 2628617 addresses the necessary adaptations for ALE (Application Link Enabling) and BDBG (Business Data Generation) in relation to the changes made to currency amount field lengths in SAP S/4HANA On-Premise edition 1809 and higher. The field lengths for selected currency amounts, which previously ranged from 9 to 22 digits including two decimals, have been extended to a total length of 23 digits including two decimals.

The note specifies that these changes are relevant to those converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher. It highlights the adjustments needed for ALE interfaces that work with BAPIs, ensuring that they can handle the extended field lengths for amount fields.

Key points from this SAP Note include:

- Currency amount fields have been extended to accommodate larger values, and this extension has been realized mainly via domain exchanges of existing data elements.
- An ALE interface for BAPIs needs to be maintained, including the generation of message types, IDoc types, segment types, and ALE outbound and inbound function modules.
- Additional parameters may need to be added to BOR method calls if the extended field is a scalar parameter and a new LONG parameter has been added to the function module's signature.
- Existing IDoc segments that were generated before converting to S/4HANA may need to be repaired to reflect the extension in field lengths.
- ALE interfaces might need to be regenerated using transaction BDBG.
- Custom coding that calls generated ALE outbound function modules needs to adapt to fill both short and long amount fields based on extension capability.
- Changes in custom code should be protected from loss during re-generation following the practices outlined in SAP Note 513785, which details how to ensure custom extensions in source code generated by BDBG/BDFG are not overwritten.

It's recommended that before implementing these changes, one should also refer to related SAP Notes such as 2628040 (general information on the amount field length extension), 2628654 (background, technical details, and implications of the change), 2628724 (adaptations for usages of BAPIs and RFCs), and 513785 (preserving custom code extensions during regeneration).

In summary, this note provides guidance on adapting ALE interfaces and related coding to support the enhancement of currency amount fields in SAP S/4HANA.