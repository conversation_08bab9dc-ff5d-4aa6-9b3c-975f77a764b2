SAP Note 2364207 addresses performance issues encountered with the synchronization of employee data to business partners in SAP S/4HANA. Specifically, it highlights the challenges of synchronizing a large number of employees, as the process can be time-consuming and does not support parallel processing due to the absence of a selection screen in the daily sync job.

The purpose of this note is to provide a solution for customers who need to process the synchronization job in parallel sessions utilizing a selection screen. To implement the solution, certain steps must be followed, which are detailed in an attached document that comes with the note.

As a prerequisite, customers are required to import SAP Note 2364202 before implementing the solution from SAP Note 2364207. SAP Note 2364202 details an enhancement to the Synchronization API that introduces a "lock identifier parameter." When enabled, this parameter allows the Synchronization report to run in parallel without being locked, thereby avoiding conflicts when it is called from multiple reports or when multiple instances are scheduled to run at the same time.

Overall, SAP Note 2364207 is intended to enhance performance and parallel processing capabilities during the employee to business partner synchronization process in SAP S/4HANA for customers with a large employee base.