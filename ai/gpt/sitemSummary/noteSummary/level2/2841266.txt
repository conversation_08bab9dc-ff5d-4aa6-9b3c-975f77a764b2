SAP Note 2841266 is a release note for the Support Package 06 (SP06) of "PLVAT 100", which is part of the "SAP ERP, add-on for Polish SAF-T regulatory requirements 1.0". The primary purpose of this package is to bring the "PLVAT 100" software in line with the latest Polish SAF-T (Standard Audit File-Tax) requirements.

Key highlights from the note:

- SP06 includes new features for compatibility and functionality, such as:
  - Compatibility with S/4HANA 1909.
  - Implementation of a new version of the JPK FA(2) structure for VAT Invoices.
  - Introduction of a new structure, JPK_FA_RR, for RR Invoices, with data import from text files only.
  - Expanded support for a wider range of ledgers in R3 and S4 systems concerning Accounting Books.
  - Accounting Books support for second and third local currency in the balance sheet and in the journal.

- To benefit from the additions in SP06, PLVAT 100 SP05 must already be installed on the SAP system.
- The documentation (Configuration Guide 1.13 and Application Help 1.11) has been updated to reflect the changes in SP06 and is available on the SAP Help Portal.

The note outlines detailed steps for SAP customers who have purchased the license to:
1. Download the new support package from the SAP Service Market Place, specifically the PLVAT 100: SP 0006 SAR file.
2. Upgrade the software by importing the support package using the SPAM transaction and download the BC Set attached to this note.
3. Configure the software, including instructions to set the new parameter FA_DOWNPAYMENT_ACCOUNTS.
4. Access the updated documentation on the SAP Help Portal.
5. Understand additional information regarding import file structures and support procedures through the specified SAP component XX-PROJ-CDP-571.

The software and documentation are only visible and accessible to customers who have purchased the license for this particular add-on. This note serves as a comprehensive guide to acquiring, upgrading, and configuring the latest support package to ensure compliance with the SAF-T obligations in Poland.