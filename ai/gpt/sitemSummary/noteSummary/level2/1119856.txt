SAP Note 1119856 provides a comprehensive explanation and guidance on the use of Attribute Change Packages (ACPs) in the context of the Support Package Manager (SPAM) and the Add-On Installation Tool (SAINT). ACPs were introduced with SPAM/SAINT Version 0025 and are designed to deliver and correct the import attributes of ABAP OCS packages (such as Support Packages and Add-On Installation/Upgrade Packages) after they have been released.

Key points from the note:

1. **Technical Background**: ACPs are a technical OCS package type that only contains attributes, primarily to modify the attributes of already downloaded OCS packages without necessitating a redownload. ACPs only change package attributes and do not contain transport objects, which means they do not require actual import but take effect upon upload to the system.

2. **Naming Convention**: The name of an ACP is derived from its software component version, which is composed of the first 10 characters for the software component and the version of the software component starting from the 11th character. ACPs are updated whenever there's a change in attributes, containing the latest attributes for all the OCS packages of a specific software component version.

3. **Usage**: ACPs are used to modify package attributes for enhancing import prerequisites. They can be included in normal delivery archives or downloaded separately from SAP Service Marketplace.

4. **FAQs, Tips and Tricks**:
   - ACPs are created when needed, and there are no fixed dates for their creation.
   - ACPs can be obtained integrated within CAR/SAR archives of normal delivery packages or downloaded directly from SAP Service Marketplace.
   - To use ACPs included with normal delivery packages, you can use the standard upload functionality in SPAM/SAINT, and for those downloaded directly or attached to a note, you should load the SAR or CAR archive.
   - For proper processing of ACPs, SPAM/SAINT Version 0025 or higher is required.
   - It is important to load the latest version of the ACP to ensure the system is updated with the most recent attribute modifications.

The note also contains references to other related documents and notes that help to better understand the prerequisites, the issues that may arise with support packages, and the handling of certain situations like Vendor Key handling during updates or upgrades.

This SAP Note is essential for administrators and users dealing with the upload and maintenance of OCS packages in their SAP systems, ensuring the most efficient and correct process is followed.