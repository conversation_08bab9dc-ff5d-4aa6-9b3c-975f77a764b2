SAP Note 2057542 provides recommendations on using HANA-based transformations in SAP BW with version 7.4 SP05 or higher. The main advantage of using HANA-based transformations is the enhanced performance due to data not needing to be moved between the application server and the database server and the ability to utilize the full processing power of the HANA database. Lab results have shown performance improvements ranging from 3 to 10 times, although for low data volumes, the improvement may be negligible.

However, not all transformations can be executed in HANA. Specifically, those transformations that involve custom ABAP code cannot be pushed down to HANA directly. Instead, users must either rewrite the logic using SQL Script (preferably as an ABAP-Managed-Database-Procedure or AMDP) or use standard transformation features as much as possible. The online help provides a detailed list of what is supported for push down to HANA. Users can check their transformations for HANA compatibility within the transformation maintenance screen.

Before converting a transformation to “SAP HANA execution” mode, users should consider if the performance gain is significant and critical. It is suggested to identify long-running DTPs/transformations and consider the feasibility of rewriting custom ABAP code to SQL Script after a thorough analysis and risk assessment. Decisions should be made based on multiple factors including the cost drivers, maintenance feasibility of the SQL Script, and complexity of implementing the business logic. 

Users are encouraged to use formulas in transformations because they are likely to be supported for push down and are easier to analyze and support. Some features will not be supported for push down, and the "SAP HANA execution" mode should be considered as an alternative rather than a complete replacement to the current ABAP-based execution mode, which will continue to be supported and improved. 

Ultimately, the note lays out the potential benefits of using HANA-based transformations for performance improvement, while also cautioning about the need for careful evaluation and planning when modifying existing transformations for HANA compatibility.