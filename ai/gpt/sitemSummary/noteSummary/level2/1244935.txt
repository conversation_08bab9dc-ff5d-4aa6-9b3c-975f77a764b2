SAP Note 1244935 addresses an issue where an endless loop occurs in the method 'ALL_OPERANDS_GET_INTERNAL' of the class 'CL_FOBU_FORMULA' during the migration of transfer rules or update rules, which can potentially lead to memory overflow problems. The issue occurs when formulas are used in those rules.

The problem is identified as a program error, and SAP provides solutions in the form of specific support packages for different NetWeaver BI versions:

- For SAP NetWeaver BI 7.00, customers should import Support Package 20 (SAPKW70020).
- For SAP NetWeaver BI 7.01 (Enhancement Package 1), Support Package 03 (SAPKW70103) should be imported.
- For SAP NetWeaver BI 7.10, Support Package 07 (SAPKW71007) should be imported.
- For SAP NetWeaver BI 7.11, Support Package 01 (SAPKW71101) should be imported.

Each support package is available after the release of corresponding SAP Notes that give more details on these packages:

- Note 1136885 for NW7.01 BI SP 20
- Note 1227875 for NW 7.01 BI ABAP SP 04
- Note 1158395 for NW 7.10 BI SP 7
- Note 1011771 for NW 7.11 BI SP 00

In urgent cases, SAP provides the option to implement correction instructions as an advance correction. Before doing so, customers must read Note 875986 about using transaction SNOTE (which is how you would apply such corrections).

The SAP Note also refers to Note 1052648, which discusses the migration process for transfer and update rules into SAP BW 7.x and provides an overview of known limitations and issues with the migration tool. It advises that manual adjustments may be required post-migration and provides a list of solutions for common errors in migration.

This information is useful for SAP NetWeaver BI administrators who are planning to migrate transfer rules or update rules and may encounter an endless loop issue in the process. With the prescribed support packages, the issue can be resolved according to the version of SAP NetWeaver BI in use.