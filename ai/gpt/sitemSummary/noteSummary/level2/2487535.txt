SAP Note 2487535 addresses the transition of personalization features in SAP Business Explorer (BEx) as organizations migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge. It specifies that traditional BEx personalization is not supported in these newer environments, making classic DataStore Objects (e.g., 0PERS*) unavailable.

For customers targeting SAP BW/4HANA, the note clarifies that when moving from Compatibility Mode to B4H Mode, the system preserves personalization settings by storing them in transparent tables. It provides a table mapping classic DataStore Objects to their corresponding database tables in SAP BW/4HANA.

For those targeting SAP Datasphere, SAP BW bridge, the note informs that this environment serves as a staging layer without support for the OLAP engine or related OLAP functionalities, which implies that BEx Personalization won't be supported either.

The note mentions prerequisites, like running the program RS_B4<PERSON>NA_RC to identify objects available or convertible to the new platforms, and it also includes links to additional information on personalization in BEx.

Furthermore, references are provided to other related SAP Notes:
- SAP Note 2487597 discusses issues and solutions related to the conversion of DataSource Fields into InfoObjects when migrating to SAP BW/4HANA or SAP Datasphere.
- SAP Note 2451013 explains the unavailability of classic DSOs in SAP BW/4HANA and how to convert them to modern ADOSs using the SAP BW/4HANA Transfer Tool.
- SAP Note 2421930 elaborates on the Simplification List for SAP BW/4HANA which helps customers understand the functional changes and consolidations during their transition to the new platform.

In essence, SAP Note 2487535 is a guide for handling personalization features as businesses upgrade their SAP BW technology to SAP BW/4HANA or use SAP Datasphere, SAP BW bridge, emphasizing the need to adapt to the new system capabilities and reiterating the discontinued support for certain legacy functionalities.