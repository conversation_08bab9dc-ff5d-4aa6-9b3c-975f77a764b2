SAP Note 2270527 addresses the discontinuation of simulations in transaction management with the transition to SAP S/4HANA. This note is relevant for customers planning to convert their systems to SAP S/4HANA and includes the following key points:

1. **Discontinuation of Simulation Feature**: The simulation feature available in transaction management in previous SAP versions will not be available in S/4HANA.

2. **Pre-Upgrade Check Warning**: A pre-upgrade check (check ID SI13_SIMULATION) may issue a warning if simulations were found in the system's clients. However, this warning is just for informational purposes, and no mandatory action is expected to be taken upon receiving this message.

3. **Custom Code Analysis**: If custom code analysis detects any customer-specific coding related to simulation functionality, the note recommends adapting the custom code accordingly. This is because SAP objects that were used by the detected customer code will no longer be supported in S/4HANA.

The note references two other important SAP notes:

- **SAP Note 2294371**: It provides information on the implementation of the EA-FINSERV check class for system conversion checks to S/4HANA. The note gives details on the technical necessities for pre-conversion checks.

- **SAP Note 2190420**: It offers guidelines on adapting customer-specific ABAP code for compatibility with SAP S/4HANA. Recommendations include code housekeeping, running code checks, optimizing for SAP HANA, setting testing strategies, and adhering to best coding practices. It also mentions the regular checkups, especially before major lifecycle events like conversions or upgrades, and the use of ABAP Test Cockpit for S/4HANA-specific custom code checks.

In summary, Note 2270527 informs users about the unavailability of the simulation function in transaction management after migrating to S/4HANA and what to do if they encounter warnings about this during the pre-upgrade checks or custom code analysis. It also highlights the need to adjust any affected custom code to ensure compatibility with S/4HANA.