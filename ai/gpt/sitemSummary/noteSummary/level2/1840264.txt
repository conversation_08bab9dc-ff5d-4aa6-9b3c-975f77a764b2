SAP Note 1840264 addresses the issue of reducing the wait time during late locks that occur when there are blocking attempts during goods movements. In the standard system behavior, the wait time is set to one second, which can be configured using transaction OMJI or the integration guide.

The solution provided in this note includes implementing the correction instructions or importing the relevant Support Package. Post-implementation, it will be possible to define the waiting time in milliseconds, which can be less than one second. To accomplish this, the new Business Add-In (BAdI) MB_LATE_LOCK_BADI needs to be implemented.

The BAdI comes with a fallback implementation (CL_MB_LATE_LOCK_BADI_FB) that serves as a sample. Within the BAdI method GET_LOCK_OPTIONS, users can assign their own values to the structure ES_MBLWS to control the waiting time and number of retries:

- MBLWA = 1: The waiting time is set by the customer's implementation of the BAdI.
- MBLWA = 2: The waiting time is based on the standard value from TCURM-MBLOCKWAIT.
- MBLWT: Specifies the waiting time in milliseconds (set via customer implementation).
- MBLWR: Defines the number of retries for the late lock (set via customer implementation).

The note provides an example of field assignments in a custom implementation and advises users to perform individual load tests to find the optimal waiting time and number of retries for their specific environment. Additionally, there is flexibility in the implementation to cater to different processing modes or user types by assigning varying waiting times.

Should no custom values be set, the system defaults to the standard behavior of waiting ten times for one second. This note does not reference any other documents.