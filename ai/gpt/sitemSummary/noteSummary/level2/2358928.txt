SAP Note 2358928 outlines the status and support of Catch Weight Management (CWM) in SAP S/4HANA. CWM is available from SAP S/4HANA On-Premise 1610 FPS01 onwards. However, several features and aspects of previous CWM versions (old architecture from ERP 6.0 EHP3 and before) are not supported in S/4HANA, such as the parallel unit of measure with category 'B', the Flexible Material Prices (FMP) application, and old BOR objects and BAPIs. 

Important points from the note include:

1. The old CWM architecture is not supported in SAP S/4HANA. Customers are advised to contact the CWM Solution Management before migrating from the old to the new architecture. CWM migration tools are provided starting from S/4HANA 1709. References to specific related notes are included for guidance on migration (e.g., Note 1450225).

2. For materials with unit of measure category 'B', this category will not be supported because it's reserved for Extended Warehouse Management (EWM) in SAP S/4HANA. Transactions involving materials with unit of measure category 'B' should be checked and updated accordingly, with further instructions available in SAP Note 2358909.

3. The Flexible Material Prices application is not supported, and businesses should check if they use FMP through specific database tables and adjust their processes (reference Note 2414624 for more details).

4. CWM BOR objects and related BAPIs, IDocs, and ALE functions are not available in S/4HANA, and enhancements are made to standard BAPIs for CWM functionality. Custom coding using obsolete objects will need to be updated.

5. Screen sequences 'CW' and 'CE' for the Material Master are no longer delivered in S/4HANA. The related CW information is now integrated into the standard screen sequence '21'. Customized entries should be reviewed and adjusted.

6. Certain table attributes that were previously used in CWM are no longer supported and should be replaced with new standard fields.

7. Certain BAdI definitions specific to CWM are removed, and any customer-specific implementations related to these are obsolete and can be deleted.

References to multiple other SAP notes are provided to guide users through the impacts of these changes and additional checks required before and after transitioning to SAP S/4HANA. Users are encouraged to contact CWM Solution Management with questions or concerns regarding these transitions and updates.