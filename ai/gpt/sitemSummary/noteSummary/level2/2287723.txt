SAP Note 2287723 provides guidance on using Legacy System Migration Workbench (LSMW) for data migration to SAP S/4HANA. It highlights that LSMW is no longer the recommended tool for data migration to SAP S/4HANA, including both the on-premise and cloud versions. Instead, SAP suggests using the SAP S/4HANA migration cockpit.

Key points of this note include:

- **Symptom:** LSDMW's utility is restricted in the context of data migrations to SAP S/4HANA, as it may propose outdated migration interfaces and has no support for S/4HANA migrations.

- **Solution:** Instead of LSMW, SAP recommends using the SAP S/4HANA migration cockpit, which provides predefined migration content and supports two migration approaches: 
  1. Migrate Data Using Staging Tables
  2. Migrate Data Directly from SAP System (available from SAP S/4HANA 1909 for On-Premise only)

- **LSMW Use at Own Risk:** If LSMW is still to be used, the customer must thoroughly test it to ensure its compatibility with SAP S/4HANA. Some functions, like transaction recording and standard batch input programs, may no longer work or may have changed in S/4HANA.

- **Custom Data Integration:** The migration cockpit allows for the integration of custom data objects using the migration object modeler.

- **Roles Required:** Different roles are required for the file and staging approaches, and the direct transfer approach, which are outlined in supporting documents on the SAP Help Portal.

- **Further Information:** Links to additional documentation and lists of available migration objects for both SAP S/4HANA on-premise and cloud versions are provided.

Additionally, the note recommends SAP Rapid Data Migration with SAP Data Services as an alternative migration tool, especially for on-premise installations. This tool will be maintained for migrating data into SAP S/4HANA, although no new objects will be created in favor of the migration cockpit content.

The note is relevant for customers implementing SAP S/4HANA and advises them to consider transitioning to recommended data migration tools instead of continuing with LSMW. The implication is that LSMW is increasingly being phased out in favor of tools that are better integrated with the new features and data structures of SAP S/4HANA.