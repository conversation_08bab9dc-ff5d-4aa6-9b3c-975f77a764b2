SAP Note 1552845 identifies an issue where an authorization check is missing when users access the plan overview in SAP. It points out that the authorization check is performed only upon navigation to the plan details, not at the overview level.

Key Points of SAP Note 1552845:

1. **Symptom**: Users can access the plan overview without an authorization check, potentially allowing unauthorized viewing of information.

2. **Other Terms**: The note references terms relevant to the issue, like F_REORG_PL (which likely represents an authorization object for reorganization plans), FAGL_REORGANIZATION126 (which may be a transaction or program linked to the issue), reorganization, profit center, and FM (Funds Management) reassignment.

3. **Reason and Prerequisites**: The note indicates that the cause of this issue is a programming error, suggesting that the underlying software code is not performing the expected checks.

4. **Solution**: The proposed resolution is to implement corrections, but details on these corrections are not provided in the summary. This typically involves either applying a patch or manual adjustment in the system as directed by the detailed content in the full note.

The referenced SAP Note 1471153 relates to reorganizations involving profit centers and Funds Management, providing users with a composite guide to addressing various related issues within the context of the new General Ledger functionality. However, specifics about the correlation between the two notes, such as whether the missing authorization check in SAP Note 1552845 is directly connected to transactions or processes discussed in SAP Note 1471153, are not provided in this summary.

To summarize SAP Note 1552845, users should be aware of an existing program error that fails to perform an authorization check on the plan overview screen and should take steps to implement corrections as prescribed in the full text of the note to ensure proper authorization controls are in place.