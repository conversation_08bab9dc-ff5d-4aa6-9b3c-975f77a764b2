SAP Note 2496706 addresses the deployment and limitations of the Business Explorer (BEx) Web Application Designer (WAD) and BEx Web functionality in the SAP BW/4HANA environment. It specifically discusses how customers can continue using elements of WAD/BEx Web functionality in SAP BW/4HANA despite BEx being phased out in favor of SAP Analytics Cloud.

**Key aspects of the note:**

- **Symptom**: Customers transitioning to SAP BW/4HANA wish to continue using certain parts of WAD/BEx Web functionality.

- **Reason and Prerequisites**: To support customers during their transition to SAP Analytics Cloud, SAP has introduced a pilot program that allows temporary use of existing BEx Web Applications in SAP BW/4HANA. This program has been extended to the end of 2025. Prerequisites for using this feature include specific versions of SAP BW, BW/4HANA Starter Add-On, SAP NetWeaver BI, BI Add-on for SAP GUI, and implementation of certain SAP Notes specified in the document.

- **Solution**: To access this functionality, customers must participate in the pilot program, which requires them to open an OSS (Online Service System) message. The solution involves enabling a dedicated code change to use WAD and BEx Web functionality in SAP BW with the BW/4HANA Starter Add-On installed, and setting the RSADMIN parameter as indicated in the pilot note. 

- **References to other SAP Notes**: 
  - SAP Note 2620191 discusses an issue with connecting to a BW system during in-place conversion to BW4HANA via BEx Analyzer.
  - SAP Note 2542290 guides how to enable WAD/BEx Web functionality in BW/4HANA.
  - SAP Note 2444138 refers to the redesign of BEx objects not supported in BW/4HANA.
  - SAP Note 2383530 details conversion methods from SAP BW to SAP BW/4HANA.
  - SAP Note 2246699 elaborates on the pre-requisites, installation, and other processes related to the SAP BW/4HANA Starter Add-On.

The note is essential for customers who are in the process of transitioning from SAP BW to SAP BW/4HANA and wish to maintain certain BEx functionalities during this transition phase. By following the guidelines and becoming part of the pilot program, users can continue to use their current BEx Web Applications until they are able to complete the conversion of their reports and applications to newer SAP Analytics tools within the extended timeline.