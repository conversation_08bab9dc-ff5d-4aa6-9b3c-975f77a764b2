SAP Note 2228139 addresses a specific issue for customers in the automotive sector who are planning to migrate their SAP ERP system to SAP S/4HANA. The note highlights that the DIMP (Discrete Industries and Mill Products) industry solution for tracking inbound deliveries and shipments is not available in SAP S/4HANA. Consequently, custom code that utilizes related SAP objects needs to be adapted for compatibility with the new system.

The symptom section clarifies that during a system conversion to SAP S/4HANA, the custom code check might identify customer objects that are affected by these simplifications. This occurs because SAP objects used within these customer objects may have changed in a way that is not syntactically compatible with SAP S/4HANA.

The solution prescribed in the note is to remove the usage of affected SAP objects from the custom code. Specifically, it mentions the need to remove usages of objects in the piece list SI_AUT_01_TRI as dictated by the custom code check.

The note references SAP Note 2190420, which provides a broader set of recommendations for adapting custom specific code for SAP S/4HANA, outlining best practices and tools that were applicable in SAP ERP and remain relevant in the context of SAP S/4HANA. It suggests that most custom code from SAP ERP is compatible with only minimal changes required for SAP S/4HANA. It also emphasizes the importance of regular checks, optimizations, and testing to maintain compatibility, especially before lifecycle events like conversions and upgrades. The referenced note also explains that SAP provides specific custom code checks for S/4HANA to identify necessary adaptations and stresses the critical role of developer assessment to determine the precise adaptations needed based on the functional and business context.

In conclusion, SAP Note 2228139 is about the necessary adaptations for custom code related to the DIMP Automotive solution, which is no longer supported in SAP S/4HANA, and references additional resources for guidance on custom code adaptation overall.