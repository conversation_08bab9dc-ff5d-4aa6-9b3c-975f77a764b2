SAP Note 2900458 details guidelines for users upgrading to S/4HANA 1809 or greater when migrating action definitions to task definitions and specifically addresses how to manage the attachments associated with these definitions.

Summary of SAP Note 2900458:

**Symptom**:
- Users upgrading to S/4HANA 1809+ must migrate action definitions to task definitions. During this process, attachments from action definitions are transferred to task definitions and the originals are deleted.
- Users need options to skip or modify the standard attachment migration process.

**Other Terms**:
- Files, Enhancement, Customer Development

**Reason and Prerequisites**:
- Performance concerns or specific requirements for attachment migration.
- Prerequisites include migrating from an older version than S/4HANA 1809, not having run the migration program R_EHFND_WFF_RESTART_PROCESSES or having run it only in test mode, and already having executed XPRA R_EHFND_XPRA_WFF_MIGRATION.

**Solution**:
Users have three options for managing the attachment migration:

1. **Skip attachment migration**:
- Create an overwrite-exit method for method `CREATE_CORRESPONDING_ATT` in class `CL_EHFND_WFF_MIGRATOR_ACTION` and leave it empty.
- Run the remaining steps of the migration program R_EHFND_WFF_RESTART_PROCESSES normally.

2. **Change the migration process**:
- Create an overwrite-exit method and implement customization within it.
- Avoid committing changes in this custom code; the migration framework will handle commits.
- Use the R_EHFND_WFF_RESTART_PROCESSES program in test mode to debug.
- Run the migration program normally to complete the process.

3. **Migrate attachments after the main migration process**:
- Same initial steps as option 1 to skip standard attachment migration.
- After running the migration program, implement custom logic to migrate attachments.
- A program template is provided in an attached .TXT file, which can be copied and customized for the attachment migration.

The note mentions a program with specific selection texts such as "Created on", "Host Object Class", "Task Definition Key", "Start Date", and "Status" to assist users in running attachment migration. Users have the ability to filter and run migrations in smaller batches or across multiple work processes.

No references are provided in this note. The purpose of the note is to enable users to adapt the migration of action definition attachments to fulfill specific performance requirements or custom needs during the upgrade process.