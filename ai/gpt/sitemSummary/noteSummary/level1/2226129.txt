SAP Note 2226129 addresses the changes in data structures for the Budget Control System (BCS) in the SAP S/4 HANA environment. Here's a summary of the note:

**Symptom:**
Customers who have custom coding based on the BCS database tables could be impacted.

**Reason:**
With the transition to SAP S/4 HANA, there have been changes in how data structures for the BCS are updated and used.

**Solution:**
1. Customers are advised to use the transport piecelist SI_PSM_BCS to identify any custom coding that might be affected by the changes in BCS data structures.
2. Details of the changes include:
   - For budget totals (table FMBDT): Totals are no longer stored directly but are instead calculated from the line items in table FMBDP. A compatibility view (CDS view V_FMBDT) acts as a proxy for table FMBDT.
   - For Revenue Increasing Budget (RIB) totals (table FMRBT): RIB functions are restored by reinitializing the fiscal year data via transaction 'FMRBREINIT', populating the line item table FMRBP. The table FMRBT will not be updated, and a compatibility view (CDS view V_FMRBT) is attached.
   - For Active Availability Control (AVC) totals (table FMAVCT): Reinitialization of AVC data through transaction FMAVCREINIT will update change records in table FMAVCT_RECON_YR for migrating customers. New customers will have change records from the beginning without updating the totals table. A compatibility view (CDS view V_FMAVCT_COMPAT) is provided as a proxy for table FMAVCT.

3. The compatibility views ensure that read access can still obtain the totals as if they were stored in the original tables, though write access to these totals tables could cause inconsistencies and is not advised.
4. Customers should adapt custom code to use read access directly through the item tables instead of the totals tables.

**References:**
The note refers to SAP Note 2190420, which provides recommendations for adapting customer-specific ABAP code to be compatible with SAP S/4HANA. It emphasizes that most custom code from SAP ERP will work in SAP S/4 HANA with minimal changes. Tools and processes for developing and maintaining custom code in SAP ERP are also applicable to SAP S/4 HANA. The SAP Note 2190420 suggests using the ABAP Test Cockpit (ATC) for checking custom code compatibility with SAP S/4 HANA and highlights the importance of regular testing, especially before lifecycle events like conversions and upgrades.

In conclusion, SAP Note 2226129 details the restructuring of BCS table updates in SAP S/4 HANA, providing guidance for adapting custom code to align with these changes, ensuring data consistency, and leveraging improved flexibility within the new system's architecture.