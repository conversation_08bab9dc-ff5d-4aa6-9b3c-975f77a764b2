SAP Note 2988692 provides comprehensive information regarding the different versions of the SAP S/4HANA Migration Cockpit, detailing the transition from the Web Dynpro-based application (the old version) to the newer SAP Fiori application for data migration.

**Key Points of the SAP Note:**

- **Two versions**: There are two versions of the SAP S/4HANA Migration Cockpit: the legacy Web Dynpro-based application (Migrate Your Data – Migration Cockpit (Old)) and the newer SAP Fiori application (Migrate Your Data - Migration Cockpit).

- **Deprecation of the old version**: 
   - **For Cloud**: The old Web Dynpro-based application can no longer be used to create new migration projects from SAP S/4HANA Cloud 2105 onwards but existing projects can be viewed.
   - **For On-Premise**: The LTMC transaction used in SAP S/4HANA 2020 allows access only to old projects, and from SAP S/4HANA 2021, new projects can't be created. Existing projects can be viewed.
   
- **Availability of the new Fiori app**: 
   - The new Fiori version is available starting with SAP S/4HANA Cloud 2008 and SAP S/4HANA 2020.
   - Projects created in the old version cannot be accessed in the new Fiori app.

- **Recommendations**:
   - Users of SAP S/4HANA 2020 and SAP S/4HANA Cloud 2008 or higher should use the new Fiori app for migration projects.
   - Those using the old version should plan to switch to the new app as soon as possible, especially if their go-live is post the deprecation dates mentioned above.

- **Technical restrictions**: Projects created with either the old or new app are not interchangeable – they cannot be accessed or used by the other version.
  
- **No app-specific migration objects**: The migration objects themselves can be used by both the apps.

- The note also contains references to several FAQs and other documents that provide further information about the SAP S/4HANA Migration Cockpit, and it emphasizes regularly checking for updates and planning transitions to the new app well in advance of the deprecation dates to avoid migration issues.