SAP Note 2227532 addresses changes in subcontracting within material requirements planning (MRP) as customers transition from SAP ERP to SAP S/4HANA. It focuses on alterations to the planning logic for parts provided to subcontractors.

**Symptom**:
Custom code checks may display incompatibilities due to syntactical and semantical changes of SAP objects upon conversion to SAP S/4HANA.

**Other Terms**:
Terms such as S/4 simplification, PP, PP-MRP, Subcontracting, MRP area, and piece lists are referenced.

**Reason and Prerequisites**:
The note explains that previously, SAP ERP supported three ways of planning for subcontracting, and with the release of SAP S/4HANA on-premise edition 1511, the 4.0 planning logic was removed, and the 4.5 planning logic was streamlined. In S/4HANA, MRP uses default parameters if material master records for MRP areas are not maintained; an MRP area for each subcontractor is sufficient.

**Solution**:
Customers must modify or delete any custom code using SAP objects that do not align with the updated behavior in S/4HANA. This might involve examining whether existing custom reports or data evaluations can be substituted by new SAP S/4HANA apps like the MRP cockpit. If the code changes are still required, it should be adapted to the new logic.

Specifically, customers are encouraged to:

1. Implement subcontracting MRP areas in customizing without forcibly assigning materials in the master data.
2. Adjust code evaluations related to subcontracting processes, shifting the checks to the presence of subcontracting MRP areas for materials using function modules like MD_MRP_AREA_GET.
3. Review and potentially revise business add-ins associated with legacy logic (e.g., MD_SUBCONT_LOGIC), as they may not be processed in S/4HANA.
4. Address changes to custom code that affects how planning data is read and evaluated. The note particularly points to the need to adapt function module calls to consider MRP areas.
5. Thoroughly test custom code, bearing in mind that some changes in behavior might only appear in specific conditions relating to master and transactional data.

References are made to SAP Note 2190420, which provides an overarching guide for adapting custom code for S/4HANA, maintaining compatibility of custom developments, and recommendations for using the ABAP Test Cockpit and Custom Code Migration app for compatibility checks.

In essence, SAP Note 2227532 outlines the necessary steps and considerations for adjusting customer-specific coding in the area of subcontracting MRP within the SAP S/4HANA environment. It directs customers on how to deal with the removal of legacy logic, the adoption of the streamlined MRP area planning, and the requirement for accurate testing and adaptation of code for compatibility.