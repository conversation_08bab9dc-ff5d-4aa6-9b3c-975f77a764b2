SAP Note 932272 discusses the transition from using FI-SL ledger 0F for functional area reporting in classic General Ledger (G/L) Accounting to using the new General Ledger Accounting system available in SAP, specifically the considerations required when adopting the new system to ensure proper management and reporting of the functional area.

Here's a summary of the note:

1. **Discontinuation of FI-SL 0F**: The note addresses the discontinuation of ledger 0F in FI-SL due to the introduction of CSA (cost of sales accounting) reporting in the new G/L system.

2. **Conditions for G/L Functional Area Management**: In the new G/L Accounting, functional areas can be managed if the correct scenarios are used since the first posting or if the migration process considers the proper implementation of the functional area.

3. **Migration Considerations**: SAP Notes 1154791 and 1070629 need to be considered before migrating to the new G/L system. Adjustments related to functional area derivation should be done prior to the migration key date (the start of the new fiscal year).

4. **Required Settings**: Specific indicators must be activated in table FAGL_ACTIVEC to enable proper derivation and persistence of the functional area for document entry, CO postings, and AC-INT postings.

5. **Real-Time Integration Customizing**: For classic G/L, the real-time integration feature of the new G/L can be set up using report RFAGL_SWAP_IMG_SEW or transaction FAGLCOFIIMG.

6. **Post-Migration Reporting**: After migration, functional area reporting can only take place in the new G/L if all CO postings in the migration year are properly updated with the functional area and customized correctly.

7. **Transition Period**: If conditions are not met, FI-SL ledger 0F should be used for a transition period. Notably, functional area does not carry forward during balance carryforward, making it possible to activate CSA reporting in the new G/L system later without a full migration.

8. **Adjustment Postings and CSA Reporting in FI-SL**: FI-SL offers the flexibility to selectively transfer or manually adjust postings without affecting FI and CO processes.

9. **S/4HANA Considerations**: When migrating to S/4HANA, CSA reporting's usability is dependent on the customization of the previous system.

10. **CO Company Code Clearing Lines Enhancement**: SAP Note 2680760 introduces enhancements for processing functional area derivation for company code clearing lines, which is significant for cross-company-code postings.

11. **Advantages of FI-SL Reporting**: Starting from SAP Note 2165793, COFI postings are transferred to FI-SL in SAP S/4HANA, where SL allows exclusion of updates and flexible handling through selective deletion of ledger 0F postings, subsequent posting, and manual adjustment.

In summary, the note provides a framework for businesses transitioning to the new G/L Accounting system to ensure that functional area reporting can effectively take place in the G/L. It underscores the necessary preparations, settings, customizing steps, and considerations for those migrating from classic G/L or to SAP S/4HANA.