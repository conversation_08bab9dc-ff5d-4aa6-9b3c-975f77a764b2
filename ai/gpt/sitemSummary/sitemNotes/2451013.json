{"Request": {"Number": "2451013", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 394, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018760712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002451013?language=E&token=8E47A0D793C45E4BBAAF0CD855D8BF88"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002451013", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002451013/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2451013"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2451013 - BW4SL - DataStore Objects (classic)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The SAP BW DataStore Object (classic) is not available in SAP BW/4HANA. It can be converted to a DataStore Object (advanced) via the SAP BW/4HANA Transfer Tool.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ODSO, DODS, ADSO, DSO</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Run program RS_B4HANA_CONVERSION_CONTROL to determine which objects are available in or can be converted to SAP BW/4HANA:</p>\r\n<p>See node: \"Supported by Transfer Tool\" --&gt; \"BW4 Transfer Tool\"</p>\r\n<p>For the in-place conversion your system needs to be on SAP BW 7.5 SP 5, powered by SAP HANA or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Under node \"Supported by Transfer Tool\" --&gt; \"BW4 Transfer Tool\" --&gt; \"TLOGO ODSO (DataStore Object (classic))\" the DataStore Objects&#160;are listed. They can be transferred \"in-place\" or via \"remote conversion\" to DataStore Objects (advanced) (ADSOs).</p>\r\n<p>Transfer:</p>\r\n<ul>\r\n<li><strong>Write-optimized ODSOs</strong> used in Queries or MultiProviders</li>\r\n<ul>\r\n<li>Will be transferred into&#160;ADSOs with option \"Keep inbound data, extract from inbound queue\".</li>\r\n<li>Please note that the write-optimized ODSO needs to have a semantic key. Write-optimized ODSOs w/o semantic key cannot be transferred and will cause the Transfer Tool to stop. Remove the ODSO from the MultiProvider or delete the Queries to continue.</li>\r\n<li>The data won't automatically be activated after the transfer.&#160;</li>\r\n</ul>\r\n<li><strong>Write-optimized ODSO</strong> not used in Queries or MultiProviders</li>\r\n<ul>\r\n<li>Will be transferred into ADSOs without activation.</li>\r\n<li>Please note that ADSOs provide more flexibility for write-optimized use-cases like corporate memories.</li>\r\n</ul>\r\n<li><strong>Standard ODSOs</strong></li>\r\n<ul>\r\n<li>Will be transferred into ADSOs with activation and change log.</li>\r\n</ul>\r\n<li><strong>ODSO for direct update</strong></li>\r\n<ul>\r\n<li>Will be transferred into ADSOs for direct update.</li>\r\n</ul>\r\n<li>In ODSOs, if a characteristic is key, compounding characteristics did not have to be in the key as well. This has changed for ADSOs. The transfer tool will check and adapt the key during transfer, so that also the compounding characteristics are included in the key automatically.</li>\r\n<li>The Transfer Tool will automatically replace process chain variant \"Activate Data in DataStore Objects (classic)\"&#160;with process chain variant \"Activate Data in DataStore Objects (advanced)\".</li>\r\n<li>The Transfer Tool will automatically replace process chain variant \"Deletion of Requests from Change Log of DSO (classic)\" and \"Deletion of Requests from Write-optimized DSO (classic)\" with process chain variant \"Clean Up Old Requests in DataStore Objects (advanced)\".</li>\r\n</ul>\r\n<p>Restrictions:</p>\r\n<ul>\r\n<li>In-memory optimized DataStore Objects: ODSOs with option \"HANA-optimized\" cannot be converted into ADSOs. Use report&#160;RSDRI_RECONVERT_DATASTORE to revoke the option first.</li>\r\n<li>Field based DSO are not supported by the BW/4 conversion.&#160;Please remodel this kind of scenario manually.</li>\r\n<li>For write-optimized ADSOs, a semantic key cannot be defined without enabling the option \"Activate Data\". If a write-optimized ODSO is transferred into such an ADSO, the semantic key will be ignored. This might&#160;produce an unexpected behavior if the semantic key was used in a transformation for currency conversion for example. Please remodel this kind of scenario manually as follows:</li>\r\n<ul>\r\n<li>Create an InfoSource with the corresponding ODSO as template</li>\r\n<li>Make sure that the semantic key of the ODSO is defined as the key for the InfoSource</li>\r\n<li>Create a transformation connecting the source of the ODSO with the InfoSource</li>\r\n<li>Enable the currency conversion in the corresponding rule</li>\r\n<li>Create a transformation connecting the InfoSource with the ODSO</li>\r\n</ul>\r\n<li>If a&#160;process variant for the deletion of old requests from changelogs or write-optimized ODSOs&#160;is used, please&#160;note that there are restrictions on the corresponding process variant for the deletion of old requests from DataStore objects (advanced).&#160;See SAP&#160;Note <a target=\"_blank\" href=\"/notes/2448086\">2448086</a>&#160;for details.</li>\r\n<li>Transfer of ODSO with Key Figure Aggregation Type NO2 is only supported for&#160;direct update&#160;ODSOs, for other DSO types manual redesign is&#160;required before transfer.</li>\r\n</ul>\r\n<p><strong>Related Information</strong></p>\r\n<p>See the documentation for more details:&#160;<br /><a target=\"_blank\" href=\"https://help.sap.com/viewer/107a6e8a38b74ede94c833ca3b7b6f51/1.0.2/en-US/b76fca02fa804d569c386b9b437f85d6.html\">Templates for Modeling the Data Warehousing Layers</a></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-DSO (DataStore Object (classic))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041133)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002451013/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002451013/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}, {"RefNumber": "1849498", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "SAP HANA: Reconversion of SAP HANA-optimized DataStores", "RefUrl": "/notes/1849498"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2487535", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Personalization", "RefUrl": "/notes/2487535 "}, {"RefNumber": "2487597", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Conversion of DataSource Field into InfoObject", "RefUrl": "/notes/2487597 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}