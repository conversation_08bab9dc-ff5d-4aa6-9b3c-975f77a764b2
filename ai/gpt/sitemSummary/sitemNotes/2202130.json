{"Request": {"Number": "2202130", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 236, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002202130?language=E&token=1D88E7937A610459EB20E61C0581CC40"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002202130", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002202130/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2202130"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-MM-IM"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Inventory Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2202130 - 2LIS_03_BF: MENGE is zero, doesn't match with MSEG"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>While&#160;running RSA3 for 2LIS_03_BF the field MENGE&#160;does not&#160;display the same result as the MENGE in Table MSEG.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP R/3</li>\r\n<li>SAP R/3 Enterprise</li>\r\n<li>SAP ERP</li>\r\n<li>SAP ERP&#160;Central Component</li>\r\n<li>SAP enhancement package for SAP ERP</li>\r\n<li>SAP enhancement package for SAP ERP, version for SAP HANA</li>\r\n<li>SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>During the filling of setup table (TCode OLI1BW) the MENGE quantities are not populated for the records where MSEG-MENGU is blank.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>During filling of the setup table,&#160;following code will be called.</p>\r\n<pre>SAPLMCB1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; / LMCB1F20&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; / 1,526 SY-SUBRC&#160;&#160;&#160; 0<br />FORM&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; / XMCMSEG_ANREICHERN&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; SY-TABIX&#160;&#160;&#160; 7</pre>\r\n<pre>&#160;FORM&#160;XMCMSEG_ANREICHERN&#160;SAPLMCB1<br />&#160;FORM&#160;MCMSEG_ZUSAETZE&#160;SAPLMCB1<br />&#160;FUNCTION&#160;MCB_STATISTICS_NEU&#160;SAPLMCB1<br />&#160;FORM&#160;LIS_VERBUCHUNG&#160;RMCBNEUA<br />&#160;FORM&#160;BELEGE_PER_BELEGNUMMER&#160;RMCBNEUA<br />&#160;FORM&#160;RESIDENZBELEGE_VERARBEITEN&#160;RMCBNEUA<br />&#160;EVENT&#160;START-OF-SELECTION&#160;RMCBNEUA</pre>\r\n<pre>&#160; &#160;Pr&#252;fen, ob Verbrauchswert fortgeschrieben werden mu&#223; /* Check whether consumption value must be updated */</pre>\r\n<pre>&#160;&#160;&#160; IF xmcmseg-mgvbr &lt;&gt; 0.<br />&#160;&#160;&#160;&#160;&#160; preis = xmcmseg-dmbtr / xmcmseg-menge.<br />&#160;&#160;&#160; ENDIF.</pre>\r\n<pre>&#160;&#160;&#160; IF xmcmseg-mengu IS INITIAL AND<br />&#160;&#160;&#160;&#160;&#160;&#160; xmcmseg-bstaus NE 'V' AND xmcmseg-bsttyp NE 'V'.<br />*&#160; Keine Mengenfortschreibung<br />&#160;&#160;&#160;&#160;&#160; xmcmseg-menge = 0.&#160; <strong>&gt;&gt;&gt;&gt;&gt;&gt;Value of Field MENGE&#160;is set to 0.</strong><br />&#160;&#160;&#160; ENDIF.</pre>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>In material management, the movements with BSTAUS and BSTTYP = V&#160;are only usages, which means that they&#160;do not&#160;change the stock figures.</p>\r\n<p>If Field MENGU is from table MSEG not filled, &#160;a movement is also not supposed to change the stock figures.</p>\r\n<p>In case&#160;BSTAUS and BSTTYP do not equal V,&#160;the movement is &#160;also not&#160;considered to be&#160;a usage, so&#160;the above code clears the quantity field to make sure it won&#8217;t change the stock figures in the BW.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p>If the material document has no material number (MSEG-MATNR = ''), the MENGE = MSEG-ERFMG (Quantity in unit of entry).</p>\r\n<pre>FUNCTION&#160; &#160;MCB_MCMSEG_FILL</pre>\r\n<pre>  IF e_mcmseg_l-matnr IS INITIAL.<br />    ...<br />&#160; &#160; e_mcmseg_l-menge = i_mseg-erfmg.&#160; &lt;-------</pre>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I074505)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I074505)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002202130/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP ERP Central Component all versions "}, {"Product": "SAP ERP all versions "}, {"Product": "SAP R/3 Enterprise all versions "}, {"Product": "SAP R/3 all versions "}, {"Product": "SAP S/4HANA all versions "}, {"Product": "SAP enhancement package for SAP ERP all versions "}, {"Product": "SAP enhancement package for SAP ERP, version for SAP HANA all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}