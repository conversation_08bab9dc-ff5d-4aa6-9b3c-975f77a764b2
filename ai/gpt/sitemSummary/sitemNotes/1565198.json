{"Request": {"Number": "1565198", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 294, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001565198?language=E&token=42CBB3B9F23E08A29DE347A204DB4668"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001565198", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001565198/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1565198"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "04.04.2011"}, "SAPComponentKey": {"_label": "Component", "value": "GRC-ACP"}, "SAPComponentKeyText": {"_label": "Component", "value": "GRC Access Control Plug-In"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Governance, Risk and Compliance", "value": "GRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "GRC Access Control Plug-In", "value": "GRC-ACP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'GRC-ACP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1565198 - Cannot install GRC 10.0 plug-in due to low BASIS/ABA SP lvl"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You are unable to install GRC 10.0 plug-in because the existing SAP_BASIS &amp; SAP_ABA Support Package levels are lower that the required import conditions for the Plug-In<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GRCPINW 1000_700 , GRC 10.0 Netweaver Plug-in<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The Support Package levels of your system is lower than the import conditions required by the GRC Plug-In<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Upgrade your system to the following Support Package levels as needed by the import conditions<br /><br />SAP_BASIS 700 SAPKB70013<br />SAP_BW 700 SAPKW70013<br /><br />If an upgrade is not possible, upload the Archive ACP_GRCPINW_V1000_700.SAR attached to this note. This ACP will change the import attributes of the OCS package as follows<br /><br />SAP_BASIS 700 SAPKB70006<br />SAP_BW 700 SAPKW70006<br /></p> <UL><LI>At least SPAM/SAINT Version 0038 is a prerequisite for theprocessing of ACPs in release 700.</LI></UL> <UL><LI>Copy the ACP-Archive ACP_GRCPINW_V1000_700.SAR to your transportdirectory, e.g. /usr/sap/trans for UNIX. Unpack the Archive withthe command</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'SAPCAR -xvf ACP_GRCPINW_V1000_700.SAR'</p> <UL><LI>Afterwards, the file M6D0120007986_0003021.PAT should beavailable in the EPS-Inbox (directory /usr/sap/trans/EPS/in forUNIX).</LI></UL> <UL><LI>No other ACP GRCPINW===V1000_700 than the one with numberM6D0120007986_0003015.PAT should be available in this directory,please check this in your system via transaction SEPS -&gt; Goto -&gt; Inbox.</LI></UL> <UL><LI>Download the installation package for GRCINW V1000_700 from theService Marketplace.</LI></UL> <UL><LI>Load the contents of the file GRCPINW===V1000_700 into the EPSinbox (SAINT &gt; Installation Package &gt; Load Packages &gt; From Front</LI></UL> <UL><LI>Then you can use SAINT to define the required queue.</LI></UL> <UL><LI>Note - IMPORTANT</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After implementing this OSS-note PRIOR to implementing GRC 10. 0 plug-inplease implement OSS-note 1560308!<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041454)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I819251)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001565198/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001565198/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ACP_GRCPINW_V1000_700.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000217422011&iv_version=0003&iv_guid=2BB51509EB6DAF44B98EF88CBE731187"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560308", "RefComponent": "GRC-ACP", "RefTitle": "Cannot install GRC 10 plug-in because of missing ACH node", "RefUrl": "/notes/1560308"}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1560308", "RefComponent": "GRC-ACP", "RefTitle": "Cannot install GRC 10 plug-in because of missing ACH node", "RefUrl": "/notes/1560308 "}, {"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "GRCPINW", "From": "V1000_700", "To": "V1000_700", "Subsequent": ""}, {"SoftwareComponent": "GRCPINW", "From": "V1000_731", "To": "V1000_731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}