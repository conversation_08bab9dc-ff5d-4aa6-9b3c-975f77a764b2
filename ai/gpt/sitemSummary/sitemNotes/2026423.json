{"Request": {"Number": "2026423", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 891, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017886602017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002026423?language=E&token=44369A559B4F75F3C83E443079BE5823"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002026423", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2026423"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.06.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-FI"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Financial Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Financial Accounting", "value": "BW-BCT-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2026423 - SAP HANA-optimized BI Content: FI-AR Program Error in End Routine (/IMO/FIAR_030 -> /IMO/FIAR_D30)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>When&#160;loading data from DataStore Object /IMO/FIAR_030 to /IMO/FIAR_D30, the DTP may end with an error in the End Routine.</p>\r\n<p>SAP Note: This error only occurs if the&#160;source DSO&#160;/IMO/FIAR_030 does&#160;not&#160;contain&#160;any customer line items with Item Status cleared (0FI_DOCSTAT = 'C').</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>HANA optimized-BI Content, /IMO/FIAR_030, /IMO/FIAR_D30, Transformation,&#160;End Routine</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is due to a program error in the End Routine&#160;of transformation TRCS /IMO/FIAR_IS30 -&gt; ODSO /IMO/FIAR_D30.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This error is fixed with the following Support Packages:<br /><br /></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>SAP NetWeaver 7.40 BI Content 7.57 SP 03</td>\r\n</tr>\r\n<tr>\r\n<td>SAP NetWeaver 7.40 BI Content 7.47 SP 10</td>\r\n</tr>\r\n<tr>\r\n<td>SAP NetWeaver 7.31 BI Content 7.47 SP 10</td>\r\n</tr>\r\n<tr>\r\n<td>SAP NetWeaver 7.30 BI Content 7.37 SP 10</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>If you need a fix before the&#160;mentioned Support Packages are released, please follow the steps below in your BW system manually. The SAP Note itself is not implementable.</p>\r\n<p>In the&#160;End Routine of transformation&#160;TRCS /IMO/FIAR_IS30 -&gt; ODSO /IMO/FIAR_D30 replace the&#160;following section of the old coding by the new coding stated below.</p>\r\n<p>*** START OF OLD CODING ***</p>\r\n<p><em>&#160;*&#160;fill&#160;DSO&#160;content&#160;into&#160;buffer&#160;table</em><br />&#160;&#160;&#160;&#160;&#160;&#160;SELECT&#160;DISTINCT&#160;comp_code&#160;debitor&#160;fiscyear&#160;ac_doc_no&#160;createdon<br />&#160;&#160;&#160;&#160;&#160;&#160;FROM&#160;(l_dsotable)<br />&#160;&#160;&#160;&#160;&#160;&#160;INTO&#160;CORRESPONDING&#160;FIELDS&#160;OF&#160;TABLE&#160;gt_cleardoc<br />&#160;&#160;&#160;&#160;&#160;&#160;WHERE&#160;fi_docstat&#160;=&#160;'C'.<br />&#160;&#160;&#160;&#160;if&#160;sy-subrc&#160;=&#160;4.<br />&#160;&#160;&#160;&#160;&#160;&#160;RAISE&#160;EXCEPTION&#160;TYPE&#160;CX_RSROUT_ABORT.<br />&#160;&#160;&#160;&#160;endif.<br /><br /><br />&#160;&#160;&#160;&#160;&#160;&#160;LOOP&#160;AT&#160;RESULT_PACKAGE&#160;ASSIGNING&#160;&lt;result_fields&gt;.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CLEAR&#160;&lt;result_fields&gt;-clr_entry.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IF&#160;&lt;result_fields&gt;-clr_fyear&#160;&lt;&gt;&#160;'0'.<br /><br /><em>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\"&#160;Check&#160;if&#160;respective&#160;Clearing&#160;Document&#160;exists.</em><br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;READ&#160;TABLE&#160;gt_cleardoc<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ASSIGNING&#160;&lt;ls_cleardoc&gt;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;WITH&#160;TABLE&#160;KEY<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;comp_code&#160;=&#160;&lt;result_fields&gt;-comp_code<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;debitor&#160;=&#160;&lt;result_fields&gt;-debitor<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;fiscyear&#160;=&#160;&lt;result_fields&gt;-clr_fyear<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ac_doc_no&#160;=&#160;&lt;result_fields&gt;-clr_doc_no.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IF&#160;sy-subrc&#160;EQ&#160;0.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;result_fields&gt;-clr_entry&#160;=&#160;&lt;ls_cleardoc&gt;-createdon.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ELSE.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ENDIF.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ENDIF.<br />&#160;&#160;&#160;&#160;&#160;&#160;ENDLOOP.</p>\r\n<p><em>&#160;*--&#160;&#160;fill&#160;table&#160;\"MONITOR\"&#160;with&#160;values&#160;of&#160;structure&#160;\"MONITOR_REC\"</em></p>\r\n<p>***&#160;END OF OLD CODING ***</p>\r\n<p>&#160;</p>\r\n<p>*** START&#160;OF NEW CODING ***</p>\r\n<p><em>&#160;*&#160;fill&#160;DSO&#160;content&#160;into&#160;buffer&#160;table</em><br />&#160;&#160;&#160;&#160;&#160;SELECT&#160;DISTINCT&#160;comp_code&#160;debitor&#160;fiscyear&#160;ac_doc_no&#160;createdon<br />&#160;&#160;&#160;&#160;FROM&#160;(l_dsotable)<br />&#160;&#160;&#160;&#160;INTO&#160;CORRESPONDING&#160;FIELDS&#160;OF&#160;TABLE&#160;gt_cleardoc<br />&#160;&#160;&#160;&#160;WHERE&#160;fi_docstat&#160;=&#160;'C'.<br /><br />&#160;&#160;&#160;&#160;IF&#160;sy-subrc&#160;=&#160;0.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;LOOP&#160;AT&#160;RESULT_PACKAGE&#160;ASSIGNING&#160;&lt;result_fields&gt;.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CLEAR&#160;&lt;result_fields&gt;-clr_entry.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IF&#160;&lt;result_fields&gt;-clr_fyear&#160;&lt;&gt;&#160;'0'.<br /><br /><em>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;\"&#160;Check&#160;if&#160;respective&#160;Clearing&#160;Document&#160;exists.</em><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;READ&#160;TABLE&#160;gt_cleardoc<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ASSIGNING&#160;&lt;ls_cleardoc&gt;<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;WITH&#160;TABLE&#160;KEY<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;comp_code&#160;=&#160;&lt;result_fields&gt;-comp_code<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;debitor&#160;=&#160;&lt;result_fields&gt;-debitor<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;fiscyear&#160;=&#160;&lt;result_fields&gt;-clr_fyear<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ac_doc_no&#160;=&#160;&lt;result_fields&gt;-clr_doc_no.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IF&#160;sy-subrc&#160;EQ&#160;0.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&lt;result_fields&gt;-clr_entry&#160;=&#160;&lt;ls_cleardoc&gt;-createdon.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ELSE.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ENDIF.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ENDIF.<br />&#160;&#160;&#160;&#160;&#160;&#160;ENDLOOP.<br /><br />&#160;&#160;&#160;&#160;ELSE.<br /><br />&#160;&#160;&#160;&#160;&#160;&#160;LOOP&#160;AT&#160;RESULT_PACKAGE&#160;ASSIGNING&#160;&lt;result_fields&gt;.<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;CLEAR&#160;&lt;result_fields&gt;-clr_entry.<br />&#160;&#160;&#160;&#160;&#160;&#160;ENDLOOP.<br /><br />&#160;&#160;&#160;&#160;ENDIF.<br /><br /><em>*--&#160;&#160;fill&#160;table&#160;\"MONITOR\"&#160;with&#160;values&#160;of&#160;structure&#160;\"MONITOR_REC\"</em></p>\r\n<p>***&#160;END OF NEW CODING ***</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I037341)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D047954)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002026423/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002026423/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "737", "To": "737", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "747", "To": "747", "Subsequent": ""}, {"SoftwareComponent": "BI_CONT", "From": "757", "To": "757", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "BI_CONT 757", "SupportPackage": "SAPK-75703INBICONT", "URL": "/supportpackage/SAPK-75703INBICONT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}