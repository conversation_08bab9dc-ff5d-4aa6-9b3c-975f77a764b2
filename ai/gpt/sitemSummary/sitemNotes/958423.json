{"Request": {"Number": "958423", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1423, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016120382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000958423?language=E&token=DD8897DC67FF242A07F91E8C7E269FB6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000958423", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000958423/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "958423"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "20.11.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-UA-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-UA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-LRQ-UA (Legal Requirement Clarification)", "value": "XX-CSC-UA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-UA-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-UA", "value": "XX-CSC-UA-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-UA-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "958423 - IS-U Energy losses in transformers and lines for Ukraine"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The solution allows computing of losses in power lines and transformers according to legal requirements and business practices in Ukraine.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Power line losses, grid losses, transformer losses, /SAPCE/IUUA_LOLI, /SAPCE/IUUA_LOTR.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have to install add-on CEEISUT rel.4.72 with available add/on<br />support patches (AOPs). It contains IS-U/CCS and IS-T localization for<br />Central and Eastern Europe.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p> o&#x00A0;&#x00A0;These local enhancements are part of the Russian and Ukrainian<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;country version of IS-U/CCS delivered in add-on CEEISUT<br /><br /> o&#x00A0;&#x00A0;Local support of SAP Ukraine is responsible for maintenance and<br /> hotline support of those enhancements for Ukraine only<br /><br /> o&#x00A0;&#x00A0;These enhancements are not released for any other countries.<br /> ***********************************************************************<br /><br />1.&#x00A0;&#x00A0;Overview of enhancements<br /><br />There are two variant programs to be used in billing scheme:<br />_CE_LOTR - variant program for transformer losses<br />_CE_LOLI - variant program for power line losses<br /><br />There are customizing tables for parameters of transformers and lines:<br />/SAPCE/IUUA_LOLT - customizing table for power lines.<br />/SAPCE/IUUA_LOT2 - customizing table for double winding transformers.<br />/SAPCE/IUUA_LOT3 - customizing table for three winding transformers.<br />/SAPCE/IUUA_LOLQ - customizing table for reactive charge power of power lines.<br />/SAPCE/IUUA_LOVL - customizing table for voltage levels.<br /><br />There is an extension of installation which allows quick overview of status of connected transformers and lines. Using a button you will go to screen for displaying of full details and maintenance of connected transformers and lines, their type and status.<br /><br />***********************************************************************<br /><br />2. Installation<br /><br />Use the BC Set /CEEISUT/ISU_UA_04 for maintenance of variant programs _CE_LOLI and _CE_LOTR.<br /><br />To integrate Maintenance of Losses Data into Installation maintenance transactions you have to:</p> <UL><LI>In transaction CMOD, create project for SAP enhancement EMDI0001</LI></UL> <UL><LI>Create (or change) the screen 0100 of program SAPLXES30 (func.gr.XES30)</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;From list of CMOD's components navigate to the screen 0100 of program SAPLXES30 (by double click). If the screen 0100 doesn't exist yet, it will be created, fill the description and set screen type to Subscreen. In change mode, go to change of layout (choose \"Layout\" button) and add the new Subscreen Area to the end of screen 0100. Set the name of this new subscreen area to /SAPCE/IUUA_LOSSES and set the area's length to 83, height to 2 and staring coulumn to 1.</p> <UL><LI>Include the following pieces of source code into the flow logic of the screen 0100 of program SAPLXES30 (func.gr.XES30):</LI></UL> <UL><UL><LI>Into block PROCESS BEFORE OUTPUT:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuua_losses INCLUDING<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'/SAPCE/SAPLIUUA_LOSSES' '0100'.</p> <UL><UL><LI>Into block PROCESS AFTER INPUT:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CALL SUBSCREEN /sapce/iuua_losses.</p> <UL><LI>Include the following pieces of source code into includes (if the specified includes doesn't exist, create them):</LI></UL> <UL><UL><LI>Into include ZXES30U06</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INCLUDE /sapce/iuua_losses_zxes30u06.</p> <UL><UL><LI>Into include ZXES30U08</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INCLUDE /sapce/iuua_losses_zxes30u08.</p> <UL><UL><LI>Into include ZXES30U09</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INCLUDE /sapce/iuua_losses_zxes30u09.</p> <UL><UL><LI>Into include ZXES30U10</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;INCLUDE /sapce/iuua_losses_zxes30u10.<br /></p> <UL><LI>Activate the created enhancement project in transaction CMOD<br /></LI></UL> <p>***********************************************************************<br /><br />3.&#x00A0;&#x00A0;Installation-specific customizing<br /><br />Fill the customizing tables /SAPCE/IUUA_LOLT, /SAPCE/IUUA_LOT2, /SAPCE/IUUA_LOT3, /SAPCE/IUUA_LOLQ, and /SAPCE/IUUA_LOVL.<br /><br />Download pdf file with documentation from attachment of this note.<br />Follow instructions for customizing and use of this enhancement as<br />described there.<br /><br />***********************************************************************<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "C5062870"}, {"Key": "Processor                                                                                           ", "Value": "C5062870"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000958423/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ISU_Doc_UA_Losses_EN_100.pdf", "FileSize": "183", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000287032006&iv_version=0010&iv_guid=A8202AA79729BC43BB400E4F7374100D"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953"}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1993697", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "IS-U Energy losses in transformers, Lines and Reactors for Ukraine", "RefUrl": "/notes/1993697 "}, {"RefNumber": "1014953", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality for IS-UT 600 (collective)", "RefUrl": "/notes/1014953 "}, {"RefNumber": "1012272", "RefComponent": "XX-CSC-UA-IS-U", "RefTitle": "Ukrainian specific functionality IS-U 4.72 (collective)", "RefUrl": "/notes/1012272 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "CEEISUT", "From": "472", "To": "472", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 472", "SupportPackage": "SAPK-47213INCEEISUT", "URL": "/supportpackage/SAPK-47213INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60002INCEEISUT", "URL": "/supportpackage/SAPK-60002INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}