{"Request": {"Number": "1280060", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 229, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016679282017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=9B99C832CA38C196BEC7D2EBA603D03F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1280060"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.03.2024"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1280060 - Classic PCA or PCA in General Ledger Accounting (new)? Classic PCA or PCA in the universal journal in SAP S/4HANA?"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You plan a migration from classic general ledger accounting to General Ledger Accounting (new), and you currently use classic Profit Center Accounting. You are not sure whether you should map Profit Center Accounting in General Ledger Accounting (new) in the future, or whether you should continue to use classic Profit Center Accounting.</p>\r\n<p>You plan a migration to SAP S/4HANA and currently use classic Profit Center Accounting. You are not sure whether you should map Profit Center Accounting in the universal journal in the future, or whether you should continue to use classic Profit Center Accounting.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>General Ledger Accounting (new), FI-GL (new), profit center, PCA, SAP S/4HANA, universal journal</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use Profit Center Accounting.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>For Release mySAP ERP, Profit Center Accounting was integrated in General Ledger Accounting (new). Alternatively, classic Profit Center Accounting is still available. The integration of Profit Center Accounting has several advantages. New functions (for example, document splitting) are available, and the reconciliation of classic Profit Center Accounting with general ledger accounting, which is often very time-consuming, is no longer required. For this reason, we recommend that all new ERP customers and new SAP S/4HANA customers map Profit Center Accounting (if required) in the context of General Ledger Accounting (new) or in the universal journal in SAP S/4HANA.</p>\r\n<p>If you already use classic Profit Center Accounting in your production system and plan to migrate from classic general ledger accounting to General Ledger Accounting (new) or SAP S/4HANA, you should check carefully beforehand whether it makes more sense for you to continue to use classic Profit Center Accounting or to integrate Profit Center Accounting in General Ledger Accounting (new) or in the universal journal of SAP S/4HANA in the future. The following questions are to help you make a decision:</p>\r\n<ul>\r\n<li>Is the Profit Center Accounting that you currently use closer to FI or closer to CO? An indication for this could be, for example, from which area Profit Center Accounting is currently supported in your company; that is, is support provided by the Controlling department or by financial accounting?<br /><br />If you currently already use Profit Center Accounting that is closer to FI, an integration of Profit Center Accounting in General Ledger Accounting (new) probably has many advantages for you in the future.<br />&#x00A0;</li>\r\n<li>What are your company's reporting requirements for profit centers? Do you mainly report cost and revenue in a profit and loss statement, or do you also report balance sheet accounts or possibly even selected key figures for each profit center?<br /><br />If you also report key figures at profit center level, you should check whether document splitting, which is available in General Ledger Accounting (new) or SAP S/4HANA, has advantages for you, for example, improving the quality of your data and/or accelerating the month-end closing. As a result of document splitting, the system performs a more cause-related splitting of different line items to profit centers. For example, if you report receivables and payables for each profit center, document splitting has the advantage that the splitting to profit centers is available immediately after posting so that a transfer at the end of the period is not required. This simplifies the month-end closing.<br />&#x00A0;</li>\r\n<li>Do you require segment reporting in the future? If you do, do you want to use the new \"Segment\" entity for this?<br /><br />The \"Segment\" entity is available in General Ledger Accounting (new) by assigning the \"Segmentation\" scenario (FIN_SEGM) to the relevant ledger. The segment/partner segment is derived automatically from the profit center or partner profit center. For more information, see also SAP Note 1035140.<br />&#x00A0;</li>\r\n<li>In which form do you use secondary cost elements in Controlling? Do you require flexible reporting using secondary cost elements for profit centers or profit center groups?<br /><br />The information about the secondary cost element is also updated in General Ledger Accounting (new), but in a separate field (COST_ELEM). Since this is general ledger accounting and not a parallel accounting system, as in the case of classic Profit Center Accounting, each line item must contain a G/L account in the \"Account\" field. For this reason, the system executes an account determination during the real-time integration when the documents are transferred from CO to General Ledger Accounting (new). However, in classic Profit Center Accounting, the secondary cost element is updated directly in the \"Account\" field. This enables you to display both secondary cost elements and profit accounts and balance sheet accounts in the account field in a Report Painter report or drilldown report. In General Ledger Accounting (new), you can create a report using only cost elements, or you can display the cost element as additional information in a report.<br />&#x00A0;</li>\r\n<li>Do you regularly perform reconciliation between classic Profit Center Accounting and General Ledger Accounting (new)? If you do, what is your current reconciliation expense per month or per year?<br /><br />With the integration of Profit Center Accounting in General Ledger Accounting (new), there is no more monthly/annual reconciliation expense. This may save your company time and costs.</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">For more information about mapping Profit Center Accounting in General Ledger Accounting (new) or changing the functions of classic Profit Center Accounting when General Ledger Accounting (new) is active, see SAP Note 826357 and the SAP application documentation.</span></p>\r\n<p><span style=\"font-size: 14px;\">For more information about mapping Profit Center Accounting in the universal journal in SAP S/4HANA or about changing the functions of classic Profit Center Accounting in SAP S/4HANA, see SAP Note 2425255 and the SAP application documentation.</span></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EC-PCA (Profit Center Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D023724)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D025743)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357"}, {"RefNumber": "2425255", "RefComponent": "EC-PCA-ACT", "RefTitle": "Profit Center Accounting in the universal journal in SAP S/4HANA, on-premise and private cloud", "RefUrl": "/notes/2425255"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}