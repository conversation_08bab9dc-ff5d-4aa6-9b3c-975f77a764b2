{"Request": {"Number": "168188", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 298, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014712462017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=970E1347E238436C6D7D7043E1F2A16F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "168188"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.05.2000"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AU-GST"}, "SAPComponentKeyText": {"_label": "Component", "value": "Goods and Services Tax"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Australia", "value": "XX-CSC-AU", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AU*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Goods and Services Tax", "value": "XX-CSC-AU-GST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AU-GST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "168188 - GST Information Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The Australian Government will introduce a Goods and Services Tax commencing 1 July 2000. GST is a broad based tax of 10 % on the supply of most goods and services consumed in Australia. This note is for information purposes and outlines additional notes which may be of benefit to customers.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>GST, Goods and Services Tax, Tax, Australia, Tax Reform</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note provides a central point outlining where you can obtain information on the GST and SAP's approach to the implementation of GST in an SAP environment: This note will continue to be updated with information as it comes to hand. This note will provide information on the following:-<br />1. Relevant OSS notes.<br />2. Relevant Internet sites.<br /><br /><B>1. Relevant OSS notes.</B><br /><B>======================</B><br />Please review the 'Related Notes' section at the end of this note.<br /><br /><br /><br /><B>2. Internet Sites.</B><br /><B>==================</B><br /><br />SAP:<br />www.sapnet.sap.com/gst&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Customer and Partner site)<br />www.sap.com/australia/gst&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Public Site)<br /><br />Other:<br />www.taxreform.ato.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Australian Taxation Office)<br />www.treasury.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Dept of Treasury)<br />www.rbt.treasury.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Review of Business Taxation-RBT)<br />www.rbt.treasury.gov.au/taxreform (The Pay As You Go (PAYG) System)<br />www.gststartup.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Cwealth Govt GST Start-Up Office)<br />www.business.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Government Business Entry Point-BEP)<br />www.dofa.gov.au/gst&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(DOFA Assistance to Cwealth Govt with<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The New Tax System-TNTS)<br />www.cpaonline.com.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ASCPA activity, News related to TNTS)<br />www.icaa.org.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ICAA activity, News related to TNTS)<br />www.accc.gov.au&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ACCC price monitoring/impact of TNTS)<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I009370"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "306529", "RefComponent": "FI", "RefTitle": "ABN: Posting to a ONE-TIME account", "RefUrl": "/notes/306529"}, {"RefNumber": "304275", "RefComponent": "MM-IV", "RefTitle": "Australia: Handling of GST on freight in Purchasing", "RefUrl": "/notes/304275"}, {"RefNumber": "216861", "RefComponent": "SD-BF-TX", "RefTitle": "Australian GST Conversion Requirement - Open Sales Orders", "RefUrl": "/notes/216861"}, {"RefNumber": "216761", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUVDE00: Termination with error message F7 212", "RefUrl": "/notes/216761"}, {"RefNumber": "216359", "RefComponent": "FI-GL-GL-F", "RefTitle": "Australian GST: Simplified BAS reporting", "RefUrl": "/notes/216359"}, {"RefNumber": "216109", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "GST - Simplified ERS reporting", "RefUrl": "/notes/216109"}, {"RefNumber": "212933", "RefComponent": "FI-AR-AR-J", "RefTitle": "Error F2013 'Country &1 not defined in system'", "RefUrl": "/notes/212933"}, {"RefNumber": "211354", "RefComponent": "FI-AR-AR-Q", "RefTitle": "Rounding withholding Taxes in Australia", "RefUrl": "/notes/211354"}, {"RefNumber": "208810", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUVDE00: rounding of base amounts not required", "RefUrl": "/notes/208810"}, {"RefNumber": "204972", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00: Deactivating document summarization (BSET)", "RefUrl": "/notes/204972"}, {"RefNumber": "203365", "RefComponent": "FI-GL-GL-F", "RefTitle": "BAS - Report for processing the GST in Australia", "RefUrl": "/notes/203365"}, {"RefNumber": "202730", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "ERS processing for GST tax Australia", "RefUrl": "/notes/202730"}, {"RefNumber": "201753", "RefComponent": "FI", "RefTitle": "ABN: Australian business number", "RefUrl": "/notes/201753"}, {"RefNumber": "122332", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00 Wrong transaction key for n.ded.input tax", "RefUrl": "/notes/122332"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "216761", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUVDE00: Termination with error message F7 212", "RefUrl": "/notes/216761 "}, {"RefNumber": "211354", "RefComponent": "FI-AR-AR-Q", "RefTitle": "Rounding withholding Taxes in Australia", "RefUrl": "/notes/211354 "}, {"RefNumber": "216109", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "GST - Simplified ERS reporting", "RefUrl": "/notes/216109 "}, {"RefNumber": "202730", "RefComponent": "XX-CSC-AU-GST", "RefTitle": "ERS processing for GST tax Australia", "RefUrl": "/notes/202730 "}, {"RefNumber": "216359", "RefComponent": "FI-GL-GL-F", "RefTitle": "Australian GST: Simplified BAS reporting", "RefUrl": "/notes/216359 "}, {"RefNumber": "203365", "RefComponent": "FI-GL-GL-F", "RefTitle": "BAS - Report for processing the GST in Australia", "RefUrl": "/notes/203365 "}, {"RefNumber": "201753", "RefComponent": "FI", "RefTitle": "ABN: Australian business number", "RefUrl": "/notes/201753 "}, {"RefNumber": "204972", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00: Deactivating document summarization (BSET)", "RefUrl": "/notes/204972 "}, {"RefNumber": "304275", "RefComponent": "MM-IV", "RefTitle": "Australia: Handling of GST on freight in Purchasing", "RefUrl": "/notes/304275 "}, {"RefNumber": "122332", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUMSV00 Wrong transaction key for n.ded.input tax", "RefUrl": "/notes/122332 "}, {"RefNumber": "306529", "RefComponent": "FI", "RefTitle": "ABN: Posting to a ONE-TIME account", "RefUrl": "/notes/306529 "}, {"RefNumber": "216861", "RefComponent": "SD-BF-TX", "RefTitle": "Australian GST Conversion Requirement - Open Sales Orders", "RefUrl": "/notes/216861 "}, {"RefNumber": "212933", "RefComponent": "FI-AR-AR-J", "RefTitle": "Error F2013 'Country &1 not defined in system'", "RefUrl": "/notes/212933 "}, {"RefNumber": "208810", "RefComponent": "FI-GL-GL-F", "RefTitle": "RFUVDE00: rounding of base amounts not required", "RefUrl": "/notes/208810 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}