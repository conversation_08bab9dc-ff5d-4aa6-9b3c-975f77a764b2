{"Request": {"Number": "317576", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 340, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014867872017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000317576?language=E&token=88A5449EE2F3012D2A62603D9A331766"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000317576", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000317576/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "317576"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "11.12.2002"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-ISA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internet Sales"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Customer Relationship Management", "value": "CRM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internet Sales", "value": "CRM-ISA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CRM-ISA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "317576 - Missing or incorrect IMS.DLL"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note provides the latest version of the Index Management Service Module Provider (IMS.DLL) for the Internet Transaction Server (ITS). The IMS.DLL is required by the Internet Sales application (ISAB2C and ISAB2B).<br />SAP recommends that this note is used in the following cases:</p> <UL><LI>Within the Internet Sales application, the system displays the error message \"Unable to load the Module Provider DLL for Module Type = ims\".</LI></UL> <UL><LI>Stability problems (flow execution error, hanging ITS session) occur during operation with several users.</LI></UL> <UL><LI>With logon language \"Spanish\" (ES), texts are displayed in \"English\" (EN).</LI></UL> <UL><LI>Only DrFuzzy: for search strings without \"*\" the system incorrectly uses search by natural language.</LI></UL> <UL><LI>If the RFC destination of the SearchServerRelation (SSR) contains whole path names, the AGate process runs into an endless loop.</LI></UL> <UL><LI>Endless loop in the AGate process with an IMS call with non-coupled \"'\" or \"`\" characters</LI></UL> <UL><LI>Partially incorrect results when using the caching mode:</LI></UL> <UL><UL><LI>The pushbuttons for scrolling in the product list are partially missing.</LI></UL></UL> <UL><UL><LI>The list of product recommendations for a Web shop is identical for all target groups (ISAB2B).</LI></UL></UL> <p></p> <UL><LI>For Version 2.0.8.0, the following errors habe been fixed:</LI></UL> <UL><UL><LI>Error when caching an IN ARRAY command with large field</LI></UL></UL> <UL><UL><LI>Error when accessing a non-existing ITS context variable</LI></UL></UL> <UL><UL><LI>Handling of several parenthetical levels</LI></UL></UL> <UL><UL><LI>Error in ITS with variables of more than 4095 characters</LI></UL></UL> <UL><UL><LI>COM_PCAT_IMS_GET_LASTREP used to determine the last replication</LI></UL></UL> <UL><UL><LI>Several MultiByte/Asian problems</LI></UL></UL> <UL><UL><LI>The maximum length of a command is 16000 now (instead of 4096).</LI></UL></UL> <UL><UL><LI>Registered IMS RFC servers are no longer kept in the RFC pool.</LI></UL></UL> <p></p> <UL><LI>For Version *******, the following errors habe been fixed:</LI></UL> <UL><UL><LI>Before calling CRM function module RFC_GET_FUNCTION_INTERFACE, the system checks for its existence. If it is not available, a lot of Syslog entries have been logged in CRM before.</LI></UL></UL> <UL><UL><LI>Modifications for use of an AS/400 as CRM application server</LI></UL></UL> <UL><UL><LI>The sort sequence of \"multiple-value attributes\" is kept now.<br /></LI></UL></UL> <UL><LI>For Version *******, the following errors habe been fixed:</LI></UL> <UL><UL><LI>Elimination of IMS error -53 (although there is no syntax error in IMS command)</LI></UL></UL> <UL><UL><LI>Multi-valued attributes are RTRIM'ed before concatenation with delimiter (which is normally \";\")</LI></UL></UL> <UL><UL><LI>Added ProcMon calls for tracing purpose</LI></UL></UL> <UL><UL><LI>IMS_RFC_EXCEPTION \"NOCATID\" no more handled with return code -70; instead string \"NOCATID\" traced after \"RfcCallReceive failed: ...\"</LI></UL></UL> <UL><UL><LI>different locale enhancements (e.g. Polish support).<br /></LI></UL></UL> <UL><LI>For Version *******, the following errors have been fixed:</LI></UL> <UL><UL><LI>IPC error message (\"Inconsistent parameters were transferred to a command\", \"These parameter arrays have different lengths\") when two different IMS module provider calls overwrite their result sets from cache.</LI></UL></UL> <p></p> <UL><LI>For Version *******, the following errors have been fixed:</LI></UL> <UL><UL><LI>supporting multiple \"IN ARRAY\" constructs</LI></UL></UL> <UL><UL><LI>eliminated problem with endless-loop (******* only) when accessing an empty ITS context variable in an \"IN ARRAY\" construct<br /></LI></UL></UL> <UL><LI>For Version ******* the following errors have been eliminated:</LI></UL> <UL><UL><LI>In some Windows systems, Version ******* leads to the error that the DLL cannot be loaded because it used a standard library named \"MSVCRTD.dll\" which is available only if the development environment is installed.</LI></UL></UL> <UL><UL><LI>Since Version *******, function module COM_PCAT_IDXCAT_FIND_BY_QUERY (if available) has been used (instead of SRET_IDXCAT_FIND_BY_QUERY, see Note 572940). The new module bundles area-specific indexes by the LOC (and no longer by the area name).</LI></UL></UL> <p></p> <UL><LI>For Version *******, the following errors have been eliminated:</LI></UL> <UL><UL><LI>Error messages in AGate.trc due to incorrect conversion in function MbcsM2U; the calling source code in IMS.dll is correct, however.</LI></UL></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IMS.DLL, ITS, Internet Sales, ISAB2C, ISAB2B<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>No IMS.DLL is installed or an incorrect version of the IMS.DLL is installed.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>File IMS.zip with the required ims.dll has been appended to this note.<br />You can download the zip file into a local directory and unpack it to the target directory.<br />The target file is called (if D: is the ITS installation disk drive):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;D:\\Program Files\\SAP\\ITS\\2. 0\\programs\\IMS.dll<br />To copy this file, you must stop the ITS instance temporarily because a DLL that is currently being used by a program cannot be exchanged.<br />You can check the version of the IMS.dll installed on your system as follows:<br />Start a file manager on the ITS machine on operating system level, select the above-mentioned file with the right mouse button and choose the \"Attributes\" option from the context menu. Select the \"Version\" tab page in the attributes window displayed.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating system", "Value": "NT/INTEL4.0"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D032281)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035884)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000317576/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000317576/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000317576/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "IMS_2250_opt.zip", "FileSize": "80", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700007320102001&iv_version=0016&iv_guid=DB894B6F89B08B40A32D70CD03B07DBB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "557225", "RefComponent": "CRM-ISA-CAT", "RefTitle": "IMSmpr: Endless loop with empty ITS context variable", "RefUrl": "/notes/557225"}, {"RefNumber": "428763", "RefComponent": "CRM-ISA", "RefTitle": "IMS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  error number -70", "RefUrl": "/notes/428763"}, {"RefNumber": "394375", "RefComponent": "CRM-ISA", "RefTitle": "Use of registered IMS-RFC servers", "RefUrl": "/notes/394375"}, {"RefNumber": "390549", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/390549"}, {"RefNumber": "350441", "RefComponent": "CRM-ISA", "RefTitle": "IMS ModuleProvider: Sort option with DrFuzzy", "RefUrl": "/notes/350441"}, {"RefNumber": "337873", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/337873"}, {"RefNumber": "336733", "RefComponent": "CRM-ISA", "RefTitle": "Specifications of RFC destination for IMS access", "RefUrl": "/notes/336733"}, {"RefNumber": "319148", "RefComponent": "SRM-EBP", "RefTitle": "Incorrect ITS in BBP/CRM 2.0B Support Releases 1+2", "RefUrl": "/notes/319148"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "350441", "RefComponent": "CRM-ISA", "RefTitle": "IMS ModuleProvider: Sort option with DrFuzzy", "RefUrl": "/notes/350441 "}, {"RefNumber": "394375", "RefComponent": "CRM-ISA", "RefTitle": "Use of registered IMS-RFC servers", "RefUrl": "/notes/394375 "}, {"RefNumber": "428763", "RefComponent": "CRM-ISA", "RefTitle": "IMS-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  error number -70", "RefUrl": "/notes/428763 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "557225", "RefComponent": "CRM-ISA-CAT", "RefTitle": "IMSmpr: Endless loop with empty ITS context variable", "RefUrl": "/notes/557225 "}, {"RefNumber": "319148", "RefComponent": "SRM-EBP", "RefTitle": "Incorrect ITS in BBP/CRM 2.0B Support Releases 1+2", "RefUrl": "/notes/319148 "}, {"RefNumber": "336733", "RefComponent": "CRM-ISA", "RefTitle": "Specifications of RFC destination for IMS access", "RefUrl": "/notes/336733 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46D", "To": "46D", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "610", "To": "610", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "20B", "To": "20C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}