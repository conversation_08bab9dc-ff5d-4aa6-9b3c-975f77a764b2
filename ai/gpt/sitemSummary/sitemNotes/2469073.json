{"Request": {"Number": "2469073", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 365, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018794432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002469073?language=E&token=4404DA4D489255CB4DAA8D64F99E5C3C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002469073", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002469073/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2469073"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW4-ME"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON><PERSON><PERSON> (Design time)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP BW/4HANA", "value": "BW4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON><PERSON><PERSON> (Design time)", "value": "BW4-ME", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW4-ME*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2469073 - Use of conversion routine MATNB instead of MATN1 not allowed"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p id=\"\">The use of the conversion routine MATNB instead of the conversion routine MATN1 is not allowed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">MATN1, MATNB</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement this SAP Note. Following the implementation of this SAP Note, you can use the conversion routine MATNB instead of the conversion routine MATN1. As an alternative to implementing this SAP Note, you can import the following Support Package:</p>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li><strong>SAP BW 7.40</strong><br />Import Support Package 18 for SAP BW 7.40 (SAPKW74018) into your BW system. The support package is available once <strong>SAP Note 2445563</strong>&#x00A0;(\"SAPBWNews NW BW 7.40 ABAP SP18\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li><strong>SAP BW 7.50</strong><br />Import Support Package 9 for SAP BW 7.50 (SAPK-75009INSAPBW) into your BW system. The support package is available once <strong>SAP Note 2467627</strong>&#x00A0;(\"SAPBWNews NW BW 7.50 ABAP SP9\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li><strong>SAP BW 7.51</strong><br />Import Support Package&#x00A0;4 for SAP BW 7.51 (SAPK-75104INSAPBW) into your BW system. The support package is available once <strong>SAP Note 2467673</strong>&#x00A0;(\"SAPBWNews NW BW 7.51 ABAP SP4\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>In urgent cases, you can implement the correction instructions as an advance correction.</p>\r\n<p><strong>You must first read SAP Notes 1668882 and 2248091, which provide information about transaction SNOTE.</strong></p>\r\n<p>To provide information in advance, the SAP Note(s) specified above might already be available before the Support Package is released. In this case, however, the short text of the SAP Note still contains the term \"preliminary version\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D053379)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029975)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002469073/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002469073/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2422224", "RefComponent": "BW-BCT-MM-BW", "RefTitle": "SAP S/4HANA Long Material number integration with SAP BW / SAP BW/4HANA", "RefUrl": "/notes/2422224 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74018", "URL": "/supportpackage/SAPKW74018"}, {"SoftwareComponentVersion": "SAP_BW 750", "SupportPackage": "SAPK-75009INSAPBW", "URL": "/supportpackage/SAPK-75009INSAPBW"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 5, "URL": "/corrins/0002469073/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 12, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2129226 ", "URL": "/notes/2129226 ", "Title": "Supplement to SAP Note 1981371 - \"InfoObject length can be changed in SAP HANA systems even though present in DataStore objects filled with data\"", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2137394 ", "URL": "/notes/2137394 ", "Title": "Enhancements and problem solutions with SAP BW 7.40 SP11 (SAPBW74011)", "Component": "BW-WHM-DBA-RMT"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2154771 ", "URL": "/notes/2154771 ", "Title": "Impact analysis SAP HANA CompositeProvider & Navigationsattribute/MIME type is truncated after 32 characters", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2158001 ", "URL": "/notes/2158001 ", "Title": "No results displayed on performing wildcard(*) search in selector screen.", "Component": "BW-BEX-OT-F4"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2164273 ", "URL": "/notes/2164273 ", "Title": "Impact analysis for SAP HANA CompositeProviders and display attributes", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2171738 ", "URL": "/notes/2171738 ", "Title": "Length of InfoObject in SAP HANA systems cannot be changed even if SAP HANA supports extension of partitioning fields", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2193000 ", "URL": "/notes/2193000 ", "Title": "Corrections for InfoObject editor on basis of SAP BW 7.40 SP13", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "750", "Number": "2194245 ", "URL": "/notes/2194245 ", "Title": "Column views of advanced DataStore objects not adjusted when adding navigation attribute to InfoObject", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "750", "Number": "2293744 ", "URL": "/notes/2293744 ", "Title": "Unexpected search result for characteristic with data type \"Numeric Text\"/NUMC", "Component": "BW-BEX-OT-F4"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "750", "Number": "2341729 ", "URL": "/notes/2341729 ", "Title": "740SP16: Short dump during activation of Characterstics", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "750", "ValidTo": "750", "Number": "2220685 ", "URL": "/notes/2220685 ", "Title": "Corrections for InfoObject editor of SAP Business Warehouse modeling tools based on SAP BW 7.50 SP 1", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "750", "ValidTo": "750", "Number": "2341729 ", "URL": "/notes/2341729 ", "Title": "740SP16: Short dump during activation of Characterstics", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "750", "ValidTo": "750", "Number": "2368923 ", "URL": "/notes/2368923 ", "Title": "Characteristic with conversion routine \"MATN1\" cannot be extended", "Component": "BW-WHM-DBA-IOBJ"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "750", "ValidTo": "751", "Number": "2368923 ", "URL": "/notes/2368923 ", "Title": "Characteristic with conversion routine \"MATN1\" cannot be extended", "Component": "BW-WHM-DBA-IOBJ"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}