{"Request": {"Number": "2623507", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 422, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000592682018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002623507?language=E&token=B41C4A458A31CB1E0D47E7C72D2440A7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002623507", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002623507/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2623507"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "31.01.2022"}, "SAPComponentKey": {"_label": "Component", "value": "FI-FIO-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON>ori UI for General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fiori UI for Financial Accounting", "value": "FI-FIO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-FIO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON>ori UI for General Ledger Accounting", "value": "FI-FIO-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-FIO-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2623507 - Fiori Multidimensional Reporting in S/4 HANA onPremise using custom analytical queries"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>SAP&#160;delivers in S/4 HANA various Fiori Multidimensional&#160;Reports which allow analyzing the data in a flexible way (e.g. by easily adding dimensions and measures to the rows or columns).</p>\r\n<p>Customers and Partners can also create Fiori Multidimensional&#160;Reports based on an Analytcial Query and visualize them in the Fiori Launchpad. SAP offers for this puppose in S/4 HANA on Premise&#160;2 generic player&#160;applications which require mainly the Analytical Query as parameter:&#160;a Web Dynpro based reporting application&#160;and a SAPUI5 based&#160;reporting application (this reporting tool is obsolete since&#160;S/4 HANA on Premise 2021).&#160;The following describes how to configure these 2 reporting applications in Fiori Launchpad (FLP). (Please note: The Analytical Query can be either a ABAP CDS View - exposed as anyltical query - or a BW Query.)</p>\r\n<p>&#160;</p>\r\n<p><strong>1. Web Dynpro based reporting application for S/4 HANA&#160;(\"Web Dynpro Data Grid\")</strong></p>\r\n<p>(To be used as default reporting tool in S/4 HANA)</p>\r\n<p><strong>a) FLP Target Mapping</strong></p>\r\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\r\n<p>Action: &lt;arbitary action&gt;</p>\r\n<p>Application Type: Web Dynpro</p>\r\n<p>Title: &lt;arbitary title&gt;</p>\r\n<p>Application: FPM_BICS_OVP</p>\r\n<p>Configutation: FPM_BICS_OVP</p>\r\n<p>System Alias: &lt;RFC Connection to the backend, e.g. S4FIN; can be maintained via IMG (<em>Manage RFC Destinations</em>)&#160;or via the transaction SM59&gt;</p>\r\n<p>Device Types: Desktop</p>\r\n<p>Parameters:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Name</strong></td>\r\n<td><strong>Mandatory</strong></td>\r\n<td><strong>Value</strong></td>\r\n<td><strong>Default Value</strong></td>\r\n</tr>\r\n<tr>\r\n<td>bsa_query</td>\r\n<td>false</td>\r\n<td>&nbsp;</td>\r\n<td>&lt;ID of the custom analytical query&gt; (Please note:&#160;if an ABAP CDS View&#160;is exposed as anayltical query then you should use&#160;for the&#160;ID of the query the syntax 2C&lt;SQL View Name of&#160;the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\r\n</tr>\r\n<tr>\r\n<td>sap-ui-tech-hint</td>\r\n<td>true</td>\r\n<td>WDA</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>sap-ushell-next-navmode</td>\r\n<td>false</td>\r\n<td>&nbsp;</td>\r\n<td>explace</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>b) FLP Tile Configuration</strong></p>\r\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\r\n<p>Information: &lt;Accessible&gt; (recommended setting)</p>\r\n<p>Semantic Object: &lt;use the same&#160;Semantic Object&#160;as defined in the Target Mapping&gt;</p>\r\n<p>Action: &lt;use the same&#160;Action as defined in the Target Mapping&gt;</p>\r\n<p>Parameters: sap-ui-tech-hint=WDA</p>\r\n<p>&#160;</p>\r\n<p><strong>2. SAPUI5 based generic reporting application for S/4 HANA (\"Design Studio Data Grid\") - available&#160;since SAPUI5 1.48&#160;(S/4 HANA on Premise 1709)</strong></p>\r\n<p>(This reporting tool is obsolete since S/4 HANA on Premise 2021)</p>\r\n<p><strong>a) FLP Target Mapping</strong></p>\r\n<p>Semantic Object: &lt;choose a valid Semantic Object&gt;</p>\r\n<p>Action: &lt;arbitary action&gt;</p>\r\n<p>Application Type: SAPUI5 Fiori App</p>\r\n<p>Title: &lt;arbitary title&gt;</p>\r\n<p>URL: /sap/bc/ui5_ui5/sap/FIN_DS_ANALYZE</p>\r\n<p>ID: fin.acc.query.analyze</p>\r\n<p>Device Types: Desktop</p>\r\n<p>Parameters:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><strong>Name</strong></td>\r\n<td><strong>Default Value</strong></td>\r\n</tr>\r\n<tr>\r\n<td>XQUERY</td>\r\n<td>&lt;ID of the custom analytical query&gt; (Please note:&#160;if an ABAP CDS View&#160;is exposed as anayltical query then you should use&#160;for the&#160;ID of the query the syntax 2C&lt;SQL View Name of&#160;the CDS View&gt;; e.g. the ID of the query is 2CCFITRIALBALQ0001 if the SQL View Name is CFITRIALBALQ0001)</td>\r\n</tr>\r\n<tr>\r\n<td>XSEMANTIC_OBJECTS</td>\r\n<td>*</td>\r\n</tr>\r\n<tr>\r\n<td>XSYSTEM</td>\r\n<td>&lt;SAP System Alias (to the backend system), e.g. S4FIN; can be maintained via IMG (<em>Manage SAP System Aliases</em>)&#160;or via the transaction SM30 (View /IWFND/V_DFSYAL)&gt;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong>b) FLP Tile Configuration</strong></p>\r\n<p>Title: &lt;use the same Title as defined in the Target Mapping&gt;</p>\r\n<p>Semantic Object: &lt;use the same&#160;Semantic Object&#160;as defined in the Target Mapping&gt;</p>\r\n<p>Action: &lt;use the same&#160;Action as defined in the Target Mapping&gt;</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>(No correction required.)</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-RUI-FPM (FPM Analytical GUIBB)"}, {"Key": "Other Components", "Value": "BI-RA-AD-EA ( Embedded Analytics for S/4)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I047781)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I343530)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002623507/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002623507/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3010791", "RefComponent": "BI-RA-AD-EA", "RefTitle": "Result row title shows \"Total\" instead of Measure text", "RefUrl": "/notes/3010791 "}, {"RefNumber": "2675934", "RefComponent": "CA-GTF-VDM-QB", "RefTitle": "Can the user drill-down from one application to another application from within Query Browser", "RefUrl": "/notes/2675934 "}, {"RefNumber": "2557379", "RefComponent": "BI-RA-AD-EA", "RefTitle": "System alias '<systemAlias>' does not exist", "RefUrl": "/notes/2557379 "}, {"RefNumber": "2356997", "RefComponent": "BI-RA-AD-EA", "RefTitle": "Configuration steps for Design Studio in S/4HANA Environments with a Remote Gateway Server with UI5 1.44 or newer", "RefUrl": "/notes/2356997 "}, {"RefNumber": "2714996", "RefComponent": "BI-RA-AD-EA", "RefTitle": "INTERNAL: Embedded Analytics Design Studio Control Documentation", "RefUrl": "/notes/2714996 "}, {"RefNumber": "2579584", "RefComponent": "CO-OM-IS", "RefTitle": "Recommendations for Usage of Reports in Financial Reporting in S/4 HANA", "RefUrl": "/notes/2579584 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}