{"Request": {"Number": "1814877", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 388, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017592202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=EE48666D8E34CBEDE7908CFE1BD4A1AD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1814877"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.09.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1814877 - SAP HANA-optimized BI Content - Missing HANA DU with NW 7.30"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><strong>This procedure is only relevant if you are using SAP NetWeaver BW on HANA 7.30. </strong></p>\r\n<p><strong>For any higher release please follow the steps described in the documentation.</strong><br /><br /></p>\r\n<p>You want to use one of the following components of the SAP HANA-optimized BI Content</p>\r\n<p>A) Financial Accounting - Accounts Receivable (Real Time)<br /><br />The HANA Information Models<br />Analytic View AN_AR_ITEMS_CLEARED<br />Analytic View AN_AR_ITEMS_OPEN<br />Calculation View CV_AR_PAYMENT_FC<br />are not available on your SAP HANA database in package sap.bicont.fi.ar after installing BI Content AddOn 737/747 Support Package 4 or later.</p>\r\n<p>B) Sales &amp; Distribution - Backorders</p>\r\n<p>Calculation View CV_BACKORDERS</p>\r\n<p>is not available on your SAP HANA&#160;database in package sap.bicont.sd.dlv after installing BI Content AddOn 737/747 Support Package&#160;6 or later.</p>\r\n<p>C) Product Cost Forecast &amp; Simulation</p>\r\n<p>Calculation View CV_MAT_PRC</p>\r\n<p>Calculation View CV_PROD_COST</p>\r\n<p>Calculation View CV_PROD_MARGIN</p>\r\n<p>Stored Procedure SP_BEXP</p>\r\n<p>Stored Procedure SP_CITM</p>\r\n<p>Stored Procedure SP_CMSP</p>\r\n<p>Stored Procedure SP_COIT</p>\r\n<p>Stored Procedure SP_MTPR</p>\r\n<p>Stored Procedure SP_MTPR1</p>\r\n<p>are not available on your SAP HANA database in package sap.bicont.co.copc.cfs after installing BI Content AddOn 737/747 Support Package&#160;7 or later.</p>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP HANA-opimized BI Content, SAP HANA Information Models</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This is due to missing functionality in SAP NetWeaver 7.30.<br /><br />The ABAP transport framework to deploy SAP HANA DU'S is available with SAP NetWeaver &gt;= 7.31.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Create a customer message refering to this SAP note (number 1814877) and mention the component(s) you want to use:</p>\r\n<p>- A) Financial Accounting - Accounts Receivable (Real Time)</p>\r\n<p>- B) Sales &amp; Distribution - Backorders</p>\r\n<p>- C) Product Cost Forecast &amp; Simulation<br /><br />Assign your customer messsage to component BW-BCT-GEN (BI Content - Generic).<br /><br />You will be provided with the SAP HANA delivery unit.</p>\r\n<p>&#160;</p>\r\n<p>Be informed that this SAP Note is only valid for you, if your BW system runs on SAP NetWeaver 7.30.</p>\r\n<p>If your BW system runs on SAP NetWeaver 7.31 and higher, the HANA content objects were already imported into your system&#160;during installation of the software component BI&#160;CONT 747/757. For detailed information about the required support packages of the BI Content release&#160;please review paragraph 'Symptom' above.</p>\r\n<p>In this case you will only need to follow the step-by-step guidance ('<em>Prerequisites</em>')&#160;which is part of the 'SAP HANA-optimized BI Content' documentation in SAP Online Help:</p>\r\n<p><a target=\"_blank\" href=\"http://sap.help.com/\">http://sap.help.com</a> -&gt; NetWeaver -&gt; NetWeaver Business Warehouse -&gt; BI Content -&gt; <em>&lt;Your SAP Netweaver BI Content Release &amp; Support Package&gt;</em> -&gt; Application Help <em>&lt;Your language&gt;</em>&#160;-&gt; BI Content -&gt; SAP HANA-Optimized BI Content</p>\r\n<p>You will find the 'Prerequisites' as sub chapter in the corresponding content component that you want to use&#160;( <em>Financial Accounting: Accounts Receivable</em>, <em>Sales &amp; Distribution: Backorders</em>&#160;and/or <em>Product Cost Forecasting &amp; Simulation</em>)</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-CO-PC (BW only - Product Cost Controlling)"}, {"Key": "Other Components", "Value": "BW-BCT-FI (BW only - Financial Accounting)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BW (BW only - SD Content im BW System)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I037341)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028763)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1830665", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1830665"}, {"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1932464", "RefComponent": "BW-BCT-CO-PC", "RefTitle": "SAP HANA-optimized BI Content CO-PC CFS Deploy HANA DU NW 7.30", "RefUrl": "/notes/1932464 "}, {"RefNumber": "1936332", "RefComponent": "BW-BCT-SD-BW", "RefTitle": "SAP HANA-optimized BI Content SD-DLV-Deploy HANA DU NW 7.30", "RefUrl": "/notes/1936332 "}, {"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}