{"Request": {"Number": "1627018", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 601, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017302572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001627018?language=E&token=B9817656D87484335EED1915D7ACE379"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001627018", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001627018/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1627018"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.01.2012"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-REO"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Reorganization"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Reorganization", "value": "FI-GL-REO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-REO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1627018 - Composite SAP Note for segment reorganization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is a composite SAP Note. Under \"Related Notes\" you will find all SAP Notes that are relevant for the reorganization of segments.<br /><br />Important information:<br />Contact your SAP Account Executive to find out whether there are additional license fees for the use of segment reorganization.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Reorganization, segment<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You have activated the business function FIN_GL_REORG_SEG.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Check the related notes.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D021112)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D038166)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001627018/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001627018/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2130322", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2130322"}, {"RefNumber": "2152390", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Check Manager: ATC - missing interface methods II", "RefUrl": "/notes/2152390"}, {"RefNumber": "2150945", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects II", "RefUrl": "/notes/2150945"}, {"RefNumber": "2148718", "RefComponent": "FI-GL-REO", "RefTitle": "Additional info displayed incorrectly", "RefUrl": "/notes/2148718"}, {"RefNumber": "2148363", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXV)", "RefUrl": "/notes/2148363"}, {"RefNumber": "2146307", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Postprocessing orders without material assignment", "RefUrl": "/notes/2146307"}, {"RefNumber": "2139388", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Information for table FAGL_R_SPL and FAGL_SPLINFO", "RefUrl": "/notes/2139388"}, {"RefNumber": "2136922", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR reassigned despite complete clearing", "RefUrl": "/notes/2136922"}, {"RefNumber": "2132800", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Central master data system MDG", "RefUrl": "/notes/2132800"}, {"RefNumber": "2131911", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Error log is incomplete", "RefUrl": "/notes/2131911"}, {"RefNumber": "2131691", "RefComponent": "FI-GL-REO-CO", "RefTitle": "CO-PA revaluation does not work for cancelation invoices", "RefUrl": "/notes/2131691"}, {"RefNumber": "2130053", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: Analysis report for account assignment change/generating receivables and payables II a", "RefUrl": "/notes/2130053"}, {"RefNumber": "2127072", "RefComponent": "FI-GL-REO-CO", "RefTitle": "The settlement rule to CO-PA has incorrect characteristics after reassignment", "RefUrl": "/notes/2127072"}, {"RefNumber": "2127049", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 534 (II)", "RefUrl": "/notes/2127049"}, {"RefNumber": "2127046", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Navigation to an incorrect asset", "RefUrl": "/notes/2127046"}, {"RefNumber": "2125807", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Segment reorganization: Do not select deactivated assets", "RefUrl": "/notes/2125807"}, {"RefNumber": "2125315", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error 566 during reassignment for documents from online CO-FI integration (XXIV)", "RefUrl": "/notes/2125315"}, {"RefNumber": "2120604", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump when generating AR (CX_SY_DYNAMIC_OSQL_SEMANTICS)", "RefUrl": "/notes/2120604"}, {"RefNumber": "2120457", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Reorganization: Entries disappeared from the objectlist after regeneration.", "RefUrl": "/notes/2120457"}, {"RefNumber": "2116852", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Segment Reorganization for GL accounts is not correct", "RefUrl": "/notes/2116852"}, {"RefNumber": "2115768", "RefComponent": "FI-GL-REO", "RefTitle": "FAQ: Profit center reorganization hierarchy", "RefUrl": "/notes/2115768"}, {"RefNumber": "2106730", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Unable to reverse the settlement", "RefUrl": "/notes/2106730"}, {"RefNumber": "2105830", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Assert on Profit Center in Segment Reorganization of sales orders", "RefUrl": "/notes/2105830"}, {"RefNumber": "2100217", "RefComponent": "FI-GL-REO-CO", "RefTitle": "FAGL_REORGANIZATION 335: Empty key date in the long text", "RefUrl": "/notes/2100217"}, {"RefNumber": "2100111", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Adding of an additional field in the object list display results in a shortdump", "RefUrl": "/notes/2100111"}, {"RefNumber": "2098891", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Migrated documents not reassigned (FAGL_REORGANIZATION 533)", "RefUrl": "/notes/2098891"}, {"RefNumber": "2098392", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Analysis report for balance determination for network activities and cost objects", "RefUrl": "/notes/2098392"}, {"RefNumber": "2097091", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Analysis report for stock determination of CO orders", "RefUrl": "/notes/2097091"}, {"RefNumber": "2095521", "RefComponent": "FI-GL-REO", "RefTitle": "License audit measurement", "RefUrl": "/notes/2095521"}, {"RefNumber": "2094622", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Analysis report for stock determination for SD documents and WBS elements", "RefUrl": "/notes/2094622"}, {"RefNumber": "2093824", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXIII)", "RefUrl": "/notes/2093824"}, {"RefNumber": "2090764", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Maintenance of specific restrictions for unassigned purchase orders not possible", "RefUrl": "/notes/2090764"}, {"RefNumber": "2082695", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG:  Objects without plan assignment in the reorganization tables", "RefUrl": "/notes/2082695"}, {"RefNumber": "2079684", "RefComponent": "FI-GL-REO", "RefTitle": "Error when implementing SAP Note 1878955 (SAP_FIN 617)", "RefUrl": "/notes/2079684"}, {"RefNumber": "2078642", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Error AA003 (\"Asset ... is being processed by user ...\")", "RefUrl": "/notes/2078642"}, {"RefNumber": "2070401", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXII)", "RefUrl": "/notes/2070401"}, {"RefNumber": "2059369", "RefComponent": "FI-GL-REO-MM", "RefTitle": "Specific restrictions not considered for MAT object type", "RefUrl": "/notes/2059369"}, {"RefNumber": "2057183", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: Analysis report for account reassignment of receivables and payables", "RefUrl": "/notes/2057183"}, {"RefNumber": "2055537", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXI)", "RefUrl": "/notes/2055537"}, {"RefNumber": "2048665", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generate AP/AR - recognition of rounding differences III", "RefUrl": "/notes/2048665"}, {"RefNumber": "2048514", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generating clearing documents", "RefUrl": "/notes/2048514"}, {"RefNumber": "2045737", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR reassignment - reading of split information", "RefUrl": "/notes/2045737"}, {"RefNumber": "2043647", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generate AP/AR - recognition of rounding differences II", "RefUrl": "/notes/2043647"}, {"RefNumber": "2028638", "RefComponent": "FI-GL-REO", "RefTitle": "Derivation of the profit center from the cost center after reorganization", "RefUrl": "/notes/2028638"}, {"RefNumber": "2025982", "RefComponent": "FI-GL-REO", "RefTitle": "Change of an assignment possible even though the reorg. plan is closed", "RefUrl": "/notes/2025982"}, {"RefNumber": "2023029", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generate AP/AR - recognition of rounding differences", "RefUrl": "/notes/2023029"}, {"RefNumber": "2019645", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR-REORG: Error F5 800", "RefUrl": "/notes/2019645"}, {"RefNumber": "2015638", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Additional balancing fields - exceptions handling", "RefUrl": "/notes/2015638"}, {"RefNumber": "2010551", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Payables for purchase orders", "RefUrl": "/notes/2010551"}, {"RefNumber": "2009252", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Checkpoint group for dialog indicator", "RefUrl": "/notes/2009252"}, {"RefNumber": "2009162", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Rounding differences for foreign currency valuation differences", "RefUrl": "/notes/2009162"}, {"RefNumber": "2006565", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Activating checkpoints", "RefUrl": "/notes/2006565"}, {"RefNumber": "2005825", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (XX)", "RefUrl": "/notes/2005825"}, {"RefNumber": "2005245", "RefComponent": "FI-GL-REO", "RefTitle": "CheckMan: ATC Missing Interface Methods", "RefUrl": "/notes/2005245"}, {"RefNumber": "2002242", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566/567 II - code", "RefUrl": "/notes/2002242"}, {"RefNumber": "1994499", "RefComponent": "FI-GL-FL", "RefTitle": "Error FAGL_REORGANIZATION 566 during reassignment", "RefUrl": "/notes/1994499"}, {"RefNumber": "1988777", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Exclusion of cleared documents during display II", "RefUrl": "/notes/1988777"}, {"RefNumber": "1987684", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: Generate AP/AR - runtime analysis", "RefUrl": "/notes/1987684"}, {"RefNumber": "1987207", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: AP/AR - Document account not reassigned despite status message of account reassignment successful or higher", "RefUrl": "/notes/1987207"}, {"RefNumber": "1987194", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during object generation III.", "RefUrl": "/notes/1987194"}, {"RefNumber": "1984829", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump during reassignment - TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1984829"}, {"RefNumber": "1984719", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Check on Hierarchy modification", "RefUrl": "/notes/1984719"}, {"RefNumber": "1984087", "RefComponent": "FI-GL-REO", "RefTitle": "Accessing unsorted tables for profit center reorganization", "RefUrl": "/notes/1984087"}, {"RefNumber": "1982408", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Object type not determined (FAGL_REORGANIZATION505)", "RefUrl": "/notes/1982408"}, {"RefNumber": "1981792", "RefComponent": "FI-GL-REO", "RefTitle": "Additional info for users", "RefUrl": "/notes/1981792"}, {"RefNumber": "1975633", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: AR simulation of ext. document splitting characteristics", "RefUrl": "/notes/1975633"}, {"RefNumber": "1974980", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Exclusion of cleared documents during display", "RefUrl": "/notes/1974980"}, {"RefNumber": "1973715", "RefComponent": "FI-GL-REO-AA", "RefTitle": "AA347 with account assignment change for assets", "RefUrl": "/notes/1973715"}, {"RefNumber": "1965884", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump when generating AP/AR 566 (XIX)", "RefUrl": "/notes/1965884"}, {"RefNumber": "1964587", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump when generating AP/AR 566 (XVIII)", "RefUrl": "/notes/1964587"}, {"RefNumber": "1964504", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Check of transfer of billing documents (IV)", "RefUrl": "/notes/1964504"}, {"RefNumber": "1961091", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects", "RefUrl": "/notes/1961091"}, {"RefNumber": "1958869", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump during the reassignment of receivables and payables - manual split 566 (XIX)", "RefUrl": "/notes/1958869"}, {"RefNumber": "1955532", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Determining material stock for cost element category 90", "RefUrl": "/notes/1955532"}, {"RefNumber": "1955477", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error in reassignment of AP/AR; 566 (XVI)", "RefUrl": "/notes/1955477"}, {"RefNumber": "1952112", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 535 during reassignment of AP/AR", "RefUrl": "/notes/1952112"}, {"RefNumber": "1939237", "RefComponent": "FI-GL-FL", "RefTitle": "Document splitting: Line items returned with zero amounts and quantities", "RefUrl": "/notes/1939237"}, {"RefNumber": "1924356", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump during generation / ASSERTION_FAILED", "RefUrl": "/notes/1924356"}, {"RefNumber": "1910342", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566/567", "RefUrl": "/notes/1910342"}, {"RefNumber": "1895810", "RefComponent": "FI-GL-REO", "RefTitle": "Timout for CO accrual/deferral posting", "RefUrl": "/notes/1895810"}, {"RefNumber": "1891569", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Error FAGL_REORGANIZATION 051 during transfer posting", "RefUrl": "/notes/1891569"}, {"RefNumber": "1885761", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump when generating APAR / 566 (V)", "RefUrl": "/notes/1885761"}, {"RefNumber": "1881515", "RefComponent": "FI-GL-REO", "RefTitle": "Reorganization: Profit center in cost center cannot be changed (II)", "RefUrl": "/notes/1881515"}, {"RefNumber": "1880655", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 535", "RefUrl": "/notes/1880655"}, {"RefNumber": "1878955", "RefComponent": "FI-GL-REO", "RefTitle": "Changing a purchase order after profit center reorganization", "RefUrl": "/notes/1878955"}, {"RefNumber": "1873064", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Valuation classes ignored", "RefUrl": "/notes/1873064"}, {"RefNumber": "1870718", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Settlement reversal is possible after PC reorganization", "RefUrl": "/notes/1870718"}, {"RefNumber": "1869309", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (IV)", "RefUrl": "/notes/1869309"}, {"RefNumber": "1866467", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Object list of assets with service orders", "RefUrl": "/notes/1866467"}, {"RefNumber": "1865363", "RefComponent": "FI-GL-REO-BF", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED during display of object list", "RefUrl": "/notes/1865363"}, {"RefNumber": "1864587", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Incorrect company code for a purchase order with account assignment", "RefUrl": "/notes/1864587"}, {"RefNumber": "1863965", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incorect numbers of objects in reorganization plan", "RefUrl": "/notes/1863965"}, {"RefNumber": "1853736", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during object generation II.", "RefUrl": "/notes/1853736"}, {"RefNumber": "1844856", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during object generation", "RefUrl": "/notes/1844856"}, {"RefNumber": "1824220", "RefComponent": "FI-GL-REO-BF", "RefTitle": "MESSAGE_TYPE_X during object list generation", "RefUrl": "/notes/1824220"}, {"RefNumber": "1820945", "RefComponent": "FI-GL-REO", "RefTitle": "Reorganization incorrectly uses default account assignment", "RefUrl": "/notes/1820945"}, {"RefNumber": "1816152", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "RefUrl": "/notes/1816152"}, {"RefNumber": "1812836", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR objects unjustified at first level", "RefUrl": "/notes/1812836"}, {"RefNumber": "1810605", "RefComponent": "FI-GL-REO", "RefTitle": "General information about profit center reorganization", "RefUrl": "/notes/1810605"}, {"RefNumber": "1810392", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (II)", "RefUrl": "/notes/1810392"}, {"RefNumber": "1809521", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error messages FAGL_REORGANIZATION 534 and 536", "RefUrl": "/notes/1809521"}, {"RefNumber": "1808980", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566", "RefUrl": "/notes/1808980"}, {"RefNumber": "1801767", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: Scope of functions", "RefUrl": "/notes/1801767"}, {"RefNumber": "1797558", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_SPLINFO - incorr. Prctr/segment after reorg", "RefUrl": "/notes/1797558"}, {"RefNumber": "1793304", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Performance: Generation not performed in parallel processing", "RefUrl": "/notes/1793304"}, {"RefNumber": "1790465", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Transfer posting of cleared items", "RefUrl": "/notes/1790465"}, {"RefNumber": "1787464", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Generatn after acct assgmt chg: Incorr first-level objects", "RefUrl": "/notes/1787464"}, {"RefNumber": "1783742", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Message FAGL_REORGANIZATION 123: Cut name of parameters", "RefUrl": "/notes/1783742"}, {"RefNumber": "1778408", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR - Reorg: Runtime error MESSAGE_TYPE_X gen. AP/AR", "RefUrl": "/notes/1778408"}, {"RefNumber": "1774057", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 535", "RefUrl": "/notes/1774057"}, {"RefNumber": "1773094", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump ITAB_DUPLICATE_KEY Gen. object list AP/AR", "RefUrl": "/notes/1773094"}, {"RefNumber": "1772403", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 575", "RefUrl": "/notes/1772403"}, {"RefNumber": "1768369", "RefComponent": "FI-GL-REO", "RefTitle": "Object list not marked as 'Completely processed'", "RefUrl": "/notes/1768369"}, {"RefNumber": "1765330", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Closing plans for which successful processing is not complete", "RefUrl": "/notes/1765330"}, {"RefNumber": "1763488", "RefComponent": "FI-GL-REO-BF", "RefTitle": "No account assignment change for sales orders (XCLOSED = X)", "RefUrl": "/notes/1763488"}, {"RefNumber": "1760882", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Rntme err TSV_TNEW_PAGE_ALLOC_FAILED due to too many objects", "RefUrl": "/notes/1760882"}, {"RefNumber": "1759519", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Source document of transfer posting is not displayed", "RefUrl": "/notes/1759519"}, {"RefNumber": "1757942", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime err. TSV_TNEW_PAGE_ALLOC_FAILED due to too many objs", "RefUrl": "/notes/1757942"}, {"RefNumber": "1756691", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "RefUrl": "/notes/1756691"}, {"RefNumber": "1752608", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED in CL_FAGL_R_OBJLIST...", "RefUrl": "/notes/1752608"}, {"RefNumber": "1751367", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassigned amount is incomplete for AP/AR", "RefUrl": "/notes/1751367"}, {"RefNumber": "1750217", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 533", "RefUrl": "/notes/1750217"}, {"RefNumber": "1749843", "RefComponent": "FI-GL-GL-A", "RefTitle": "Termination when generating payables", "RefUrl": "/notes/1749843"}, {"RefNumber": "1747717", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Short dump when generating AP/AR", "RefUrl": "/notes/1747717"}, {"RefNumber": "1746777", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Reorg job terminates", "RefUrl": "/notes/1746777"}, {"RefNumber": "1746704", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Unjustified issue of AIST009 for reorganization transfer posting", "RefUrl": "/notes/1746704"}, {"RefNumber": "1744690", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Poorer performance after activating a business function", "RefUrl": "/notes/1744690"}, {"RefNumber": "1739651", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message when generating AP/AR", "RefUrl": "/notes/1739651"}, {"RefNumber": "1738491", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Failed split sim. leads to objects class. as 1st level", "RefUrl": "/notes/1738491"}, {"RefNumber": "1737132", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Job status display is unreliable", "RefUrl": "/notes/1737132"}, {"RefNumber": "1734472", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Meaningful message text for message 503", "RefUrl": "/notes/1734472"}, {"RefNumber": "1733459", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Runtime error during generation of receivables", "RefUrl": "/notes/1733459"}, {"RefNumber": "1733158", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Objects are not reorganized", "RefUrl": "/notes/1733158"}, {"RefNumber": "1731429", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Performance of WD applicatn for reorganizatn plan in detail", "RefUrl": "/notes/1731429"}, {"RefNumber": "1731380", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: wrong balances of materials(object type MAT)", "RefUrl": "/notes/1731380"}, {"RefNumber": "1721168", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassignment AP/AP: Unjustified error messages", "RefUrl": "/notes/1721168"}, {"RefNumber": "1721166", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Error 505 for down payment requests", "RefUrl": "/notes/1721166"}, {"RefNumber": "1718793", "RefComponent": "FI-GL-REO", "RefTitle": "FIN_GL_REORG_SEG Object type GL should be reassignable", "RefUrl": "/notes/1718793"}, {"RefNumber": "1718567", "RefComponent": "FI-GL-REO-BF", "RefTitle": "BAdI FAGL_R_GENERATE: Status cannot be set", "RefUrl": "/notes/1718567"}, {"RefNumber": "1716690", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Short dump when generating object type AR", "RefUrl": "/notes/1716690"}, {"RefNumber": "1707832", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Various errors", "RefUrl": "/notes/1707832"}, {"RefNumber": "1707428", "RefComponent": "FI-GL-REO-BF", "RefTitle": "BAdI for overwriting the transaction type", "RefUrl": "/notes/1707428"}, {"RefNumber": "1706481", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Short dump when generating object types AP/AR", "RefUrl": "/notes/1706481"}, {"RefNumber": "1702346", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD generates transfer postings", "RefUrl": "/notes/1702346"}, {"RefNumber": "1696418", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Inconsistencies in reorganization of receivables", "RefUrl": "/notes/1696418"}, {"RefNumber": "1694903", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: FIN_GL_REORG_SEG should check whether New G/L is active", "RefUrl": "/notes/1694903"}, {"RefNumber": "1693664", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incomplete deletion of a reorganization plan", "RefUrl": "/notes/1693664"}, {"RefNumber": "1687685", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Assets for investment measure", "RefUrl": "/notes/1687685"}, {"RefNumber": "1687570", "RefComponent": "FI-GL-REO", "RefTitle": "Wrong plan status when using service to create plan", "RefUrl": "/notes/1687570"}, {"RefNumber": "1686832", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Minor adjustments in area of reorganization framework", "RefUrl": "/notes/1686832"}, {"RefNumber": "1684748", "RefComponent": "FI-GL-REO", "RefTitle": "Transfer posting object list not flagged as processed", "RefUrl": "/notes/1684748"}, {"RefNumber": "1683456", "RefComponent": "FI-GL-REO", "RefTitle": "Objects not reassigned or transferred", "RefUrl": "/notes/1683456"}, {"RefNumber": "1683439", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Preventing inconsistencies for invoice reference", "RefUrl": "/notes/1683439"}, {"RefNumber": "1677834", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Inconsistencies in reorganization of receivables", "RefUrl": "/notes/1677834"}, {"RefNumber": "1675485", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Error AAPO190 when you make an account assignment change for assets", "RefUrl": "/notes/1675485"}, {"RefNumber": "1674191", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer posting: System error in FI interface (F5 691)", "RefUrl": "/notes/1674191"}, {"RefNumber": "1672235", "RefComponent": "FI-GL-REO", "RefTitle": "Dump ASSERTION_FAILED during reassignment", "RefUrl": "/notes/1672235"}, {"RefNumber": "1671509", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error F5 800: Inconsistent currency information", "RefUrl": "/notes/1671509"}, {"RefNumber": "1667603", "RefComponent": "FI-GL-REO", "RefTitle": "Archiving in reorganization", "RefUrl": "/notes/1667603"}, {"RefNumber": "1665678", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED when changing acct assignment", "RefUrl": "/notes/1665678"}, {"RefNumber": "1664739", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: ASSERTION_FAILED when reassigning AP/AR", "RefUrl": "/notes/1664739"}, {"RefNumber": "1664060", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Data inconsistency during generation of AP/AR", "RefUrl": "/notes/1664060"}, {"RefNumber": "1663603", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Message GLT0 002 after successful reorganization", "RefUrl": "/notes/1663603"}, {"RefNumber": "1663555", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message when reassigning AP/AR", "RefUrl": "/notes/1663555"}, {"RefNumber": "1661117", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Short dump DBIF_RSQL_INVALID_RSQL in RKERV002", "RefUrl": "/notes/1661117"}, {"RefNumber": "1660943", "RefComponent": "FI-GL-REO", "RefTitle": "Runtime error DBIF_RSQL_INVALID_RSQL during reassignment", "RefUrl": "/notes/1660943"}, {"RefNumber": "1660067", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED: Update of dummy objects", "RefUrl": "/notes/1660067"}, {"RefNumber": "1658386", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Warning message \"Document was not simulated\"", "RefUrl": "/notes/1658386"}, {"RefNumber": "1656973", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Authorization check", "RefUrl": "/notes/1656973"}, {"RefNumber": "1655685", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Object lists are not indicated as processed", "RefUrl": "/notes/1655685"}, {"RefNumber": "1655212", "RefComponent": "FI-GL-REO-AA", "RefTitle": "No new time interval during reorg for assests not posted to", "RefUrl": "/notes/1655212"}, {"RefNumber": "1651132", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Input help for hierarchy version", "RefUrl": "/notes/1651132"}, {"RefNumber": "1650324", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generation terminates: Unstable grouping AP/AR", "RefUrl": "/notes/1650324"}, {"RefNumber": "1649426", "RefComponent": "FI-GL-REO", "RefTitle": "Restriction Characteristics for Segment Reorganization", "RefUrl": "/notes/1649426"}, {"RefNumber": "1643715", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: APAR objs. missng for docs. w/ partner assignment", "RefUrl": "/notes/1643715"}, {"RefNumber": "1641528", "RefComponent": "FI-GL-REO", "RefTitle": "Lock entry for processing object list does not exist", "RefUrl": "/notes/1641528"}, {"RefNumber": "1640228", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Warning message \"Document was not simulated\"", "RefUrl": "/notes/1640228"}, {"RefNumber": "1639690", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Unjustified error FAGL_REORGANIZATION 532", "RefUrl": "/notes/1639690"}, {"RefNumber": "1628687", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Cash discount clearing lines not reassigned", "RefUrl": "/notes/1628687"}, {"RefNumber": "1628255", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Configuring message FAGL_R<PERSON>ORGANIZATION 029", "RefUrl": "/notes/1628255"}, {"RefNumber": "1627187", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: append dump in CL_FAGL_R_OBJ_TYPE_001_MAT", "RefUrl": "/notes/1627187"}, {"RefNumber": "1626583", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error FAGL_REORGANIZATION 533 during reassignment", "RefUrl": "/notes/1626583"}, {"RefNumber": "1626389", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Document splitting not updated (without message)", "RefUrl": "/notes/1626389"}, {"RefNumber": "1619269", "RefComponent": "FI-GL-REO", "RefTitle": "Transf.posting: Security check, no account or CoCd specified", "RefUrl": "/notes/1619269"}, {"RefNumber": "1609861", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Background processes terminate due to deadlock", "RefUrl": "/notes/1609861"}, {"RefNumber": "1599168", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Message FAGL_REORGANIZATION 544", "RefUrl": "/notes/1599168"}, {"RefNumber": "1598745", "RefComponent": "FI-AA-AA-A", "RefTitle": "Reorg transfer posting is made w/ unexpected posting date", "RefUrl": "/notes/1598745"}, {"RefNumber": "1598626", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Posting date for ledger-specific transfers", "RefUrl": "/notes/1598626"}, {"RefNumber": "1597693", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR: Various PRCTR/SEG for same account assgnmt", "RefUrl": "/notes/1597693"}, {"RefNumber": "1589176", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Asset master data with profit center / filling segment", "RefUrl": "/notes/1589176"}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - Profit Center Reorganization", "RefUrl": "/notes/1534197"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3303225", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Organizational Change: Profitcenterderivation during Cancellation", "RefUrl": "/notes/3303225 "}, {"RefNumber": "2472456", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Object detail in the hierarchy", "RefUrl": "/notes/2472456 "}, {"RefNumber": "3271352", "RefComponent": "FI-GL-REO-GL", "RefTitle": "OrglChange: Suppress lines with initial amount", "RefUrl": "/notes/3271352 "}, {"RefNumber": "3257997", "RefComponent": "FI-GL-REO-GL", "RefTitle": "OrglChange: Not pass KTOSL except for GRIR", "RefUrl": "/notes/3257997 "}, {"RefNumber": "3261132", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Message Fagl_reorganization 342 for WBS elements", "RefUrl": "/notes/3261132 "}, {"RefNumber": "2517602", "RefComponent": "FI-GL-REO-AA", "RefTitle": "AS02: Cost center cannot be changed even though it is not in reorganization", "RefUrl": "/notes/2517602 "}, {"RefNumber": "2511442", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Error AAPO 522 'Asset was already reorganized after the posting period'", "RefUrl": "/notes/2511442 "}, {"RefNumber": "2525477", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXXIII) - rounding differences AP/AR/GLOI", "RefUrl": "/notes/2525477 "}, {"RefNumber": "2562544", "RefComponent": "FI-GL-REO", "RefTitle": "FAGL_R_ASSETS_CONSISTENZ_CHECK: Dump SAPSQL_PARSE_ERROR", "RefUrl": "/notes/2562544 "}, {"RefNumber": "2561764", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAQ: Filling asset master data for segment reporting", "RefUrl": "/notes/2561764 "}, {"RefNumber": "3221549", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generation: Parallel processing by company code AP/AR/GLOI II", "RefUrl": "/notes/3221549 "}, {"RefNumber": "2551517", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Changes in SD data model for Rel. S4Core", "RefUrl": "/notes/2551517 "}, {"RefNumber": "2626588", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Fagl_reorganization 008 for sales and distribution documents", "RefUrl": "/notes/2626588 "}, {"RefNumber": "2953916", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 514/515 when generating AP/AR/GLOI", "RefUrl": "/notes/2953916 "}, {"RefNumber": "2547997", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Change date of purchasing document item", "RefUrl": "/notes/2547997 "}, {"RefNumber": "2508710", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Deletion of sales and distribution documents", "RefUrl": "/notes/2508710 "}, {"RefNumber": "2989795", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Open items posted only to specifc ledgers not selected during reorganization of open G/L account items (GLOI)", "RefUrl": "/notes/2989795 "}, {"RefNumber": "2997721", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Material Ledger closing CKLMP in connection with profit center reorganization plan derives incorrect PRCTR", "RefUrl": "/notes/2997721 "}, {"RefNumber": "3018334", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: Incorrect object list for material stock", "RefUrl": "/notes/3018334 "}, {"RefNumber": "3024382", "RefComponent": "FI-AA-AA-A", "RefTitle": "Transfer of organizational units: Incorrect amount for investment support", "RefUrl": "/notes/3024382 "}, {"RefNumber": "3025393", "RefComponent": "FI-GL-REO-BF", "RefTitle": "SEG: Specific Restrictions not applied during Reassignment", "RefUrl": "/notes/3025393 "}, {"RefNumber": "3044229", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Error in FAGLCORC due to open profit center Reorganization Plan", "RefUrl": "/notes/3044229 "}, {"RefNumber": "3087195", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization and segment reorganization", "RefUrl": "/notes/3087195 "}, {"RefNumber": "3088873", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Segment reorganization: AA347 with account assignment change for assets", "RefUrl": "/notes/3088873 "}, {"RefNumber": "3094395", "RefComponent": "FI-GL-REO", "RefTitle": "OrgChange: Enrichment of document splitting information", "RefUrl": "/notes/3094395 "}, {"RefNumber": "3115636", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR reorganization: FI documents with exclusively cleared customer/vendor line items are processed at first level", "RefUrl": "/notes/3115636 "}, {"RefNumber": "3010483", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS02: Cost center cannot be changed despite reorganization plan without restrictions", "RefUrl": "/notes/3010483 "}, {"RefNumber": "3137756", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Deadlock during reassignment of sales documents", "RefUrl": "/notes/3137756 "}, {"RefNumber": "3195251", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Profit Center Reorganization: Object list generation continues even though reorg plan is already closed.", "RefUrl": "/notes/3195251 "}, {"RefNumber": "2883324", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Reorganization Profit Center: Runtime Error BCD_FIELD_OVERFLOW occurs", "RefUrl": "/notes/2883324 "}, {"RefNumber": "3012930", "RefComponent": "FI-GL-REO-GL", "RefTitle": "REORG: PRCTR/SEG currency type missing in manual split", "RefUrl": "/notes/3012930 "}, {"RefNumber": "3131931", "RefComponent": "FI-AA-AA-A", "RefTitle": "Missing transfer between organizational units in reserves areas", "RefUrl": "/notes/3131931 "}, {"RefNumber": "3134230", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Memory overflow during generation of receivables and payables", "RefUrl": "/notes/3134230 "}, {"RefNumber": "3098840", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error in FAGLCORC, MIGO, ME21 due to open PC Reorg plan", "RefUrl": "/notes/3098840 "}, {"RefNumber": "3104831", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Dump SAPSQL_PARSE_ERROR when generating projects", "RefUrl": "/notes/3104831 "}, {"RefNumber": "3106652", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Last change date II", "RefUrl": "/notes/3106652 "}, {"RefNumber": "3116795", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Payables/open items with active revenue recognition", "RefUrl": "/notes/3116795 "}, {"RefNumber": "3123561", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Profit center of an older period for account assignment to cost center", "RefUrl": "/notes/3123561 "}, {"RefNumber": "3124258", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: Specific restriction for the object type GLOI", "RefUrl": "/notes/3124258 "}, {"RefNumber": "3127233", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Generation of SD takes too long", "RefUrl": "/notes/3127233 "}, {"RefNumber": "3138191", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: FAGL_REORGANIZATION049 when changing PRCTR", "RefUrl": "/notes/3138191 "}, {"RefNumber": "3140667", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generate receivables, payables, and open items - transfer XLEVEL1", "RefUrl": "/notes/3140667 "}, {"RefNumber": "3142521", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PCA Reorganization: CONVT_OVERFLOW raised in FAGL_R_PRCTR_CHECK_COIOB", "RefUrl": "/notes/3142521 "}, {"RefNumber": "3165467", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Performance when reading the table FAGL_R_OI_TRACK1 during posting III", "RefUrl": "/notes/3165467 "}, {"RefNumber": "3165603", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassignment - FAGL_REORGANIZATION 555 - no items from splitter/GLOI 566 due to tax account", "RefUrl": "/notes/3165603 "}, {"RefNumber": "3102522", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Performance when reading the table FAGL_R_OI_TRACK1 during posting II", "RefUrl": "/notes/3102522 "}, {"RefNumber": "3067234", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Definition of display characteristics by object type (GLOI/AP/AR)", "RefUrl": "/notes/3067234 "}, {"RefNumber": "3000502", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassignment - FAGL_Reorganization 566 (XXXV) - use of extended local currencies", "RefUrl": "/notes/3000502 "}, {"RefNumber": "3009638", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Dump ITAB_ILLEGAL_SORT_ORDER when saving APAR tables", "RefUrl": "/notes/3009638 "}, {"RefNumber": "2965896", "RefComponent": "FI-GL-REO", "RefTitle": "OrgChange: Wrong Profit Center derived", "RefUrl": "/notes/2965896 "}, {"RefNumber": "2909449", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Selection of object type GLOI takes a long time", "RefUrl": "/notes/2909449 "}, {"RefNumber": "3017681", "RefComponent": "FI-GL-GL-J", "RefTitle": "extension of the exceptions in FI for reorganization transfer postings to reference transaction 'BKPWF'", "RefUrl": "/notes/3017681 "}, {"RefNumber": "3009624", "RefComponent": "FI-GL-REO", "RefTitle": "Profit Center and Segment Reorganization in S/4", "RefUrl": "/notes/3009624 "}, {"RefNumber": "3007825", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PRCTR: FAGL_REORGANIZATION 078", "RefUrl": "/notes/3007825 "}, {"RefNumber": "3005237", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump R_SPLIT_MANUALLY_CHECK_SIGN_P (MOVE_TO_LIT_NOTALLOWED_NODATA)", "RefUrl": "/notes/3005237 "}, {"RefNumber": "2959624", "RefComponent": "FI-GL-REO", "RefTitle": "OrgChange: Error message GLT2201", "RefUrl": "/notes/2959624 "}, {"RefNumber": "2954683", "RefComponent": "FI-GL-GL-A", "RefTitle": "OrgChange: Overflow while reset clearing and reversal", "RefUrl": "/notes/2954683 "}, {"RefNumber": "2954617", "RefComponent": "FI-GL-REO", "RefTitle": "OrgChange: Organizational Change was wrong detected", "RefUrl": "/notes/2954617 "}, {"RefNumber": "2948256", "RefComponent": "FI-AA-AA-A", "RefTitle": "Transfer posting of organizational units: Incorrect amount in derived area", "RefUrl": "/notes/2948256 "}, {"RefNumber": "2938220", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: G/L accounts managed on open item basis as first-level objects II", "RefUrl": "/notes/2938220 "}, {"RefNumber": "2932005", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Derivation of incorrect profit center for posting to a prior period", "RefUrl": "/notes/2932005 "}, {"RefNumber": "2908810", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: Correction of debit/credit indicator", "RefUrl": "/notes/2908810 "}, {"RefNumber": "2893707", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Poor performance during reassignment of maintenance orders/internal orders", "RefUrl": "/notes/2893707 "}, {"RefNumber": "2885142", "RefComponent": "FI-GL-REO-BF", "RefTitle": "After profit center reorganization the reorganization plan cannot be open", "RefUrl": "/notes/2885142 "}, {"RefNumber": "2844337", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Runtime error SAPSQL_PARSE_ERROR for object list: Asset", "RefUrl": "/notes/2844337 "}, {"RefNumber": "2822857", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Export of object list  results with corrupted file", "RefUrl": "/notes/2822857 "}, {"RefNumber": "2790169", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: Transfer posting of material documents", "RefUrl": "/notes/2790169 "}, {"RefNumber": "2786702", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PCA: Missing objects in general restrictions in report FAGL_R_OBJ_GENERATE_SIM", "RefUrl": "/notes/2786702 "}, {"RefNumber": "2779370", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Error KI 100 during generation of SOI", "RefUrl": "/notes/2779370 "}, {"RefNumber": "2864542", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AR/AP/GLOI: Dump during generation - COMPUTE_INT_PLUS_OVERFLOW (II)", "RefUrl": "/notes/2864542 "}, {"RefNumber": "2795117", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Reorganization: Transfer posting of open items without clearing information in the G/L", "RefUrl": "/notes/2795117 "}, {"RefNumber": "2833271", "RefComponent": "FI-GL-REO", "RefTitle": "Segment Reorganization: Transfer Posting into Predictive Ledger", "RefUrl": "/notes/2833271 "}, {"RefNumber": "2750178", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Tracking table for open items is not completely deleted", "RefUrl": "/notes/2750178 "}, {"RefNumber": "2627020", "RefComponent": "FI-GL-REO", "RefTitle": "Performance issues during planning", "RefUrl": "/notes/2627020 "}, {"RefNumber": "2629244", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PCA REO: Performance optimization of objectlist processing", "RefUrl": "/notes/2629244 "}, {"RefNumber": "2657219", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PCA: General plan restrictions called repeatedly", "RefUrl": "/notes/2657219 "}, {"RefNumber": "2660941", "RefComponent": "FI-GL-REO", "RefTitle": "License audit measurement returns incorrect values", "RefUrl": "/notes/2660941 "}, {"RefNumber": "2671933", "RefComponent": "FI-GL-REO", "RefTitle": "Runtime error STRING_OFFSET_LENGTH_TOO_LARGE in Excel upload", "RefUrl": "/notes/2671933 "}, {"RefNumber": "2674126", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Negative values are showing in PC reorganization cockpit", "RefUrl": "/notes/2674126 "}, {"RefNumber": "2675545", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Profit center reassignment simulation issue in PO and POA", "RefUrl": "/notes/2675545 "}, {"RefNumber": "2687304", "RefComponent": "FI-GL-REO", "RefTitle": "Details popup cannot be opened in Transfer posting simulation", "RefUrl": "/notes/2687304 "}, {"RefNumber": "2583848", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AR/AP/GLOI: Dump during generation - COMPUTE_INT_PLUS_OVERFLOW", "RefUrl": "/notes/2583848 "}, {"RefNumber": "2658694", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: G/L accounts managed on an open item basis (GLOI) - supplement to SAP Note 2295892 II", "RefUrl": "/notes/2658694 "}, {"RefNumber": "2658207", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXXIV) - dump AP/AR/GLOI", "RefUrl": "/notes/2658207 "}, {"RefNumber": "2657238", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: G/L accounts managed on an open item basis (GLOI) - supplement to SAP Note 2295892", "RefUrl": "/notes/2657238 "}, {"RefNumber": "2651582", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Generation: Delta mode; runtime when generating AP/AR/GLOI", "RefUrl": "/notes/2651582 "}, {"RefNumber": "2667654", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Performance when reading the table FAGL_R_OI_TRACK1 during posting", "RefUrl": "/notes/2667654 "}, {"RefNumber": "2632837", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: Error 505 with generation/termination during account assignment change for POI objects II", "RefUrl": "/notes/2632837 "}, {"RefNumber": "2602823", "RefComponent": "FI-GL", "RefTitle": "Missing code correction in note 2257555", "RefUrl": "/notes/2602823 "}, {"RefNumber": "2621265", "RefComponent": "FI-GL-REO", "RefTitle": "VA01: Short dump while creation of an order for free item determination in SD", "RefUrl": "/notes/2621265 "}, {"RefNumber": "2604684", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Excel download cannot be started", "RefUrl": "/notes/2604684 "}, {"RefNumber": "2559153", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 534 (III) (GLOI)", "RefUrl": "/notes/2559153 "}, {"RefNumber": "2570092", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AR/AP/GLOI: Dump during generation - TSV_TNEW_PAGE_ALLOC_FAILED or MEMORY_NO_MORE_PAGING", "RefUrl": "/notes/2570092 "}, {"RefNumber": "2555575", "RefComponent": "FI-GL-REO-AA", "RefTitle": "AS02 ASSERTION_FAILED CL_FAGL_R_OBJ_TYPE_003_FA", "RefUrl": "/notes/2555575 "}, {"RefNumber": "2525842", "RefComponent": "FI-GL-REO", "RefTitle": "PCA Timeout occurs during payroll run (KSUB/KSUV)", "RefUrl": "/notes/2525842 "}, {"RefNumber": "2533601", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump due to doc_from_external_system, correction to SAP Note 2514833", "RefUrl": "/notes/2533601 "}, {"RefNumber": "2537423", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Dump during reassignment of sales and distribution documents", "RefUrl": "/notes/2537423 "}, {"RefNumber": "2564180", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Update for table fagl_r_spl_his", "RefUrl": "/notes/2564180 "}, {"RefNumber": "2556144", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: GLOI - empty PRCTR after generation", "RefUrl": "/notes/2556144 "}, {"RefNumber": "2548644", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Error FAGL_REORGANIZATION 533 for reassignment event", "RefUrl": "/notes/2548644 "}, {"RefNumber": "2514254", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Warning FAGL_REORGANIZATION 505 for inconsistent account assignment in documents/recursive start of generation during account assignment change", "RefUrl": "/notes/2514254 "}, {"RefNumber": "2502139", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Memory overflow during generation of open items TSV_TNEW_PAGE_ALLOC_FAILED II", "RefUrl": "/notes/2502139 "}, {"RefNumber": "2509393", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error in simulation of Receivables/Payables", "RefUrl": "/notes/2509393 "}, {"RefNumber": "2527701", "RefComponent": "FI-GL-REO", "RefTitle": "Unjustified messeage FAGL_REORGANIZATION 053 during mass CO posting", "RefUrl": "/notes/2527701 "}, {"RefNumber": "2520717", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: CheckMan: ATC class: CL_FAGL_R_SPLIT_REORG", "RefUrl": "/notes/2520717 "}, {"RefNumber": "2520712", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Simulation object generation: BAdI for change assignment is not called", "RefUrl": "/notes/2520712 "}, {"RefNumber": "2516755", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Selection of SD documents III", "RefUrl": "/notes/2516755 "}, {"RefNumber": "2517561", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: CheckMan: ATC class: CL_FAGL_R_OBJ_TYPE_APAR", "RefUrl": "/notes/2517561 "}, {"RefNumber": "2510511", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Dispatch session analysis - enhancement", "RefUrl": "/notes/2510511 "}, {"RefNumber": "2514833", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error FAGL_REORGANIZATION 505 for documents from external systems", "RefUrl": "/notes/2514833 "}, {"RefNumber": "2479817", "RefComponent": "FI-GL-REO", "RefTitle": "Error message FAGL_ORG_UNITS001 is ignored", "RefUrl": "/notes/2479817 "}, {"RefNumber": "2483046", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Reassignment simulation: Assertion dump", "RefUrl": "/notes/2483046 "}, {"RefNumber": "2489310", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer postings to individual ledgers are not permitted", "RefUrl": "/notes/2489310 "}, {"RefNumber": "2488468", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Object list overview: TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/2488468 "}, {"RefNumber": "2231013", "RefComponent": "EC-PCA-MD", "RefTitle": "KE52: Long runtime for changeable segment", "RefUrl": "/notes/2231013 "}, {"RefNumber": "2294010", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Individual evaluation for Company Code when GL Reorganization", "RefUrl": "/notes/2294010 "}, {"RefNumber": "2456090", "RefComponent": "FI-GL-REO-CO", "RefTitle": "SEG_REORG:  ASSERTION DUMP during CO settlement", "RefUrl": "/notes/2456090 "}, {"RefNumber": "2443558", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassignment FAGL_Reorganization 566 (XXXIII) - use of extended local currencies", "RefUrl": "/notes/2443558 "}, {"RefNumber": "2449749", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Delta_Mode for complete exclusion of receivables and payables at 1st hierarchy level", "RefUrl": "/notes/2449749 "}, {"RefNumber": "2451161", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Parallelization generation: Using object type specific fields II", "RefUrl": "/notes/2451161 "}, {"RefNumber": "2483861", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Reassignment: Customized package size is not used", "RefUrl": "/notes/2483861 "}, {"RefNumber": "2482310", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer posting: Message FAGL_REORGANIZATION 119 is logged exponentially", "RefUrl": "/notes/2482310 "}, {"RefNumber": "2472182", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Simulation object generation : profit center as paralel criterium is not considered", "RefUrl": "/notes/2472182 "}, {"RefNumber": "2455472", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 507/508 with transfer posting of receivables or payables", "RefUrl": "/notes/2455472 "}, {"RefNumber": "2472931", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: G/L accounts managed on open item basis as first-level objects", "RefUrl": "/notes/2472931 "}, {"RefNumber": "2456893", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Identify non-reorganized open items (Corrections)", "RefUrl": "/notes/2456893 "}, {"RefNumber": "2453892", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 568 for AP/AR/GLOI reassignment", "RefUrl": "/notes/2453892 "}, {"RefNumber": "2444339", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Simulation of object generation - enhancements to SAP Note 2379381 for AP/AR/GLOI", "RefUrl": "/notes/2444339 "}, {"RefNumber": "2377429", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Troubleshooting tool to correct invoice references not reassigned to the new segment", "RefUrl": "/notes/2377429 "}, {"RefNumber": "2433517", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AR/GLOI - reassignment/simulation of reassignment terminates with exception ASSERTION_FAILED in CX_FINS_FATAL_EXCEPTION", "RefUrl": "/notes/2433517 "}, {"RefNumber": "2432139", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Parallelization generation: Using object type specific fields", "RefUrl": "/notes/2432139 "}, {"RefNumber": "2431877", "RefComponent": "FI-GL-REO", "RefTitle": "Enhancements for the Reassignment simulation", "RefUrl": "/notes/2431877 "}, {"RefNumber": "2437863", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Flag for regeneration of document at first level", "RefUrl": "/notes/2437863 "}, {"RefNumber": "2379381", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Simulation object generation", "RefUrl": "/notes/2379381 "}, {"RefNumber": "2431560", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AR/GLOI - long runtime during generation", "RefUrl": "/notes/2431560 "}, {"RefNumber": "2431810", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Field UMSKS not to be checked during reorganization of GLOI items", "RefUrl": "/notes/2431810 "}, {"RefNumber": "2383149", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXXI) - Enhancement to SAP Note 2356519", "RefUrl": "/notes/2383149 "}, {"RefNumber": "2381338", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Additional balancing fields for transfer posting for AP/AR/GLOI III - specific restrictions", "RefUrl": "/notes/2381338 "}, {"RefNumber": "2392823", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Q & A about Segment Reorganization for GL", "RefUrl": "/notes/2392823 "}, {"RefNumber": "2398318", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Runtime improvement for material stock determination", "RefUrl": "/notes/2398318 "}, {"RefNumber": "2401544", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Runtime improvement for generation of unassigned purchase orders (PO)", "RefUrl": "/notes/2401544 "}, {"RefNumber": "2111249", "RefComponent": "FI-AA", "RefTitle": "Filling the consolidation transaction type: Document date is not transferred", "RefUrl": "/notes/2111249 "}, {"RefNumber": "2401492", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Deleted objects are included in generation", "RefUrl": "/notes/2401492 "}, {"RefNumber": "2424926", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Objects from improperly closed plans in object list overview", "RefUrl": "/notes/2424926 "}, {"RefNumber": "2420451", "RefComponent": "FI-GL-REO", "RefTitle": "Error FAGL_REORGANIZATION 019 during mass CO posting", "RefUrl": "/notes/2420451 "}, {"RefNumber": "2417628", "RefComponent": "CO-PA-ACT", "RefTitle": "Profitability segment redetermination during profit center reorganization", "RefUrl": "/notes/2417628 "}, {"RefNumber": "2362233", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Export and import of Object lists to Excel", "RefUrl": "/notes/2362233 "}, {"RefNumber": "2416438", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXXII) - Enhancement to SAP Note 2383149", "RefUrl": "/notes/2416438 "}, {"RefNumber": "2383877", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump MOVE_TO_LIT_NOTALLOWED_NODATA in R_SPLIT_MANUALLY_CHECK_SIGN_P", "RefUrl": "/notes/2383877 "}, {"RefNumber": "2389685", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Unjustified error message 544 during a reassignment III", "RefUrl": "/notes/2389685 "}, {"RefNumber": "2377751", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects V", "RefUrl": "/notes/2377751 "}, {"RefNumber": "2376260", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR/SEG: Simulation of the reassignment of OXX/WBS/SO II.", "RefUrl": "/notes/2376260 "}, {"RefNumber": "2225792", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Definition of periods for purchase orders", "RefUrl": "/notes/2225792 "}, {"RefNumber": "2363760", "RefComponent": "FI-GL-REO-BF", "RefTitle": "PC reorg: Wrong old profit center in object list display", "RefUrl": "/notes/2363760 "}, {"RefNumber": "2306900", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Dump for reassignment of PO", "RefUrl": "/notes/2306900 "}, {"RefNumber": "2367153", "RefComponent": "FI-AR-CR", "RefTitle": "RFDKLI50: Technical note for delivery cleanup", "RefUrl": "/notes/2367153 "}, {"RefNumber": "2366832", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Unjustified error message 544 during a reassignment II", "RefUrl": "/notes/2366832 "}, {"RefNumber": "2361365", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Dump ITAB_NON_NUMERIC_COMPONENT", "RefUrl": "/notes/2361365 "}, {"RefNumber": "2343197", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS02: Error from profit center check for reorganized profit centers", "RefUrl": "/notes/2343197 "}, {"RefNumber": "2370038", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Additional balancing fields for transfer posting for AP/AR/GLOI II", "RefUrl": "/notes/2370038 "}, {"RefNumber": "2367404", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Troubleshooting tool to identify non-reorganized open items", "RefUrl": "/notes/2367404 "}, {"RefNumber": "2311248", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Parallelization generation: more fields in general restrictions", "RefUrl": "/notes/2311248 "}, {"RefNumber": "2356519", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXX) - rounding differences - technical fields such as XSKRL are not taken into account", "RefUrl": "/notes/2356519 "}, {"RefNumber": "2354628", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR/GLOI optimization of the job log", "RefUrl": "/notes/2354628 "}, {"RefNumber": "2340711", "RefComponent": "FI-AA-AA-A", "RefTitle": "Transfer posting of organizational units: Account assignment objects", "RefUrl": "/notes/2340711 "}, {"RefNumber": "2355789", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Deletion of program RFAGL_R_NOTE_2245040", "RefUrl": "/notes/2355789 "}, {"RefNumber": "2347991", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generating AR/GLOI - various error messages during the document split simulation", "RefUrl": "/notes/2347991 "}, {"RefNumber": "2342900", "RefComponent": "FI-GL-FL", "RefTitle": "Performance problems during subsequent processing of partially archived cross-company code transactions", "RefUrl": "/notes/2342900 "}, {"RefNumber": "2326584", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: MESSAGE_TYPE_X when generating AR/AP (5)", "RefUrl": "/notes/2326584 "}, {"RefNumber": "2342059", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generating AP/AR/GLOI: TSV_TNEW_PAGE_ALLOC_FAILED in CL_FAGL_R_SPLIT_REORG/runtime problems", "RefUrl": "/notes/2342059 "}, {"RefNumber": "2308865", "RefComponent": "FI-GL-REO", "RefTitle": "Inconsistentcies in object counts after reassignment simulation", "RefUrl": "/notes/2308865 "}, {"RefNumber": "2296987", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Simulation of reassignment function for MM", "RefUrl": "/notes/2296987 "}, {"RefNumber": "2295617", "RefComponent": "FI-GL-REO-CO", "RefTitle": "PRCTR: Error message 'Controlling area & does not exist' when you reassign sales and distribution documents", "RefUrl": "/notes/2295617 "}, {"RefNumber": "2328896", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects IV - missing FAGL_SPLINFO_VAL", "RefUrl": "/notes/2328896 "}, {"RefNumber": "2324638", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Open item reassignment not possible because of an error in document splitting", "RefUrl": "/notes/2324638 "}, {"RefNumber": "2257555", "RefComponent": "FI-AA", "RefTitle": "S4TWL - Asset Accounting: Business Functions from FI-AA and FI-GL", "RefUrl": "/notes/2257555 "}, {"RefNumber": "2303211", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Simulation of transfer posting for AP/AR/GLOI objects", "RefUrl": "/notes/2303211 "}, {"RefNumber": "2283164", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer posting/balance simulation", "RefUrl": "/notes/2283164 "}, {"RefNumber": "2285105", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: For object type POI, too few receivables determined as second-level objects", "RefUrl": "/notes/2285105 "}, {"RefNumber": "2288244", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Syntax error in attribute GT_T882G", "RefUrl": "/notes/2288244 "}, {"RefNumber": "2294343", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 - MWSKZ, TXJCD (invalid combination, see SAP Note 1497956)", "RefUrl": "/notes/2294343 "}, {"RefNumber": "2292486", "RefComponent": "FI-GL-REO-MM", "RefTitle": "PRCTR: Error 216 during account determination", "RefUrl": "/notes/2292486 "}, {"RefNumber": "2286552", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime errors in Reassignment Simulation", "RefUrl": "/notes/2286552 "}, {"RefNumber": "2265316", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error TSV_TNEW_PAGE_ALLOC_FAILED during display of particular objects", "RefUrl": "/notes/2265316 "}, {"RefNumber": "2263466", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Original document for residual item triggers FAGL_REORGANIZATION 535", "RefUrl": "/notes/2263466 "}, {"RefNumber": "2179249", "RefComponent": "FI-GL-REO", "RefTitle": "Segment during reversal after reorganization", "RefUrl": "/notes/2179249 "}, {"RefNumber": "2287346", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 339 for SD orders (cross-company)", "RefUrl": "/notes/2287346 "}, {"RefNumber": "2280287", "RefComponent": "FI-GL-REO", "RefTitle": "Excel Upload for first level objects in Profit Center Reorganization with New General Ledger", "RefUrl": "/notes/2280287 "}, {"RefNumber": "2280483", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Simulation of the reassignment of OXX/WBS/SO", "RefUrl": "/notes/2280483 "}, {"RefNumber": "2257740", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Dispatch session analysis", "RefUrl": "/notes/2257740 "}, {"RefNumber": "2271524", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incorrect job numbering within a dispatching job", "RefUrl": "/notes/2271524 "}, {"RefNumber": "2276845", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generation: Parallel processing by company code AP/AR/GLOI", "RefUrl": "/notes/2276845 "}, {"RefNumber": "141009", "RefComponent": "EC-PCA-ACT", "RefTitle": "Resetting new profit center for PO", "RefUrl": "/notes/141009 "}, {"RefNumber": "1683168", "RefComponent": "MM-PUR-OA-BAPI", "RefTitle": "BAPI: KO_PRCTR not filled by BAPI_SAG_CREATE", "RefUrl": "/notes/1683168 "}, {"RefNumber": "2275360", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Process/Production  orders: WIP is not calculated completely", "RefUrl": "/notes/2275360 "}, {"RefNumber": "2265202", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXIX)", "RefUrl": "/notes/2265202 "}, {"RefNumber": "2271699", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Items with clearing specific to ledger groups are not stored in deltas table", "RefUrl": "/notes/2271699 "}, {"RefNumber": "2268500", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Open items collected for further processing despite closed reorganization plan", "RefUrl": "/notes/2268500 "}, {"RefNumber": "2269224", "RefComponent": "FI-GL-REO-CO", "RefTitle": "MESSAGE TYPE X: COIOB RECORD NOT FOUND FOR <PERSON>B<PERSON>NR", "RefUrl": "/notes/2269224 "}, {"RefNumber": "2257415", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 575 during reassignment of AP/AR II", "RefUrl": "/notes/2257415 "}, {"RefNumber": "2262612", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Transfer posting document for items with clearing specific to ledger group is posted to a ledger group", "RefUrl": "/notes/2262612 "}, {"RefNumber": "2176373", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Support for AP/AR runtime analysis", "RefUrl": "/notes/2176373 "}, {"RefNumber": "2182937", "RefComponent": "FI-GL-REO", "RefTitle": "Missing authorization for closing and deleting of reorganization plans", "RefUrl": "/notes/2182937 "}, {"RefNumber": "2248773", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Simulating the reassignment of AP/AR/GLOI objects", "RefUrl": "/notes/2248773 "}, {"RefNumber": "2262311", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Transfer posting document for items with clearing specific to ledger group is not cleared", "RefUrl": "/notes/2262311 "}, {"RefNumber": "2255494", "RefComponent": "FI-GL-FL", "RefTitle": "CONVT_OVERFLOW in the INCLUDE LGLT0F61", "RefUrl": "/notes/2255494 "}, {"RefNumber": "2251404", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Exclude clearing specific to ledger group for GL account", "RefUrl": "/notes/2251404 "}, {"RefNumber": "2251240", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Items with clearing specific to ledger groups are not reorganized as objec GLOI", "RefUrl": "/notes/2251240 "}, {"RefNumber": "2223977", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Expired Settlement Rules in Sales Orders just skipped", "RefUrl": "/notes/2223977 "}, {"RefNumber": "2225484", "RefComponent": "FI-GL-REO", "RefTitle": "Hierarchy not displayed correctly", "RefUrl": "/notes/2225484 "}, {"RefNumber": "2215739", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Reorganization of contracts", "RefUrl": "/notes/2215739 "}, {"RefNumber": "2197338", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Checkman fixes", "RefUrl": "/notes/2197338 "}, {"RefNumber": "2215637", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Not possible to define server group for background jobs", "RefUrl": "/notes/2215637 "}, {"RefNumber": "2219754", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during transfer posting", "RefUrl": "/notes/2219754 "}, {"RefNumber": "2171014", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL report to analyze balances", "RefUrl": "/notes/2171014 "}, {"RefNumber": "2177720", "RefComponent": "FI-GL-REO", "RefTitle": "Reassignment simulation", "RefUrl": "/notes/2177720 "}, {"RefNumber": "2245040", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Object type O04 (CO Production Order) does not appear at the first hierarchy level", "RefUrl": "/notes/2245040 "}, {"RefNumber": "2244184", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: Additional balancing fields in transfer posting - see SAP Note 1921058", "RefUrl": "/notes/2244184 "}, {"RefNumber": "2228286", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Currency inconsistency in FI document", "RefUrl": "/notes/2228286 "}, {"RefNumber": "2236750", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Cash discount clearing line calculated incorrectly", "RefUrl": "/notes/2236750 "}, {"RefNumber": "2202677", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Exclude Profit/Loss accounts from currency adjustments", "RefUrl": "/notes/2202677 "}, {"RefNumber": "2192732", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: Memory problem when selecting GL balances", "RefUrl": "/notes/2192732 "}, {"RefNumber": "2233557", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXVIII) - too many XLEVEL1 objects for receivables and payables", "RefUrl": "/notes/2233557 "}, {"RefNumber": "2233780", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Wrong amount is posted in the reorganization of downpayments", "RefUrl": "/notes/2233780 "}, {"RefNumber": "2211157", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Incorrect amounts after the reorganization transfer posting for the foreign currency valuation", "RefUrl": "/notes/2211157 "}, {"RefNumber": "2228806", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXVII) - local currencies", "RefUrl": "/notes/2228806 "}, {"RefNumber": "2227550", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 552", "RefUrl": "/notes/2227550 "}, {"RefNumber": "2219599", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 545", "RefUrl": "/notes/2219599 "}, {"RefNumber": "2226517", "RefComponent": "FI-GL-REO-BF", "RefTitle": "F5 800 Inconsistent currency information II.", "RefUrl": "/notes/2226517 "}, {"RefNumber": "2221505", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 533 during reassignment of AP/AR objects III", "RefUrl": "/notes/2221505 "}, {"RefNumber": "2158294", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Segment Reorganization Security Corrections", "RefUrl": "/notes/2158294 "}, {"RefNumber": "2222654", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Transfer postings: WIP with OI managed accounts II", "RefUrl": "/notes/2222654 "}, {"RefNumber": "2168957", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Transfer postings: WIP with OI managed accounts", "RefUrl": "/notes/2168957 "}, {"RefNumber": "2221608", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: GLOI report to analyze object list selection", "RefUrl": "/notes/2221608 "}, {"RefNumber": "2219373", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: Performance correction in object list generation and reassignment of GLOI objects", "RefUrl": "/notes/2219373 "}, {"RefNumber": "2200318", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 566 (XXVI)", "RefUrl": "/notes/2200318 "}, {"RefNumber": "2207357", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Display of customer/vendor reconciliation accounts as \"open\" after transfer posting", "RefUrl": "/notes/2207357 "}, {"RefNumber": "2022736", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR: AR simulation of ext. document splitting characteristics II", "RefUrl": "/notes/2022736 "}, {"RefNumber": "2158037", "RefComponent": "FI-GL-REO-GL", "RefTitle": "Error FAGL_REORGANIZATION 551 when reassigning AP/AR", "RefUrl": "/notes/2158037 "}, {"RefNumber": "2182509", "RefComponent": "FI-GL-REO-GL", "RefTitle": "After profit center reorganization, profit center assigned to account for down payment clearing", "RefUrl": "/notes/2182509 "}, {"RefNumber": "2184134", "RefComponent": "FI-GL-REO-BF", "RefTitle": "SEG_REORG: Profit center needs to be checked if is in SEG reorganization", "RefUrl": "/notes/2184134 "}, {"RefNumber": "2191854", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR generation ignores the field GSBER", "RefUrl": "/notes/2191854 "}, {"RefNumber": "2203690", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_REORGANIZATION 568 during reassignment", "RefUrl": "/notes/2203690 "}, {"RefNumber": "2175821", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Performance for generation of receivables and payables", "RefUrl": "/notes/2175821 "}, {"RefNumber": "2205457", "RefComponent": "FI-GL-REO", "RefTitle": "License audit measurement - database size determination for HDB", "RefUrl": "/notes/2205457 "}, {"RefNumber": "2199437", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: More checks for segment change in profit center (Tx: KE52)", "RefUrl": "/notes/2199437 "}, {"RefNumber": "2195074", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Error message in BAPI BAPI_PROFITCENTER_CHANGE processing Segment Reorg", "RefUrl": "/notes/2195074 "}, {"RefNumber": "2125431", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Generation: Parallelization per company code, number of parallel jobs", "RefUrl": "/notes/2125431 "}, {"RefNumber": "2101879", "RefComponent": "FI-GL-REO", "RefTitle": "Reorganization: TSV_TNEW_PAGE_ALLOC_FAILED in CL_FAGL_R_OBJLIST", "RefUrl": "/notes/2101879 "}, {"RefNumber": "2200897", "RefComponent": "FI-GL-REO-BF", "RefTitle": "SEG REORG: Segment has to be defined as time-based field", "RefUrl": "/notes/2200897 "}, {"RefNumber": "2100111", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Adding of an additional field in the object list display results in a shortdump", "RefUrl": "/notes/2100111 "}, {"RefNumber": "2187251", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL only calculate 2 currencies", "RefUrl": "/notes/2187251 "}, {"RefNumber": "2174489", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Performance for check of migrated documents", "RefUrl": "/notes/2174489 "}, {"RefNumber": "2185939", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Reassignment fails for open item objects (AP/AR/GLOI) in an invoice reference", "RefUrl": "/notes/2185939 "}, {"RefNumber": "2172205", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL specific restrictions is not working", "RefUrl": "/notes/2172205 "}, {"RefNumber": "2167048", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL is not reorganized in a flat hierarchy", "RefUrl": "/notes/2167048 "}, {"RefNumber": "2179664", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL period calculation for Company Code", "RefUrl": "/notes/2179664 "}, {"RefNumber": "2173123", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL reposting balances that are already 0 but in different periods", "RefUrl": "/notes/2173123 "}, {"RefNumber": "2171629", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Transfer posting without profit center in foreign currency valuated amounts", "RefUrl": "/notes/2171629 "}, {"RefNumber": "2167047", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: dumping when displaying an object list for GL", "RefUrl": "/notes/2167047 "}, {"RefNumber": "2116852", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Segment Reorganization for GL accounts is not correct", "RefUrl": "/notes/2116852 "}, {"RefNumber": "2162798", "RefComponent": "FI-GL-REO", "RefTitle": "SegReorg: GL are not taking carry-forward balances", "RefUrl": "/notes/2162798 "}, {"RefNumber": "2154393", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Error message FAGL_REORGANIZATION 566 during the reassignment of GLOI (II)", "RefUrl": "/notes/2154393 "}, {"RefNumber": "2145871", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: Open Item is split across different Profit Centers, leading to erroneous object list generation", "RefUrl": "/notes/2145871 "}, {"RefNumber": "2145770", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: The split simulation does not return the profit center for GL Open Items", "RefUrl": "/notes/2145770 "}, {"RefNumber": "2129943", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Incorrect exclusion of receivables and payables at the first hierarchy level", "RefUrl": "/notes/2129943 "}, {"RefNumber": "2129508", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Document lines in an invoice reference are reorganized while the invoice reference is not reorganized", "RefUrl": "/notes/2129508 "}, {"RefNumber": "2121381", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer Posting - wrong amount and currency for additional currency displayed in the object list", "RefUrl": "/notes/2121381 "}, {"RefNumber": "2118399", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: Open Item is split across different Profit Centers, leading to erroneous transfer posting", "RefUrl": "/notes/2118399 "}, {"RefNumber": "2116823", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Include splitting information sequence number in the reassignment (2)", "RefUrl": "/notes/2116823 "}, {"RefNumber": "2116822", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Include splitting information sequence number in the reassignment (1)", "RefUrl": "/notes/2116822 "}, {"RefNumber": "2111727", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Error message FAGL_REORGANIZATION 566 during the reassignment of GLOI", "RefUrl": "/notes/2111727 "}, {"RefNumber": "2098818", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Unjustified error message 544 during a reassignment", "RefUrl": "/notes/2098818 "}, {"RefNumber": "2088977", "RefComponent": "FI-GL-REO-GL", "RefTitle": "SEG: Open item reassignment not possible because of an error in document splitting", "RefUrl": "/notes/2088977 "}, {"RefNumber": "2088236", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Table FAGL_R_OI_TRACK1 not filled during segment reorganization", "RefUrl": "/notes/2088236 "}, {"RefNumber": "2088106", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Add Profit Center to Segment Reorganization transfer posting", "RefUrl": "/notes/2088106 "}, {"RefNumber": "2085069", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Memory overflow during generation of open items TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/2085069 "}, {"RefNumber": "2082736", "RefComponent": "FI-GL-REO-BF", "RefTitle": "FAGL_REORGANIZATION005 GBSTA is not a valid entry for field name", "RefUrl": "/notes/2082736 "}, {"RefNumber": "2076489", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump with error ITAB_DUPLICATE_KEY", "RefUrl": "/notes/2076489 "}, {"RefNumber": "2079042", "RefComponent": "FI-GL-REO-GL", "RefTitle": "AP/AR: Asser<PERSON> failed during the generation", "RefUrl": "/notes/2079042 "}, {"RefNumber": "2072300", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Reassignment of GL Open Items takes very long during Segment Reorganization (2)", "RefUrl": "/notes/2072300 "}, {"RefNumber": "2067084", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Reassignment of GL Open Items takes very long during Segment Reorganization", "RefUrl": "/notes/2067084 "}, {"RefNumber": "2053449", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incorrect amount displayed for certain currencies", "RefUrl": "/notes/2053449 "}, {"RefNumber": "2035119", "RefComponent": "CA-LT-FRM", "RefTitle": "SAP LT : Segment Reorg. - phase remains in running state", "RefUrl": "/notes/2035119 "}, {"RefNumber": "2029353", "RefComponent": "FI-GL-REO-CO", "RefTitle": "FAGL_REORGANIZATION 342 at balance determination", "RefUrl": "/notes/2029353 "}, {"RefNumber": "2017983", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error F5 800 during transfer posting", "RefUrl": "/notes/2017983 "}, {"RefNumber": "2017149", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer postings: Document currency determination", "RefUrl": "/notes/2017149 "}, {"RefNumber": "2014156", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error 'Structure name XXXX YYYY not supported' when you display AP/AR objects", "RefUrl": "/notes/2014156 "}, {"RefNumber": "2011705", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Aditional balancing fields are not updated into transfer postings", "RefUrl": "/notes/2011705 "}, {"RefNumber": "1921058", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Additional balancing fields in transfer posting", "RefUrl": "/notes/1921058 "}, {"RefNumber": "2013173", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error message FAGL_REORGANIZATION 073 during transfer posting", "RefUrl": "/notes/2013173 "}, {"RefNumber": "1971695", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: RAISE_EXCEPTION 'NO_DOCUMENT_REQUIRED' in generation of AP/AR", "RefUrl": "/notes/1971695 "}, {"RefNumber": "1870718", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Settlement reversal is possible after PC reorganization", "RefUrl": "/notes/1870718 "}, {"RefNumber": "1918511", "RefComponent": "FI-GL-REO-CO", "RefTitle": "Settlement reversal is possible after PC reorganization II", "RefUrl": "/notes/1918511 "}, {"RefNumber": "1865363", "RefComponent": "FI-GL-REO-BF", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED during display of object list", "RefUrl": "/notes/1865363 "}, {"RefNumber": "1812836", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR objects unjustified at first level", "RefUrl": "/notes/1812836 "}, {"RefNumber": "1696418", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Inconsistencies in reorganization of receivables", "RefUrl": "/notes/1696418 "}, {"RefNumber": "1863965", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incorect numbers of objects in reorganization plan", "RefUrl": "/notes/1863965 "}, {"RefNumber": "1844856", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during object generation", "RefUrl": "/notes/1844856 "}, {"RefNumber": "1853736", "RefComponent": "FI-GL-REO-BF", "RefTitle": "ASSERTION_FAILED during object generation II.", "RefUrl": "/notes/1853736 "}, {"RefNumber": "1801767", "RefComponent": "FI-GL-REO", "RefTitle": "Profit center reorganization: Scope of functions", "RefUrl": "/notes/1801767 "}, {"RefNumber": "1824220", "RefComponent": "FI-GL-REO-BF", "RefTitle": "MESSAGE_TYPE_X during object list generation", "RefUrl": "/notes/1824220 "}, {"RefNumber": "1773094", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump ITAB_DUPLICATE_KEY Gen. object list AP/AR", "RefUrl": "/notes/1773094 "}, {"RefNumber": "1599168", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Message FAGL_REORGANIZATION 544", "RefUrl": "/notes/1599168 "}, {"RefNumber": "1716690", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Short dump when generating object type AR", "RefUrl": "/notes/1716690 "}, {"RefNumber": "1810392", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566 (II)", "RefUrl": "/notes/1810392 "}, {"RefNumber": "1816152", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "RefUrl": "/notes/1816152 "}, {"RefNumber": "1808980", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 566", "RefUrl": "/notes/1808980 "}, {"RefNumber": "1797558", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: FAGL_SPLINFO - incorr. Prctr/segment after reorg", "RefUrl": "/notes/1797558 "}, {"RefNumber": "1809521", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error messages FAGL_REORGANIZATION 534 and 536", "RefUrl": "/notes/1809521 "}, {"RefNumber": "1667603", "RefComponent": "FI-GL-REO", "RefTitle": "Archiving in reorganization", "RefUrl": "/notes/1667603 "}, {"RefNumber": "1765330", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Closing plans for which successful processing is not complete", "RefUrl": "/notes/1765330 "}, {"RefNumber": "1793304", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Performance: Generation not performed in parallel processing", "RefUrl": "/notes/1793304 "}, {"RefNumber": "1744690", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Poorer performance after activating a business function", "RefUrl": "/notes/1744690 "}, {"RefNumber": "1774057", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 535", "RefUrl": "/notes/1774057 "}, {"RefNumber": "1772403", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 575", "RefUrl": "/notes/1772403 "}, {"RefNumber": "1790465", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Transfer posting of cleared items", "RefUrl": "/notes/1790465 "}, {"RefNumber": "1707832", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Various errors", "RefUrl": "/notes/1707832 "}, {"RefNumber": "1589176", "RefComponent": "FI-GL-REO-AA", "RefTitle": "Asset master data with profit center / filling segment", "RefUrl": "/notes/1589176 "}, {"RefNumber": "1787464", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Generatn after acct assgmt chg: Incorr first-level objects", "RefUrl": "/notes/1787464 "}, {"RefNumber": "1783742", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Message FAGL_REORGANIZATION 123: Cut name of parameters", "RefUrl": "/notes/1783742 "}, {"RefNumber": "1778408", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR - Reorg: Runtime error MESSAGE_TYPE_X gen. AP/AR", "RefUrl": "/notes/1778408 "}, {"RefNumber": "1768369", "RefComponent": "FI-GL-REO", "RefTitle": "Object list not marked as 'Completely processed'", "RefUrl": "/notes/1768369 "}, {"RefNumber": "1655212", "RefComponent": "FI-GL-REO-AA", "RefTitle": "No new time interval during reorg for assests not posted to", "RefUrl": "/notes/1655212 "}, {"RefNumber": "1763488", "RefComponent": "FI-GL-REO-BF", "RefTitle": "No account assignment change for sales orders (XCLOSED = X)", "RefUrl": "/notes/1763488 "}, {"RefNumber": "1760882", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Rntme err TSV_TNEW_PAGE_ALLOC_FAILED due to too many objects", "RefUrl": "/notes/1760882 "}, {"RefNumber": "1759519", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Source document of transfer posting is not displayed", "RefUrl": "/notes/1759519 "}, {"RefNumber": "1757942", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime err. TSV_TNEW_PAGE_ALLOC_FAILED due to too many objs", "RefUrl": "/notes/1757942 "}, {"RefNumber": "1752608", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED in CL_FAGL_R_OBJLIST...", "RefUrl": "/notes/1752608 "}, {"RefNumber": "1756691", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Dump GLT0 000 BALANCE_CCODE after reorganization", "RefUrl": "/notes/1756691 "}, {"RefNumber": "1751367", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassigned amount is incomplete for AP/AR", "RefUrl": "/notes/1751367 "}, {"RefNumber": "1750217", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message FAGL_REORGANIZATION 533", "RefUrl": "/notes/1750217 "}, {"RefNumber": "1702346", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD generates transfer postings", "RefUrl": "/notes/1702346 "}, {"RefNumber": "1749843", "RefComponent": "FI-GL-GL-A", "RefTitle": "Termination when generating payables", "RefUrl": "/notes/1749843 "}, {"RefNumber": "1747717", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Short dump when generating AP/AR", "RefUrl": "/notes/1747717 "}, {"RefNumber": "1693664", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Incomplete deletion of a reorganization plan", "RefUrl": "/notes/1693664 "}, {"RefNumber": "1663555", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message when reassigning AP/AR", "RefUrl": "/notes/1663555 "}, {"RefNumber": "1706481", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Short dump when generating object types AP/AR", "RefUrl": "/notes/1706481 "}, {"RefNumber": "1746777", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Reorg job terminates", "RefUrl": "/notes/1746777 "}, {"RefNumber": "1684748", "RefComponent": "FI-GL-REO", "RefTitle": "Transfer posting object list not flagged as processed", "RefUrl": "/notes/1684748 "}, {"RefNumber": "1660067", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED: Update of dummy objects", "RefUrl": "/notes/1660067 "}, {"RefNumber": "1739651", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error message when generating AP/AR", "RefUrl": "/notes/1739651 "}, {"RefNumber": "1738491", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: Failed split sim. leads to objects class. as 1st level", "RefUrl": "/notes/1738491 "}, {"RefNumber": "1733158", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Objects are not reorganized", "RefUrl": "/notes/1733158 "}, {"RefNumber": "1737132", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Job status display is unreliable", "RefUrl": "/notes/1737132 "}, {"RefNumber": "1718567", "RefComponent": "FI-GL-REO-BF", "RefTitle": "BAdI FAGL_R_GENERATE: Status cannot be set", "RefUrl": "/notes/1718567 "}, {"RefNumber": "1598745", "RefComponent": "FI-AA-AA-A", "RefTitle": "Reorg transfer posting is made w/ unexpected posting date", "RefUrl": "/notes/1598745 "}, {"RefNumber": "1661117", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR: Short dump DBIF_RSQL_INVALID_RSQL in RKERV002", "RefUrl": "/notes/1661117 "}, {"RefNumber": "1734472", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Meaningful message text for message 503", "RefUrl": "/notes/1734472 "}, {"RefNumber": "1733459", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Runtime error during generation of receivables", "RefUrl": "/notes/1733459 "}, {"RefNumber": "1731429", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Performance of WD applicatn for reorganizatn plan in detail", "RefUrl": "/notes/1731429 "}, {"RefNumber": "1718793", "RefComponent": "FI-GL-REO", "RefTitle": "FIN_GL_REORG_SEG Object type GL should be reassignable", "RefUrl": "/notes/1718793 "}, {"RefNumber": "1687685", "RefComponent": "FI-GL-REO-AA", "RefTitle": "FAGL_ASSET_MASTERDATA_UPD: Assets for investment measure", "RefUrl": "/notes/1687685 "}, {"RefNumber": "1721168", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Reassignment AP/AP: Unjustified error messages", "RefUrl": "/notes/1721168 "}, {"RefNumber": "1721166", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Error 505 for down payment requests", "RefUrl": "/notes/1721166 "}, {"RefNumber": "1707428", "RefComponent": "FI-GL-REO-BF", "RefTitle": "BAdI for overwriting the transaction type", "RefUrl": "/notes/1707428 "}, {"RefNumber": "1686832", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Minor adjustments in area of reorganization framework", "RefUrl": "/notes/1686832 "}, {"RefNumber": "1694903", "RefComponent": "FI-GL-REO", "RefTitle": "SEG: FIN_GL_REORG_SEG should check whether New G/L is active", "RefUrl": "/notes/1694903 "}, {"RefNumber": "1687570", "RefComponent": "FI-GL-REO", "RefTitle": "Wrong plan status when using service to create plan", "RefUrl": "/notes/1687570 "}, {"RefNumber": "1683439", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Preventing inconsistencies for invoice reference", "RefUrl": "/notes/1683439 "}, {"RefNumber": "1683456", "RefComponent": "FI-GL-REO", "RefTitle": "Objects not reassigned or transferred", "RefUrl": "/notes/1683456 "}, {"RefNumber": "1674191", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Transfer posting: System error in FI interface (F5 691)", "RefUrl": "/notes/1674191 "}, {"RefNumber": "1650324", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Generation terminates: Unstable grouping AP/AR", "RefUrl": "/notes/1650324 "}, {"RefNumber": "1677834", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Inconsistencies in reorganization of receivables", "RefUrl": "/notes/1677834 "}, {"RefNumber": "1663603", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Message GLT0 002 after successful reorganization", "RefUrl": "/notes/1663603 "}, {"RefNumber": "1672235", "RefComponent": "FI-GL-REO", "RefTitle": "Dump ASSERTION_FAILED during reassignment", "RefUrl": "/notes/1672235 "}, {"RefNumber": "1671509", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Error F5 800: Inconsistent currency information", "RefUrl": "/notes/1671509 "}, {"RefNumber": "1665678", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Runtime error ASSERTION_FAILED when changing acct assignment", "RefUrl": "/notes/1665678 "}, {"RefNumber": "1664739", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: ASSERTION_FAILED when reassigning AP/AR", "RefUrl": "/notes/1664739 "}, {"RefNumber": "1660943", "RefComponent": "FI-GL-REO", "RefTitle": "Runtime error DBIF_RSQL_INVALID_RSQL during reassignment", "RefUrl": "/notes/1660943 "}, {"RefNumber": "1640228", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Warning message \"Document was not simulated\"", "RefUrl": "/notes/1640228 "}, {"RefNumber": "1664060", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Data inconsistency during generation of AP/AR", "RefUrl": "/notes/1664060 "}, {"RefNumber": "1658386", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Warning message \"Document was not simulated\"", "RefUrl": "/notes/1658386 "}, {"RefNumber": "1656973", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Authorization check", "RefUrl": "/notes/1656973 "}, {"RefNumber": "1641528", "RefComponent": "FI-GL-REO", "RefTitle": "Lock entry for processing object list does not exist", "RefUrl": "/notes/1641528 "}, {"RefNumber": "1655685", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Object lists are not indicated as processed", "RefUrl": "/notes/1655685 "}, {"RefNumber": "1651132", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Input help for hierarchy version", "RefUrl": "/notes/1651132 "}, {"RefNumber": "1649426", "RefComponent": "FI-GL-REO", "RefTitle": "Restriction Characteristics for Segment Reorganization", "RefUrl": "/notes/1649426 "}, {"RefNumber": "1628255", "RefComponent": "FI-GL-REO", "RefTitle": "PRCTR/SEG: Configuring message FAGL_R<PERSON>ORGANIZATION 029", "RefUrl": "/notes/1628255 "}, {"RefNumber": "1639690", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Unjustified error FAGL_REORGANIZATION 532", "RefUrl": "/notes/1639690 "}, {"RefNumber": "1609861", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Background processes terminate due to deadlock", "RefUrl": "/notes/1609861 "}, {"RefNumber": "1628687", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Cash discount clearing lines not reassigned", "RefUrl": "/notes/1628687 "}, {"RefNumber": "1534197", "RefComponent": "CA-LT-CNV", "RefTitle": "SAP LT: Transformation Solution - Profit Center Reorganization", "RefUrl": "/notes/1534197 "}, {"RefNumber": "1643715", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: APAR objs. missng for docs. w/ partner assignment", "RefUrl": "/notes/1643715 "}, {"RefNumber": "1626389", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Document splitting not updated (without message)", "RefUrl": "/notes/1626389 "}, {"RefNumber": "1626583", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: Error FAGL_REORGANIZATION 533 during reassignment", "RefUrl": "/notes/1626583 "}, {"RefNumber": "1619269", "RefComponent": "FI-GL-REO", "RefTitle": "Transf.posting: Security check, no account or CoCd specified", "RefUrl": "/notes/1619269 "}, {"RefNumber": "1598626", "RefComponent": "FI-GL-REO-BF", "RefTitle": "Posting date for ledger-specific transfers", "RefUrl": "/notes/1598626 "}, {"RefNumber": "1597693", "RefComponent": "FI-GL-REO-GL", "RefTitle": "PRCTR/SEG: AP/AR: Various PRCTR/SEG for same account assgnmt", "RefUrl": "/notes/1597693 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}