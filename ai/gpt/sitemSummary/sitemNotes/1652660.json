{"Request": {"Number": "1652660", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 378, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017519062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001652660?language=E&token=DA68793BE6F251D765AF5C85BBE2672E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001652660", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001652660/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1652660"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.10.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-MON"}, "SAPComponentKeyText": {"_label": "Component", "value": "CCMS Monitoring & Alerting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "CCMS Monitoring & Alerting", "value": "BC-CCM-MON", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-MON*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1652660 - JMonAPI: UnsatisfiedLinkError, InvocationTargetException, deadlock of Java threads"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The Java instance with stack version 7.01 or 6.45 fails to initialise CCMS connector (JMonAPI), if the instance is started with a downwards compatible kernel 7.20 (DCK) or even with the old release corresponding kernel. CCMS connector (JMonAPI) is used to transfer performance information from Java AS to a CCMS monitoring.<br /><br />You can notice this problem by:<br /><br />(1) missing values in CCMS for Java instance<br /><br />(2) observing exceptions \"UnsatisfiedLinkError\" or \"InvocationTargetException\" catched in the method initAttach of the class com.sap.mona.api.MonitoringAgent<br />(recorded in the trace file defaultTrace.0.trc in /usr/sap/&lt;SID&gt;/J00/j2ee/cluster/server0/log or in ccms_jmon.trc in /usr/sap/CCMS/&lt;SID&gt;_&lt;instance nr&gt;/j2ee&lt;node_number&gt;)<br /><br />The error message can look like:<br /><br />1.5##005056A67E31004200000000000017940004B0D735E3D4D0#1320338275422#com<br />sap.mona.api.MonitoringAgent##com.sap.mona.api.MonitoringAgent#######SA<br />Engine_System_Thread[impl:5]<br />72##0#0#Fatal##Plain###initAttach: java.lang.UnsatisfiedLinkError: no jmon.dll in java.library.path#<br />com.sap.mona.api.MonitoringAgent [SAPEngine_System_Thread[impl:6]_9] Fatal: initAttach: java.lang.reflect.InvocationTargetException</p>\r\n<p>Another even more evil observed symptoms caused by loading of old shared Jmon library that was mismatching the updated downwards compatible kernel&#160;&#160;were:</p>\r\n<ul>\r\n<li>Deadlock of&#160;threads of Java application server preventing the start.&#160;The deadlock occured during the write access to CCMS monitoring segment, therefore stack traces ends up with error messages referring to&#160;functions from com.sap.mona.api (std_dispatcher.out, defaultTrace.trc):<br />&#160;&#160;&#160; <br />\"Timeout Service Synchronous Internal Thread\" cpu=500.00 [reset 500.00] ms allocated=14782800 B (14.10 MB) [reset 14782800 B (14.10 MB)] defined_classes=76<br />io= file i/o: 0/0 B, net i/o: 0/0 B, files opened:0, socks opened:8&#160; [reset file i/o: 0/0 B, net i/o: 0/0 B, files opened:0, socks opened:8 ] <br />prio=10 tid=0x00002aaaeba83f80 nid=0x3396 runnable&#160; [_thread_in_native (_at_safepoint), stack(0x0000000047443000,0x0000000047644000)] [0x0000000047642000]<br />at com.sap.mona.api.JMonAPI.reportPerfValueExt(Lcom/sap/mona/api/TID;IIJLjava/lang/String;Ljava/lang/String;Lcom/sap/mona/api/MessageParameters;Ljava/lang/String;)V(Native Method)<br />at com.sap.mona.api.PerfValueAttribute.report(IILjava/util/Date;Ljava/lang/String;Ljava/lang/String;Lcom/sap/mona/api/MessageParameters;Ljava/lang/String;)V(PerfValueAttribute.java:196)<br />at com.sap.mona.api.PerfValueAttribute.report(I)V(PerfValueAttribute.java:56)<br />.....<br />....</li>\r\n<li>Damaged CCMS Monitoring segment.&#160;This produces suddenly similar error messages (dev_disp, dev_rd, sapstartsrv_sapccmsr.log):<br />&#160;&#160;&#160;&#160; <br />&lt;<br />+1 [AlReportMscLine&#160;&#160;&#160;&#160; ]<br />CCMS: [102] TID [&lt;invalid TID&gt;: Monitoring Context] not found in monitoring segment.<br />-1 [AlReportMscLine&#160;&#160;&#160;&#160; ] returns [102 TID_INVALID]&#160; [alxxcore.c 21429]<br />&lt;<br />&#160;&#160;&#160;&#160;&#160; <br />*** ERROR =&gt; GwReportClients: AlReportPerfValue failed, rc=102 [gwxxalrt.c&#160;&#160; 886]</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>JMonAPI jmon.dll 720 DCK UnsatisfiedLinkError InvocationTargetException deadlock</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The ininialisation sequence of the method initAttach() as implemented in the Java Server, ver. 7.01 or 6.45, is not correct any more if one uses a Java Startup Framework of the version 7.20, as it happens in the DCK scenario. Namely, the older version of the Java Startup Framework provided jmon.dll as a separate shared library. The newer Java Startup Framework 7.20 have JMON library is statically linked. Therefore the correct initialisation of the JMON library requires an algorithm, which automatically handles the load of the library in both cases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Please use the recent version of the support package SAPJTECHF.SCA (SAP JAVA TECHNOLOGY S OFFLINE). You will find the corresponding patch number of the package in the SP Patch level section of this note.<br /><br />The fix is in the included patch jmon.SDA. You can use either SDM (up to in NW702) or JSPM (since NW700) to deploy only the jmon.SDA.<br /><br />The instance restart will be necessory to restore the CCMS monitoring. After successful deployment the above mentioned trace files will not contain any error messages with regard to JMonAPI or MonitoringAgent anymore.<br /><br />There is currently no workaround available for this problem.<br /><br />Copying of the jmon.dll from another installation in order to satisfy the link dependency is not a valid workaround. This will fail, because the symbols conflict will occurs between the statically linked object and dynamically loaded library. This can cause a crash of the Java server on some platforms.<br /><br />Caution: If you decide to import the entire SAPJTECHF.SCA, you have to import the latest available SAPJTECHS.SCA as well (SAP JAVA TECHNOLOGY SERVICES) to avoid issues in WebDynproRuntime. Please check SAP note 1395627 for details.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I047532)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D056056)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001652660/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001652660/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001652660/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1696692", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Trace of JMonAPI in J2EE and VMC", "RefUrl": "/notes/1696692"}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252"}, {"RefNumber": "1457530", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: JMON library is locked in a kernel staging directory", "RefUrl": "/notes/1457530"}, {"RefNumber": "1395627", "RefComponent": "BC-WD-JAV", "RefTitle": "Import a patch for WebDynproRuntime Java", "RefUrl": "/notes/1395627"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1737528", "RefComponent": "BC-JAS-COR", "RefTitle": "Could not open the ICU common library: AS JAVA startup issue", "RefUrl": "/notes/1737528 "}, {"RefNumber": "1633731", "RefComponent": "BC-CST", "RefTitle": "Usage of the 7.20 Downward-Compatible Kernel", "RefUrl": "/notes/1633731 "}, {"RefNumber": "1457530", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: JMON library is locked in a kernel staging directory", "RefUrl": "/notes/1457530 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1696692", "RefComponent": "BC-CCM-MON", "RefTitle": "CCMS: Trace of JMonAPI in J2EE and VMC", "RefUrl": "/notes/1696692 "}, {"RefNumber": "1395627", "RefComponent": "BC-WD-JAV", "RefTitle": "Import a patch for WebDynproRuntime Java", "RefUrl": "/notes/1395627 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_JTECHF", "From": "7.00", "To": "7.01", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP004", "SupportPackagePatch": "000019", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP004&patch_level=000019"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP003", "SupportPackagePatch": "000013", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP003&patch_level=000013"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP005", "SupportPackagePatch": "000020", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP005&patch_level=000020"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP009", "SupportPackagePatch": "000011", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP009&patch_level=000011"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP008", "SupportPackagePatch": "000012", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP008&patch_level=000012"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP012", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP012&patch_level=000000"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP007", "SupportPackagePatch": "000013", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP007&patch_level=000013"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP006", "SupportPackagePatch": "000015", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP006&patch_level=000015"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP011", "SupportPackagePatch": "000002", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP011&patch_level=000002"}, {"SoftwareComponentVersion": "SAP TECH S OFFLINE 7.01", "SupportPackage": "SP010", "SupportPackagePatch": "000013", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200615320200010878&support_package=SP010&patch_level=000013"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP014", "SupportPackagePatch": "000024", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP014&patch_level=000024"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP015", "SupportPackagePatch": "000020", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP015&patch_level=000020"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP016", "SupportPackagePatch": "000023", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP016&patch_level=000023"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP017", "SupportPackagePatch": "000018", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP017&patch_level=000018"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP018", "SupportPackagePatch": "000022", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP018&patch_level=000022"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP019", "SupportPackagePatch": "000018", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP019&patch_level=000018"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP020", "SupportPackagePatch": "000017", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP020&patch_level=000017"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP021", "SupportPackagePatch": "000012", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP021&patch_level=000012"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP022", "SupportPackagePatch": "000008", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP022&patch_level=000008"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP023", "SupportPackagePatch": "000015", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP023&patch_level=000015"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP024", "SupportPackagePatch": "000014", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP024&patch_level=000014"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP025", "SupportPackagePatch": "000013", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP025&patch_level=000013"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP026", "SupportPackagePatch": "000003", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP026&patch_level=000003"}, {"SoftwareComponentVersion": "SAP TECH S 7.00 OFFLINE", "SupportPackage": "SP027", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200004226&support_package=SP027&patch_level=000000"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}