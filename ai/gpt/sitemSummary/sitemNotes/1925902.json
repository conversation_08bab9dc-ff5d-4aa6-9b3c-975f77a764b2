{"Request": {"Number": "1925902", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 261, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017737862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001925902?language=E&token=AB1C9CA733FA5650BEF7DCC73FFF9509"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001925902", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001925902/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1925902"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 87}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.09.2020"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1925902 - Release Information Note: SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This Release Information Note (RIN) contains information and references to notes for applying Support Package (SP) Stacks of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA.</p>\r\n<p><strong>For a proper data migration to the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA it is key to apply the corrections and enhancements to the migration programs as listed below.</strong></p>\r\n<p><strong>Note:</strong> This SAP Note is subject to change. Listed below are points to keep in mind:<br /><br /><span style=\"text-decoration: underline;\"><strong>GENERAL INFORMATION</strong></span></p>\r\n<p>Read this note completely BEFORE applying Support Package (SP) Stacks of&#160;the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA and follow the instructions given below.</p>\r\n<ul>\r\n<li>Check this note for changes on a regular basis. All changes made after release of a Support Package (SP) Stack are documented in section \"Changes made after Release of SP Stack &lt;xx&gt;\".</li>\r\n<li>You will find general information about <strong>SP Stacks&#160;</strong>on SAP Support Portal at&#160;<a target=\"_blank\" href=\"https://support.sap.com/software/patches/stacks.html\">support.sap.com/software/patches/stacks.html</a>.<br />The Schedule for SP Stacks&#160;is available at <a target=\"_blank\" href=\"http://support.sap.com/maintenance-schedule\">support.sap.com/maintenance-schedule</a>.</li>\r\n<li>Be informed that all corrective Software Packages, including Support Package Stacks,&#160; are available through the Maintenance Optimizer in SAP Solution Manager or the Maintenance Planner in SL Toolset. For more information about Maintenance Optimizer and Maintenance Planner, see information on the <a target=\"_blank\" href=\"http://help.sap.com/sltoolset\">SL Toolset page</a>..</li>\r\n</ul>\r\n<p><strong>SPAM Update</strong></p>\r\n<ul>\r\n<li>We strongly recommend that you apply the latest version of the Support Package Manager before you apply any other Support Packages. <strong>Go to at least SP stack 05 </strong>of SAP Simple Finance add-on 1.0, but not SP stack 04.</li>\r\n<li>SPAM Updates are available on the download page for the SP Stack or on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">http</a><a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">://</a><a target=\"_blank\" href=\"http://service.sap.com/patches\">service.sap.com/patches</a> -&gt; Support Packages and Patches -&gt; SAP Support Packages in Detail --&gt; ABAP Support Package Manager --&gt; Download the Support Package Manager for your release. For additional information, see the initial screen of transaction SPAM, choose the 'i' icon (online documentation: help -&gt; application help).</li>\r\n<li>When you import components using the Support Package Manager, ensure that no system activities occur in parallel and no background jobs are running.</li>\r\n</ul>\r\n<p><strong>Installation and Upgrade Information</strong></p>\r\n<ul>\r\n<li>You can find the relevant information on installation, configuration and dependencies in the <a target=\"_blank\" href=\"https://websmp110.sap-ag.de/&#126;form/sapnet?_SCENARIO=01100035870000000202&amp;_SHORTKEY=01100035870000765769&amp;\">Admin Guide</a> for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA. Please note that there is a new option to directly install SAP Simple Finance add-on 1.0 on top of SAP enhancement package 7 for SAP ERP 6.0 in one step. You can find the relevant software on <a target=\"_blank\" href=\"https://support.sap.com/software/installations.html\">https://support.sap.com/software/installations.html</a>&#160;under <a target=\"_blank\" href=\"javascript:historyBack(1);\">SAP ERP</a><span style=\"font-size: xx-small; font-family: Wingdings 3;\">\"</span>&#160;<a target=\"_blank\" href=\"javascript:historyBack(2);\">SAP ERP ENHANCE PACKAGE</a><span style=\"font-size: xx-small; font-family: Wingdings 3;\">\"</span>&#160;<a target=\"_blank\" href=\"javascript:historyBack(3);\">EHP7 FOR SAP ERP 6.0</a><span style=\"font-size: xx-small; font-family: Wingdings 3;\">\"</span>&#160;<a target=\"_blank\" href=\"javascript:historyBack(4);\">SAP ERP on HANA incl. SAP Simple Finance add-on</a><span style=\"font-size: xx-small; font-family: Wingdings 3;\">\"</span>&#160;<a target=\"_blank\" href=\"javascript:historyBack(5);\">ERP EHP7 INCL. SFINANCIALS 1.0</a>.</li>\r\n<li>For a&#160;new server installation or system copy of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA there are currently problems with HANA Content activation. For more information please see SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</li>\r\n<li>The&#160;service \"Financials Add-on f&#252;r SAP Business Suite powered by SAP HANA V1 &#8211; Installation und Datentransformation\" supports you with the installation and transformation of your existing financial data into the new structures. You can find information in the SAP Store under&#160;<a target=\"_blank\" href=\"http://www.sapstore.de/\">www.sapstore.de</a> (search for \"simple finance\")&#160;or directly under the following link <a target=\"_blank\" href=\"https://store.sap.com/sap/cp/ui/resources/store/html/SolutionDetails.html?pid=**********&amp;catID=&amp;pcntry=DE&amp;sap-language=DE&amp;_cp_id=id-1416904830477-0\">https://store.sap.com/sap/cp/ui/resources/store/html/SolutionDetails.html?pid=**********&amp;catID=&amp;pcntry=DE&amp;sap-language=DE&amp;_cp_id=id-1416904830477-0</a>. This service is currently available for Germany only but planned to be enhanced for further countries soon.</li>\r\n<li>You can find information about browser support in SAP note <a target=\"_blank\" href=\"/notes/1971111\">1971111</a>.</li>\r\n<li>You can find information on the license for SAP Cash Management powered by SAP HANA in SAP note <a target=\"_blank\" href=\"/notes/2044295\">2044295</a>.</li>\r\n<li>\r\n<p>Please note that it is only possible to install and update to SAP Simple Finance add-on 1.0 SP05 directly. All lower Support Package Stacks (SPS00 - SPS04) have been archived and are not selectable in Maintenance Optimizer any longer. The reason is a technical issue during DDIC activation. If this is no option for you, please contact SAP for an individual solution.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Important Considerations</strong></p>\r\n<ul>\r\n<li>Release scope information for the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA including information on the compatibility of enterprise extensions, industry solutions, and add-ons can be found in SAP note <a target=\"_blank\" href=\"/notes/1968568\">1968568</a>.</li>\r\n<li>If you want to use Add-Ons with the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, then refer to SAP note <a target=\"_blank\" href=\"/notes/1976158\">1976158</a>.</li>\r\n<li>Please be informed that the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only released on In-Memory databases.&#160;Please be aware that it is not&#160; released for SAP HANA database 2.0.</li>\r\n<li>Release restrictions can be found in SAP note <a target=\"_blank\" href=\"/notes/1977782\">1977782</a>.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>financials add-on for SAP Business Suite powered by SAP HANA 1.0, SAP SFINANCIALS 1.0, SAP ERP 6.0, Upgrade, SFIN</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You want to run&#160;the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA and look for overall information.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;18 (09/2020)&#160;</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP18 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP21. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 21 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 24. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 21 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS24, use kernel 749 PL 800 or higher.</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;17 (03/2020)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP17 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP20. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 20 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 22. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 20 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS23, use kernel 749 PL 800 or higher.</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;16 (09/2019)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP16 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP19. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 19 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 22. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 19 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS21, use kernel 749 PL 700 or higher.</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;15 (03/2019)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP15 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP18. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 18 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 21. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 18 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS21, use kernel 745&#160;PL 800 or higher or kernel 749 PL 600 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP15 as assumed).</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;14 (10/2018)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP14 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP17. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 17 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 20. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;17 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS20, use kernel 745&#160;PL 700 or higher or kernel 749 PL&#160;500 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP14 as assumed).</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;13 (03/2018)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP13 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP16. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 16 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;19. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note&#160;<a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;16 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS19, use kernel 745&#160;PL 500 or higher or kernel 749 PL&#160;300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP13 as assumed).</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;12 (01/2018)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP12 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP15. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 15 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;18. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;15 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database.</p>\r\n<p>Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS18, use kernel 745&#160;PL 500 or higher or kernel 749 PL&#160;300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP12 as assumed).</p>\r\n<p>Important SAP Notes need to be checked or applied:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><em>Note ID</em></p>\r\n</td>\r\n<td>\r\n<p><em>Description</em></p>\r\n</td>\r\n<td>\r\n<p><em>Manual activity&#160;</em><em><br /><em>required</em></em></p>\r\n</td>\r\n<td>\r\n<p><em>&#160;Added on</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2738698\">2738698</a></p>\r\n</td>\r\n<td>\r\n<p>Bank chains: determination of intermediary banks with unexpected results</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>\r\n<p>2019-01-11</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>______________________________________________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;11 (07/2017)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA (SAP SFINANCIALS 1.0) SP11 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP14. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 14 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;17. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160; is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;14 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision&#160;122 of SP stack&#160;12&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database. Please be aware that SAP SFINANCIALS 1.0 is not&#160; released for SAP HANA database 2.</p>\r\n<p>Before update to NetWeaver 7.40 SPS17, use kernel 745&#160;PL 500 or higher or kernel 749 PL&#160;300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP11 as assumed).</p>\r\n<p><strong>Changes made after release of SP stack 11</strong></p>\r\n<p>[18.07.2017] Before update to NetWeaver 7.40 SPS17, use kernel 745&#160;PL 500 or higher or kernel 749 PL&#160;<span style=\"text-decoration: underline;\">300</span> or higher</p>\r\n<p>________________________________________________________________________</p>\r\n<p><strong>SUPPORT PACKAGE STACK&#160;10 (01/2017)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP10 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP13. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 123 see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;16. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;13 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 97 of SP stack&#160;10&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS16, use the kernel 742 SP301 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP10 as assumed).</p>\r\n<p><strong>_________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 09 (07/2016)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP09 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP12. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 12, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;15. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;12 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 97 of SP stack 09&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS15, use the kernel 742 SP300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP09 as assumed).</p>\r\n<p><strong>_________________________________________________________</strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 08 (01/2016)</strong></span></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP08 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP11. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 11, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;13. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;11 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 97 of SP stack 09&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS13, use the kernel 742 SP300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP08 as assumed).</p>\r\n<p><strong><strong>_________________________________________________________</strong></strong></p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 07 (10/2015)</strong></span></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP07 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP10. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 10, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;12. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at&#160;<a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note&#160;<a target=\"_blank\" href=\"/notes/1813548\">1813548</a>&#160;for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack&#160;10 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 97 of SP stack 09&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS12, use the kernel 742 SP300 or higher.</p>\r\n<p>Please be aware that the specific SP of the frontend version is \"SAP Fiori for the SAP Simple Finance add-on 1.0\" SP06 (and not SP07 as assumed).</p>\r\n<p><strong><strong>_________________________________________________________</strong></strong></p>\r\n<p><span style=\"text-decoration: underline;\"><strong>SUPPORT PACKAGE STACK 06 (07/2015)</strong></span></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP06 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP09. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 09, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;11. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"https://service.sap.com/maintenanceNW74\">https://service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP13.</p>\r\n<p>For source systems which are not based on HANA yet, please see note <a target=\"_blank\" href=\"/notes/1813548\">1813548</a> for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), read SAP note <a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 09 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 97 of SP stack 09&#160;of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS11, use the kernel 742 SP101 or higher.</p>\r\n<p><strong><strong>_________________________________________________________</strong></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 05 (04/2015)</span></strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP05 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP08. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 08, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at https://service.sap.com/erp-inst.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack&#160;10. For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at https://service.sap.com/maintenanceNW74</li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP12 PL05.</p>\r\n<p>For source systems which are not based on HANA yet, please see note <a target=\"_blank\" href=\"/notes/1813548\">1813548</a> for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), <strong>read SAP note </strong><a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 08 of Enhancement Package&#160;7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 85 of SP stack 08 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS10, use the kernel 742 SP23 or higher.</p>\r\n<p>In case you want to install 'SAP Simple Finance add-on 1.0' with SP05, please read the specific SP05-statement in the 'Installation and Upgrade Information' chapter above (which is&#160;only valid for an installation; it is not valid, if you have already installed 'SAP Simple Finance add-on 1.0' and&#160;do an update to the SP05-stack).</p>\r\n<p>If you plan the installation of the SAP Simple Finance add-on 1.0 in a system, which is already on EHP7 SP stack 07, it is only possible to go directly to <strong>SP stack 05 </strong>of SAP Simple Finance add-on 1.0! If you already have installed SAP Simple Finance add-on 1.0 SP stack 03 in a system, it is only possible to import SP stack 04 together with SP05 in a queue. This procedure will prevent you from problems in case of an upgrade at any point of time. If you have started the installation with SP04, the DDIC activation error regarding the view &#8220;V_MHNEX&#8221; may be ignored by choosing the option &#8220;accept non-severe errors&#8221;. <strong>Important</strong>: The related DDL view must be corrected after the add-on installation! <br />This can be done by importing SP05 of SAP Simple Finance add-on 1.0. If this is no option for you, please contact SAP for an individual solution.</p>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>_________________________________________________________</strong></span></strong></p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 04 (01/2015)</span></strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP04 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP07. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 07, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at https://service.sap.com/erp-inst.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 09 (11/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at https://service.sap.com/maintenanceNW74</li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP12 PL05.</p>\r\n<p>For source systems which are not based on HANA yet, please see note <a target=\"_blank\" href=\"/notes/1813548\">1813548</a> for more information regarding the Database Migration Option (DMO).</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), <strong>read SAP note </strong><a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 07 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 85 of SP stack 08 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS09, use the kernel 742 SP23 or higher.</p>\r\n<p>In case you want to install 'SAP Simple Finance add-on 1.0' with SP04, please read the specific SP04-statement in the 'Installation and Upgrade Information' chapter above (which is&#160;only valid for an installation; it is not valid, if you have already installed 'SAP Simple Finance add-on 1.0' and&#160;do an update to the SP04-stack).</p>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>_________________________________________________________</strong></span></strong></p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 03 (11/2014)</span></strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP03 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP06. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 06, see SAP Note 1737650. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at https://service.sap.com/erp-inst.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 08 (09/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at https://service.sap.com/maintenanceNW74</li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. To install the latest version of the financials add-on for SAP Business Suite powered by SAP HANA, the minimum required version is SUM 1.0 SP11.</p>\r\n<p>Additionally, to solve the delivery unit activation error during&#160;new&#160;server installation or system copy of&#160;SAP enhancement package 7 for SAP ERP 6.0 including SAP Simple Finance add-on 1.0 using SAP&#160;Software Provisioning Manager (SWPM), <strong>read SAP note </strong><a target=\"_blank\" href=\"/notes/2090914\">2090914</a>.</p>\r\n<p><strong>Important Considerations:</strong></p>\r\n<p>Before update to SP Stack 06 of Enhancement Package 7 for SAP ERP 6.0 check if the SAP HANA database was already updated to the required minimal revision 82 of SP stack 08 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p>Before update to NetWeaver 7.40 SPS09, use the kernel 742 SP23 or higher.</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>Before the update with Support Package 03 using SPAM:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>Note ID</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Description</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>Manual activity required</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2081053</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Cannot create CDS views: Missing base objects</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2094432</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>SPAM Import: Feld einer Struktur wird nicht an Abh&#228;ngige weitergegeben</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2086899</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Upgrade phase MAIN_SHDRUN/ACT_UPG returns error messages</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Before preparing the installation of financials add-on for SAP Business Suite powered by SAP HANA 1.0:</strong></p>\r\n<p>The migration to the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is prepared on the source system before the installation of the financials add-on. For this purpose check whether the following SAP notes apply to your source system's stack:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td>\r\n<p><em>Manual activity <br /></em><em>required</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2026835</td>\r\n<td>Technical SAP Note (correction for program SAPLFMCH, Include LFMCHU11)</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2038384</td>\r\n<td>J_1IHBKD: The database view \"BSIK\" is write-protected, so it cannot be changed</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1959235</td>\r\n<td>IL Cash System: Access to SAP_FIN Totals and Index Tables</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2031297</td>\r\n<td>IL Cash System: Access to SAP_FIN Totals and Index Tables</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2023763</td>\r\n<td>SFIN: check old dc engine and parallel currency in precheck report RASFIN_MIGR_PRECHECK (added 2014-08-07)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>During the installation of SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, consider the information provided in this note:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>Note ID</p>\r\n</td>\r\n<td valign=\"top\" width=\"267\">\r\n<p>Description</p>\r\n</td>\r\n<td valign=\"top\" width=\"136\">\r\n<p>Manual activity required</p>\r\n</td>\r\n<td valign=\"top\" width=\"120\">\r\n<p>Installation Step</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2019282</p>\r\n</td>\r\n<td valign=\"top\" width=\"267\">\r\n<p>SAP Simple Finance add-on: View activation errors during add-on installation (using SUM with DMO))</p>\r\n</td>\r\n<td valign=\"top\" width=\"136\">\r\n<p>X</p>\r\n</td>\r\n<td valign=\"top\" width=\"120\">\r\n<p>financials add-on installation</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>1967187</p>\r\n</td>\r\n<td valign=\"top\" width=\"267\">\r\n<p>Activation errors for SAP HANA content package \"sap.hba.ecc\" in Financials add-on for SAP Business Suite powered by SAP HANA</p>\r\n</td>\r\n<td valign=\"top\" width=\"136\">\r\n<p>X</p>\r\n</td>\r\n<td valign=\"top\" width=\"120\">\r\n<p>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Before start of post-installation migration activities:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>Note ID</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Description</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>Manual activity required</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2091882</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Migrating accounting documents from SP2 to SP3 in sFIN</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Latest before application configuration and testing: </strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>Note ID</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Description</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>Manual activity required</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>2067176</p>\r\n<p>2075705</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Very important notes - check and apply both (added 2014-10-13):</p>\r\n<p>ES Modeler: Model data for SWC SAP_FIN not up to date</p>\r\n<p>Financials add-on: Missing ODP models in reporting</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2097136</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Change of Offset Account Logic for DMBTR = 0 Documents</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2088040</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>SAPLF005 Dump COMPUTE_BCD_OVERFLOW</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td valign=\"top\" width=\"67\">\r\n<p>2098397</p>\r\n</td>\r\n<td valign=\"top\" width=\"354\">\r\n<p>Correction to Note 2088040 - SAPLF005: Dump COMPUTE_BCD_OVERFLOW</p>\r\n</td>\r\n<td valign=\"top\" width=\"169\">\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>As of SPS 03 of the SAP Simple Finance add-on powered by SAP HANA, you must perform the following activities in the SAP Customizing Implementation Guide after you have updated to SPS 03 or higher for the first time, even if you have already initially migrated your data:</p>\r\n<p><em>Migration to SAP Accounting powered by SAP HANA</em> &gt; <em>Migration of Transaction Data and Documents Fill Due Dates in FI Document: </em>In this activity, the new due date fields in the FI line items for customers, vendors, and G/L account line items that have a base line date, are filled.</p>\r\n<p><em>Migration to SAP Accounting powered by SAP HANA</em> &gt; <em>Migration of Transaction Data and Documents</em>:</p>\r\n<ul>\r\n<li><em>Define Offsetting Account Determination Type:</em> In this activity, you define how the system calculates the offsetting account in all applications.</li>\r\n<li><em>Fill the Offsetting Account in FI Document:</em> In this activity, the new offsetting account fields in the FI line items are filled.</li>\r\n<li>Perform the activity <em>Migrate Document Lines to Fill New Local Currency Fields for FI Documents</em>. The necessary objects for this activity are available in SAP Note 2091882.</li>\r\n</ul>\r\n<p>Furthermore, if you are using SAP NetWeaver 7.40 SPS 07 or higher, apply the corrections listed in the collective SAP Note 1970542.</p>\r\n<p><strong>_________________________________________________________</strong></p>\r\n<p><strong>SUPPORT PACKAGE STACK 02 (08/2014)</strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP02 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP05 (07/2014). For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 05, see SAP Note <a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package&#160;Stack 07 (06/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://</a><a target=\"_blank\" href=\"https://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. You need at least SUM 1.0 SP10 patchlevel 6.</p>\r\n<p>Additionally, to solve specific DDIC activation errors during the implementation with SUM or during the SP update with SPAM, <strong>read SAP note 2076656</strong>. (added 2014-10-09)</p>\r\n<p>Before update to SP Stack 02 of&#160;the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160;check if the SAP HANA database was already updated to the required minimal revision&#160;81 of SP stack&#160;08 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p><strong>Notes to be applied as part of this Stack:</strong></p>\r\n<p><strong>Before the update with Support Package 02 using SPAM:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>2022630</td>\r\n<td>Tx. UDO dumps for DDL Source (CDS view)</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Before preparing the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA:</strong></p>\r\n<p>The migration to the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is prepared on the source system <span style=\"text-decoration: underline;\">before</span> the installation of the SAP Simple Finance add-on. For this purpose check whether the following SAP notes apply to your source system's stack:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td>\r\n<p><em>Manual activity <br /></em><em>required</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2026835</td>\r\n<td>Technical SAP Note</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2038384</td>\r\n<td>J_1IHBKD: The database view \"BSIK\" is write-protected, so it cannot be changed</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1959235</td>\r\n<td>IL Cash System: Access to SAP_FIN Totals and Index Tables</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2031297</td>\r\n<td>IL Cash System: Access to SAP_FIN Totals and Index Tables</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2023763</td>\r\n<td>SFIN: check old dc engine and parallel currency in precheck report RASFIN_MIGR_PRECHECK (added 2014-08-07)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">During</span> the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, consider the information provided in these notes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity</em></td>\r\n<td><em>Installation Step</em></td>\r\n</tr>\r\n<tr>\r\n<td>2076656</td>\r\n<td>Activation Error for CDS Views in the financials add-on 1.0 (added 2014-10-09)</td>\r\n<td>X</td>\r\n<td>installation or update of SAP_FIN 700 using SUM / SPAM</td>\r\n</tr>\r\n<tr>\r\n<td>2019282</td>\r\n<td>SAP Simple Finance add-on: View activation errors during add-on installation (using SUM with DMO)</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1967187</td>\r\n<td>Activation errors for SAP HANA content package \"sap.hba.ecc\" in Financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n<td>X</td>\r\n<td>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">Before</span></strong><strong> start of post-installation migration activities:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>2022161</td>\r\n<td>sFIN: Customer posting with special G/L missing from balance carryforward</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2033979</td>\r\n<td>sFIN: Incorrect balance carryforwards for customers</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2048244</td>\r\n<td>sFIN: Incorrect balance carryforwards for vendors</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2048723</td>\r\n<td>Error when activating views for FAGLFLEXT</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2051105 and 2056602&#160;</td>\r\n<td>Error when activating views for migration (GLT0) (2056602 (added 2014-09-01)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2036355</td>\r\n<td>Error during activation of the views FAGLFLEXT and GLT0&#160;</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2063052</td>\r\n<td>Error when activating views for migration (added 2014-10-27)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Latest before application configuration and testing: </strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" style=\"height: 69px; width: 897px;\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><br />2067176<br />2075705</p>\r\n</td>\r\n<td>\r\n<p><strong>Very important notes - check and apply both (added 2014-10-13):<br /></strong>ES Modeler: Model data for SWC SAP_FIN not up to date<br />Financials add-on: Missing ODP models in reporting</p>\r\n</td>\r\n<td><br /><br />X</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>2050166</p>\r\n</td>\r\n<td>\r\n<p>Integrated Business Planning - Planning View Generation (only applicable&#160;after Setup Integrated Business Planning see SAP Note 1972819)</p>\r\n</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>As of SP02 of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, you must perform the activity Fill Due Dates in FI Documents in the SAP Customizing Implementation Guide (IMG) under Migration to SAP Accounting powered by SAP HANA &gt; Migration of Transaction Data and Documents. In this activity, the new due date fields in the FI line items for customers, vendors, and G/L account line items that have a base line date, are filled. Perform this activity after you have implemented SP02, even if you have already migrated your data earlier.</p>\r\n<p>Furthermore, if you are using SAP NetWeaver 7.40 SP07, apply the corrections listed in the collective SAP Note <a target=\"_blank\" href=\"/notes/1970542\">1970542</a>.</p>\r\n<p>_________________________________________________________</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 01 (05/2014)</span></strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP01 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP04. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 04, see SAP Note <a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 06 (03/2014). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://</a><a target=\"_blank\" href=\"https://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool. You need at least SUM 1.0 SP10 patchlevel 7.</p>\r\n<p>Additionally, to solve specific DDIC activation errors during the implementation with SUM or during the SP update with SPAM, <strong>read SAP note 2076656</strong>. (added 2014-10-09)</p>\r\n<p>Before update to SP Stack 04 of Enhancement Package 7 for SAP ERP 6.0 and the fincials add-on, check if the SAP HANA database was already updated to the required minimal revision 74 of SP stack 07 of SAP HANA database.&#160;SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p><strong>Notes to be applied as part of this Stack:<br /></strong></p>\r\n<p><strong>Before the update with Support Package 01 using SPAM:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>2022630</td>\r\n<td>Tx. UDO dumps for DDL Source (CDS view)</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>Before preparing the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA:</strong></p>\r\n<p>The migration to the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is prepared on the source system <span style=\"text-decoration: underline;\">before</span> the installation of the SAP Simple Finance add-on. For this purpose check whether the following SAP notes apply to your source system's stack:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td>\r\n<p><em>Manual activity <br /></em><em>required</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1963424</td>\r\n<td>Dump in RKACOR04</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1939592</td>\r\n<td>SFIN: Pre-Check Report for migrating to New Asset Accouting</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1958713</td>\r\n<td>SFIN: Precheck report RASFIN_MIGR_PRECHECK gives error message ACC_AA206</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1972590</td>\r\n<td>Activation problems due to _BAK or _BCK tables (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1972584</td>\r\n<td>Sequence problems for DDL sources and used Basis objects (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1997873</td>\r\n<td>Error FAGL_LEDGER_CUST 400 when activating new General Ledger Accounting</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1997151</td>\r\n<td>ASSERTION_FAILED when posting to fixed asset with multiple ANLZ records</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1976675</td>\r\n<td>S2I: Various errors for calculating depreciations</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2023763</td>\r\n<td>SFIN: check old dc engine and parallel currency in precheck report RASFIN_MIGR_PRECHECK (added 2014-08-07)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">During</span> the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, consider the information provided in these notes:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity</em></td>\r\n<td><em>Installation Step</em></td>\r\n</tr>\r\n<tr>\r\n<td>2076656</td>\r\n<td>Activation Error for CDS Views in the financials add-on 1.0 (added 2014-10-09)</td>\r\n<td>X</td>\r\n<td>installation or update of SAP_FIN 700 using SUM / SPAM, only relevant&#160;if being combined with NW 7.40 SP07</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1972590</td>\r\n<td>Activation problems due to _BAK or _BCK tables (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1972584</td>\r\n<td>Sequence problems for DDL sources and used Basis objects (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>2019282</td>\r\n<td>SAP Simple Finance add-on: View activation errors during add-on installation (using SUM with DMO)</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1967187</td>\r\n<td>Activation errors for SAP HANA content package \"sap.hba.ecc\" in Financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n<td>X</td>\r\n<td>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n</tr>\r\n<tr>\r\n<td>2005862</td>\r\n<td>Clean up HANA views in sap.erp.sfin.fi.ar and related ABAP objects</td>\r\n<td>X</td>\r\n<td>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">Before</span></strong><strong> start of post-installation migration activities:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>2001102</td>\r\n<td>sFIN data migration is too slow</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1998371</td>\r\n<td>S2I: Termination with importing the activation of the \"New Asset Accounting\"</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2022161</td>\r\n<td>sFIN: Customer posting with special G/L missing from balance carryforward</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2033979</td>\r\n<td>sFIN: Incorrect balance carryforwards for customers (added 2014-08-08)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2048244</td>\r\n<td>sFIN: Incorrect balance carryforwards for vendors (added 2014-08-08)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2036355</td>\r\n<td>Error during activation of the views FAGLFLEXT and GLT0 (added 2014-08-07)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2048723</td>\r\n<td>Error when activating views for FAGLFLEXT (added 2014-08-07)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2051105 and 2056602</td>\r\n<td>Error when activating views for migration (GLT0) (added 2014-08-07 and 2014-09-01)</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2063052</td>\r\n<td>Error when activating views for migration (added 2014-10-27)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>Latest before application configuration and testing: </strong></span></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em><em>Manual activity<br />required</em></em></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p><br />2067176<br />2075705</p>\r\n</td>\r\n<td>\r\n<p><strong>Very important notes - check and apply both (added 2014-10-13):<br /></strong>ES Modeler: Model data for SWC SAP_FIN not up to date<br />Financials add-on: Missing ODP models in reporting</p>\r\n</td>\r\n<td><br /><br />X</td>\r\n</tr>\r\n<tr>\r\n<td>1994045</td>\r\n<td>Dump bei Abrechnung eines Gesch&#228;ftes</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1975577</td>\r\n<td>Unable to call certain transactions or reports</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2013133</td>\r\n<td>S2I: Short dump GETWA_NOT_ASSIGNED when posting to asset</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1996159</td>\r\n<td>Performance of cost center reports in sFin</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>2023557</td>\r\n<td>Depreciation error when posting assets with index</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Furthermore, if you are using SAP NetWeaver 7.40 SPS&#160;06, apply the corrections listed in the collective SAP Note <a target=\"_blank\" href=\"/notes/1970542\">1970542</a>.</p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">SUPPORT PACKAGE STACK 00 (02/2014)</span></strong></p>\r\n<p><strong>Installation Requirements</strong></p>\r\n<p>SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA SP00 is based on:</p>\r\n<ul>\r\n<li>SAP enhancement package 7 for SAP ERP 6.0 SP03. For more information about SAP enhancement package 7 for SAP ERP 6.0 SP Stack 03, see SAP Note <a target=\"_blank\" href=\"/notes/1737650\">1737650</a>. Refer to the corresponding Upgrade Master Guide for detailed information on Enhancement Package 7. For more details about the guide, see SAP Service Marketplace at <a target=\"_blank\" href=\"http://service.sap.com/erp-inst\">https://service.sap.com/erp-inst</a>.</li>\r\n<li>SAP NetWeaver 7.40 Support Package Stack 05 (12/2013). For more information about SAP NetWeaver, see the stack guide on SAP Service Marketplace at <a target=\"_blank\" href=\"http://wiki.sdn.sap.com/wiki/display/SLGB/Version+Interoperability+Strategy\">https://</a><a target=\"_blank\" href=\"https://service.sap.com/maintenaceNW74\">service.sap.com/maintenanceNW74</a></li>\r\n</ul>\r\n<p>The implementation of the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA is only possible via SUM tool.</p>\r\n<p>Additionally, to solve specific DDIC activation errors during the implementation with SUM or during the SP update with SPAM, <strong>read SAP note 2076656</strong>. (added 2014-10-09)</p>\r\n<p>Before update to SP Stack 03 of Enhancement Package 7 for SAP ERP 6.0 and the&#160;SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA&#160;check if the SAP HANA database was already updated to the required minimal revision 70 of SP stack 07 of SAP HANA database. SAP in general recommends customers to implement the highest revision available to get all available fixes if facing an issue with their SAP HANA installation and not told otherwise by their implementation partner. You can find the highest available revision here: service.sap.com/swdc -&gt;&#160;Support Packages and Patches&#160;-&gt; H -&gt;SAP HANA PLATFORM EDITION -&gt;&#160;SAP HANA PLATFORM EDIT. 1.0 -&gt;&#160;Entry by Component -&gt;&#160;HANA database</p>\r\n<p><strong>Notes to be applied as part of this Stack:<br /></strong><strong>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;</strong><br /><strong>Before preparing the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, check whether these notes apply:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td>\r\n<p><em>Manual activity <br /></em><em>required</em></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1963424</td>\r\n<td>Dump in RKACOR04</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1939592</td>\r\n<td>SFIN: Pre-Check Report for migrating to New Asset Accouting</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1958713</td>\r\n<td>SFIN: Precheck report RASFIN_MIGR_PRECHECK gives error message ACC_AA206</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1964911</td>\r\n<td>Different errors during activation</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1972590</td>\r\n<td>Activation problems due to _BAK or _BCK tables</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1972584</td>\r\n<td>Sequence problems for DDL sources and used Basis objects</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><span style=\"text-decoration: underline;\">During</span> the installation of the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA, consider the information provided in this note:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity</em></td>\r\n<td><em>Installation Step</em></td>\r\n</tr>\r\n<tr>\r\n<td>2076656</td>\r\n<td>Activation Error for CDS Views in the financials add-on 1.0 (added 2014-10-09)</td>\r\n<td>X</td>\r\n<td>installation or update of SAP_FIN 700 using SUM / SPAM - only relevant&#160;if being combined with NW 7.40 SP07</td>\r\n</tr>\r\n<tr>\r\n<td>1925679</td>\r\n<td>DB-Views FMKK_BKPF_BSIK and FMKK_BKPF_BSAK have been deleted (relevant in case software component version EA-PS 600 is used)</td>\r\n<td>X</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1972590</td>\r\n<td>Activation problems due to _BAK or _BCK tables (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1972584</td>\r\n<td>Sequence problems for DDL sources and used Basis objects (added 2014-07-04)</td>\r\n<td>&nbsp;</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>2019282</td>\r\n<td>SAP Simple Finance add-on: View activation errors during add-on installation (using SUM with DMO)</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n<td>financials add-on installation</td>\r\n</tr>\r\n<tr>\r\n<td>1967187</td>\r\n<td>Activation errors for SAP HANA content package \"sap.hba.ecc\" in Financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n<td>X</td>\r\n<td>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n</tr>\r\n<tr>\r\n<td>2005862</td>\r\n<td>Clean up HANA views in sap.erp.sfin.fi.ar and related ABAP objects</td>\r\n<td>X</td>\r\n<td>SAP HANA Live for financials add-on for SAP Business Suite powered by SAP HANA</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><strong><span style=\"text-decoration: underline;\">Before</span></strong><strong> start of post-installation migration activities:</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>1971688</td>\r\n<td>Collective Note for Corrections to Documentation, IMG, and Menus in the financials add-on for SAP Business Suite powered by SAP HANA 1.0 SP00</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1968144</td>\r\n<td>Migration of deactivated company code</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1970110</td>\r\n<td>S2I: Short dump MESSAGE_TYPE_X during posting of integrated asset transactions</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1962911</td>\r\n<td>SFIN: Intial Entry missing in table T093_BSN_FUNC when activating New Asset Acc</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1978660</td>\r\n<td>Incorrect deletion of switches in the delivery</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1967357</td>\r\n<td>GLT0 balance carryforward during the migration to sFIN</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1978030</td>\r\n<td>Role SAP_SFIN_ACC_MASTERDATA: Maintainence of Cost Center Groups and Cost Center Standard Hierarchy</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1977924</td>\r\n<td>\r\n<p>Role SAP_SFIN_ACC_MASTERDATA: Deprecated Transactions</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1983794</td>\r\n<td>\r\n<p>sFIN data migration: Error in migration of GLT0 and reconciliation of FAGLFLEXT</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1990200</td>\r\n<td>\r\n<p>sFIN data migration: Missing balance carry forwards</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1985403</td>\r\n<td>\r\n<p>sFIN new G/L migration: delta mechanism</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1968305</td>\r\n<td>\r\n<p>S2I: New Asset Accounting Check report should not check on inactive company code</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1962190</td>\r\n<td>\r\n<p>SFIN: OADB the scenario for account solution or ledger solution is not transported</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1962313</td>\r\n<td>\r\n<p>SFIN: Account solution is wrongly determined from user input</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1991098</td>\r\n<td>\r\n<p>SFIN: Migration von Bewertungsplan mit dem Migrationsreport</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1991227</td>\r\n<td>\r\n<p>sFIN: DDIC prerequisites for note 1991228 (parallelization of reconciliation, basis for delta migration and CO performance optimization) (added 2014-03-19)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>1991228<br />1994640</p>\r\n</td>\r\n<td>\r\n<p>sFIN: addition to note 1991227 (parallelization of reconciliation, basis for delta migration and CO performance optimization) (added 2014-03-19)<br />sFIN migration: too many actions in delta run (added 2014-03-27)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1990800</td>\r\n<td>\r\n<p>Migration of accounting documents during the installation of sFIN&#160;(added 2014-03-19; replacing note 1967175)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1993069</td>\r\n<td>\r\n<p>Partner Profit Center derived during Posting&#160;(added 2014-03-19)</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>1997257</td>\r\n<td>\r\n<p>&#160;Migration to financials add-on for SAP Business Suite powered by SAP HANA: Opening Balances (added 2014-03-27)</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2022161</td>\r\n<td>\r\n<p>&#160;sFIN: Customer posting with special G/L missing from balance carryforward (added 2014-08-08)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2033979</td>\r\n<td>\r\n<p>&#160;sFIN: Incorrect balance carryforwards for customers (added 2014-08-08)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>2048244</td>\r\n<td>\r\n<p>&#160;sFIN: Incorrect balance carryforwards for vendors (added 2014-08-08)</p>\r\n</td>\r\n<td>\r\n<p>X</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><span style=\"text-decoration: underline;\">Update&#160;of further important notes as of 2014-05-15:</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n<td><em>Manual activity<br />required</em></td>\r\n</tr>\r\n<tr>\r\n<td>1965085</td>\r\n<td>SoH LDB SDF: Incorrect results with transaction FBL3N (2)</td>\r\n<td>&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>1994048</td>\r\n<td>Differences after creating opening balances from GLT0 or GLT3, cannot maintain currency codes in view V_FGL_MIG_BCF</td>\r\n<td>&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>1994342</td>\r\n<td>sFIN: Wrong reference to Currency for BSIK/BSAK in field PENFC</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1994485</td>\r\n<td>sFIN: KNC1 Verkehrszahlen f&#252;r Sonderhauptbuchvorg&#228;nge</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1995252</td>\r\n<td>sFIN Migrationsmonitor zeigt falsches Protokoll nach Delta-Laufs</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1996456</td>\r\n<td>sFIN: Excluding sample documents from index views</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1996459</td>\r\n<td>sFIN: Reconciliation of accounts with line item management missing</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1996589</td>\r\n<td>sFIN: Prerequisite for SAP Note 1996459</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1997239</td>\r\n<td>sFIN: Missing messages in summary report</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1997246</td>\r\n<td>sFIN: Too many error messages in reconciliation</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2000716</td>\r\n<td>sFIN: Correction to SAP Note 1997246</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1997789</td>\r\n<td>Deadlock during data migration for sFIN</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1997873</td>\r\n<td>Error FAGL_LEDGER_CUST 400 when activating new General Ledger Accounting</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>1998371</td>\r\n<td>S2I: Termination with importing the activation of the \"New Asset Accounting\"</td>\r\n<td>X</td>\r\n</tr>\r\n<tr>\r\n<td>1998577</td>\r\n<td>sFIN: Document migration does not process sample documents</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n<tr>\r\n<td>2001102</td>\r\n<td>sFIN data migration is too slow</td>\r\n<td>&nbsp;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;<br /><strong>Latest before application configuration and testing:&#160;&#160;&#160;&#160;</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td><em>Note ID</em></td>\r\n<td><em>Description</em></td>\r\n</tr>\r\n<tr>\r\n<td>1969651</td>\r\n<td>Master Data Transactions in Role SAP_SFIN_ACC_MASTERDATA do not work</td>\r\n</tr>\r\n<tr>\r\n<td>1973087</td>\r\n<td>Type 'TUH_S_TEST_CONTEXT' missing error in SFIN</td>\r\n</tr>\r\n<tr>\r\n<td>1971366</td>\r\n<td>Role SAP_SFIN_ACC_PLANNING need some adjustment</td>\r\n</tr>\r\n<tr>\r\n<td>1972481</td>\r\n<td>Error when selecting CO-PA fields in the G/L Account Line Item Display (Entry V</td>\r\n</tr>\r\n<tr>\r\n<td>1973989</td>\r\n<td>KT851: \"Warning Index 9 for table COSS is not yet available\"</td>\r\n</tr>\r\n<tr>\r\n<td>1974335</td>\r\n<td>Line item display shows incorrect items for selection by \"Open at key date\".</td>\r\n</tr>\r\n<tr>\r\n<td>1975314</td>\r\n<td>Derive HBKID and HKTID logic in program RFEBBU00</td>\r\n</tr>\r\n<tr>\r\n<td>1975337</td>\r\n<td>Line item display: Unable to navigate to a cost center</td>\r\n</tr>\r\n<tr>\r\n<td>1973560</td>\r\n<td>BRAIN 643: Kostenstellenvariable falsch definiert</td>\r\n</tr>\r\n<tr>\r\n<td>1976030</td>\r\n<td>Termination during the currency translation</td>\r\n</tr>\r\n<tr>\r\n<td>1975577</td>\r\n<td>Call of general ledger reporting reports not possible</td>\r\n</tr>\r\n<tr>\r\n<td>1968864</td>\r\n<td>financials add-on for SAP Business Suite powered by SAP HANA 1.0: Collective note for asset accounting</td>\r\n</tr>\r\n<tr>\r\n<td>1970542</td>\r\n<td>Financials Add-on for SAP Business Suite powered by SAP HANA: Important SAP NETWEAVER 7.40 SPS 5 and SPS 6 Corrections</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Furthermore, <strong>if you are using SAP NetWeaver&#160;7.40 SPS 5 or SPS6, apply the corrections listed in the collective SAP Note 1970542</strong> (Important SAP NETWEAVER 7.40 SPS 5 and SPS 6&#160;Corrections for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA).</p>\r\n<p>&#160;</p>\r\n<div id=\"gtx-trans\" style=\"position: absolute; left: 637px; top: 874.667px;\">\r\n<div class=\"gtx-trans-icon\"></div>\r\n</div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I027565)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001925902/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2049996", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information Note: SAP Smart Business for the financials add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2049996"}, {"RefNumber": "1977782", "RefComponent": "XX-SER-REL", "RefTitle": "Release restrictions for SAP Simple Finance add-on 1.0", "RefUrl": "/notes/1977782"}, {"RefNumber": "1976158", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA: Compatible Add-ons", "RefUrl": "/notes/1976158"}, {"RefNumber": "1974650", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition: ICF Service Information for WebDynpro ABAP applications", "RefUrl": "/notes/1974650"}, {"RefNumber": "1971111", "RefComponent": "FI-GL-IS", "RefTitle": "SAP S/4HANA Finance: Browser Requirements", "RefUrl": "/notes/1971111"}, {"RefNumber": "1970542", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA: Important SAP NETWEAVER 7.40 SPS 5 / SPS 6 / SPS 7 / SPS 8 / SPS 9 corrections", "RefUrl": "/notes/1970542"}, {"RefNumber": "1968568", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1968568"}, {"RefNumber": "1939991", "RefComponent": "BC-UPG-ADDON", "RefTitle": "Release strategy: financials add-on for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1939991"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2261242", "RefComponent": "FIN-MIG", "RefTitle": "SAP S/4HANA Finance: Information on Upgrade Paths", "RefUrl": "/notes/2261242 "}, {"RefNumber": "2157996", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Finance: Checklist for Technical Installation / Upgrade", "RefUrl": "/notes/2157996 "}, {"RefNumber": "2129659", "RefComponent": "FI-FIO-GL", "RefTitle": "Release Information Note: Journal Entry History", "RefUrl": "/notes/2129659 "}, {"RefNumber": "2112354", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Simple Finance, on-premise edition: Important installation information", "RefUrl": "/notes/2112354 "}, {"RefNumber": "2035994", "RefComponent": "XX-SER-REL", "RefTitle": "SAP S/4HANA Finance: Installation with components / products not yet released for the financials add-on", "RefUrl": "/notes/2035994 "}, {"RefNumber": "2105948", "RefComponent": "FI-GL", "RefTitle": "Check report for Financials add-on", "RefUrl": "/notes/2105948 "}, {"RefNumber": "1955437", "RefComponent": "XX-SER-REL", "RefTitle": "Release Information Note: SAP Fiori for the SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1955437 "}, {"RefNumber": "1968568", "RefComponent": "XX-SER-REL", "RefTitle": "Release Scope Information: SAP Simple Finance add-on 1.0 for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/1968568 "}, {"RefNumber": "1898076", "RefComponent": "FI-AR", "RefTitle": "Collective Note sFIN - prereq. Notes", "RefUrl": "/notes/1898076 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "EA-FIN", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "FSCM_CCD", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}