{"Request": {"Number": "2495864", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 523, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019085032017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002495864?language=E&token=D9105C25C57E68A1CEAE04E0E936E2DD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002495864", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002495864/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2495864"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.07.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FS-CML"}, "SAPComponentKeyText": {"_label": "Component", "value": "Consumer and Mortgage Loans"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Consumer and Mortgage Loans", "value": "FS-CML", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-CML*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2495864 - <PERSON><PERSON><PERSON> CML - Pre-transition check for RE Classic"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You intend to convert your ERP system to SAP S/4HANA.</p>\r\n<p>FS-CML Consumer and Mortgage Loans in the context of SAP S/4HANA: The CML-specific functions with regard to collaterals and collateral objects are <span style=\"text-decoration: underline;\">no longer</span> available. This is due to the entry with the subject \"Real Estate Classic\" in the \"Simplification List for SAP S/4HANA, on-premise edition 1511\". At this point, it is highly recommended to use the functions of application FS-CMS Collateral Management.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S4TC</p>\r\n<p>S/4 transition</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>S/4 transition</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions or import the specified Support Package.</p>\r\n<p>This note provides&#160;class CL_FVD_S4TC_CHK, that is called from the class CLS4H_CHECKS_RE_CLASSIC created by the note <a target=\"_blank\" href=\"/notes/2339534\">2339534</a>.\r\n<script language=\"JavaScript\" src=\"https://i7p.wdf.sap.corp/sap/public/bc/ur/sap_secu.js\" type=\"text/javascript\"></script>\r\n</p>\r\n<p>The class CL_FVD_S4TC_CHK checks, if CML is still using RE-Classic data.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D036589)"}, {"Key": "Processor                                                                                           ", "Value": "D027488"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002495864/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002495864/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2369934", "RefComponent": "FS-CML", "RefTitle": "S4TWL - CML-specific functions with regard to collaterals and collateral objects", "RefUrl": "/notes/2369934"}, {"RefNumber": "2339534", "RefComponent": "RE-FX", "RefTitle": "S4TC SAP_APPL – Pre-transition check for Classic RE", "RefUrl": "/notes/2339534"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2369934", "RefComponent": "FS-CML", "RefTitle": "S4TWL - CML-specific functions with regard to collaterals and collateral objects", "RefUrl": "/notes/2369934 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAPSCORE", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "800", "To": "800", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10103INS4CORE", "URL": "/supportpackage/SAPK-10103INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10201INS4CORE", "URL": "/supportpackage/SAPK-10201INS4CORE"}, {"SoftwareComponentVersion": "EA-FINSERV 600", "SupportPackage": "SAPKGPFD31", "URL": "/supportpackage/SAPKGPFD31"}, {"SoftwareComponentVersion": "EA-FINSERV 603", "SupportPackage": "SAPK-60320INEAFINSRV", "URL": "/supportpackage/SAPK-60320INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 604", "SupportPackage": "SAPK-60421INEAFINSRV", "URL": "/supportpackage/SAPK-60421INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 605", "SupportPackage": "SAPK-60518INEAFINSRV", "URL": "/supportpackage/SAPK-60518INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 606", "SupportPackage": "SAPK-60620INEAFINSRV", "URL": "/supportpackage/SAPK-60620INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 616", "SupportPackage": "SAPK-61612INEAFINSRV", "URL": "/supportpackage/SAPK-61612INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 617", "SupportPackage": "SAPK-61715INEAFINSRV", "URL": "/supportpackage/SAPK-61715INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 618", "SupportPackage": "SAPK-61808INEAFINSRV", "URL": "/supportpackage/SAPK-61808INEAFINSRV"}, {"SoftwareComponentVersion": "EA-FINSERV 800", "SupportPackage": "SAPK-80005INEAFINSRV", "URL": "/supportpackage/SAPK-80005INEAFINSRV"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002495864/19773"}, {"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 1, "URL": "/corrins/0002495864/201"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2506393", "RefTitle": "Technical adjustment", "RefUrl": "/notes/0002506393"}, {"RefNumber": "2509838", "RefTitle": "CL_FVD_S4TC_CHK: technical adjustment", "RefUrl": "/notes/0002509838"}, {"RefNumber": "2526409", "RefTitle": "Technical adjustment", "RefUrl": "/notes/0002526409"}, {"RefNumber": "2599127", "RefTitle": "CL_FVD_S4TC_CHK - error in pre_check routine", "RefUrl": "/notes/0002599127"}]}}}}}