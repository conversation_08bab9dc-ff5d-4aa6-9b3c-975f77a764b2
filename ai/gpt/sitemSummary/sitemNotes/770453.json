{"Request": {"Number": "770453", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 338, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015754072017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000770453?language=E&token=509482455682A9278EEF7E753A6E3685"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000770453", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000770453/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "770453"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.10.2004"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-REL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Suite and SAP S/4HANA Release Information"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Messages", "value": "XX-SER", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Suite and SAP S/4HANA Release Information", "value": "XX-SER-REL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-SER-REL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "770453 - ERP 2004 SP Stack 3 Release and Information Note"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p><br />This Release- and Information Note (RIN) contains information and references to notes in the context of applying Support Package Stack 3 for ERP 2004.<br /><br />This note is subject to change. Please make sure to check for changes on a regular basis. For your convenience, changes will be documented in a section at the end of the note called \"Changes made after Release of SP Stack 3.<br /><br />Currently there are restrictions for scenarios. Details see note 741821.<br /><br />Please make sure you install new Support Package Stacks regularly to prevent problems that have already been corrected from occurring in your SAP solution.<br />(SAP's Release Strategy: http://service.sap.com/releasestrategy )<br /><br />Before you start, you should have a clear picture of the components you use in your system landscape. Only instructions and notes for components which you use are relevant for you.<br /><br />In addition to the notes mentioned here, you should also take into account the list of known side effects for Support Packages, which will be created especially for your situation (SP queue). This list can be requested in the SAP Service Marketplace under quick link http://service.sap.com/notes . You will find a corresponding link also at the end of the download page for the SP Stack.<br /><br />While this note (RIN) in general refers to other notes of very high priority and also some notes of high priority, that are critical for successful implementation and operation of the SP Stack, the list of side effects will include notes of any priority, that are related to the range of Support Packages you will apply with your selected SP queue.<br /><br />Single patches can be found in the SAP Service Marketplace under the quick link http://service.sap.com/patches .<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>Please read this note completely BEFORE applying ERP 2004 SP Stack 3 to your system and follow the instructions.</B><br /></p> <OL>1. <B>Important information about SP Stack 3</B></OL> <UL><LI>You will find general information about SP Stacks under<br />http://service.sap.com/sp-stacks.<br /></LI></UL> <p></p> <OL>2. <B>The following components are part of this ERP SP Stack 03: </B></OL> <p><br /><B>Software Components in Master Guide</B></p> <div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\"><TR><TH ALIGN=LEFT><B>Details</TH><TH ALIGN=LEFT> SP level</TH><TH ALIGN=LEFT> </B> </TH></TR> <TR><TH ALIGN=LEFT>SPAM Update</TH><TH ALIGN=LEFT> </TH><TH ALIGN=LEFT> </TH></TR> <TR><TD>We strongly recommend to apply the latest version of the Support Package Manager before you apply any other Support Packages. SPAM Updates are available at the Download Page for the SP Stack or at the SAP Service Marketplace:<br />http://service.sap.com/patches -&gt; Download Support Packages -&gt; SPAM/SAINT Updates -&gt; SPAM Update<br />For additional information see initial screen of Transaction SPAM, click the 'i' icon (online documentation: help -&gt; application help).<br />During the import of components via the Support Package Manager, no system activities should occur in parallel and no background job should be running. </TD></TR> <TR><TH>SAP NetWeaver 04</TH><TH> </TH><TH> </TH></TR> <TR><TD>SAP NetWeaver 04</TD><TD> NW 04 SP Stack 5</TD><TD> </TD></TR> <TR><TD>See also http://service.sap.com/instguidesnw04 -&gt; Operations </TD></TR> <TR><TH>SAP ECC (See also release information note 746576)</TH></TR> <TR><TD>EA-APPL 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-DFPS 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-FINSERV 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-GLTRADE 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-HR 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-IPPE 300</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-PS 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>EA-RETAIL 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>PI 2004_1_500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP APPL 5.00</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP HR 5.00</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>ST-PI 003C_640</TD><TD> SP01</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>PI_BASIS 2004_1_640</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP ABA 640</TD><TD> SP05</TD><TD></TD></TR> <TR><TD>SAP BASIS 6.40</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP BW 3.50</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP KERNEL (details see download page)</TD></TR> <TR><TD></TD></TR> <TR><TH>SAP XSS</TH></TR> <TR><TD>SAP ESS 100</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP MSS 100</TD><TD> SP03</TD><TD></TD></TR> <TR><TD>SAP PCUI_GP 100</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP SEM (BW)</TH></TR> <TR><TD>FINBASIS 300</TD><TD> SP04</TD><TD> </TD></TR> <TR><TD>SEM-BW 400</TD><TD> SP04</TD><TD> </TD></TR> <TR><TD></TD></TR> <TR><TH>SAP FSCM-Biller Direct</TH></TR> <TR><TD>FSCM-BILLER DIRECT 3.0</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP FSCM- FSCM Server (ABAP)</TH></TR> <TR><TD>FINBASIS 300</TD><TD> SP04</TD><TD> </TD></TR> <TR><TH>SAP Learning Solution Client (Lern)</TH></TR> <TR><TD>LSOOP 300 (OFFLINE PLAYER)</TD><TD> SP03</TD><TD> </TD></TR> <TR><TH>SAP Learning Solution Client (Auth)</TH></TR> <TR><TD>LSOAE 300 (AUTHORS ENVIRONMENT)</TD><TD> SP03</TD><TD> </TD></TR> <TR><TH>SAP Learning Solution Frontend CP</TH></TR> <TR><TD>LSOCP (CONTENT PLAYER)</TD><TD> SP03</TD><TD> </TD></TR> <TR><TH>SAP Learning Solution Frontend ABAP</TH></TR> <TR><TD>LSOFE 300 (FRONT END)</TD><TD> SP03</TD><TD> </TD></TR> <TR><TH>SAP SRM - Server</TH></TR> <TR><TD>SAP SRM Server 5.0</TD><TD> SP02</TD><TD> </TD></TR> <TR><TD>required NW components (exception from NW Stack 5):</TD></TR> <TR><TD>PI_BASIS 2004_1_640</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP ABA 640</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP BASIS 6.40</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP BW 3.50</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>SAP KERNEL (details see download page) </TD></TR> <TR><TH>SAP SRM - Catalog Content</TH></TR> <TR><TD>SAP CATALOG CONT. MGT. 1.0</TD><TD> SP02</TD><TD> </TD></TR> <TR><TH>SAP SRM - IPC</TH></TR> <TR><TD>CRM IPC 4.0</TD><TD> SP06</TD><TD> </TD></TR> <TR><TH>SAP CRM - IPC (for SAP CRM - IPC Web Applications see below)</TH></TR> <TR><TD>CRM IPC 4.0</TD><TD> SP07</TD><TD> </TD></TR> <TR><TH>SAP Internet Sales</TH></TR> <TR><TD>SAP INTERNET SALES WAC_640</TD><TD> SP07</TD><TD> </TD></TR> <TR><TH>SAP cProject Suite</TH></TR> <TR><TD>CPROJECTS 310 ON 6.40</TD><TD> SP02</TD><TD> </TD></TR> <TR><TH>SAP Workforce Management</TH></TR> <TR><TD>WMFCORE 110_640</TD><TD> SP02</TD><TD> </TD></TR> <TR><TH>SAP E-Recruiting</TH></TR> <TR><TD>ERECRUIT 300</TD><TD> SP03</TD><TD> </TD></TR> <TR><TH>SAP NW - BW</TH></TR> <TR><TD>BI CONT 3.52 (details see download page)</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>PI_BASIS 2004_1_640</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP ABA 640</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP BASIS 6.40</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP BW 3.50</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP KERNEL (details see download page) </TD></TR> <TR><TH>SAP NW - BW UDI</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>BI UDI 350</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP NW - Enterprise Portal</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>CM+COLLABORATION 6.0_640</TD><TD> SP07</TD><TD> </TD></TR> <TR><TD>PORTAL PLATFORM 6.0_640</TD><TD> SP07</TD><TD> </TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP NW - XI</TH></TR> <TR><TD>XI CONTENT CATALOG CCM 1.0</TD><TD> SP02</TD><TD> </TD></TR> <TR><TD>XI CONTENT EA-APPL 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>XI Content FINBASIS_300</TD><TD> SP04</TD><TD> </TD></TR> <TR><TD>XI CONTENT PI 2004_1_500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>XI CONTENT PI_BASIS 2004_1_640</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>XI CONTENT SAP_APPL 500</TD><TD> SP03</TD><TD> </TD></TR> <TR><TD>XI Content SRM SERVER 5.0</TD><TD> SP02</TD><TD> </TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>PI_BASIS 2004_1_640</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP ABA 640</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP BASIS 6.40</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP BW 3.50</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TD>XI CONTENT SAP_BASIS 6.40</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>XI ADAPTER FRAMEWORK 3.0</TD><TD> SP05</TD><TD></TD></TR> <TR><TD>XI ADAPTER FRAMEWORK 3.0 CORE</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>XI TOOLS 3.0</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>SAP KERNEL (details see download page)</TD><TD> </TD><TD> </TD></TR> <TR><TH>SAP NW - XI Adapter Engine J2SE</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>XI CONNECTIVITY SE 3.0</TD><TD> SP05</TD><TD> </TD></TR> <TR><TH>SAP NW - XI Adapter/Proxy Engine</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP J2EE ENGINE 6.40</TD><TD> SP08</TD><TD> </TD></TR> <TR><TD>XI ADAPTER FRAMEWORK 3.0</TD><TD> SP05</TD><TD> </TD></TR> <TR><TD>XI ADAPTER FRAMEWORK 3.0 CORE</TD><TD> SP05</TD><TD> </TD></TR> <TR><TH>SAP NW - MI Client</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP MI CLIENT 2.5</TD><TD> SP04</TD><TD> </TD></TR> <TR><TH>SAP NW - TREX</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>TREX 6.1</TD><TD> SP05</TD><TD> </TD></TR> <TR><TH>SAP NW - ADOBE</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>ADOBE DOCUMENT SERVICES 1.00</TD><TD> SP03</TD><TD></TD></TR> <TR><TD>SAP J2EE ENGINE 6.40 </TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP CRM - IPC Web Applications</TH></TR> <TR><TD>IPC WEB APP 4.0</TD><TD> SP07</TD><TD> </TD></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP J2EE ENGINE 6.40 </TD><TD> SP08</TD><TD> </TD></TR> <TR><TH>SAP Frontend GUIs</TH></TR> <TR><TD>required NW components:</TD></TR> <TR><TD>SAP GUI FOR WINDOWS 6.20 (details see download page)</TD></TR> <TR><TD>SAP GUI FOR WINDOWS 6.40 (details see download page)</TD></TR> <TR><TD>SAP GUI FOR JAVA 6.30 (details see download page)</TD><TD> </TD><TD> </TD></TR> <TR><TH>SAP Mobile Sales for HH (with ECC) 2.0</TH></TR> <TR><TD>MSR 2.0 FOR HH</TD><TD> no SP</TD><TD> </TD></TR> <TR><TD></TD></TR> </TABLE> <p><B>Changes made after Release of SP Stack 3:</B><br />05.10.2004: Sorting of components due to download page; related notes added<br />12.10.2004: Restriction for several components removed; related notes added</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D032735)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D032735)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000770453/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364"}, {"RefNumber": "779962", "RefComponent": "FIN-SEM-BCS-INT-UP", "RefTitle": "Program determined with \"Program error(s)\"", "RefUrl": "/notes/779962"}, {"RefNumber": "779709", "RefComponent": "FIN-SEM-BCS-INT-LD", "RefTitle": "Mapping customizing: \"conversion exit\" flag cannot be saved", "RefUrl": "/notes/779709"}, {"RefNumber": "779635", "RefComponent": "FIN-FSCM-BD-AR", "RefTitle": "FSCM-BD: Creation of credit memos w/ partial payments", "RefUrl": "/notes/779635"}, {"RefNumber": "779406", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/779406"}, {"RefNumber": "779284", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Incorrect deductions in PAYRQ in external payment", "RefUrl": "/notes/779284"}, {"RefNumber": "778920", "RefComponent": "PA-ER", "RefTitle": "Category RCFBRA does not exist", "RefUrl": "/notes/778920"}, {"RefNumber": "778822", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "JCo resets Table rows to initial values", "RefUrl": "/notes/778822"}, {"RefNumber": "778135", "RefComponent": "CRM-ISA-CAT", "RefTitle": "Trexr3 admin UI: displays \"processing\" when should not.", "RefUrl": "/notes/778135"}, {"RefNumber": "778133", "RefComponent": "CRM-ISA-CAT", "RefTitle": "Trex Admin UI, catalog table headers are in german", "RefUrl": "/notes/778133"}, {"RefNumber": "777654", "RefComponent": "FIN-FB-MDF", "RefTitle": "Conversion exit for InfoObjects", "RefUrl": "/notes/777654"}, {"RefNumber": "777280", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Account statement does not create any IHC payment order", "RefUrl": "/notes/777280"}, {"RefNumber": "777166", "RefComponent": "CRM-ISA-R3", "RefTitle": "ISA-R3: B2C with TREX - wrong parameter in XCM", "RefUrl": "/notes/777166"}, {"RefNumber": "776424", "RefComponent": "FS-CML-PO-WA", "RefTitle": "STM: Incorrect values in F4 Help for partial rescission", "RefUrl": "/notes/776424"}, {"RefNumber": "774055", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ERP2004: ProfitCenter variances incorrect", "RefUrl": "/notes/774055"}, {"RefNumber": "774046", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: inbound IDoc - various corrections", "RefUrl": "/notes/774046"}, {"RefNumber": "772489", "RefComponent": "FS-CML", "RefTitle": "STM: Allowed entry of non-existing business partners", "RefUrl": "/notes/772489"}, {"RefNumber": "769958", "RefComponent": "RE-FX-CN", "RefTitle": "Cash flow for rental object and proposal conditions", "RefUrl": "/notes/769958"}, {"RefNumber": "769391", "RefComponent": "PE-LSO-TM", "RefTitle": "Dynamic menus: System error when expanding trees", "RefUrl": "/notes/769391"}, {"RefNumber": "768181", "RefComponent": "FIN-SEM-BCS-MD-IO", "RefTitle": "Program termination when reading the item Customizing", "RefUrl": "/notes/768181"}, {"RefNumber": "764804", "RefComponent": "PA-ER", "RefTitle": "Rejection during the application entry", "RefUrl": "/notes/764804"}, {"RefNumber": "760874", "RefComponent": "XX-SER-REL", "RefTitle": "ERP 2004: Support Package Stacks Release and Info Notes", "RefUrl": "/notes/760874"}, {"RefNumber": "751739", "RefComponent": "CRM-BF-CFG", "RefTitle": "Simultaneous multiple request causes blank page in IPC JSPUI", "RefUrl": "/notes/751739"}, {"RefNumber": "737709", "RefComponent": "CRM-ISA-BCS", "RefTitle": "Double Entry of Email & Password combination in B2C", "RefUrl": "/notes/737709"}, {"RefNumber": "717771", "RefComponent": "PE-PR", "RefTitle": "MESSAGE_TYPE_X in planning menu (PSVP)", "RefUrl": "/notes/717771"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "760874", "RefComponent": "XX-SER-REL", "RefTitle": "ERP 2004: Support Package Stacks Release and Info Notes", "RefUrl": "/notes/760874 "}, {"RefNumber": "717771", "RefComponent": "PE-PR", "RefTitle": "MESSAGE_TYPE_X in planning menu (PSVP)", "RefUrl": "/notes/717771 "}, {"RefNumber": "737709", "RefComponent": "CRM-ISA-BCS", "RefTitle": "Double Entry of Email & Password combination in B2C", "RefUrl": "/notes/737709 "}, {"RefNumber": "971364", "RefComponent": "XX-INT-DOCU-FIN", "RefTitle": "SAP ERP 2004 VERSION INFORMATION", "RefUrl": "/notes/971364 "}, {"RefNumber": "768181", "RefComponent": "FIN-SEM-BCS-MD-IO", "RefTitle": "Program termination when reading the item Customizing", "RefUrl": "/notes/768181 "}, {"RefNumber": "779962", "RefComponent": "FIN-SEM-BCS-INT-UP", "RefTitle": "Program determined with \"Program error(s)\"", "RefUrl": "/notes/779962 "}, {"RefNumber": "776424", "RefComponent": "FS-CML-PO-WA", "RefTitle": "STM: Incorrect values in F4 Help for partial rescission", "RefUrl": "/notes/776424 "}, {"RefNumber": "769391", "RefComponent": "PE-LSO-TM", "RefTitle": "Dynamic menus: System error when expanding trees", "RefUrl": "/notes/769391 "}, {"RefNumber": "779284", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Incorrect deductions in PAYRQ in external payment", "RefUrl": "/notes/779284 "}, {"RefNumber": "777280", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: Account statement does not create any IHC payment order", "RefUrl": "/notes/777280 "}, {"RefNumber": "772489", "RefComponent": "FS-CML", "RefTitle": "STM: Allowed entry of non-existing business partners", "RefUrl": "/notes/772489 "}, {"RefNumber": "778920", "RefComponent": "PA-ER", "RefTitle": "Category RCFBRA does not exist", "RefUrl": "/notes/778920 "}, {"RefNumber": "779635", "RefComponent": "FIN-FSCM-BD-AR", "RefTitle": "FSCM-BD: Creation of credit memos w/ partial payments", "RefUrl": "/notes/779635 "}, {"RefNumber": "779709", "RefComponent": "FIN-SEM-BCS-INT-LD", "RefTitle": "Mapping customizing: \"conversion exit\" flag cannot be saved", "RefUrl": "/notes/779709 "}, {"RefNumber": "778822", "RefComponent": "BC-MID-CON-JCO", "RefTitle": "JCo resets Table rows to initial values", "RefUrl": "/notes/778822 "}, {"RefNumber": "774046", "RefComponent": "FIN-FSCM-IHC", "RefTitle": "IHC: inbound IDoc - various corrections", "RefUrl": "/notes/774046 "}, {"RefNumber": "777166", "RefComponent": "CRM-ISA-R3", "RefTitle": "ISA-R3: B2C with TREX - wrong parameter in XCM", "RefUrl": "/notes/777166 "}, {"RefNumber": "774055", "RefComponent": "EP-PCT-MGR-CO", "RefTitle": "ERP2004: ProfitCenter variances incorrect", "RefUrl": "/notes/774055 "}, {"RefNumber": "778135", "RefComponent": "CRM-ISA-CAT", "RefTitle": "Trexr3 admin UI: displays \"processing\" when should not.", "RefUrl": "/notes/778135 "}, {"RefNumber": "778133", "RefComponent": "CRM-ISA-CAT", "RefTitle": "Trex Admin UI, catalog table headers are in german", "RefUrl": "/notes/778133 "}, {"RefNumber": "777654", "RefComponent": "FIN-FB-MDF", "RefTitle": "Conversion exit for InfoObjects", "RefUrl": "/notes/777654 "}, {"RefNumber": "769958", "RefComponent": "RE-FX-CN", "RefTitle": "Cash flow for rental object and proposal conditions", "RefUrl": "/notes/769958 "}, {"RefNumber": "764804", "RefComponent": "PA-ER", "RefTitle": "Rejection during the application entry", "RefUrl": "/notes/764804 "}, {"RefNumber": "751739", "RefComponent": "CRM-BF-CFG", "RefTitle": "Simultaneous multiple request causes blank page in IPC JSPUI", "RefUrl": "/notes/751739 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}