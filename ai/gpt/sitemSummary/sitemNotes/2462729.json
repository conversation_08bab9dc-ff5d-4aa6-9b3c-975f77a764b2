{"Request": {"Number": "2462729", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 506, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018767612017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002462729?language=E&token=D58370F26B330D5C6F715785FFFC0E89"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002462729", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002462729/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2462729"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-RA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Revenue Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Revenue Accounting", "value": "FI-RA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-RA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2462729 - Inflight check: enable E4/E5/E15/E18/E19 and E2 enhancement for operational load and migration"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>In RAR, Revenue Accounting&#160;contracts&#160;are generated and updated from operational documents. At generation and updates, customer implementations are called.</p>\r\n<p>As operational documents can be sent via SAP system or Non-SAP system and customer implementation can change the data, error in customer implementation and inconsistent data from operational documents can result in data inconsistency in RAR and this will have revenue impact. If the inconsistent data is generated in the database, it is very costly to correct those inconsistent data, therefore it is necessary for RAR to prevent any inconsistencies by checking the data before it is committed to DB.</p>\r\n<p>If the inconsistent data is detected by the checks, the corresponding processing will fail of this specific contract.</p>\r\n<ul>\r\n<li>If the process is triggered from RAI, the RAI processing is stopped and RAI item will have status error. Revenue contract is not created or updated during processing of the RAI item. &#160;In case of mass processing,&#160; other contracts without inconsistency will still be processed and the consistent data will be saved to database.</li>\r\n<li>If the process is triggered from manual editing the revenue contract, end user will get error message and cannot save the revenue contract.</li>\r\n</ul>\r\n<p>You have already implemented the following notes and you want to have further inflight checks:</p>\r\n<ul>\r\n<li>Note 2456711</li>\r\n<li>Note 2460096</li>\r\n<li>Note 2466117</li>\r\n</ul>\r\n<p>After this note is implemented, the following checks are&#160;avaiable (Notice each check is given a category):</p>\r\n<ul>\r\n<li>E01: The system checks that the total&#160;amount of allocation effects for performance obligations&#160;in a revenue accounting contract&#160;is zero. The total allocated&#160;price of a contract from performance obligation shall be the same as total transaction price.</li>\r\n<li>E02:&#160;The system checks that the&#160;allocated amount&#160;of a performance obligation&#160;is equal to the&#160;total amount&#160;of each periods from&#160;revenue schedule for this performance obligation in&#160;deferral item table.</li>\r\n<li>E03:&#160;For event-based POB, if POB is fully fulfilled, the effective quantity of the POB must be equal to the total quantity of all fulfillments for the same performance obligation. For time-based performance obligations, the effective quantity of the POB must be equal to the total quantity of all fulfillments.</li>\r\n<li>E04: Time-based quantity shall have the same scheduled quantity from deferral item and fulfilled quantity.</li>\r\n<li>E05:&#160;The latest deferral&#160; item flag is set correctly.</li>\r\n<li>E06: The total&#160;amount of posted revenues&#160;is equal to the scheduled number in the deferral items</li>\r\n<li>E07:&#160;The total&#160;amount of posted invoice corrections&#160; is equal to the invoiced&#160;amount in the deferral items.</li>\r\n<li>E08: The special indicator is populated correctly. It means&#160; for every main price condition type from the sender component, the special indicator field must be populated with&#160;value 'P'. For each reconciliation key and performance obligation there must be one condition indicated as main price condition</li>\r\n<li>E09: The system checks that the allocation&#160;effect&#160;of a performance obligation is equal to the&#160;allocated amount minus the transaction price of this performance obligation.</li>\r\n<li>E13: Transferred invoices amount in FARR_D_INVOICE table&#160;must equal with posted invoice amount in FARR_D_POSTING</li>\r\n<li>E15: POB has no deferral item</li>\r\n<li>E17: Different signs between transaction and local currency shall not happen</li>\r\n<li>E18: Check PAOBJNR is correct according to CO-PA settings.</li>\r\n<li>E19: Deferral item shall be created/updated/deleted correctly with correct recon key.</li>\r\n</ul>\r\n<p>This note adds the following inflight check: E04/E05/E15/E18/E19.</p>\r\n<p>And also inflight check E2 is enhanced so that during migration the inflight check E2 can be correctly checked. Before this note is implemented,&#160;you may encounter E02 error saying \"E02: Allocated amount &lt;&gt; Rev schedule, Contract xxx/POB yyy -&gt; Contact SAP\", this is due to a bug in the coding.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FARR, Revenue Accounting, Inflight check, Data Validation, E01, E02, E03, E04,E06,E07,E08,E09,E13,E15,E17,E18,E19</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Inconsistent data shall be prevented to be committed to database</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Apply this note.</p>\r\n<p>After the note is applied, if you encounter an error message with the above message, please raise a customer incident to SAP.</p>\r\n<p>When you raise a customer incident,&#160;please also mention the steps to reproduce the error.</p>\r\n<p>The message&#160;class for the inflight check is FARR_INFLIGHT_CHECK. You can use the filter in transaction code SLG1 to filter out by message class \"FARR_INFLIGHT_CHECK\".</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I042117)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I037746)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002462729/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002462729/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2533254", "RefComponent": "FI-RA", "RefTitle": "SAP Revenue Accounting and Reporting: Inflight Checks", "RefUrl": "/notes/2533254 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "REVREC", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "REVREC", "From": "120", "To": "120", "Subsequent": ""}, {"SoftwareComponent": "REVREC", "From": "130", "To": "130", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "REVREC 110", "SupportPackage": "SAPK-11008INREVREC", "URL": "/supportpackage/SAPK-11008INREVREC"}, {"SoftwareComponentVersion": "REVREC 120", "SupportPackage": "SAPK-12004INREVREC", "URL": "/supportpackage/SAPK-12004INREVREC"}, {"SoftwareComponentVersion": "REVREC 130", "SupportPackage": "SAPK-13002INREVREC", "URL": "/supportpackage/SAPK-13002INREVREC"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "REVREC", "NumberOfCorrin": 3, "URL": "/corrins/0002462729/14019"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; REVREC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 110&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-11007INREVREC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 120&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-12003INREVREC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>| Release 130&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Until SAPK-13001INREVREC&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/><br/>Please execute report FARR_NOTE_2462729.<br/><br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "REVREC", "ValidFrom": "110", "ValidTo": "110", "Number": "2462729 ", "URL": "/notes/2462729 ", "Title": "Inflight check: enable E4/E5/E15/E18/E19 and E2 enhancement for operational load and migration", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "110", "ValidTo": "110", "Number": "2466117 ", "URL": "/notes/2466117 ", "Title": "Inflight Check Enhancement for E02 / E03 / E07 / E09", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2462729 ", "URL": "/notes/2462729 ", "Title": "Inflight check: enable E4/E5/E15/E18/E19 and E2 enhancement for operational load and migration", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "120", "ValidTo": "120", "Number": "2466117 ", "URL": "/notes/2466117 ", "Title": "Inflight Check Enhancement for E02 / E03 / E07 / E09", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2462729 ", "URL": "/notes/2462729 ", "Title": "Inflight check: enable E4/E5/E15/E18/E19 and E2 enhancement for operational load and migration", "Component": "FI-RA"}, {"SoftwareComponent": "REVREC", "ValidFrom": "130", "ValidTo": "130", "Number": "2466117 ", "URL": "/notes/2466117 ", "Title": "Inflight Check Enhancement for E02 / E03 / E07 / E09", "Component": "FI-RA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}