{"Request": {"Number": "2016689", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 567, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002016689?language=E&token=51FE98A5B006C80722257CE581AAF41E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002016689", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002016689/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2016689"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2016689 - DO NOT DELIVER- IS REPLACED BY 1921675 ---  SAP HANA SPS 07 Database Maintenance Revision - base note"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This&#160;is the SAP HANA SPS 07 database maintenance revisions base release note.</p>\r\n<p>From this base release note you can forward to the different maintenance release notes (see below).<br /><br />The maintenance revisions&#160;are based on the last revision of SAP HANA SPS (Support Package Stack) 07, revision 74.00 (<a target=\"_blank\" href=\"/notes/2003736\">2003736)</a>.</p>\r\n<p><strong>What are SAP HANA Maintenance Revisions?</strong></p>\r\n<p>SAP has decided to introduce an additional SAP HANA maintenance revision stream with temporary availability and a clear focus on stabilization, to address the needs of a growing number of our customers who are running their mission-critical workloads on top of SAP HANA. The SAP HANA maintenance revisions focus on productive, business-critical SAP HANA scenarios.&#160; <br /><br />The existing SAP HANA&#160;revisions strategy stays untouched and will be continued.</p>\r\n<p>A maintenance revision contains only major bug fixes found in key SAP HANA scenarios. <br />Provisioning of maintenance revisions is temporary; it ends approximately 3 months after the release of the successor Support Package Stack (SPS) and is intended to allow better planning of maintenance windows for production environments running on SAP HANA.<br /><br />From this date, customer must adopt the regular SP (Support Package) revisions to receive further fixes.</p>\r\n<p>Please refer to the <a target=\"_blank\" href=\"https://service.sap.com/%7Esapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;document on SAP Service Marketplace for an overview regarding the SAP HANA revision and maintenance strategy.<br />&#160;<br />For details about the SAP HANA revision and maintenance strategy and recommended upgrade paths between SAP HANA Revisions, Support Package Stacks, Maintenance Revisions and Datacenter Service points, see \"SAP Note&#160; 2021789:&#160;<strong>SAP HANA Revision and Maintenance Strategy</strong>\".</p>\r\n<p><strong>Please note the following:</strong></p>\r\n<ul>\r\n<li>Please check \"SAP Note <a target=\"_blank\" href=\"/notes/1948334\">1948334</a> : SAP HANA Database Update Paths for Maintenance Revisions\" for the possible update paths from maintenance revisions to SP revisions.<strong><br /><br /></strong></li>\r\n<li>Customers can directly upgrade from a lower revision (e.g. Revision 58, .., 73, 74) to the latest maintenance revision on SAP Service Marketplace.<br /><br /></li>\r\n<li>Updating from one maintenance revision to another maintenance revision&#160;is done according to the same procedure&#160;as implementing any other revision update. <br />The customer performs a regular update from lower maintenance revisions (e.g. 69.07) to higher&#160;maintenance revisions.<br /><strong>&#160;</strong></li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NewDB, in-memory database, Hybrid database, update, installation, SPS 07</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>Running the installation and the update is only supported on a validated SAP HANA appliance and SAP HANA Tailored Datacenter Integration setup.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>If you upgrade from a lower SAP HANA SPS, it is recommended to update ALL other components (Studio, Modeler, DB Clients, DBSL, SLT, DS, ...) to at least the minimal version of SAP HANA SPS 07.</strong></p>\r\n<p><strong>Documentation</strong></p>\r\n<ul>\r\n<li>See SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/hana_platform\">http://help.sap.com/hana_platform</a> for the complete SAP HANA product documentation set.</li>\r\n</ul>\r\n<p><strong>Installation and Update</strong></p>\r\n<ul>\r\n<li>For the installation of the SAP HANA client on SUSE Linux Enterprise Server 9 and 10, use the package \"Linux SUSE 9 on x86_64 64bit\".<br /><br /></li>\r\n</ul>\r\n<p>In the following notes we describe the prereqisites and the fixes we deliver with the different maintenance revisions (newest revision first).</p>\r\n<ul>\r\n<li><strong><strong>&#160;<a target=\"_blank\" href=\"/notes/2016740\">2016740</a> -&#160; Maintenance Revision 74.01 (June 06, 2014)<br /><br /><br /></strong></strong></li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D019628"}, {"Key": "Processor                                                                                           ", "Value": "D019628"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002016689/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "201840", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain(299):SAPLRRK0:CHECK_KHANDLE-GETWA_NOT_ASSIGNED", "RefUrl": "/notes/201840"}, {"RefNumber": "2016740", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS07 Revision 074.01", "RefUrl": "/notes/2016740"}, {"RefNumber": "2003736", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS07 Revision 074.00", "RefUrl": "/notes/2003736"}, {"RefNumber": "1988396", "RefComponent": "HAN-DB", "RefTitle": "Defective persistence after key change for volume encryption", "RefUrl": "/notes/1988396"}, {"RefNumber": "1964644", "RefComponent": "HAN-DB-BAC", "RefTitle": "HANA data backup via DB13 fails with SQLCode -10108", "RefUrl": "/notes/1964644"}, {"RefNumber": "1963791", "RefComponent": "HAN-DB", "RefTitle": "Avoid rowstore data loss or recover to consistent state", "RefUrl": "/notes/1963791"}, {"RefNumber": "1963779", "RefComponent": "HAN-DB", "RefTitle": "Reaching the 768 GB limit of rowstore can cause data loss", "RefUrl": "/notes/1963779"}, {"RefNumber": "1950647", "RefComponent": "HAN-AS-XS", "RefTitle": "XSEngine does not send any response", "RefUrl": "/notes/1950647"}, {"RefNumber": "1948334", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Update Paths for SAP HANA Maintenance Revisions", "RefUrl": "/notes/1948334"}, {"RefNumber": "1931536", "RefComponent": "HAN-DB", "RefTitle": "HANA: Possible DB corruption with NFSv3 based scale out", "RefUrl": "/notes/1931536"}, {"RefNumber": "1927154", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Possible incomplete data when one node restarts", "RefUrl": "/notes/1927154"}, {"RefNumber": "1927092", "RefComponent": "HAN-DB", "RefTitle": "DO NOT DELIVER - SAP HANA DB:Wrong result updating with CURRENT_UTC_TIMESTMAP", "RefUrl": "/notes/1927092"}, {"RefNumber": "1927090", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Wrong value for count func. in analytic views", "RefUrl": "/notes/1927090"}, {"RefNumber": "1921675", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Platform 1.0 SPS 07 Release Note", "RefUrl": "/notes/1921675"}, {"RefNumber": "1919034", "RefComponent": "HAN-DB", "RefTitle": "Related to 1918267: How to run procedure MERGE_ALL_TABLES", "RefUrl": "/notes/1919034"}, {"RefNumber": "1919033", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1919033"}, {"RefNumber": "1918267", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1918267"}, {"RefNumber": "1912332", "RefComponent": "HAN-DB-CLI", "RefTitle": "SQL array DML operations fail with error code \"-10709\"", "RefUrl": "/notes/1912332"}, {"RefNumber": "1911404", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 60 Enhancements for Planning Functions 2", "RefUrl": "/notes/1911404"}, {"RefNumber": "1900919", "RefComponent": "HAN-AS-XS", "RefTitle": "HANA XS Web Disp not running after Upgrade to rev. >= 60", "RefUrl": "/notes/1900919"}, {"RefNumber": "1898526", "RefComponent": "HAN-DB-AFL", "RefTitle": "SAP HANA AFL: Revision 61 der SAP-HANA-Datenbank", "RefUrl": "/notes/1898526"}, {"RefNumber": "1893911", "RefComponent": "HAN-DB", "RefTitle": "\"NOT BETWEEN\" queries triggered from DBSL", "RefUrl": "/notes/1893911"}, {"RefNumber": "1891354", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Duplicate entries in column store table", "RefUrl": "/notes/1891354"}, {"RefNumber": "1880721", "RefComponent": "HAN-LM-UPG", "RefTitle": "Upgrade fails for a few Custom Storage Connectors", "RefUrl": "/notes/1880721"}, {"RefNumber": "1880503", "RefComponent": "HAN-DB-BAC", "RefTitle": "SAP HANA studio compatibility error for recovery", "RefUrl": "/notes/1880503"}, {"RefNumber": "1877480", "RefComponent": "HAN-DB", "RefTitle": "Incorrect CODE_SIZE", "RefUrl": "/notes/1877480"}, {"RefNumber": "1876879", "RefComponent": "HAN-STD-DEV-MOD-SRV", "RefTitle": "Calculated Attribute of Calculaton View missing (rev 60)", "RefUrl": "/notes/1876879"}, {"RefNumber": "1871386", "RefComponent": "HAN-LM-INS", "RefTitle": "SAP HANA: Paged Attributes", "RefUrl": "/notes/1871386"}, {"RefNumber": "1866126", "RefComponent": "HAN-DB", "RefTitle": "Unknown file system \"nfs4\"", "RefUrl": "/notes/1866126"}, {"RefNumber": "1861395", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 60 Enhancements for Planning Functions", "RefUrl": "/notes/1861395"}, {"RefNumber": "1852300", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: Filters on SQL CalcViews are not being pushed down", "RefUrl": "/notes/1852300"}, {"RefNumber": "1850561", "RefComponent": "PP-SFC-IS", "RefTitle": "AFL Authorization for MfgOrder-Views", "RefUrl": "/notes/1850561"}, {"RefNumber": "1848976", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1848976"}, {"RefNumber": "1843067", "RefComponent": "HAN-DB", "RefTitle": "No/incomplete data BW query empty result/filter", "RefUrl": "/notes/1843067"}, {"RefNumber": "1837760", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "AttributeEngine: operation is not implemented calcEngine", "RefUrl": "/notes/1837760"}, {"RefNumber": "1837029", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "\"error getting chunk for index getNext\" DTP fails aft.20 min", "RefUrl": "/notes/1837029"}, {"RefNumber": "1826139", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "\"Effective Process Allocation Limit\" incorrect", "RefUrl": "/notes/1826139"}, {"RefNumber": "1825771", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: data structure corrupt in distributed landscape", "RefUrl": "/notes/1825771"}, {"RefNumber": "1821785", "RefComponent": "HAN-DB", "RefTitle": "DSO conversion is hanging due low number of latematcol pages", "RefUrl": "/notes/1821785"}, {"RefNumber": "1820266", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 50 Enhancements for Planning Functions", "RefUrl": "/notes/1820266"}, {"RefNumber": "1818430", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help: Error during plan execution;Failed to find sourc", "RefUrl": "/notes/1818430"}, {"RefNumber": "1811804", "RefComponent": "HAN-DB", "RefTitle": "Deadlock caused by Queries on Olap Views", "RefUrl": "/notes/1811804"}, {"RefNumber": "1802461", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 46 Enhancements for Planning Functions", "RefUrl": "/notes/1802461"}, {"RefNumber": "1800806", "RefComponent": "HAN-DB-MDX", "RefTitle": "Indexserver-crash bei MDX über ODBO und Multithreading", "RefUrl": "/notes/1800806"}, {"RefNumber": "1789828", "RefComponent": "HAN-DB", "RefTitle": "HANA Studio Activation errors", "RefUrl": "/notes/1789828"}, {"RefNumber": "1785171", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Revision 40 using Client Statement Routing", "RefUrl": "/notes/1785171"}, {"RefNumber": "1774187", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: GNav queries do not work in distr. environment", "RefUrl": "/notes/1774187"}, {"RefNumber": "1774082", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: [2999] exception 2999: unable create itabindex", "RefUrl": "/notes/1774082"}, {"RefNumber": "1774074", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Duplicate entries in primary key", "RefUrl": "/notes/1774074"}, {"RefNumber": "1771591", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1771591"}, {"RefNumber": "1769188", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 38 Enhancements for Planning Functions 2", "RefUrl": "/notes/1769188"}, {"RefNumber": "1766006", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 38 Enhancements for Planning Functions", "RefUrl": "/notes/1766006"}, {"RefNumber": "1764452", "RefComponent": "HAN-STD-DEV-MOD", "RefTitle": "SAP HANA Studio-Revision 37-data preview errof for rest meas", "RefUrl": "/notes/1764452"}, {"RefNumber": "1764251", "RefComponent": "HAN-STD-DEV-MOD", "RefTitle": "Documentation- Importing BW Models in SAP HANA Modeler", "RefUrl": "/notes/1764251"}, {"RefNumber": "1753944", "RefComponent": "HAN-DB", "RefTitle": "Revalidation of procedures/views using R script", "RefUrl": "/notes/1753944"}, {"RefNumber": "1749467", "RefComponent": "HAN-DB", "RefTitle": "Copying SAP HANA From a Multiple- to a Single-Host System", "RefUrl": "/notes/1749467"}, {"RefNumber": "1744687", "RefComponent": "HAN-DB-ENG", "RefTitle": "Wrong length for calculated attribute of type decimal", "RefUrl": "/notes/1744687"}, {"RefNumber": "1739779", "RefComponent": "HAN-DB", "RefTitle": "Activation of procedures might fail", "RefUrl": "/notes/1739779"}, {"RefNumber": "1735130", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 33 Enhancements for Planning Functions", "RefUrl": "/notes/1735130"}, {"RefNumber": "1731572", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Check for wrong values in E-Part of fact table", "RefUrl": "/notes/1731572"}, {"RefNumber": "1731569", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "SAP HANA DB: Check for correct InfoCube migration", "RefUrl": "/notes/1731569"}, {"RefNumber": "1725574", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Data inconsistencies in BW on HANA", "RefUrl": "/notes/1725574"}, {"RefNumber": "1724330", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 30 Enhancements for Planning Functions", "RefUrl": "/notes/1724330"}, {"RefNumber": "1718854", "RefComponent": "HAN-STD-DEV-MOD", "RefTitle": "SAP HANA Modeler: Error when opening Analytic privilege", "RefUrl": "/notes/1718854"}, {"RefNumber": "1710832", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA BW: I_RESULT_INDEX_NAME with TREX_EXT_AGGREGATE", "RefUrl": "/notes/1710832"}, {"RefNumber": "1710392", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB (Rev27): PlanViz not showing correct CPU times", "RefUrl": "/notes/1710392"}, {"RefNumber": "1703675", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1703675"}, {"RefNumber": "1701909", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB (Rev26): Data compression setting before upgrade", "RefUrl": "/notes/1701909"}, {"RefNumber": "1698032", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB (Rev26): Reactivate models after upgrade", "RefUrl": "/notes/1698032"}, {"RefNumber": "1695758", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA for BW: Deserialization error in TREX_EXT_AGGREGATE", "RefUrl": "/notes/1695758"}, {"RefNumber": "1694574", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 26 Enhancements for Planning Functions", "RefUrl": "/notes/1694574"}, {"RefNumber": "1666976", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666976"}, {"RefNumber": "1654455", "RefComponent": "HAN-STD-DEV-MOD", "RefTitle": "Upgrade News and Tips Hana Modeler from SP3 (Revision 20) on", "RefUrl": "/notes/1654455"}, {"RefNumber": "1652078", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA database: Hardware check", "RefUrl": "/notes/1652078"}, {"RefNumber": "1598623", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1598623"}, {"RefNumber": "1523337", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database 1.00 - Central Note", "RefUrl": "/notes/1523337"}, {"RefNumber": "1514967", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: Central Note", "RefUrl": "/notes/1514967"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2021789", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2021789 "}, {"RefNumber": "2016740", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1 SPS07 Revision 074.01", "RefUrl": "/notes/2016740 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "HDB", "From": "1.00", "To": "1.00", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}