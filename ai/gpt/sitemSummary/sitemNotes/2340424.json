{"Request": {"Number": "2340424", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 301, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018362962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002340424?language=E&token=F8BF8CD9EF5FC87FD28D1E8AFD8BD3C8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002340424", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2340424"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.07.2016"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-SP-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Generic Applications"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use the sub-components CA-GTF-SP-FIN*", "value": "CA-GTF-SP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-SP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Generic Applications", "value": "CA-GTF-SP-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-SP-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2340424 - Availability of Side Panels in S/4HANA (on-premise)"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA (on-premise). You use Side Panels in the SAP Business Client and would like to understand the conversion behavior.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CHIPs, Side Panel for Business Suite, S/4HANA (on-premise), Conversion</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP S/4HANA (on-premise) comes with a new UI product. The S/4HANA (on-premise) UI product is not the successor of any Business Suite product and delivers a set of applications which is from a technical and scope perspective independent from any previous Business Suite UI product. The SAP Business Client Side Panel functionality is available in S/4HANA (on-premise). Some of the&#160;Business Suite Side Panel CHIPs&#160; (Collaborative Human Interface Part) are no longer available in S/4HANA&#160;and&#160;the delivered CHIP catalog is adjusted accordingly.</p>\r\n<p>A CHIP (Collaborative Human Interface Part) is an encapsulated piece of software used to provide functions in collaboration with other CHIPs in a Web Dynpro ABAP Page Builder page or side panel. All available CHIPs are registered in a library (CHIP catalog). From a technical point of view, CHIPs are Web Dynpro ABAP components that implement a specific Web Dynpro component interface.</p>\r\n<p>More information is available at:&#160;&#160;<a target=\"_blank\" href=\"http://help.sap.com/erp2005_ehp_07/helpdata/en/f2/f8478f40ca420991a72eed8f222c8d/content.htm?current_toc=/en/58/327666e82b47fd83db69eddce954bd/plain.htm\">http://help.sap.com/erp2005_ehp_07/helpdata/en/f2/f8478f40ca420991a72eed8f222c8d/content.htm?current_toc=/en/58/327666e82b47fd83db69eddce954bd/plain.htm</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The new program paradigm in S/4HANA and the consumption of functionality via SAP Fiori results in an adjustment of the delivered CHIP catalog for the SAP Business Client Side Panel functionality.</p>\r\n<p style=\"font-size: 12pt; font-family: 'Times New Roman'; color: black; margin: 0in;\">The following SAP Business Client Side Panel CHIPs are no longer available:</p>\r\n<div>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p><strong>CHIP Name</strong></p>\r\n</td>\r\n<td>\r\n<p><strong>CHIP</strong></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_FI_AP</p>\r\n</td>\r\n<td>\r\n<p>Accounts Payable Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_FI_AR</p>\r\n</td>\r\n<td>\r\n<p>Accounts Receivable Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_FI_AA</p>\r\n</td>\r\n<td>\r\n<p>Asset Accounting Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_CO_OM_CCA</p>\r\n</td>\r\n<td>\r\n<p>Cost Center Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_FI_GL</p>\r\n</td>\r\n<td>\r\n<p>General Ledger Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_CO_OM_OPA</p>\r\n</td>\r\n<td>\r\n<p>Internal Order Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_PM_OM_OPA</p>\r\n</td>\r\n<td>\r\n<p>Maintenance / Service Order Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_CO_PC</p>\r\n</td>\r\n<td>\r\n<p>Product Cost Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_LPD_FI_GL_PRCTR</p>\r\n</td>\r\n<td>\r\n<p>Profit Center Reporting (Links)</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AA_POSTED_DEPREC</p>\r\n</td>\r\n<td>\r\n<p>Asset Accounting: Posted Depreciation (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_CC_ACT_PLAN_AGGR</p>\r\n</td>\r\n<td>\r\n<p>Cost Center: Cumulative Actual/Planned Costs (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_TOTALS</p>\r\n</td>\r\n<td>\r\n<p>Cost Center Balance: Totals (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_ANALYSIS</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Analysis (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_FORECAST</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Forecast (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_OVERDUE</p>\r\n</td>\r\n<td>\r\n<p>Customer Overdue Analysis (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_IO_ACT_PLAN_AGGR</p>\r\n</td>\r\n<td>\r\n<p>Internal Order: Cumulative Actual/Planned Costs (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_PC_MAT_ACT_PLAN_AGGR</p>\r\n</td>\r\n<td>\r\n<p>Material: Aggregated Actual / Planned Costs (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_GR_IR_GOODS_RECEIPT</p>\r\n</td>\r\n<td>\r\n<p>Purchase Order History: Goods Receipt (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_GR_IR_INVOICE_RECEIPT</p>\r\n</td>\r\n<td>\r\n<p>Purchase Order History: Invoice Receipt(Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_GR_IR_OPEN_ITEM</p>\r\n</td>\r\n<td>\r\n<p>Purchase Order: Open Items (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_TOTALS</p>\r\n</td>\r\n<td>\r\n<p>Vendor Balance Totals (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_DUE_ANALYSIS</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Analysis (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_DUE_FORECAST</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Forecast (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_OVERDUE</p>\r\n</td>\r\n<td>\r\n<p>Vendor Overdue Analysis (Reports (Display as Form / List))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AA_POSTED_DEPREC_C</p>\r\n</td>\r\n<td>\r\n<p>Asset Accounting: Posted Depreciation (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_CC_PERIOD_BD_C</p>\r\n</td>\r\n<td>\r\n<p>Cost Center: Breakdown Actual/Planned Costs by Period (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_CC_ACT_PLAN_AGGR_C</p>\r\n</td>\r\n<td>\r\n<p>Cost Center: Cumulative Actual/Planned Costs (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_PERIOD_C</p>\r\n</td>\r\n<td>\r\n<p>Customer Balance: Period Drilldown (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_TOTALS_C</p>\r\n</td>\r\n<td>\r\n<p>Customer Balance: Totals (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_ANALYSIS_C</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Analysis (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_FORECAST_C</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Forecast (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_OVERDUE_C</p>\r\n</td>\r\n<td>\r\n<p>Customer Overdue Analysis (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_GL_ACC_BALANCE_C</p>\r\n</td>\r\n<td>\r\n<p>G/L Account Balance (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_IO_PERIOD_BD_C</p>\r\n</td>\r\n<td>\r\n<p>&#160;Internal Order: Actual/Planned Costs by Period (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_IO_ACT_PLAN_AGGR_C</p>\r\n</td>\r\n<td>\r\n<p>Internal Order: Cumulative Actual/Planned Costs (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_PC_MAT_ACT_PLAN_AGGR_C</p>\r\n</td>\r\n<td>\r\n<p>Material: Aggregated Actual / Planned Costs (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_PC_MAT_PERIOD_BD_C</p>\r\n</td>\r\n<td>\r\n<p>Material: Breakdown Actual/Planned Costs by Period (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_PERIOD_C</p>\r\n</td>\r\n<td>\r\n<p>Vendor Balance: Period Drilldown (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;BSSP_FI_AP_TOTALS_C</p>\r\n</td>\r\n<td>\r\n<p>Vendor Balance Totals (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;BSSP_FI_AP_DUE_ANALYSIS_C</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Analysis (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_DUE_FORECAST_C</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Forecast (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_OVERDUE_C</p>\r\n</td>\r\n<td>\r\n<p>Vendor Overdue Analysis (Reports (Display as Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n<td>\r\n<p>&#160;</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AA_POSTED_DEPREC_C2</p>\r\n</td>\r\n<td>\r\n<p>Asset Accounting: Posted Depreciation (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_CC_PERIOD_BD_C2</p>\r\n</td>\r\n<td>\r\n<p>Cost Center: Breakdown Actual/Planned Costs by Period (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_CC_ACT_PLAN_AGGR_C2</p>\r\n</td>\r\n<td>\r\n<p>Cost Center: Cumulative Actual/Planned Costs (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_PERIOD_C2</p>\r\n</td>\r\n<td>\r\n<p>Customer Balance: Period Drilldown (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_TOTALS_C2</p>\r\n</td>\r\n<td>\r\n<p>Customer Balance: Totals (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_ANALYSIS_C2</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Analysis (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_DUE_FORECAST_C2</p>\r\n</td>\r\n<td>\r\n<p>Customer Due Date Forecast (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AR_OVERDUE_C2</p>\r\n</td>\r\n<td>\r\n<p>&#160;Customer Overdue Analysis (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_GL_ACC_BALANCE_C2</p>\r\n</td>\r\n<td>\r\n<p>G/L Account Balance (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_IO_PERIOD_BD_C2</p>\r\n</td>\r\n<td>\r\n<p>Internal Order: Actual/Planned Costs by Period (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_OM_IO_ACT_PLAN_AGGR_C2</p>\r\n</td>\r\n<td>\r\n<p>Internal Order: Cumulative Actual/Planned Costs (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_PC_MAT_ACT_PLANAGGR_C2</p>\r\n</td>\r\n<td>\r\n<p>Material: Aggregated Actual / Planned Costs (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_CO_PC_MAT_PERIOD_BD_C2</p>\r\n</td>\r\n<td>\r\n<p>Material: Breakdown Actual/Planned Costs by Period (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_PERIOD_C2</p>\r\n</td>\r\n<td>\r\n<p>Vendor Balance: Period Drilldown (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_TOTALS_C2</p>\r\n</td>\r\n<td>\r\n<p>Vendor Balance Totals (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_DUE_ANALYSIS_C2</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Analysis (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>BSSP_FI_AP_DUE_FORECAST_C2</p>\r\n</td>\r\n<td>\r\n<p>Vendor Due Date Forecast (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>&#160;BSSP_FI_AP_OVERDUE_C2</p>\r\n</td>\r\n<td>\r\n<p>Vendor Overdue Analysis (Reports (Display as HTML5 Chart))</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-GTF-SP-FIN (Content for ERP Financials)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D020891)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027132)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002340424/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002340424/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2302074", "RefComponent": "BC-FES-BUS-DSK", "RefTitle": "Maintenance strategy and deadlines for SAP Business Client / NWBC", "RefUrl": "/notes/2302074"}, {"RefNumber": "2288828", "RefComponent": "XX-SER-REL", "RefTitle": "S4TWL - Fiori Applications for SAP Business Suite powered by SAP HANA", "RefUrl": "/notes/2288828"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "UIS4HOP1", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}