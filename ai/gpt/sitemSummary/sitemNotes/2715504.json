{"Request": {"Number": "2715504", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 200, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002715504?language=E&token=E16AD9756569D364F24F6543AA9AE116"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002715504", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002715504/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2715504"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-FRN-INF-SDA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Simple Diagnostic Agent (SDA)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2715504 - Configure Early Watch Alert Job"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to automatically enable an SAP ABAP system to provide and send data for the \"Early Watch Alert\" (EWA) report to FRUN.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>On the host with the ABAP system an SDA with version &gt; 1.28 is installed. The related roles must be generated by the configuration task <em><a target=\"_blank\" href=\"https://me.sap.com/notes/3239965\">GenerateMonitoringUserRoles</a></em>&#160;task in advance.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p class=\"header4\">Task name</p>\r\n<div class=\"longtext\">\r\n<p>The&#160;name of the configuration task in the Preparation Tool is:&#160;<strong>'</strong>C<PERSON><PERSON>waJob<strong>'</strong></p>\r\n<p class=\"header4\">Task Configuration Steps</p>\r\n<p>In order to configure the EWA Job the preparation tool performs the following steps</p>\r\n</div>\r\n<ul>\r\n<li>Discover all instances of the ABAP system and detect a dialog instance to perform the JCo calls against.</li>\r\n<li>Update an existing destination&#160;\"frun.destination.name\"&#160;or create a new one \"frun.destination.name\"</li>\r\n<li>Set the configured password for the destination</li>\r\n<li>Create the EWA&#160;user defined at \"ewa.username\" with the password defined at \"ewa.password\" or use an existing user.</li>\r\n<ul>\r\n<li>Check whether the EWA user already exists and using this one instead of creating a new user.&#160;<strong>If an existing user has to be taken, ensure that this user has the role 'SAP_SDCCN_ALL' assigned. Otherwise the postprocessing of the configuration will fail. The configuration task will not add missing roles to an exsisting user for legal reasons.&#160;</strong></li>\r\n<li>Assign the role defined at&#160;ewa.user_role&#160;with the prefix defined at&#160;customer.namespace&#160;to the newly created user.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li class=\"p1\">create and schedule the EWA job</li>\r\n</ul>\r\n<p class=\"header4\"><span style=\"font-size: 14px;\">Task Configuration Parameters</span></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\" class=\"relative-table wrapped confluenceTable tablesorter tablesorter-default stickyTableHeaders\"><colgroup><col /><col /><col /><col /><col /></colgroup>\r\n<thead class=\"tableFloatingHeaderOriginal\">\r\n<tr style=\"background-color: #d6d6d6;\"><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerAsc\" scope=\"col\">\r\n<p class=\"tablesorter-header-inner\">Key</p>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" scope=\"col\">\r\n<p class=\"tablesorter-header-inner\">Description</p>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<p class=\"tablesorter-header-inner\"><strong>Default Value</strong></p>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<p class=\"tablesorter-header-inner\"><strong>Mandatory</strong></p>\r\n</th><th class=\"confluenceTh tablesorter-header sortableHeader tablesorter-headerUnSorted\" colspan=\"1\" scope=\"col\">\r\n<p class=\"tablesorter-header-inner\">Miscellaneous</p>\r\n</th></tr>\r\n</thead>\r\n<tbody>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">abap.client</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">ABAP client</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">abap.login.password</td>\r\n<td class=\"confluenceTd\">password related to \"abap.login.user\"</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">abap.login.username</td>\r\n<td class=\"confluenceTd\">log-in user for the ABAP system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\">abap.sid</td>\r\n<td class=\"confluenceTd\">SID of the ABAP system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">customer.namespace</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">customer name space to be used as a prefix of the monitoring user roles</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Z_</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">\r\n<p>ewa.password</p>\r\n</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Password to be set for <em>ewa.username</em></td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">\r\n<p>ewa.user_role</p>\r\n</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">\r\n<p class=\"p1\">EWA related role to run the EWA job</p>\r\n</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">SAP_SDCCN_ALL</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\"></td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">\r\n<p>ewa.username</p>\r\n</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">User to be created to run the EWA job</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">FRN_JOB_EWA</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">Restriction: The value must&#160;not be longer than 12 characters.</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.destination.name</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">destination name of the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">SDCC_${frun.sid}</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.hostname</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">host name of the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.keystore</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">keystore for the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">DFAULT&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">no</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.logon_method</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">logon method for the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">basicauth</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.password</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">login password for the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">no&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.port</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">port of the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.sid</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">SID for the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.tls</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">tls setting for FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;true</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">yes</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n</tr>\r\n<tr>\r\n<td class=\"confluenceTd\" colspan=\"1\">frun.username</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">login user for the FRUN system</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">&#160;no</td>\r\n<td class=\"confluenceTd\" colspan=\"1\">\r\n<p>Recommended FRN_EWA_&lt;CID&gt; where &lt;CID&gt; is the customer id</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p class=\"header4\"><span style=\"font-size: 14px;\">Example</span></p>\r\n<pre>### Task: Configure Early Watch Alert Job<br />### <br />### Parameters:<br />### abap.client: The ABAP client.<br />### abap.login.password: The login password for ABAP system.<br />### abap.login.username: The login username for ABAP system.<br />### abap.sid: The SID of the ABAP system.<br />### customer.namespace: The customer name space to be used as a prefix of the monitoring user roles. (default: Z_)<br />### ewa.password: The EWA password for the FRUN system.<br />### ewa.user_role: The EWA related role for the EWA user for the FRUN system. (default: SAP_SDCCN_ALL)<br />### ewa.username: The EWA user for the FRUN system. (default: FRN_JOB_EWA)<br />### frun.destination.name: The destination name of the FRUN system. (default: SDCC_${frun.sid})<br />### frun.hostname: The host name of the FRUN system.<br />### frun.keystore: The keystore for the FRUN system. (optional, default: DFAULT)<br />### frun.logon_method: The logon method for the FRUN system. (default: basicauth)<br />### frun.password: The login password for the FRUN system. (optional)<br />### frun.port: The port of the FRUN system.<br />### frun.sid: The SID for the FRUN system.<br />### frun.tls: The tls setting for FRUN system. (default: true)<br />### frun.username: The login user for the FRUN system. Recommended FRN_EWA&lt;CID&gt;_ where &lt;CID&gt; is the customer id. (optional)<br />########################################<br />TASK id=CreateEwaJob system-type=ABAP<br />########################################</pre>\r\n<pre class=\"line number30 index29 alt1\"><code class=\"java plain\">abap.client=</code><code class=\"java value\">001</code></pre>\r\n<pre class=\"line number31 index30 alt2\"><code class=\"java plain\">abap.login.password=Geheim</code></pre>\r\n<pre class=\"line number32 index31 alt1\"><code class=\"java plain\">abap.login.username=Iche</code></pre>\r\n<pre class=\"line number33 index32 alt2\"><code class=\"java plain\">abap.sid=BLN<br /><br />customer.namespace=Z_</code></pre>\r\n<pre class=\"line number34 index33 alt1\"><code class=\"java plain\">ewa.password=testtest</code></pre>\r\n<pre class=\"line number35 index34 alt2\"><code class=\"java plain\">ewa.user_role=SAP_SDCCN_ALL</code></pre>\r\n<pre class=\"line number36 index35 alt1\"><code class=\"java plain\">ewa.username=FRN_JOB_EWA</code></pre>\r\n<pre class=\"line number37 index36 alt2\"><code class=\"java plain\">frun.destination.name=SDCC_${frun.sid}</code></pre>\r\n<pre class=\"line number38 index37 alt1\"><code class=\"java plain\">frun.hostname=xxxx.wdf.sap.corp</code></pre>\r\n<pre class=\"line number39 index38 alt2\"><code class=\"java plain\">frun.keystore=DFAULT</code></pre>\r\n<pre class=\"line number40 index39 alt1\"><code class=\"java plain\">frun.logon_method=basicauth</code></pre>\r\n<pre class=\"line number41 index40 alt2\"><code class=\"java plain\">frun.password=test</code></pre>\r\n<pre class=\"line number42 index41 alt1\"><code class=\"java plain\">frun.port=</code><code class=\"java value\">50004</code></pre>\r\n<pre class=\"line number43 index42 alt2\"><code class=\"java plain\">frun.sid=RUN</code></pre>\r\n<pre class=\"line number44 index43 alt1\"><code class=\"java plain\">frun.tls=</code><code class=\"java keyword\">false</code></pre>\r\n<pre class=\"line number45 index44 alt2\"><code class=\"java plain\">frun.username=FRN_EWA_SDA</code></pre>\r\n<div class=\"table-wrap\"></div>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D044908)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D044908)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002715504/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "763561", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Control Center (SDCCN)  - FAQ", "RefUrl": "/notes/763561 "}, {"RefNumber": "2359359", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC Enhancement for ABAP Systems Connecting to Focused Run", "RefUrl": "/notes/2359359 "}, {"RefNumber": "2641304", "RefComponent": "SV-FRN-INF-SDA", "RefTitle": "Using SAP Focused Run System Preparation Tool for Managed System Preparation", "RefUrl": "/notes/2641304 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "1 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}