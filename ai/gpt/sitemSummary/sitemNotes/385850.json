{"Request": {"Number": "385850", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 491, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014977132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000385850?language=E&token=5B5F166E209C8B4544180938F238F413"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000385850", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000385850/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "385850"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 18}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.10.2001"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-KR"}, "SAPComponentKeyText": {"_label": "Component", "value": "South Korea"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "South Korea", "value": "XX-CSC-KR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-KR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "385850 - New VAT reports for South Korea"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New VAT reports have been provided for South Korean customers<br />to take care of Tax Invoice printing and VAT summary reporting</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>KR, Tax Invoice, VAT reports,Korea, DME,RFIDKRCTR, RFIDKRTPR,RFUMSV45R<br />RFUMSV49R.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>These reports are specific to Korea.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>History\\Versions</b><br /> <p>1.The reports were originally released by the following support packages<br />46C : SP16,<br />46B : SP26<br />45B : SP39.<br />These reports were also placed in SAPSERV in the following path;<br />/general/R3server/abap/note.0385850.<br />Server name is SAPSERV3.<br />The files are R000187.K9C, K000187.K9C</p> <b>2.The reports were further enhanced and they have been included in the following Support Packages</b><br /> <p>46C : SP20.<br />46B:&#x00A0;&#x00A0;SP31<br />These reports were also placed in SAPSERV in the following path;<br />/general/R3server/abap/note.0385850.<br />Server name is SAPSERV3.<br />K000232.K9C, R000232.K9C<br />User manual is available as&#x00A0;&#x00A0;VAT_Korea.doc.<br />If you wish to apply the enhancements, you would need to apply the<br />sapserv as in (1) and then apply this sapserv. The reason is some of<br />the user exit function modules are available only in (1).<br />The details of the enhancements are all available in the user manual.\\<br />Once the SAPSERV is imported into your system, pl apply notes as in<br />related notes section . Pl ensure to apply in the ascending order of<br />note numbers.</p> <b>3. The Final Version of the VAT reports including the sample code</b><br /> <p>for user exit for list display is available as&#x00A0;&#x00A0;download:<br />/general/R3server/abap/note.0385850.<br />Server name is SAPSERV3.<br /><STRONG>K000258.K9C, R000258.K9C</STRONG><br /><STRONG>K199095.P9C, R199095.P9C</STRONG><br /><STRONG>Note: apply strictly in this order: if you get a function group error</STRONG><br /><STRONG>on activation of programs, repair function group KRGEU and activate</STRONG><br /><STRONG>again.</STRONG><br /><STRONG>These reports are also available in the following Support Packages:</STRONG><br /><STRONG>46C - SP23</STRONG><br /><STRONG>46B - SP31</STRONG><br /><STRONG>45B - SP44</STRONG><br /><STRONG></STRONG><br /><STRONG>------------------------------------------------------------------------</STRONG><br /><STRONG>VERY IMPORTANT INSTRUCTIONS</STRONG><br /><STRONG>IF the following programs/ function group is already present in your</STRONG><br /><STRONG>system.</STRONG><br /><STRONG>Programs : RFIDKRTCR, RFIDKRTPR, RFUMSV45R, RFUMSV49R, RFIDKRMIG</STRONG><br /><STRONG>Function Group : KRGEU.</STRONG></p> <b>1. Pl back up your own developments/modifications to the above</b><br /> <b>programs before importing from SAPSERV. All of your modifications will</b><br /> <b>be lost once SAPSERV programs are imported.</b><br /> <b>2. Pl back up your programs within the user exit function group</b><br /> <b>KRGEU. Once SAPSERV is applied, the program code within user exit</b><br /> <b>function modules will be lost.</b><br /> <b>3. Similarly if you have applied SAPSERV and then the Support Packages</b><br /> <b>are applied, then your user exit developments could be lost.</b><br /> <b>Before applying such support packages, pl back up your user exit</b><br /> <b>developments.</b><br /> <b></b><br /> <b>NOTE:</b><br /> <b>Migration tool for add-on tax invoices are available as SAPSERV</b><br /> <b>download through note 414254.</b><br /> <p>------------------------------------------------------------------------</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I016439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I016439)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000385850/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385850/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "632669", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong currency format in the tax invoice line item amounts", "RefUrl": "/notes/632669"}, {"RefNumber": "618499", "RefComponent": "XX-CSC-KR", "RefTitle": "RFUMSV45R  error for One-time-customer-documents", "RefUrl": "/notes/618499"}, {"RefNumber": "610116", "RefComponent": "XX-CSC-KR", "RefTitle": "Vendor invoices not listed in VAT Summary detail list output", "RefUrl": "/notes/610116"}, {"RefNumber": "606321", "RefComponent": "XX-CSC-KR", "RefTitle": "Real estate documents are not shown in Tax Invoice report", "RefUrl": "/notes/606321"}, {"RefNumber": "590721", "RefComponent": "XX-CSC-KR", "RefTitle": "Korean characters not completely visible in VAT tax invoice", "RefUrl": "/notes/590721"}, {"RefNumber": "584981", "RefComponent": "XX-CSC-KR", "RefTitle": "Legal changes for VAT Summary report - August 2002", "RefUrl": "/notes/584981"}, {"RefNumber": "584381", "RefComponent": "XX-CSC-KR", "RefTitle": "Short dump in VAT tax invoice for AR document with mat. info", "RefUrl": "/notes/584381"}, {"RefNumber": "582247", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong base amount in tax invoice for 0% tax credit memos", "RefUrl": "/notes/582247"}, {"RefNumber": "566718", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report Legal changes - August 2002", "RefUrl": "/notes/566718"}, {"RefNumber": "561672", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout changes for VAT Tax invoice List output for Korea", "RefUrl": "/notes/561672"}, {"RefNumber": "545713", "RefComponent": "XX-CSC-KR", "RefTitle": "Business Place for Korean company codes in ALE/Idocs", "RefUrl": "/notes/545713"}, {"RefNumber": "541112", "RefComponent": "XX-CSC-KR", "RefTitle": "DME error for ID transaction in VAT Summary report", "RefUrl": "/notes/541112"}, {"RefNumber": "539042", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout error when printing VAT summary report in Korean", "RefUrl": "/notes/539042"}, {"RefNumber": "526482", "RefComponent": "XX-CSC-KR", "RefTitle": "Vendor credit memos posted by transaction FB05 not reported", "RefUrl": "/notes/526482"}, {"RefNumber": "522729", "RefComponent": "XX-CSC-KR", "RefTitle": "New reporting period field in VAT Summary report selection", "RefUrl": "/notes/522729"}, {"RefNumber": "516996", "RefComponent": "XX-CSC-KR", "RefTitle": "Time stamping issue in VAT time stamp report RFUMSV49R", "RefUrl": "/notes/516996"}, {"RefNumber": "513677", "RefComponent": "XX-CSC-KR", "RefTitle": "Short dump when generating a VAT tax invoice", "RefUrl": "/notes/513677"}, {"RefNumber": "511950", "RefComponent": "XX-CSC-KR", "RefTitle": "Error in the chronology of the DME for normal VAT invoices", "RefUrl": "/notes/511950"}, {"RefNumber": "509636", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong blanks count for negative invoices in RFIDKRTCR", "RefUrl": "/notes/509636"}, {"RefNumber": "509589", "RefComponent": "XX-CSC-KR", "RefTitle": "Error in header, sequential number in VAT Summary report", "RefUrl": "/notes/509589"}, {"RefNumber": "489603", "RefComponent": "XX-CSC-KR", "RefTitle": "Error for more than one customer or vendor with same VAT No.", "RefUrl": "/notes/489603"}, {"RefNumber": "483977", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout error (Location) in VAT summary and time stamp report", "RefUrl": "/notes/483977"}, {"RefNumber": "482330", "RefComponent": "XX-CSC-KR", "RefTitle": "Should report 0% vendor invoice in normal VAT summary format", "RefUrl": "/notes/482330"}, {"RefNumber": "481926", "RefComponent": "XX-CSC-KR", "RefTitle": "Migrated tax invoice number to include day and month details", "RefUrl": "/notes/481926"}, {"RefNumber": "459974", "RefComponent": "XX-CSC-KR", "RefTitle": "Summary format for exempt invoices in VAT Timestamp report", "RefUrl": "/notes/459974"}, {"RefNumber": "459815", "RefComponent": "XX-CSC-KR", "RefTitle": "DME file error for exempt invoices in VAT summary report", "RefUrl": "/notes/459815"}, {"RefNumber": "458501", "RefComponent": "XX-CSC-KR", "RefTitle": "Problem with title & \"Zero tax\" word in the VAT tax invoice", "RefUrl": "/notes/458501"}, {"RefNumber": "458496", "RefComponent": "XX-CSC-KR", "RefTitle": "Invoice count error in VAT reports for customers with ID nos", "RefUrl": "/notes/458496"}, {"RefNumber": "445043", "RefComponent": "XX-CSC-KR", "RefTitle": "Material name and Commercial UOM not selected in VAT program", "RefUrl": "/notes/445043"}, {"RefNumber": "438610", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report does not output details in Korean", "RefUrl": "/notes/438610"}, {"RefNumber": "432366", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Reports do not report Consignment Liability documents", "RefUrl": "/notes/432366"}, {"RefNumber": "426207", "RefComponent": "XX-CSC-KR", "RefTitle": "Error printing the fourth line in the  Korean Tax  Invoice", "RefUrl": "/notes/426207"}, {"RefNumber": "422729", "RefComponent": "XX-CSC-KR", "RefTitle": "User exit for list output for Korean tax invoices", "RefUrl": "/notes/422729"}, {"RefNumber": "418702", "RefComponent": "XX-CSC-KR", "RefTitle": "Downpayments printed wrongly in Korean tax invoices", "RefUrl": "/notes/418702"}, {"RefNumber": "418694", "RefComponent": "XX-CSC-KR", "RefTitle": "Incorrect milestone billing amounts in tax invoice reports", "RefUrl": "/notes/418694"}, {"RefNumber": "417904", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong print of tax invoices if amounts are 10 digits or more", "RefUrl": "/notes/417904"}, {"RefNumber": "417172", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report does not select Vendor documents", "RefUrl": "/notes/417172"}, {"RefNumber": "414254", "RefComponent": "XX-CSC-KR", "RefTitle": "Migration Tool for Korean Add-on Tax Invoices", "RefUrl": "/notes/414254"}, {"RefNumber": "413083", "RefComponent": "XX-CSC-KR", "RefTitle": "Corrections for the VAT enhanced reports", "RefUrl": "/notes/413083"}, {"RefNumber": "412537", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong DME and errorlist in program RFUMSV45R", "RefUrl": "/notes/412537"}, {"RefNumber": "411105", "RefComponent": "XX-CSC-KR", "RefTitle": "End of Maintenance for Korean VAT reports in 45B,46B and 46C", "RefUrl": "/notes/411105"}, {"RefNumber": "409246", "RefComponent": "XX-CSC-KR", "RefTitle": "Corrections for the enhanced VAT reports", "RefUrl": "/notes/409246"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "606321", "RefComponent": "XX-CSC-KR", "RefTitle": "Real estate documents are not shown in Tax Invoice report", "RefUrl": "/notes/606321 "}, {"RefNumber": "489603", "RefComponent": "XX-CSC-KR", "RefTitle": "Error for more than one customer or vendor with same VAT No.", "RefUrl": "/notes/489603 "}, {"RefNumber": "482330", "RefComponent": "XX-CSC-KR", "RefTitle": "Should report 0% vendor invoice in normal VAT summary format", "RefUrl": "/notes/482330 "}, {"RefNumber": "459974", "RefComponent": "XX-CSC-KR", "RefTitle": "Summary format for exempt invoices in VAT Timestamp report", "RefUrl": "/notes/459974 "}, {"RefNumber": "632669", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong currency format in the tax invoice line item amounts", "RefUrl": "/notes/632669 "}, {"RefNumber": "417904", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong print of tax invoices if amounts are 10 digits or more", "RefUrl": "/notes/417904 "}, {"RefNumber": "590721", "RefComponent": "XX-CSC-KR", "RefTitle": "Korean characters not completely visible in VAT tax invoice", "RefUrl": "/notes/590721 "}, {"RefNumber": "618499", "RefComponent": "XX-CSC-KR", "RefTitle": "RFUMSV45R  error for One-time-customer-documents", "RefUrl": "/notes/618499 "}, {"RefNumber": "610116", "RefComponent": "XX-CSC-KR", "RefTitle": "Vendor invoices not listed in VAT Summary detail list output", "RefUrl": "/notes/610116 "}, {"RefNumber": "582247", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong base amount in tax invoice for 0% tax credit memos", "RefUrl": "/notes/582247 "}, {"RefNumber": "584381", "RefComponent": "XX-CSC-KR", "RefTitle": "Short dump in VAT tax invoice for AR document with mat. info", "RefUrl": "/notes/584381 "}, {"RefNumber": "526482", "RefComponent": "XX-CSC-KR", "RefTitle": "Vendor credit memos posted by transaction FB05 not reported", "RefUrl": "/notes/526482 "}, {"RefNumber": "509589", "RefComponent": "XX-CSC-KR", "RefTitle": "Error in header, sequential number in VAT Summary report", "RefUrl": "/notes/509589 "}, {"RefNumber": "541112", "RefComponent": "XX-CSC-KR", "RefTitle": "DME error for ID transaction in VAT Summary report", "RefUrl": "/notes/541112 "}, {"RefNumber": "561672", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout changes for VAT Tax invoice List output for Korea", "RefUrl": "/notes/561672 "}, {"RefNumber": "584981", "RefComponent": "XX-CSC-KR", "RefTitle": "Legal changes for VAT Summary report - August 2002", "RefUrl": "/notes/584981 "}, {"RefNumber": "566718", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report Legal changes - August 2002", "RefUrl": "/notes/566718 "}, {"RefNumber": "459815", "RefComponent": "XX-CSC-KR", "RefTitle": "DME file error for exempt invoices in VAT summary report", "RefUrl": "/notes/459815 "}, {"RefNumber": "438610", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report does not output details in Korean", "RefUrl": "/notes/438610 "}, {"RefNumber": "545713", "RefComponent": "XX-CSC-KR", "RefTitle": "Business Place for Korean company codes in ALE/Idocs", "RefUrl": "/notes/545713 "}, {"RefNumber": "458496", "RefComponent": "XX-CSC-KR", "RefTitle": "Invoice count error in VAT reports for customers with ID nos", "RefUrl": "/notes/458496 "}, {"RefNumber": "539042", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout error when printing VAT summary report in Korean", "RefUrl": "/notes/539042 "}, {"RefNumber": "522729", "RefComponent": "XX-CSC-KR", "RefTitle": "New reporting period field in VAT Summary report selection", "RefUrl": "/notes/522729 "}, {"RefNumber": "513677", "RefComponent": "XX-CSC-KR", "RefTitle": "Short dump when generating a VAT tax invoice", "RefUrl": "/notes/513677 "}, {"RefNumber": "511950", "RefComponent": "XX-CSC-KR", "RefTitle": "Error in the chronology of the DME for normal VAT invoices", "RefUrl": "/notes/511950 "}, {"RefNumber": "516996", "RefComponent": "XX-CSC-KR", "RefTitle": "Time stamping issue in VAT time stamp report RFUMSV49R", "RefUrl": "/notes/516996 "}, {"RefNumber": "509636", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong blanks count for negative invoices in RFIDKRTCR", "RefUrl": "/notes/509636 "}, {"RefNumber": "481926", "RefComponent": "XX-CSC-KR", "RefTitle": "Migrated tax invoice number to include day and month details", "RefUrl": "/notes/481926 "}, {"RefNumber": "458501", "RefComponent": "XX-CSC-KR", "RefTitle": "Problem with title & \"Zero tax\" word in the VAT tax invoice", "RefUrl": "/notes/458501 "}, {"RefNumber": "445043", "RefComponent": "XX-CSC-KR", "RefTitle": "Material name and Commercial UOM not selected in VAT program", "RefUrl": "/notes/445043 "}, {"RefNumber": "426207", "RefComponent": "XX-CSC-KR", "RefTitle": "Error printing the fourth line in the  Korean Tax  Invoice", "RefUrl": "/notes/426207 "}, {"RefNumber": "432366", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Reports do not report Consignment Liability documents", "RefUrl": "/notes/432366 "}, {"RefNumber": "483977", "RefComponent": "XX-CSC-KR", "RefTitle": "Layout error (Location) in VAT summary and time stamp report", "RefUrl": "/notes/483977 "}, {"RefNumber": "422729", "RefComponent": "XX-CSC-KR", "RefTitle": "User exit for list output for Korean tax invoices", "RefUrl": "/notes/422729 "}, {"RefNumber": "418702", "RefComponent": "XX-CSC-KR", "RefTitle": "Downpayments printed wrongly in Korean tax invoices", "RefUrl": "/notes/418702 "}, {"RefNumber": "417172", "RefComponent": "XX-CSC-KR", "RefTitle": "VAT Summary Report does not select Vendor documents", "RefUrl": "/notes/417172 "}, {"RefNumber": "418694", "RefComponent": "XX-CSC-KR", "RefTitle": "Incorrect milestone billing amounts in tax invoice reports", "RefUrl": "/notes/418694 "}, {"RefNumber": "409246", "RefComponent": "XX-CSC-KR", "RefTitle": "Corrections for the enhanced VAT reports", "RefUrl": "/notes/409246 "}, {"RefNumber": "414254", "RefComponent": "XX-CSC-KR", "RefTitle": "Migration Tool for Korean Add-on Tax Invoices", "RefUrl": "/notes/414254 "}, {"RefNumber": "413083", "RefComponent": "XX-CSC-KR", "RefTitle": "Corrections for the VAT enhanced reports", "RefUrl": "/notes/413083 "}, {"RefNumber": "412537", "RefComponent": "XX-CSC-KR", "RefTitle": "Wrong DME and errorlist in program RFUMSV45R", "RefUrl": "/notes/412537 "}, {"RefNumber": "411105", "RefComponent": "XX-CSC-KR", "RefTitle": "End of Maintenance for Korean VAT reports in 45B,46B and 46C", "RefUrl": "/notes/411105 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B39", "URL": "/supportpackage/SAPKH45B39"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B44", "URL": "/supportpackage/SAPKH45B44"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B41", "URL": "/supportpackage/SAPKH45B41"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B58", "URL": "/supportpackage/SAPKH45B58"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B26", "URL": "/supportpackage/SAPKH46B26"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B31", "URL": "/supportpackage/SAPKH46B31"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B38", "URL": "/supportpackage/SAPKH46B38"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B27", "URL": "/supportpackage/SAPKH46B27"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C28", "URL": "/supportpackage/SAPKH46C28"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C23", "URL": "/supportpackage/SAPKH46C23"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C31", "URL": "/supportpackage/SAPKH46C31"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C25", "URL": "/supportpackage/SAPKH46C25"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C16", "URL": "/supportpackage/SAPKH46C16"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C40", "URL": "/supportpackage/SAPKH46C40"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C38", "URL": "/supportpackage/SAPKH46C38"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C19", "URL": "/supportpackage/SAPKH46C19"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C20", "URL": "/supportpackage/SAPKH46C20"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C17", "URL": "/supportpackage/SAPKH46C17"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47006", "URL": "/supportpackage/SAPKH47006"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47005", "URL": "/supportpackage/SAPKH47005"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50005", "URL": "/supportpackage/SAPKH50005"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}