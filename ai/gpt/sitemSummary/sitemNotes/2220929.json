{"Request": {"Number": "2220929", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1093, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018171642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002220929?language=E&token=AE7D21E844768661BE859528388E9D06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002220929", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002220929/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2220929"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.08.2020"}, "SAPComponentKey": {"_label": "Component", "value": "HAN-DB"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP HANA Database"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP HANA", "value": "HAN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP HANA Database", "value": "HAN-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'HAN-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2220929 - SAP HANA 1 SPS09 Revision 097.03"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This&#160;is the Release Note for SAP HANA 1 SPS09 Revision (***********).</p>\r\n<p>It is released for customers since 6th of October, 2015.</p>\r\n<p style=\"background-color: tomato;\">ATTENTION !!! ATTENTION !!! ATTENTION !!!<br /><br />If you are not using this revision yet, please refrain from installing it and install a revision that is not affected by HotNews.<br />If you are already using this revision, please take action immediately and make yourself familiar with below HotNews.<br /><br />After having released this revision for customers, we have identified issues so sever that we had to release SAP Notes of severity HotNews to document them.<br />SAP Notes of severity HotNews document issues which require immediate action by customers using the affected revisions.<br /><br />ATTENTION !!! ATTENTION !!! ATTENTION !!!</p>\r\n<p><strong>HotNews affecting this revision</strong></p>\r\n<ul>\r\n<ul>\r\n<li>SAP HotNews Note <a target=\"_blank\" href=\"/notes/2280329\">2280329</a></li>\r\n<li>SAP HotNews Note <a target=\"_blank\" href=\"/notes/2304595\">2304595</a></li>\r\n<li>SAP HotNews Note <a target=\"_blank\" href=\"/notes/2577314\">2577314</a></li>\r\n<li>SAP HotNews Note <a target=\"_blank\" href=\"/notes/2339989\">2339989</a></li>\r\n</ul>\r\n</ul>\r\n<p>To make sure you become aware of HotNews immediately from now on, please&#160;configure notifications as described in SAP Notes <a target=\"_blank\" href=\"/notes/2478289\">2478289</a>. <br /> Please also see&#160;<a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/hotnews\">https://launchpad.support.sap.com/#/hotnews</a>.</p>\r\n<p>Please refer to the <a target=\"_blank\" href=\"https://service.sap.com/&#126;sapidb/011000358700001182742013\">SAP HANA Revision and Maintenance Strategy</a>&#160;document on SAP Service Marketplace for an overview regarding the SAP HANA revision and maintenance strategy.</p>\r\n<p>Customers who are planning to upgrade to an SPS 09 revision:<br />For SPS 09 we reached the SAP HANA Datacenter Service Point (DSP) in May 2015. Past this point, it is strongly recommended that customers update to newer SAP HANA revisions of the same SPS available on SAP Support Portal.</p>\r\n<p>For details about the SAP HANA revision and maintenance strategy and recommended upgrade paths between SAP HANA Revisions, Support Package Stacks, Maintenance Revisions and Datacenter Service points, see \"SAP Note&#160; <a target=\"_blank\" href=\"/notes/2021789\">2021789</a>:&#160;<strong>SAP HANA Revision and Maintenance Strategy</strong>\".<br /><br /></p>\r\n<p><strong>Important SAP Notes: <br /></strong></p>\r\n<ul>\r\n<li>Please check out known issues detected in SPS09 in SAP Note <a target=\"_blank\" href=\"/notes/2093754\">2093754.</a></li>\r\n<li>\r\n<p>With SAP HANA SPS 09, the revision indicator in the version number has changed to 3 places (1.xx.xxx). <br />If you use a DBSL&#160;(Database Shared Library for the kernel program) that does not support the three-place revision indicator an error occurs.<br />Please check&#160;which SP Patch Level includes the correction&#160;in SAP Note&#160;<a target=\"_blank\" href=\"/notes/1952701\">1952701</a>.</p>\r\n</li>\r\n<li>If you upgrade from a Revision prior Revision 69.07,&#160;first upgrade your system to Revision 69.07 before upgrading to this Revision.</li>\r\n</ul>\r\n<ul>\r\n<li>Before you start the upgrade from Revision 64, 65, and 66, please&#160;check out&#160;SAP Hot News Note <a target=\"_blank\" href=\"/notes/1918267\">1918267</a>.<br /><br /></li>\r\n</ul>\r\n<p><strong>Please note the following:</strong></p>\r\n<ul>\r\n<li>Please check \"SAP Note&#160;<a target=\"_blank\" href=\"/notes/1948334\">1948334</a> : SAP HANA Database Update Paths for Maintenance Revisions\" for the possible update paths from Maintenance Revisions to SPS Revisions.<br /><br /></li>\r\n<li>Before you start the upgrade to this revision, please check out the SAP Release Note&#160;<a target=\"_blank\" href=\"/notes/2073243\">2073243</a> - SAP HANA Application Lifecycle Management for SAP HANA SPS 09.<br /><br /></li>\r\n<li>In&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2078425\">2078425</a> you will find a troubleshooting guide for the SAP HANA platform lifecycle management tool hdblcm.<br /><br /></li>\r\n<li>In order to run the SAP HANA Database Revision 80 (or higher) on SLES 11, additional operating system software packages are required. <br />Before you start the upgrade or the installation of Revision 80 or higher please&#160;check out&#160;SAP Note <a target=\"_blank\" href=\"/notes/2001528\">2001528</a>.<br /><br /></li>\r\n<li>You can find information about SHINE (SAP HANA Interactive Education) demo content in SAP Note&#160;<a target=\"_blank\" href=\"/notes/1934114\">1934114.</a><br />If&#160;you have updated SAP HANA Database from SPS 07 / 08 to SPS 09 with SHINE SPS 07 / 08 already installed, you will need to import SHINE SPS 09 twice for a successful deployment.<br /><br /></li>\r\n<li>With SAP HANA SPS 09, several inconsistencies in handling the data types double / float&#160;have been&#160;fixed. This leads to a change in the string representation of float / double data types and may cause inconsistencies&#160;in concat attributes (such as primary keys, unique constraints)&#160;that contain float / double data type columns.<br />Please check out SAP Note <a target=\"_blank\" href=\"/notes/2104798\">2104798</a>. <br /><br /></li>\r\n<li>With SAP HANA SPS 09, there is an error when&#160;refactoring HANA artifacts in SAP HANA studio. Please check out SAP Note <a target=\"_blank\" href=\"/notes/2097704\">2097704</a>.</li>\r\n<li>\r\n<p>If you are using SAP HANA live, please check the respective release note <a target=\"_blank\" href=\"/notes/1778607\">1778607</a> before you start upgrading to SPS 09.</p>\r\n</li>\r\n<li>As of Revision 93 the Embedded Statistics Server is the default setting. If you still running the old statistic server the upgrade triggers <span style=\"text-decoration: underline;\">automatically</span> the migration to the Embedded Statistics Server.<br />Please check out SAP Note: <a target=\"_blank\" href=\"/notes/2091313\">2091313</a>&#160; HANA Statistics Server - changed standard setting of the statistics server as of Revision 94.<br />Please note that when the Embedded Statistics Server is used, some program changes are required in the ABAP monitoring tools. See SAP Note <a target=\"_blank\" href=\"/notes/1925684\">1925684</a> for details.<br /><br /></li>\r\n<li>Recover data without catalog fails with incorrect syntax near \"USING\". Please check out the SAP Release Note <a target=\"_blank\" href=\"/notes/2157184\">2157184</a>.<br /><br /></li>\r\n<li>Using SAP Web Dispatcher as HTTP loadbalancer, additional configuration is required. Please see SAP Note <a target=\"_blank\" href=\"/notes/2146931\">2146931</a> for details.<br /><br /></li>\r\n<li>If you upgrade to this revision and you're using BW on HANA then please check the ABAP related notes which can be found in the attachment of this note.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>NewDB, in-memory database, Hybrid database, update, installation, SPS 09</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>Running the installation and the update is only supported on a validated SAP HANA appliance and SAP HANA Tailored Datacenter Integration setup.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>If you upgrade from a lower SAP HANA SPS, it is recommended to update ALL other components (Studio, Modeler, DB Clients, DBSL, SLT, DS, ...) to at least the minimal version of SAP HANA SPS 09.</strong></p>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong>Installation and Update</strong></p>\r\n<ul>\r\n<li>For the installation of the SAP HANA client on SUSE Linux Enterprise Server 9 and 10, use the package \"Linux SUSE 9 on x86_64 64bit\".<br /><br /></li>\r\n<li>As part of the installation&#160;or the upgrade to Revision 97.01 the master key of the secure store in the file system (SSFS) is changed from the initial default key to an installation specific individual key. Please checkout SAP Note <a target=\"_blank\" href=\"/notes/2183624\">2183624.</a> To change the master key of SSFS of the hdbuserstore please additional check out SAP Note<span style=\"font-family: Calibri; font-size: medium;\">&#160;</span><a target=\"_blank\" href=\"/notes/2210637\"><span style=\"color: #0563c1; font-family: Calibri; font-size: medium;\">2210637.</span></a></li>\r\n</ul>\r\n<p><strong>Documentation</strong></p>\r\n<ul>\r\n<li>See SAP Help Portal at <a target=\"_blank\" href=\"http://help.sap.com/hana_platform\">http://help.sap.com/hana_platform</a> for the complete SAP HANA product documentation set.<br /><br /></li>\r\n<li>For this revision, the most up-to-date version of the SAP HANA Developer Guide and the SAP HANA SQLScript Reference is located on the SAP Help Portal.</li>\r\n</ul>\r\n<p><strong>&#160;</strong></p>\r\n<p><strong><strong><br />Issues solved with this revision</strong></strong></p>\r\n<p>BW/OLAP<strong><br /></strong></p>\r\n<ul>\r\n<li>Fixed an issue in Epsilon Reaction in order to adapt the BW disaggregation behaviour.</li>\r\n</ul>\r\n<p>Security</p>\r\n<ul>\r\n<li>Fixed a potential remote code execution in HANA. Please refer to SAP Note <a target=\"_blank\" href=\"/notes/2197428\">2197428</a> for details.</li>\r\n</ul>\r\n<p>Supportability</p>\r\n<ul>\r\n<li>Fixed the full system info dump to now also consider the zipped alert trace files (&lt;service&gt;_alert_&lt;host&gt;_&lt;timestamp&gt;.gz) &#160;for the time restriction (e.g. in HANA Studio) when a date range has been specified for the collection of diagnosis information.</li>\r\n</ul>\r\n<p>XS Engine</p>\r\n<ul>\r\n<li>\r\n<p>Increased the default value of the preallocated size of a rowstore lock/version table partition in the SAP HANA XS engine. <br />The low value frequently caused the following alert: <br />\"The overflow ratio of the rowstore version space of &lt;host&gt;:&lt;port&gt; is too high. <br />&#160;The current ratio is &lt;X&gt; and the total number of rowstore versions is &lt;Y&gt;.\"</p>\r\n</li>\r\n</ul>\r\n<p>General</p>\r\n<ul>\r\n<li>Fixed a crash that could occur after a transaction was interrupted (for example, due to an out-of-memory situation)The crash stack was as follows:</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160;\"TRexAPI::TableUpdateForPartIteratorD::TableUpdateForPartIteratorD</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::PartitionExecution::getIterator</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::collectPartitionExecution</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::distribute_insert</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::dispatchCallToParts</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::execute_context</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::executeWithRetry</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; TRexAPI::TableUpdate::execute</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; &#160;ptime::TrexUpdate::index</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; &#160;ptime::Proc_insert_trex::operator</span><br /><span style=\"font-size: 14px;\">&#160; &#160; &#160; &#160; &#160; &#160;ptime::Query::execute\".</span></p>\r\n<ul>\r\n<li>\r\n<p>Fixed a code logic comparison issue that caused SAP HANA Studio to throw the following error:</p>\r\n</li>\r\n<li>\r\n<p>\"SAP DBTech JDBC: [3]: fatal error: Execution flow must not reach here\".</p>\r\n</li>\r\n<li>\r\n<p>Fixed a bug that could lead to unexpected results or errors, such as the following, when the column contained a mixture of values and NULL entries: <br />\"overflow in numeric calculation; $function$=aggregate; $message$=aggregation failed &lt;measure_name&gt;$sum$\".</p>\r\n</li>\r\n<li>\r\n<p>Fixed an index server crash caused by a stack overflow. The entry below appeared repeatedly in the crash stack:<br />\"planvizCS::TarjanCycleDetector::operator()\".</p>\r\n</li>\r\n<li>Fixed a bug that prevented the reclaiming of log segments under particular circumstances in a system replication scenario, when the connection between both sides was lost.</li>\r\n<li>Fixed an union column harmonization issue that lead to query compile error for non-zero values.</li>\r\n<li>Fixed the column store error that occurred when a procedure was executed with a scalar UDF (user-defined function) in the search expression:<br />\"[2048] column store error: search table error: [2018] View attribute \"_CV0xABCDEFG\" was not found in schema\".</li>\r\n<li>Improved memory usage during CHECK_TABLE_CONSISTENCY to avoid out of memory during the check.</li>\r\n<li>Fixed an issue that led to an indexserver crash when planviz preparation phase was completed.</li>\r\n<li>Fixed an out of memory crash that occurred during restart of the system after upgrading to Revision 97.</li>\r\n<li>Fixed an issue in model deployment that caused a wrong data type to be used internally. <br />This caused, for example, modeled integer fields to be treated as a DATE data type</li>\r\n<li>Fixed a bug that caused a query to fail due to an incorrect use of an evaluator constraint. This in turn led to the error \"undefined index attribute\".</li>\r\n<li>\r\n<p>Fixed an issue where a query could fail with the following error:<br />Error message : \"column store error: temp index create error: [2999] exception 2999\".</p>\r\n</li>\r\n<li>\r\n<p>Fixed an issue with a hanging CheckTableConsistency() procedure. The issue was caused by tables with very big columns that contained mostly NULL values.</p>\r\n</li>\r\n<li>\r\n<p>Fixed a crash with the following call stack:<br />\"1: ptime::TrexITab::isStringType(unsigned int)<br />&#160;2: unsigned long ptime::Proc_insert_trex::execute&lt;ptime::TrexUpdateWrapper&gt;<br />&#160;3: ptime::Proc_insert_trex::operator<br />&#160;4: ptime::Query::_execute<br />&#160;5: ptime::Query::execute.</p>\r\n</li>\r\n<li>Fixed an index server crash at <br />\"ptime::qo_Normalizer::rel_pred_normalize\". <br />This was triggered by a SELECT query on a calculation view with a single child plan under a UNION ALL operator.</li>\r\n<li>Fixed a bug that led to a delta log replay failed issue: <br />\"delta log replay failed: search table error: [1527] exception during deltalog replay\". <br />This was caused by an alter column operation from string to fixed.</li>\r\n<li>Fixed a \"ColDicVal (1000164,5) not found\" error, which occurred when UNION queries were executed.</li>\r\n<li>\r\n<p>Fixed a crash that occurred during the table consistency check with the following call stack:<br />\"[CRASH_STACK] Stacktrace of crash: (0000-00-00 00:00:00 000 Local)<br />&#160;----&gt; Symbolic stack backtrace &lt;----<br />&#160; 0: TableConsistencyCheck::TablePartChecker::checkRowid<br />&#160; 1: TableConsistencyCheck::TablePartChecker::execute <br />&#160; 2: TableConsistencyCheck::checkColumnStoreTablePart<br />&#160; 3: TableConsistencyCheck::ColumnTablePartJobNode::run2.</p>\r\n</li>\r\n<li>\r\n<p>Fixed an index server crash during error message generation when the statement memory size limit was reached. <br />The crash stack contained the following entries:<br />\"exception <br />&#160; Allocation failed ; <br />&#160;exception throw location:<br />&#160;ptime::NBaseString::_alloc<br />&#160;expr::Invoker3<br />&#160;expr::Invoker1<br />&#160;void expr::Evaluator::Dispatch<br />&#160;expr::Evaluator::DoRun<br />&#160;expr::Evaluator::Run<br />&#160;ptime::ValueExpression\".</p>\r\n</li>\r\n<li>\r\n<p>Fixed an index server crash caused by parallel EAPI statement execution in a single transaction. <br />The crash stack contained the following:<br />\"illegal use of transaction object<br />&#160;1: ptime::Transaction::partial_abort_<br />&#160;2: ptime::Transaction::partial_abort<br />&#160;3: ptime::Statement::handleSerializationFailWithTimeout<br />&#160;4: ptime::Statement::handleRetryException<br />&#160;5: ptime::Statement::executeUpdate_\".</p>\r\n</li>\r\n<li>\r\n<p>Fixed the \"fatal error: \"ColDicVal (0,0) not found\",<br />which was caused by wrong column information being generated for column aggregation during query plan generation.</p>\r\n</li>\r\n<li>\r\n<p>Fixed an index server crash with the error message \"no connection on volume\" and the following call stack:<br />\"1: DataAccess::UndoFileAnchor::process<br />&#160;2: DataAccess::PersistenceSession::txRollbackCallback<br />&#160;4: ptime::Transaction::roll_back<br />&#160;5: ptime::Transaction::preabort\".</p>\r\n</li>\r\n<li>Fixed a bug which showed Alert 78 even after the issue was fixed manually with the command \"alter system set event acknowledged ...\". <br />The Root cause for the issue was that events were not successfully acknowledged in a MDC system.</li>\r\n<li>\r\n<p>Fixed a crash that could occur in an out of memory situation when auditing was enabled. <br />The call stack was as follows:<br />\"[CRASH_STACK] stacktrace of crash: (0000-00-00 00:00:00 000 Local)<br />&#160;----&gt; Pending exceptions (possible root cause) &lt;----<br />&#160;exception 1: no.1000002 (ltt/impl/memory.cpp:86) <br />&#160;Allocation failed ; $size$=xx; $name$=Authorization; $type$=pool; $inuse_count$=xx; $allocated_size$=xx<br />&#160;xception throw location:<br />&#160; 1: PrincipalManager::getUser<br />&#160; 2: Auditing::AuditEventFactory::getAuditEventContext\".</p>\r\n</li>\r\n<li>Fixed a crash that occured as a result of a new log buffer being added to the replication send queue after connection close.<br />The crash stack is as follows:<br />\"exception&#160; 1: no.1000000&#160; () Invalid buffer position: m_lastBufferEndPos=xx, buf-&gt;m_Position=xx; $condition$=! m_lastBufferEndPos || m_lastBufferEndPos == buf-&gt;m_Position<br />&#160;exception throw location:<br />&#160;1: DataAccess::AsyncLogBufferHandlerThread::addLogBuffer()<br />&#160;2: DataAccess::ReplicationProtocolPrimaryHandler::sendLogQueueContentToAsyncBuffer()<br />&#160;3: DataAccess::ReplicationProtocolPrimaryHandler::sendLogQueueContent()<br />&#160;4: DataAccess::DisasterRecoveryPrimaryHandlerImpl::sendLog()<br />&#160;5: DataRecovery::LogSegment::triggerIO()<br />&#160;6: DataRecovery::LogPartition::startIO()\".</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D049987)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I035208)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002220929/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2577314", "RefComponent": "HAN-DB", "RefTitle": "Possible Row Store Inconsistency on IBM Power Platform", "RefUrl": "/notes/2577314"}, {"RefNumber": "2339989", "RefComponent": "HAN-DB", "RefTitle": "Insert of UTF-8 non-ASCII Characters Can Cause Wrong Dictionary Ordering", "RefUrl": "/notes/2339989"}, {"RefNumber": "2304595", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA DB: IMPORT AS BINARY WITH RENAME SCHEMA can Lead to Data Inconsistency", "RefUrl": "/notes/2304595"}, {"RefNumber": "2280329", "RefComponent": "HAN-DB-ENG", "RefTitle": "Possible Wrong Results During JOIN of More Than 3 Tables", "RefUrl": "/notes/2280329"}, {"RefNumber": "2214279", "RefComponent": "HAN-DB", "RefTitle": "Blocking situation caused by waiting writer holding consistent change lock", "RefUrl": "/notes/2214279"}, {"RefNumber": "2210637", "RefComponent": "HAN-DB-SEC", "RefTitle": "Change the encryption key of hdbuserstore", "RefUrl": "/notes/2210637"}, {"RefNumber": "2205345", "RefComponent": "HAN-DB-CLI", "RefTitle": "SAP HANA DB Client:   \"SQL code: -9300\" occurred while accessing table <table_name>", "RefUrl": "/notes/2205345"}, {"RefNumber": "2197428", "RefComponent": "HAN-DB", "RefTitle": "Potential remote code execution in HANA", "RefUrl": "/notes/2197428"}, {"RefNumber": "2193235", "RefComponent": "HAN-DB-HA", "RefTitle": "SAP HANA system replication is not working after a change of the master key", "RefUrl": "/notes/2193235"}, {"RefNumber": "2191313", "RefComponent": "HAN-DB-HA", "RefTitle": "SAP HANA multitenant database containers - failover can fail in Host Auto-Failover setup with distributed tenant DBs", "RefUrl": "/notes/2191313"}, {"RefNumber": "2184218", "RefComponent": "HAN-DB-HA", "RefTitle": "System Replication & SAP HANA Multitenant Database Containers - No SQL Connect of Tenant Databases Possible After Takeover", "RefUrl": "/notes/2184218"}, {"RefNumber": "2183624", "RefComponent": "HAN-DB-SEC", "RefTitle": "Potential information leakage using default SSFS master key in HANA", "RefUrl": "/notes/2183624"}, {"RefNumber": "2160302", "RefComponent": "HAN-STD-ADM-PVZ", "RefTitle": "Indexserver crash due to early deallocation of the PlanViz objects", "RefUrl": "/notes/2160302"}, {"RefNumber": "2157184", "RefComponent": "HAN-STD-ADM-BAC", "RefTitle": "recover data without catalog fails with: incorrect syntax near \"USING\"", "RefUrl": "/notes/2157184"}, {"RefNumber": "2146931", "RefComponent": "BC-CST-WDP", "RefTitle": "Web Dispatcher - XSSRV - Initialization fails because access to topology information is forbidden", "RefUrl": "/notes/2146931"}, {"RefNumber": "2139017", "RefComponent": "HAN-DB", "RefTitle": "Embedded Statistics Server migration failed while creating STARTUPMIGRATIONSTEPIMPLEMENTOR procedure", "RefUrl": "/notes/2139017"}, {"RefNumber": "2135596", "RefComponent": "HAN-DB", "RefTitle": "Column Store table corrupted and cannot be accessed due to orphaned timestamp", "RefUrl": "/notes/2135596"}, {"RefNumber": "2135446", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA database: Column Store Table with schema flexibility option cannot be accessed", "RefUrl": "/notes/2135446"}, {"RefNumber": "2135443", "RefComponent": "HAN-DB", "RefTitle": "Inconsistent Redo log Prevents Indexserver Startup", "RefUrl": "/notes/2135443"}, {"RefNumber": "2135097", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database: Inconsistent MVCC information prevents Indexserver startup", "RefUrl": "/notes/2135097"}, {"RefNumber": "2130083", "RefComponent": "HAN-DB-ENG", "RefTitle": "Successive DML operations on Global Temporary Column Table may cause wrong results or crashes", "RefUrl": "/notes/2130083"}, {"RefNumber": "2126637", "RefComponent": "HAN-CPT-DCC", "RefTitle": "SAP DB Control Center  Revision 93 Release & Information Note", "RefUrl": "/notes/2126637"}, {"RefNumber": "2106836", "RefComponent": "HAN-DB", "RefTitle": "Potential Data Loss During Table Reload", "RefUrl": "/notes/2106836"}, {"RefNumber": "2105764", "RefComponent": "HAN-DB", "RefTitle": "Data Inconsistency after Upgrade to SAP HANA SPS09 Revision 90", "RefUrl": "/notes/2105764"}, {"RefNumber": "2104798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2104798"}, {"RefNumber": "2101737", "RefComponent": "HAN-DB-BAC", "RefTitle": "Recovery of a Multitenant Database Container fails", "RefUrl": "/notes/2101737"}, {"RefNumber": "2099820", "RefComponent": "BC-CCM-SLD", "RefTitle": "MOPZ missing product instances::MOPZ does not find HANA Addon Products", "RefUrl": "/notes/2099820"}, {"RefNumber": "2099661", "RefComponent": "HAN-DB-SDA", "RefTitle": "While configuring LMDB, I do not get an option to select SDA (SAP HANA SPS06 update to SAP HANA SPS09)", "RefUrl": "/notes/2099661"}, {"RefNumber": "2099489", "RefComponent": "HAN-DB", "RefTitle": "Adding additional server like scriptserver for AFL usage and the usage of SYS_DATABASES views can crash the additional server", "RefUrl": "/notes/2099489"}, {"RefNumber": "2099486", "RefComponent": "HAN-LM-UPG", "RefTitle": "Repository migration might fail when updating from SPS7 revisions to Revision 90", "RefUrl": "/notes/2099486"}, {"RefNumber": "2099478", "RefComponent": "HAN-DB-SEC", "RefTitle": "Backup fails with error message: crypto provider 'commoncrypto' not available", "RefUrl": "/notes/2099478"}, {"RefNumber": "2099047", "RefComponent": "HAN-STD-DEV-TP", "RefTitle": "First table row hidden in SAP HANA Studio on Mac OS 10.10", "RefUrl": "/notes/2099047"}, {"RefNumber": "2097704", "RefComponent": "HAN-STD-DEV-REF", "RefTitle": "Error while refactoring HANA artifacts in HANA Studio", "RefUrl": "/notes/2097704"}, {"RefNumber": "2093754", "RefComponent": "HAN-DB", "RefTitle": "Known issues detected in SAP HANA 1 SPS09", "RefUrl": "/notes/2093754"}, {"RefNumber": "2092868", "RefComponent": "HAN-DB", "RefTitle": "change of string representation of float / double in Hana SPS9", "RefUrl": "/notes/2092868"}, {"RefNumber": "2091313", "RefComponent": "HAN-DB", "RefTitle": "HANA Statistics Server - changed standard setting of the statistics server as of Revision 93", "RefUrl": "/notes/2091313"}, {"RefNumber": "2078425", "RefComponent": "HAN-LM-PLT", "RefTitle": "Troubleshooting note for SAP HANA platform lifecycle management tool hdblcm", "RefUrl": "/notes/2078425"}, {"RefNumber": "2075266", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Platform SPS 09 Release Note", "RefUrl": "/notes/2075266"}, {"RefNumber": "2074556", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 84 Enhancements for Planning Functions", "RefUrl": "/notes/2074556"}, {"RefNumber": "2073243", "RefComponent": "HAN-LM-APP", "RefTitle": "Release Notes for SAP HANA Application Lifecycle Management for SAP HANA SPS 09", "RefUrl": "/notes/2073243"}, {"RefNumber": "2072211", "RefComponent": "HAN-DB-MON", "RefTitle": "Upgrade of the Embedded Statistics Service (ESS) from Revision 74.02 to Revision 81 or 82 fails with \"unknown catalog object\"", "RefUrl": "/notes/2072211"}, {"RefNumber": "2068807", "RefComponent": "HAN-DYT", "RefTitle": "SAP HANA Dynamic Tiering SPS 09 Release Note", "RefUrl": "/notes/2068807"}, {"RefNumber": "2066903", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: STRING_AGG() with subselect does not return expected result", "RefUrl": "/notes/2066903"}, {"RefNumber": "2066313", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Possible columnstore table corruption after point-in-time recovery", "RefUrl": "/notes/2066313"}, {"RefNumber": "2056079", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Planning : new check 'ALL_IN_FILTER'", "RefUrl": "/notes/2056079"}, {"RefNumber": "2054883", "RefComponent": "HAN-DB-SEC", "RefTitle": "Enabling Data Volume Encryption in a Running System", "RefUrl": "/notes/2054883"}, {"RefNumber": "2053116", "RefComponent": "HAN-DB", "RefTitle": "UDF in view definition causes HANA indexserver crash", "RefUrl": "/notes/2053116"}, {"RefNumber": "2052914", "RefComponent": "HAN-DB-ENG", "RefTitle": "ODBC driver issue after upgrade to Revision >= 82", "RefUrl": "/notes/2052914"}, {"RefNumber": "2045050", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA DB: Enterprise search query may return too few results", "RefUrl": "/notes/2045050"}, {"RefNumber": "2039810", "RefComponent": "HAN-DB", "RefTitle": "Table does not get merged automatically", "RefUrl": "/notes/2039810"}, {"RefNumber": "2039085", "RefComponent": "HAN-DB", "RefTitle": "upgrade fails with  Retcode 1: SQL-error -2048-column store error PARCONV_UPG", "RefUrl": "/notes/2039085"}, {"RefNumber": "2037509", "RefComponent": "HAN-DB", "RefTitle": "Hanging Executor::X2::calculate Threads After Query Cancellation or OOM", "RefUrl": "/notes/2037509"}, {"RefNumber": "2035443", "RefComponent": "HAN-DB-ENG", "RefTitle": "SAP HANA DB: Disconnect of connections after reaching the idle_connection_timeout in distributed landscapes", "RefUrl": "/notes/2035443"}, {"RefNumber": "2032600", "RefComponent": "HAN-LM-UPG-DB", "RefTitle": "Upgrade of SAP HANA studio from Revision 80 to a later revision", "RefUrl": "/notes/2032600"}, {"RefNumber": "2025702", "RefComponent": "CA-EPT-HCO-AUT", "RefTitle": "SAP HANA Live Authorization Assistant will not work in HANA Rev 80", "RefUrl": "/notes/2025702"}, {"RefNumber": "2023669", "RefComponent": "HAN-STD-DEV-RVR", "RefTitle": "SAP HANA SPS 08 offline and online help are out of synch", "RefUrl": "/notes/2023669"}, {"RefNumber": "2023163", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading multispanning TAR archives", "RefUrl": "/notes/2023163"}, {"RefNumber": "2023091", "RefComponent": "HAN-WDE", "RefTitle": "Error: Unable to undeploy delivery unit", "RefUrl": "/notes/2023091"}, {"RefNumber": "2022779", "RefComponent": "HAN-LM-INS", "RefTitle": "Technical Change of SAP HANA Studio Version String", "RefUrl": "/notes/2022779"}, {"RefNumber": "2022747", "RefComponent": "HAN-LM-UPG-DB", "RefTitle": "HANA Studio does not start after update from SPS5 to SPS8", "RefUrl": "/notes/2022747"}, {"RefNumber": "2021789", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA 1.0 Revision and Maintenance Strategy", "RefUrl": "/notes/2021789"}, {"RefNumber": "2014334", "RefComponent": "HAN-DB-AFL", "RefTitle": "Migration from SAP HANA AFL (SPS 07 or earlier) to Product-Specific AFLs (SPS 08)", "RefUrl": "/notes/2014334"}, {"RefNumber": "2009666", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 74 Enhancements for Planning Functions", "RefUrl": "/notes/2009666"}, {"RefNumber": "2001528", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux: SAP HANA Database SPS 08, SPS 09 and SPS 10 on RHEL 6 or SLES 11", "RefUrl": "/notes/2001528"}, {"RefNumber": "1998966", "RefComponent": "HAN-LM-APP", "RefTitle": "Release Note SAP HANA Application Lifecycle Management SPS 08", "RefUrl": "/notes/1998966"}, {"RefNumber": "1963779", "RefComponent": "HAN-DB", "RefTitle": "Reaching the 768 GB limit of rowstore can cause data loss", "RefUrl": "/notes/1963779"}, {"RefNumber": "1962287", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Planning Functions: set default connection name", "RefUrl": "/notes/1962287"}, {"RefNumber": "1957136", "RefComponent": "HAN-DB-ENG-PLE", "RefTitle": "Revision 71 Enhancements for Planning Functions", "RefUrl": "/notes/1957136"}, {"RefNumber": "1952701", "RefComponent": "BC-DB-HDB-SYS", "RefTitle": "DBSL supports new SAP HANA SP9 version number", "RefUrl": "/notes/1952701"}, {"RefNumber": "1948334", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Update Paths for SAP HANA Maintenance Revisions", "RefUrl": "/notes/1948334"}, {"RefNumber": "1934114", "RefComponent": "HAN-AS-XSA-SHN", "RefTitle": "SAP HANA DEMO MODEL - SHINE Release & Information Note", "RefUrl": "/notes/1934114"}, {"RefNumber": "1925684", "RefComponent": "BC-DB-HDB-CCM", "RefTitle": "ABAP adjustments for the new Embedded Statistic Server", "RefUrl": "/notes/1925684"}, {"RefNumber": "1918267", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1918267"}, {"RefNumber": "1898497", "RefComponent": "HAN-DB-AFL-GEN", "RefTitle": "Versioning and delivery strategy of application function libraries (AFLs)", "RefUrl": "/notes/1898497"}, {"RefNumber": "1778607", "RefComponent": "XX-SER-REL", "RefTitle": "SAP HANA Live for SAP Business Suite", "RefUrl": "/notes/1778607"}, {"RefNumber": "1710832", "RefComponent": "HAN-DB-ENG", "RefTitle": "HANA BW: I_RESULT_INDEX_NAME with TREX_EXT_AGGREGATE", "RefUrl": "/notes/1710832"}, {"RefNumber": "1666976", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1666976"}, {"RefNumber": "1598623", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1598623"}, {"RefNumber": "1523337", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database 1.00 - Central Note", "RefUrl": "/notes/1523337"}, {"RefNumber": "1514967", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA: Central Note", "RefUrl": "/notes/1514967"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2958224", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Revision Release Details", "RefUrl": "/notes/2958224 "}, {"RefNumber": "2075266", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Platform SPS 09 Release Note", "RefUrl": "/notes/2075266 "}, {"RefNumber": "1948334", "RefComponent": "HAN-DB", "RefTitle": "SAP HANA Database Update Paths for SAP HANA Maintenance Revisions", "RefUrl": "/notes/1948334 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "HDB", "From": "1.00", "To": "1.00", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}