{"Request": {"Number": "195995", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 283, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001013132017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000195995?language=E&token=49879DCA4F6D8145769D2A43CEB54B9B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000195995", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000195995/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "195995"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 12}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Workaround of missing functionality"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.09.2004"}, "SAPComponentKey": {"_label": "Component", "value": "TR-CB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cash Budget Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Treasury", "value": "TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cash Budget Management", "value": "TR-CB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'TR-CB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "195995 - Organizational dimension in cash budget management"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You use the cash budget management. The commitment item dimension is insufficient for your information requirements, and you want to evaluate by a further dimension.<br /><br />Addition of 26 June 2003 for Release 4.6B: After importing Support Package SAPKH46B52, the restriction to the fund no longer works in the line item report RFFMEP1M.<br />The reason is that the standard corrections to logical database C1F described in Note 195995 were not delivered via Support Package by mistake, i.e. they are only contained in your system, if you applied them manually. With Support Package SAPKH46B52&#x00A0;&#x00A0;(for Notes 595221 and 595794), however, Include DBC1FF00 was overwritten. To correct that, apply the correction given for DBC1FF00 below again, or import Support Package SAPKH46B54.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>-GEBER</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The cash budget management in the standard system also updates to the funds dimension. In the account assignment user exit of the cash budget management,you can include funds in the original documents and thus in cash budget management according to your requirements.<br />However, reporting has not yet been prepared for the display of this dimension.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Enhancements in reporting:<br />Old standard reporting (up to and including Release 4.5)<br />The first correction instruction in the attachment describes the enhancement that is required to restrict each of the old standard reports (up to and including Release 4.5; Transactions FMR1...) to exactly one fund. If the new parameter stays blank, there is no restriction. In addition to the attached source code enhancements, the following interface enhancement is required:<br /><br />Program: SAPMFM9R screen 0100:<br />Include the field FMSU-GEBER as text and input template. (Properties from the ABAP/4 Dictionary).<br /><br />If you directly execute program RFFMBE05 instead of the transactions, you can use the select option for the fund which provides more options.<br /><br />Line item report (all releases)<br />The second instruction in the attachment describes the enhancement in line item reporting (program RFFMEP1M). In addition to the corrections, you have to extend structure FMMP in the DDIC (Transaction SE11) by the field -GEBER with data element BP_GEBER (following the field -GTEXT) and the selection text in the logical database C1F.<br /><br />New standard reporting (as of Release 4.6)<br />The enhancement for the new standard reporting (as of Release 4.6) was originally included in the standard system in Release 4.6C but was destroyed by Hot Package&#x00A0;&#x00A0;SAPKH46C07.<br />The prerequisite for applying this note in 4.6 is the transport from Note 322580.<br />Get the selection texts from the DDIC.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "TR-CB-IN (Integration)"}, {"Key": "Other Components", "Value": "XX-RC-TR-CB (-obsolete-  RC-TR-Cash Budget Management)"}, {"Key": "Other Components", "Value": "TR-CB-IS (Information System)"}, {"Key": "Other Components", "Value": "TR-CB-PL (Planning)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I046070)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I028960)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000195995/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000195995/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000195995/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "85395", "RefComponent": "TR-CB-IS", "RefTitle": "Reactivation of standard reporting", "RefUrl": "/notes/85395"}, {"RefNumber": "557791", "RefComponent": "TR-CB-IS", "RefTitle": "Dimension FUND in Cash Budget Mgt. Drilldown reporting", "RefUrl": "/notes/557791"}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580"}, {"RefNumber": "161962", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reports per company code", "RefUrl": "/notes/161962"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "557791", "RefComponent": "TR-CB-IS", "RefTitle": "Dimension FUND in Cash Budget Mgt. Drilldown reporting", "RefUrl": "/notes/557791 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "322580", "RefComponent": "TR-CB-IS", "RefTitle": "Hierarchy in standard reports", "RefUrl": "/notes/322580 "}, {"RefNumber": "85395", "RefComponent": "TR-CB-IS", "RefTitle": "Reactivation of standard reporting", "RefUrl": "/notes/85395 "}, {"RefNumber": "161962", "RefComponent": "TR-CB-IS", "RefTitle": "Standard reports per company code", "RefUrl": "/notes/161962 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "300", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B39", "URL": "/supportpackage/SAPKH40B39"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B39", "URL": "/supportpackage/SAPKE40B39"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B20", "URL": "/supportpackage/SAPKH45B20"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B54", "URL": "/supportpackage/SAPKH46B54"}, {"SoftwareComponentVersion": "SAP_APPL 46B", "SupportPackage": "SAPKH46B04", "URL": "/supportpackage/SAPKH46B04"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C01", "URL": "/supportpackage/SAPKH46C01"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47023", "URL": "/supportpackage/SAPKH47023"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000195995/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 9, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "40B", "Number": "199101 ", "URL": "/notes/199101 ", "Title": "(MM) Change to CO account assignment in purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40A", "ValidTo": "45B", "Number": "85395 ", "URL": "/notes/85395 ", "Title": "Reactivation of standard reporting", "Component": "TR-CB-IS"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "137895 ", "URL": "/notes/137895 ", "Title": "Posting object check for documents carried forward", "Component": "FI-FM-IN"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "155969 ", "URL": "/notes/155969 ", "Title": "Update termination in payment program", "Component": "TR-CB-IN"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "354361 ", "URL": "/notes/354361 ", "Title": "Commitment is not reduced by a change of comm. item", "Component": "FI-FM-IN-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "390882 ", "URL": "/notes/390882 ", "Title": "\"DATA_MISSING\" in \"FM_DOCUMENT_UPDATE_FI\"", "Component": "FI-FM-IN-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "40B", "Number": "402930 ", "URL": "/notes/402930 ", "Title": "(MM) Change account assignment type in PReq / purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "45B", "Number": "155969 ", "URL": "/notes/155969 ", "Title": "Update termination in payment program", "Component": "TR-CB-IN"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "45B", "Number": "402930 ", "URL": "/notes/402930 ", "Title": "(MM) Change account assignment type in PReq / purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "45A", "ValidTo": "46B", "Number": "199101 ", "URL": "/notes/199101 ", "Title": "(MM) Change to CO account assignment in purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46B", "Number": "618096 ", "URL": "/notes/618096 ", "Title": "(MM) deletion of items from purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46B", "ValidTo": "46C", "Number": "402930 ", "URL": "/notes/402930 ", "Title": "(MM) Change account assignment type in PReq / purchase order", "Component": "PS-CAF-ACT"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "470", "Number": "723185 ", "URL": "/notes/723185 ", "Title": "Incorrect period for orders with invoicing plan", "Component": "TR-CB-IN"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "470", "ValidTo": "470", "Number": "618096 ", "URL": "/notes/618096 ", "Title": "(MM) deletion of items from purchase order", "Component": "PS-CAF-ACT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}