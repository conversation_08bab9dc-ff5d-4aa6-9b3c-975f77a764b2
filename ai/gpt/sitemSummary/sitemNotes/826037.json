{"Request": {"Number": "826037", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 203, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015862432017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000826037?language=E&token=17958D8C6B1509A6034D9848FF3A950A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000826037", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000826037/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "826037"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 36}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.08.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-SDB"}, "SAPComponentKeyText": {"_label": "Component", "value": "MaxDB"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "MaxDB", "value": "BC-DB-SDB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-SDB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "826037 - FAQ: SAP MaxDB/liveCache support"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. Who do I contact for what problems?</ol><ol>2. What are the telephone and fax numbers of the support centers?</ol><ol>3. What is consultation, what is support?</ol><ol>4. Under which component do I open a customer message for SAP MaxDB?</ol><ol>5. Under which component do I open a customer message for SAP liveCache?</ol><ol>6. Under which component do I open a customer message for the SAP liveCache application?</ol><ol>7. Under which component do I open a customer message for a BW system with SAP MaxDB?</ol><ol>8. Under which component do I open a customer message for anSAP Content Server?</ol><ol>9. In what language can I report a problem in the customer message system?</ol><ol>10. What priority must I select for my problem?</ol><ol>11. Does SAP also provide support at the weekends?</ol><ol>12. What information must I report to <PERSON> so that the cause of the problem can be analyzed promptly?</ol><ol>13. How do I determine which database version I use?</ol><ol>14. How do I determine what precompiler runtime I use?</ol><ol>15. How do I determine which SQLDBC version is in use?</ol><ol>16. What log files must I add to the message if I have problems with backing up the database?</ol><ol>17. What files does SAP MaxDB Support require if the database has crashed?</ol><ol>18. How do I set up a Telnet connection to my system?</ol><ol>19. How do I set up an SSH connection?</ol><ol>20. How do I set up an SAPDB connection to my system?</ol><ol>21. Why do I need an SAPDB connection?</ol><ol>22. How do I enter the logon data in the message?</ol><ol>23. Where can I find the support guide?</ol><ol>24. What is a PTS number?</ol><ol>25. Where do I find the WebPTS?</ol><ol>26. When should I forward a message to Development Support?</ol><ol>27. A database recovery is required, but not possible. What can I do?</ol><ol>28. Where can I find information about SAP services for SAP MaxDB?</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, MaxDB, support, problems</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use a SAP MaxDB database or a SAP liveCache.<br /><br />For more FAQ notes for SAP MaxDB/liveCache, see <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/maxdb/3362173553.html\">SAP MaxDB FAQ Notes</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>Who do I contact for what problems?<br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/16481\">16481</a>, which contains the SAP contacts (in Germany).<br /><br /></li>\r\n<li>What are the telephone and fax numbers of the Support Centers?<br />The telephone and fax numbers are listed in SAP Note <a target=\"_blank\" href=\"/notes/560499\">560499</a>.<br /><br /></li>\r\n<li>What is consultation, what is support?<br />For more information, see Notes <a target=\"_blank\" href=\"/notes/83020\">83020</a> and <a target=\"_blank\" href=\"/notes/1054121\">1054121</a>.<br />If you have consulting questions, you can go to the SAP Community Network (SCN), our platform for the exchange of ideas and technical knowledge concerning SAP and our SAP MaxDB database. There you can participate in current discussions or introduce new topics:&#x00A0;<br /><a target=\"_blank\" href=\"https://community.sap.com/topics/maxdb\">SAP MaxDB Community</a><br /><a target=\"_blank\" href=\"https://answers.sap.com/tags/571267881358674984386133031541144\">&#xFEFF;SAP MaxDB Blogs and Questions</a>&#xFEFF;<br /><br /></li>\r\n<li>Under which component do I open a customer message for SAP MaxDB?<br />Report problems with SAP MaxDB under the BC-DB-SDB component.<br /><br /></li>\r\n<li>Under which component do I open a customer message for SAP liveCache?<br />Report problems with the liveCache under the BC-DB-LVC component.<br /><br /></li>\r\n<li>Under which component do I open a customer message for the SAP liveCache application?<br />Assign the problems with the SAP liveCache application (LCA) to the database problems and report them under the component BC-DB-LCA.  For information about the problem descriptions of liveCache Application (LCA) problems, refer to SAP Note <a target=\"_blank\" href=\"/notes/1144828\">1144828</a> (internal only). <br /><br /></li>\r\n<li>Under which component do I open a customer message for a BW system with SAP MaxDB?<br />Report problems with BW systems on SAP MaxDB under the BW-SYS-DB-SDB component.<br /><br /></li>\r\n<li>Under which component do I open a customer message for a SAP Content Server?<br />Report problems with a SAP Content Server under the BC-SRV-KPR-CS component.<br /><br /></li>\r\n<li>In what language can I report a problem in the customer message system?<br />See SAP Notes <a target=\"_blank\" href=\"/notes/873046\">873046</a> and <a target=\"_blank\" href=\"/notes/32736\">32736</a>.<br /><br /></li>\r\n<li>What priority must I select for my problem?<br />For more information, refer to SAP Note <a target=\"_blank\" href=\"/notes/67739\">67739</a>.<br />In addition, you should also refer to SAP Note <a target=\"_blank\" href=\"/notes/376997\">376997</a> when you use BW systems.<br /><br /></li>\r\n<li>Does SAP also provide support at the weekends?<br />If you encounter a production standstill (that is, when you open a message with a \"Very high\" priority), you also receive support from SAP at the weekends. For more information, refer to SAP Note <a target=\"_blank\" href=\"/notes/46742\">46742</a>.<br /><br /></li>\r\n<li>What information must I report to Support so that the cause of the problem can be analyzed promptly?<br />For a first analysis, Support requires the following information:<br />-&gt; SAP MaxDB database version<br />-&gt; Information about the installation (for example, MSCS, VMWare, cloud or similar)<br />-&gt; Result of a current database parameter check (see SAP Note <a target=\"_blank\" href=\"/notes/1111426\">1111426</a>)<br />-&gt; knldiag (lower than SAP MaxDB 7.7) KnlMsg (as of Version 7.7)<br />-&gt; knldiag.err (lower than SAP MaxDB 7.7) KnlMsgArchive (as of Version 7.7)<br />As of SAP MaxDB Version 7.7, the files <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;KnlMsg&#xFEFF;</em></span> and <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;KnlMsgArchive&#xFEFF;</em></span> exist in PseudoXML format and must first be converted with the tool <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">PROTCONV</span> so that they can then be analyzed.<br />The following DBM command packs all of the important log files (including <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;KnlMsg&#xFEFF;</em></span> and <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;KnlMsgArchive&#xFEFF;</em></span>) into an archive (<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;diagpkg.tgz&#xFEFF;</em></span>). When this occurs, an automatic conversion takes place:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbmcli -d &lt;database_name&gt; -u &lt;dbm_operator&gt;,&lt;password&gt; diag_pack&#xFEFF;</em></span><br />For more information about this, see <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/maxdb/3362173560.html\">HowTo - New knldiag Message File Format</a>.<br /><br /></li>\r\n<li>How can I determine which database version or SAP liveCache version is used? <br />The SAP system has the operational state ONLINE:<br />You obtain this information via the DBA Cockpit (transaction DBACOCKPIT), the database assistant (transaction DB50), or the liveCache assistant (transaction LC10) -&gt; DB Version.<br />The SAP system has the operational state OFFLINE:<br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;dbmcli db_enum instancename &lt;database_name&gt; version&#xFEFF;</em></span><br /><br /></li>\r\n<li>How do I determine which precompiler runtime I use?<br />The precompiler runtime is installed on every application server with the SAP MaxDB client package. You can determine the version of the precompiler runtime from the developer trace files (dev_w*) using transaction SM50 by selecting a work process and choosing the path <em>Process -&gt; Trace -&gt; \"Display file\"</em>. In the files, the precompiler runtime version is located under <em>\"Precompiler Runtime: C-PreComp .......</em><br />See also SAP Note <a target=\"_blank\" href=\"/notes/822239\">822239</a>.<br /><br /></li>\r\n<li>How do I determine which SQLDBC version is in use?<br />The SQLDBC interface is installed on every application server as of SAP Version 7.0. To determine the SQLDBC version in the developer trace files ( dev_w*) in transaction SM50, select a work process and choose <em>Process -&gt; Trace -&gt; \"Display File\"</em>. In the files, the SQLDBC version is displayed under <em>SQLDBC Library Version: libSQLDBC.....</em><br />See also SAP Note <a target=\"_blank\" href=\"/notes/822239\">822239</a>.<br /><br /></li>\r\n<li>What log files must I add to the message if I have problems with backing up the database?<br />See SAP Note <a target=\"_blank\" href=\"/notes/820824\">820824</a>: FAQ SAP MaxDB contains information about SAP MaxDB log files and about how to determine the run directory.<br /><br /></li>\r\n<li>What files does SAP MaxDB Support require if the database has crashed and was restarted again in the meantime?<br />If you have restarted the database after a crash, the file knldiag or KnlMsg (as of SAP MaxDB 7.7) was already overwritten during the restart. The file knldiag or KnlMsg was implicitly copied to knldiag.old or KnlMsg.old. If the file knldiag.old or knlMsg.old contains the crash, make the file knldiag.old or knlMsg.old available to Support.<br />If the file knldiag.old or KnlMsg.old has already been overwritten, the logs in the subdirectory <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;&lt;run_directory&gt;/DIAGHISTORY/&lt;time_stamp&gt;&#xFEFF;</em></span> can be used for the analysis.<br />See also SAP Note <a target=\"_blank\" href=\"/notes/353158\">353158</a>.<br />The archive diagpkg.tgz that was created using the DBM command <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;diag_pack&#xFEFF;</em></span> contains all subdirectories of DIAGHISTORY that are no older than one week.<br /><br /></li>\r\n<li>How do I set up a Telnet connection to my system?<br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/37001\">37001</a>.<br /><br /></li>\r\n<li>How do I set up an SSH connection?<br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/1275351\">1275351</a>.<br /><br /></li>\r\n<li>How do I set up an SAPDB connection to my system?<br />See SAP Notes <a target=\"_blank\" href=\"/notes/202344\">202344</a> and <a target=\"_blank\" href=\"/notes/939303\">939303</a>.<br /><br /></li>\r\n<li>Why do I need an SAPDB connection?<br />The SAPDB connection enables SAP Support to log onto your system with Database Studio or the database analyzer.<br />Save the logon data for the SAPDB connection for the user SAPR3 or SAP&lt;SID&gt;  and the DBM user, for example, CONTROL.<br /><br /></li>\r\n<li>How do I enter the logon data in the message?<br />Refer to SAP Note <a target=\"_blank\" href=\"/notes/508140\">508140</a>.<br /><br /></li>\r\n<li>Where can I find the support guide?<br />You can access the SAP MaxDB support guide: <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/maxdb/3362173996.html\">SAP MaxDB Support Guide</a><br />For general information about the database, refer to the SAP MaxDB documentation, as described in SAP Note <a target=\"_blank\" href=\"/notes/767598\">767598</a>.<br /><br /></li>\r\n<li>What is a PTS number?<br />PTS (Problem Tracking System) is the internal SAP MaxDB error messaging system that contains all software errors and new features of SAP MaxDB. Every problem is entered under a number, which is the PTS number. These PTS numbers are listed in some notes. If you want to know whether a certain problem has already been solved, you can search with the number in the WebPTS. You can also search with search terms in the WebPTS to find out if errors have already been reported and corrected.<br /><br /></li>\r\n<li>Where do I find the WebPTS?<br />You can access the WebPTS with the following link: <br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#xFEFF;http://maxdb.sap.com/webpts&#xFEFF;</em></span><br /><br /></li>\r\n<li>When should I forward a message to Development Support?<br />Messages should only be forwarded to Development Support in the following cases:<br />- the note search was unsuccessful<br />- the search for similar messages was unsuccessful<br />- the search for suitable PTS messages was unsuccessful<br />- a solution could not be found, help from outside is required in order to progress<br />Development Support can only continue with the analysis if the message contains all log files and information specified in this note. The connections to the customer system must be open and we must have the logon data.<br />Special scenario: The database has crashed.<br />If the crash has occurred in the latest SAP MaxDB version and you cannot not find any adequate notes and PTS numbers, you should immediately forward the message to Development Support. The connections to the customer system must be open and we must have the logon data.<br /><br /></li>\r\n<li>A database recovery is required, but not possible. What can I do?<br />We recommend that our customers create backups regularly, check these backups for usability and perform database consistency checks. <br />The colleagues in charge of the system administration are responsible for the implementation of these recommendations.<br />SAP is NOT responsible for the implementation of these recommendations. However, SAP still checks the adherence to the recommendations regular in spot checks. For example, CCMS (Computing Center Management System) and many SAP service warnings and alerts show if there are problems with the backups or the backup strategy.<br />The repair of damaged databases is NOT covered by the maintenance fee, independently of the maintenance contract (for example, standard maintenance, Enterprise Support, MaxAttention, TopMaintenance, and so on). For the repair of a damaged database, SAP charges a fee based on the regulations for remote consulting.<br />For more information, see the following notes:<br /><a target=\"_blank\" href=\"/notes/26837\">26837</a>: SAP MaxDB: Data corruption, error -9026/-9028<br /><a target=\"_blank\" href=\"/notes/1116190\">1116190</a>: Repairs of database damages by SAP Support<br /><a target=\"_blank\" href=\"/notes/839333\">839333</a>: FAQ: SAP MaxDB error diagnosis -&gt; corrupt data pages<br /><br /></li>\r\n<li>Where can I find information about SAP services for SAP MaxDB?<br />SAP offers a whole range of services: <a target=\"_blank\" href=\"https://help.sap.com/docs/SUPPORT_CONTENT/maxdb/3362174171.html\">SAP MaxDB Services</a>&#x00A0;</li>\r\n</ol></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-LVC (liveCache)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D024848)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D019124)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000826037/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000826037/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "939303", "RefComponent": "XX-SER-NET", "RefTitle": "SAP DB connection:", "RefUrl": "/notes/939303"}, {"RefNumber": "873046", "RefComponent": "XX-TRANSL-EN", "RefTitle": "Processing customer messages in English", "RefUrl": "/notes/873046"}, {"RefNumber": "839333", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB error diagnosis -> corrupt data pages", "RefUrl": "/notes/839333"}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "767598", "RefComponent": "BC-DB-SDB", "RefTitle": "Available SAP MaxDB documentation", "RefUrl": "/notes/767598"}, {"RefNumber": "560499", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer Interaction Center: Hotline - Email - Chat", "RefUrl": "/notes/560499"}, {"RefNumber": "46742", "RefComponent": "BC-UPG", "RefTitle": "Priority 1 Support generally available", "RefUrl": "/notes/46742"}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997"}, {"RefNumber": "37001", "RefComponent": "XX-SER-NET", "RefTitle": "Telnet connection to customer systems", "RefUrl": "/notes/37001"}, {"RefNumber": "353158", "RefComponent": "BC-DB-SDB", "RefTitle": "What you must save when the database has crashed", "RefUrl": "/notes/353158"}, {"RefNumber": "32736", "RefComponent": "XX-SER-GEN", "RefTitle": "24 hour support not possible in this language", "RefUrl": "/notes/32736"}, {"RefNumber": "26837", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB: Data corruption, error -9026/-9028", "RefUrl": "/notes/26837"}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344"}, {"RefNumber": "1275351", "RefComponent": "XX-SER-NET", "RefTitle": "SSH connection to customer systems", "RefUrl": "/notes/1275351"}, {"RefNumber": "1144828", "RefComponent": "BC-DB-LCA", "RefTitle": "Preclarification for problems reported to BC-DB-LCA", "RefUrl": "/notes/1144828"}, {"RefNumber": "1116190", "RefComponent": "BC-DB-SDB", "RefTitle": "Handling of database corruptions by SAP Support", "RefUrl": "/notes/1116190"}, {"RefNumber": "1111426", "RefComponent": "BC-DB-SDB", "RefTitle": "Database parameter check for SAP MaxDB/liveCache", "RefUrl": "/notes/1111426"}, {"RefNumber": "1054121", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "The SAP Ecosystem in a Nutshell", "RefUrl": "/notes/1054121"}, {"RefNumber": "83020", "RefComponent": "XX-SER-GEN", "RefTitle": "What is Support – What is Consulting:  On Premise Solutions", "RefUrl": "/notes/83020"}, {"RefNumber": "67739", "RefComponent": "XX-SER-GEN", "RefTitle": "Priority of problem cases", "RefUrl": "/notes/67739"}, {"RefNumber": "508140", "RefComponent": "XX-SER-SAPSMP-SUP", "RefTitle": "Customer incident - Customer logon data", "RefUrl": "/notes/508140"}, {"RefNumber": "16481", "RefComponent": "XX-SER-GEN", "RefTitle": "Contacts at SAP (Germany)", "RefUrl": "/notes/16481"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2494851", "RefComponent": "BC-DB-SDB", "RefTitle": "MaxDB / liveCache crashed - How to search for known program errors?", "RefUrl": "/notes/2494851 "}, {"RefNumber": "3382437", "RefComponent": "BC-DB-SDB", "RefTitle": "<PERSON><PERSON> der SAP MaxDB-Datei  diagpkg.tgz kommt es zu Fehlern", "RefUrl": "/notes/3382437 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "820824", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB/liveCache technology", "RefUrl": "/notes/820824 "}, {"RefNumber": "376997", "RefComponent": "BW", "RefTitle": "BW-customer incidents with priority 1 (very high)", "RefUrl": "/notes/376997 "}, {"RefNumber": "1116190", "RefComponent": "BC-DB-SDB", "RefTitle": "Handling of database corruptions by SAP Support", "RefUrl": "/notes/1116190 "}, {"RefNumber": "202344", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "Set up SAP DB connection", "RefUrl": "/notes/202344 "}, {"RefNumber": "37001", "RefComponent": "XX-SER-NET", "RefTitle": "Telnet connection to customer systems", "RefUrl": "/notes/37001 "}, {"RefNumber": "1275351", "RefComponent": "XX-SER-NET", "RefTitle": "SSH connection to customer systems", "RefUrl": "/notes/1275351 "}, {"RefNumber": "1144828", "RefComponent": "BC-DB-LCA", "RefTitle": "Preclarification for problems reported to BC-DB-LCA", "RefUrl": "/notes/1144828 "}, {"RefNumber": "1100358", "RefComponent": "BC-DB-SDB", "RefTitle": "Guideline for processing BC-DB-SDB/BC-DB-LVC Prio 1 messages", "RefUrl": "/notes/1100358 "}, {"RefNumber": "353158", "RefComponent": "BC-DB-SDB", "RefTitle": "What you must save when the database has crashed", "RefUrl": "/notes/353158 "}, {"RefNumber": "939303", "RefComponent": "XX-SER-NET", "RefTitle": "SAP DB connection:", "RefUrl": "/notes/939303 "}, {"RefNumber": "873046", "RefComponent": "XX-TRANSL-EN", "RefTitle": "Processing customer messages in English", "RefUrl": "/notes/873046 "}, {"RefNumber": "32736", "RefComponent": "XX-SER-GEN", "RefTitle": "24 hour support not possible in this language", "RefUrl": "/notes/32736 "}, {"RefNumber": "46742", "RefComponent": "BC-UPG", "RefTitle": "Priority 1 Support generally available", "RefUrl": "/notes/46742 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}