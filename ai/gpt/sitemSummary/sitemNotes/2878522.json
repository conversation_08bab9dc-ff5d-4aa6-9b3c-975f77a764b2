{"Request": {"Number": "2878522", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 250, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001128952020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002878522?language=E&token=BFE79C3CAA02F784B1B208F2B67F2874"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002878522", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2878522"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.07.2020"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-IT-FI"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-FI-IT"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "XX-CSC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-FI-IT", "value": "XX-CSC-IT-FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-IT-FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2878522 - RAITAR01,RAITAR02,RAIDIT_DEPR replaced by new ACR report IT_FXDAST_REG"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>As of&#160;S/4HANA OP2020 the former asset accounting reports</p>\r\n<ul>\r\n<li>RAITAR01 'Asset Register (Italy)'</li>\r\n<li>RAITAR02 'Asset Register by Third Party Locations (IT)'</li>\r\n<li>RAIDIT_DEPR 'Italy - Depreciation Comparison Report'</li>\r\n</ul>\r\n<p>are replaced by the new Advanced Compliance Report (ACR) report IT_FXDAST_REG.</p>\r\n<p>The former reports will be disabled in&#160;S/4HANA OP2020 after a retention period of 6 months. The end of support date is January 31st, 2021.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">RAITAR01,&#160;RAITAR02,&#160;RAIDIT_DEPR,&#160;AR23,&#160;AR24,&#160;IT_FXDAST_REG, ACR, T088</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">Modernization. The new report</p>\r\n<ul>\r\n<li>has been optimized: data selection is based on CDS views - it is no longer based on logical database ADA</li>\r\n<li>can be launched from&#160;the FIORI launchpad</li>\r\n<li>allows output into PDF file</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Use please the new ACR report <strong>IT_FXDAST_REG</strong> in releases &gt;=&#160;S/4HANA OP2020:</p>\r\n<ul>\r\n<li>In <strong>FIORI launchpad</strong>, assign please predefined role&#160;SAP_BR_AA_ACCOUNTANT_IT to your user. The tile \"Run Compliance Report, Italy AA Reporting\" will be added into your launchpad. This tile opens \"Run Advance Compliance Report\" FIORI application. Enter&#160;IT_FXDAST_REG into the field Report name. For more information, check please documentation of ACR report IT_FXDAST_REG.</li>\r\n<li>The <strong>ACR configuration</strong>&#160;must be maintained in the IMG:&#160;Financial Accounting(New)/Advanced Compliance Reporting/Setting Up Your Compliance Reporting. In the IMG documentation you can navigate to country IT and to report&#160;IT_FXDAST_REG where the necessary settings are described.</li>\r\n<li>The <strong>Key Figure Group</strong> with <strong>Key Figure Hierarchy</strong> must be maintained in the IMG: Financial Accounting(New)/Asset Accounting/Information system/Asset History Sheet/Define Key Figure Groups for Asset History Sheet (Fiori). The Key Figure Group&#160;<strong>AHS_HRY_IT</strong> with Key Figure Hierarchy&#160;<strong>FXDAST_REG</strong> has been pre-configured for&#160;ACR report IT_FXDAST_REG. If you prefer to create your own Key Figure Group and Hierarchy, notice please following:</li>\r\n<ul>\r\n<li>The Key Figure Hierarchy is supposed to be created with maximum 1 hierarchy level. It is expected that hierarchy nodes 01,02 .. 08 are defined. These hierarchy nodes correspond to 8 amount fields exported into PDF file. Additionally, some other hierarchy nodes may be defined. These nodes are not displayed in the PDF file and can be used for analytical purposes only.</li>\r\n<li>Update the IDs of your Key Figure Group and your Key Figure Hierarchy in the ACR configuration.</li>\r\n</ul>\r\n</ul>\r\n<p>The configuration related to former SAPGUI reports is not relevant anymore and does not have to be configured:</p>\r\n<ul>\r\n<li>The configuration step \"Define Asset Register for Italy\" (the customizing table T088), used by&#160;RAITAR01. The fields&#160;Asset description, Location, License Plate and Acquisition Year are always displayed in the fixed asset header area of the PDF file produced by the new ACR report.</li>\r\n</ul>\r\n<p>The features of the new ACR report:</p>\r\n<ul>\r\n<li>Similarly as the previous SAPGUI reports, the new ACR report can be executed for multiple depreciation areas. This feature allows&#160;users&#160;to compare values from multiple depreciation areas easily.&#160;</li>\r\n<li>The \"Analyze data\" (premium feature) allow users to analyze data selected by report, export into MS Excel,&#160;and so on. You can also display data according to predefined key figure hierarchy and reconcile them with the output of the PDF file.</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I077797)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I356309)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002878522/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002878522/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3027951", "RefComponent": "FI-LOC-AA-IT", "RefTitle": "RAITAR01,RAITAR02,RAIDIT_DEPR: technical disablement as of S/4HANA OP2020", "RefUrl": "/notes/3027951 "}, {"RefNumber": "2480067", "RefComponent": "FI-LOC-SRF-RUN", "RefTitle": "Replacement of Existing Legal Reports with 'SAP Document and Reporting Compliance - Statutory Reports'", "RefUrl": "/notes/2480067 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}