{"Request": {"Number": "152725", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 501, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014679222017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000152725?language=E&token=155AC33D4AF28D00D337F9AE58B2A616"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000152725", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000152725/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "152725"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 45}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.06.2002"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "152725 - Collective note: Subsequent settlement (Purch.) Release 4.5"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>-- Collective note: Problems and errors in&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;subsequent settlement (purchasing)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; --<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Release 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br />--&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Version: December 20, 2001&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;--<br /></p> <b>***********************************************************************</b><br /> <b>* European Monetary Union (important):&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 480984 Currency conversion, message MN 471</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(rebate arrangement currency cannot be converted)</b><br /> <b>*</b><br /> <b>* 439493 Incorrect document currency w/ billing via billing interface *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(incorrect posting in Financial Acctng, currency exchanged)&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 452365 Rebate arrangement currency not copied to condition records&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(undesired currency in condition record)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 437429 Message SG105 List settlement documents, setup incomes&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(Revenues in Financial Accounting and/or S074 incorrect)&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 400432 Expiring currencies, rebate arrangmnt maint. (purch., sales) *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>***********************************************************************</b><br /> <b>* Notes you must implement beforehand since it might be complicated&#x00A0;&#x00A0; *</b><br /> <b>* to correct the consequences:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 422649 Error in validity display (agrmt maintenance)- deletion&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 427415 No update for pro forma documents into LIS&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 398739 Incorrect conversion of EMU currencies - Part 2&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This note must be implemented (even by customers not&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;affected by the currency changeover)!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 400893 Detailed statement, message MN 514&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 408521 MN 495 (Update simulation), MN 514 (detailed statement)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 394619 Reset amounts are not cancelled with the reversal settlement *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;document.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 315769 Subs. settlement: Retrospective business volume update&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Goods receipt pricing&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 368273 Income not updated for total income 0 settlement&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Incorrect settlement&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 357600 LIS Update agency bus., BV subs.settl. incomplete&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Business volume data is lost.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 355952 Update simulatn causes multiple updating of bus. vol&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement of incorrect business volume data and,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;consequently, of incomes!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 335654 Problem with new price determination for the GR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 305388 Subs.Settlmnt: Subs.busi.vol.update in wrong period&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 303047 RMCENEUA - Error in selection by info structures&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Settlement of incorrect business volume data and,&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;consequently, of incomes!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175341 Subsq. Settlement: Multiple update of incomes in customer&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;billing documents.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Otherwise, the wrong incomes might be settled!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* In Release 4.5B (!), you must apply Hot Package 01 or implement&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>* Note 139018. Otherwise, multiple business volume updates can&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* occur.&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 204337 Subsq. Settlement: Message MN 514, business volume update&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;goods receipt&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 180434 Subsq. Settlement: Verification of rel. documents before&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;archiving&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175160 PURCHIS - GR for several PO items with PO generation&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 179864 PURCHIS - commitments - problems during IR or GR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 175138 PURCHIS - GR with delivery generation - Volume-based rebate&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 160440 Subs. settlement: Wrong update of PI recompilation&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 151398 PURCHIS - double update for delivery costs from several POs&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>* 206060 Ind. 'Subseq. settlement', deviating vendor data&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; *</b><br /> <b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See also Note no. 214146!&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /><b>*&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Releases 4.5A and 4.5B&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;*</b><br /> <b>***********************************************************************</b><br /> <p><br />This note only applies to Releases 4.5A and 4.5B. For Release 3.0A to 3.1I, refer to the separate Note no. 40147 and for Release 4.0B to Note no. 104668; for Release 4.6, refer to Note no. 183379.<br /></p> <b>General information</b><br /> <p>This note should primarily point out the most frequent problems and errors in connection with subsequent settlement (purchasing). It should help to analyze problems which often occur and possibly solve them.<br />You can find a list of all notes on the topic subsequent settlement which refer to known problems in section \"Related notes\". All notes to solve program errors will be delivered immediately with the next available Hot Package.</p> <b>Handling</b><br /> <p>If you have problems with subsequent settlement (purchasing), you can first try to solve the problem yourself by using this note. Check to see whether one of these notes refers to your problem. Read the note and carry out the checks specified there. In it you may find references to notes which may solve the errors.<br />Please note that you can access various informational consulting notes on the topics purchasing, goods receipt, etc., using search criteria MM-PUR* (component) und FAQ (search text).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement, volume-based rebate, collective note<br />Transactions MEB1, MEB2, MEB3, MEB4, MEB6, MEB8, MEB9, MCE+, MEBA<br />Programs SAPMV13A, SAPMNB01, RWMBON01, RWMBON03, RWMBON04, RWMBON06, RWMBON08, RMCE0900<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. Notes on problem solution/consulting</OL> <p><br />167284 Subsequent settlement: Some general notes (Q&amp;As and Customizing)<br /><br />75655 Error message VK358: \"PurchOrg &amp; with CoCode &amp; is different from &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CoCode&amp;\".<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Underlying problem:</p> <UL><UL><LI>You work with several company codes, that is, your purchasing organization is not assigned to a certain company code. See also Note no. 153694.</LI></UL></UL> <p><br />153694 Subsequent settlement: Credit-side or debit-side settlement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; type</p> <UL><UL><LI>Note on the importance of the settlement type</LI></UL></UL> <p><br />72199 Subs. Settlement: Update business volume data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No business volume data update is carried out for a document.</LI></UL></UL> <UL><UL><LI>For a document, a business volume data update is carried out but the scale basis or the condition basis has value 0.</LI></UL></UL> <p><br />112413 Subsequent settlement: units of measure<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Possible problems when you use units of measure.</LI></UL></UL> <p><br />113031 Subsequent settlement: Taxes<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Tax treatment (calculation) within the process of subsequent settlement</LI></UL></UL> <p><br />77258 Subs. Settlement: Required fields settlement document<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>When you settle an agreement, the system generates the following error message:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00055 \"Required entry not made\" or<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;00344 \"No batch input data for screen SAPMM08R &amp;\"<br /><br />80233 Debit-side settlement: Customizing, error messages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>Error messages of message class VF</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure for SD Customizing for subsequent settlement purposes</LI></UL></UL> <p><br />73214 Subsequent settlement: Retrospective compilation/recompilation of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;business volume data<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>When you compile business volume data subsequently no update is carried out.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Notes:</p> <UL><UL><LI>Procedure when you use the function</LI></UL></UL> <UL><UL><LI>Functional restrictions of the function</LI></UL></UL> <p><br />381831 Consignment processing and subsequent settlement<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Frequent symptoms:</p> <UL><UL><LI>No update of business volume for settlement documents in consignment processing</LI></UL></UL> <p><br />413786 Release notes 4.5 on subsequent settlement incomplete</p> <UL><UL><LI>External data transfer by means of pro forma vendor billing document</LI></UL></UL> <UL><UL><LI></LI></UL></UL> <p>333914 Subsequent settlement: Performance (composite note)<br /></p> <OL>2. Notes on known problems:</OL> <p>See \"Related notes\".<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023678)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000152725/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000152725/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000152725/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199"}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040"}, {"RefNumber": "502747", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement via credit memo: Message MN178", "RefUrl": "/notes/502747"}, {"RefNumber": "490728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Material description is confused in rebate arrangements", "RefUrl": "/notes/490728"}, {"RefNumber": "489318", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/489318"}, {"RefNumber": "485429", "RefComponent": "LO-AB-RS", "RefTitle": "Subsequent settlement: Message SG105", "RefUrl": "/notes/485429"}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130"}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984"}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695"}, {"RefNumber": "457035", "RefComponent": "LO-AB-RS", "RefTitle": "Incorrect doc. status if document not relevant to accounting", "RefUrl": "/notes/457035"}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365"}, {"RefNumber": "452056", "RefComponent": "LO-AB", "RefTitle": "Error handling when posting agency documents", "RefUrl": "/notes/452056"}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331"}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313"}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493"}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324"}, {"RefNumber": "437976", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Price determ. in the past and periodic rebate arrangements", "RefUrl": "/notes/437976"}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429"}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199"}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752"}, {"RefNumber": "427415", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for proforma documents into LIS", "RefUrl": "/notes/427415"}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033"}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649"}, {"RefNumber": "413786", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Release notes 4.5 for subsequent settlement incomplete", "RefUrl": "/notes/413786"}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521"}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734"}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898"}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893"}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432"}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118"}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739"}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955"}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334"}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673"}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619"}, {"RefNumber": "394577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Vendor billing document not created for posting block", "RefUrl": "/notes/394577"}, {"RefNumber": "394082", "RefComponent": "LO-AB", "RefTitle": "Reference fields are not transferred to accounting", "RefUrl": "/notes/394082"}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066"}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055"}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793"}, {"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716"}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626"}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044"}, {"RefNumber": "386460", "RefComponent": "LO-AB", "RefTitle": "Pro forma documents: updating cash management data", "RefUrl": "/notes/386460"}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157"}, {"RefNumber": "385052", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect search help for the 'Rebate arrangement' field", "RefUrl": "/notes/385052"}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006"}, {"RefNumber": "383600", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income updating from settlement document not executed", "RefUrl": "/notes/383600"}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939"}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829"}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749"}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831"}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737"}, {"RefNumber": "371643", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of income data aborts with error GENERATE_SUBP", "RefUrl": "/notes/371643"}, {"RefNumber": "368273", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income not updated for total income 0", "RefUrl": "/notes/368273"}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511"}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295"}, {"RefNumber": "364754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect income update frm vendor billing docs. f. invoices", "RefUrl": "/notes/364754"}, {"RefNumber": "364725", "RefComponent": "LO-AB-RS", "RefTitle": "Cancelling settlements of arrangements", "RefUrl": "/notes/364725"}, {"RefNumber": "360194", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Compar.of bus.vol.:dump'Raise exceptn' Txtedit ctrl", "RefUrl": "/notes/360194"}, {"RefNumber": "355952", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating simultn causes multiple updating of business volume", "RefUrl": "/notes/355952"}, {"RefNumber": "353614", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messge MN582 displayed in rebate arrangmnt maintenance twice", "RefUrl": "/notes/353614"}, {"RefNumber": "353546", "RefComponent": "LO-AB-RS", "RefTitle": "Settlement of incorrect incomes via vendor billing documents", "RefUrl": "/notes/353546"}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231"}, {"RefNumber": "335654", "RefComponent": "MM-PUR-PO", "RefTitle": "Incorr. val.w/ new price determintn for GR (Pric.date cat.5)", "RefUrl": "/notes/335654"}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914"}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145"}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142"}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921"}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341"}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705"}, {"RefNumber": "324686", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN145 during rebate arrangement settlement", "RefUrl": "/notes/324686"}, {"RefNumber": "324328", "RefComponent": "LO-AB", "RefTitle": "Error handling durg document creatn via API methods", "RefUrl": "/notes/324328"}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279"}, {"RefNumber": "320728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement partners are not copied", "RefUrl": "/notes/320728"}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666"}, {"RefNumber": "316723", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/316723"}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290"}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769"}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173"}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806"}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388"}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047"}, {"RefNumber": "301592", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/Repair reports, Rel. 4.5B", "RefUrl": "/notes/301592"}, {"RefNumber": "300959", "RefComponent": "LO-AB", "RefTitle": "Incorrect LIS update for weights, volume, points", "RefUrl": "/notes/300959"}, {"RefNumber": "216051", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN543, update agency business", "RefUrl": "/notes/216051"}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716"}, {"RefNumber": "212330", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rounding taxes in settlement at header level", "RefUrl": "/notes/212330"}, {"RefNumber": "212327", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement simulation conditions on header level incorrect", "RefUrl": "/notes/212327"}, {"RefNumber": "207020", "RefComponent": "MM-PUR-VM", "RefTitle": "Creating document index (RMEBEIN3), restart impossible", "RefUrl": "/notes/207020"}, {"RefNumber": "204337", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN514, business volume update goods receipt", "RefUrl": "/notes/204337"}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036"}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492"}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859"}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703"}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188"}, {"RefNumber": "200070", "RefComponent": "LO-AB", "RefTitle": "Info rec for variant/generc matl wrongly determined", "RefUrl": "/notes/200070"}, {"RefNumber": "197417", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message 06167 when settling an arrangement", "RefUrl": "/notes/197417"}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007"}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730"}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134"}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902"}, {"RefNumber": "186453", "RefComponent": "LO-AB", "RefTitle": "S111 entries for items missing", "RefUrl": "/notes/186453"}, {"RefNumber": "186247", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr. inc. distribution with recompilation / vend.bill.doc", "RefUrl": "/notes/186247"}, {"RefNumber": "184387", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debtor w/ incorrct account group creates short dump", "RefUrl": "/notes/184387"}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340"}, {"RefNumber": "181562", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation uses up too much main memory", "RefUrl": "/notes/181562"}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434"}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864"}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341"}, {"RefNumber": "175160", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS-GR for several PO items with PO generation", "RefUrl": "/notes/175160"}, {"RefNumber": "175138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "PURCHIS-GR w/ generation of dely-volme-based rebate", "RefUrl": "/notes/175138"}, {"RefNumber": "171902", "RefComponent": "MM-PUR", "RefTitle": "Update of update BV data for return item incorrect", "RefUrl": "/notes/171902"}, {"RefNumber": "169531", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN223 together with MN277", "RefUrl": "/notes/169531"}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284"}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897"}, {"RefNumber": "160440", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect update of provisions for accrued income recompiltn", "RefUrl": "/notes/160440"}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580"}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601"}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694"}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398"}, {"RefNumber": "150738", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN607 or MN597", "RefUrl": "/notes/150738"}, {"RefNumber": "148665", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement materials cannot be maintained", "RefUrl": "/notes/148665"}, {"RefNumber": "146739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Condition rate/percentage rate in document condtns incorrect", "RefUrl": "/notes/146739"}, {"RefNumber": "143301", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update with fixed amount calculation rule", "RefUrl": "/notes/143301"}, {"RefNumber": "142303", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlmnt w/ rebate arrngmnt currncy unequal to 2 dcml places", "RefUrl": "/notes/142303"}, {"RefNumber": "141623", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of business volume processes discnt in kind", "RefUrl": "/notes/141623"}, {"RefNumber": "140022", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Bsnss vol.cmprson:Termination for check of material", "RefUrl": "/notes/140022"}, {"RefNumber": "139148", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance problems when deleting PO items", "RefUrl": "/notes/139148"}, {"RefNumber": "139018", "RefComponent": "MM-IS", "RefTitle": "Incorrect update with formulas/requirements", "RefUrl": "/notes/139018"}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863"}, {"RefNumber": "135737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Posting provisions for accrued income w/ settled arrangemts", "RefUrl": "/notes/135737"}, {"RefNumber": "134822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Formulas not allowed in calculation schema", "RefUrl": "/notes/134822"}, {"RefNumber": "132071", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for collectve purchase order", "RefUrl": "/notes/132071"}, {"RefNumber": "131316", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in recompilation of incomes", "RefUrl": "/notes/131316"}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "388716", "RefComponent": "SD-BIL-IV-IF", "RefTitle": "Value dates in the general billing interface", "RefUrl": "/notes/388716 "}, {"RefNumber": "367511", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "structure MCKONA for LIS filled incompletely", "RefUrl": "/notes/367511 "}, {"RefNumber": "371643", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation of income data aborts with error GENERATE_SUBP", "RefUrl": "/notes/371643 "}, {"RefNumber": "384006", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Tax calculation, tax trigger, foreign vendors", "RefUrl": "/notes/384006 "}, {"RefNumber": "382829", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect repricing in subsequent settlement", "RefUrl": "/notes/382829 "}, {"RefNumber": "200703", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: Error in Standard Customizing", "RefUrl": "/notes/200703 "}, {"RefNumber": "437976", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Price determ. in the past and periodic rebate arrangements", "RefUrl": "/notes/437976 "}, {"RefNumber": "197007", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Incor decimals in amount of condit.rec (II)", "RefUrl": "/notes/197007 "}, {"RefNumber": "180434", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Validation of relevant documents before archiving", "RefUrl": "/notes/180434 "}, {"RefNumber": "212330", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rounding taxes in settlement at header level", "RefUrl": "/notes/212330 "}, {"RefNumber": "439493", "RefComponent": "SD-BIL-IV", "RefTitle": "Expiring currencies: Incorrect document currency", "RefUrl": "/notes/439493 "}, {"RefNumber": "167284", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "FAQs: Subsequent settlement (consulting, tips, Customizing)", "RefUrl": "/notes/167284 "}, {"RefNumber": "394577", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Vendor billing document not created for posting block", "RefUrl": "/notes/394577 "}, {"RefNumber": "485130", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Arrangement status for interim settlement set incorrectly", "RefUrl": "/notes/485130 "}, {"RefNumber": "489318", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Verbuchungsabbruch beim Anlegen von Absprachen", "RefUrl": "/notes/489318 "}, {"RefNumber": "503040", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement by credit memo: message MN227", "RefUrl": "/notes/503040 "}, {"RefNumber": "437199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlment per customer billing document w/o accntng document", "RefUrl": "/notes/437199 "}, {"RefNumber": "371737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Copy additional value days to credit memos", "RefUrl": "/notes/371737 "}, {"RefNumber": "392066", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Maximum values cannot be entered per calculation rule", "RefUrl": "/notes/392066 "}, {"RefNumber": "490728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Material description is confused in rebate arrangements", "RefUrl": "/notes/490728 "}, {"RefNumber": "452365", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement currency not copied to condition records", "RefUrl": "/notes/452365 "}, {"RefNumber": "153694", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Credit-side or debit-side settlement type", "RefUrl": "/notes/153694 "}, {"RefNumber": "502747", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement via credit memo: Message MN178", "RefUrl": "/notes/502747 "}, {"RefNumber": "301592", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis/Repair reports, Rel. 4.5B", "RefUrl": "/notes/301592 "}, {"RefNumber": "328145", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subs. updating of bus. vol. for deleted purchase order items", "RefUrl": "/notes/328145 "}, {"RefNumber": "325341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect exception processing of user exit LWBON003", "RefUrl": "/notes/325341 "}, {"RefNumber": "485429", "RefComponent": "LO-AB-RS", "RefTitle": "Subsequent settlement: Message SG105", "RefUrl": "/notes/485429 "}, {"RefNumber": "403734", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN242 upon settlement (different calculation rules)", "RefUrl": "/notes/403734 "}, {"RefNumber": "336231", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Importance of messages MN634, MN635 and MN636", "RefUrl": "/notes/336231 "}, {"RefNumber": "400432", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Expiring currencies, rebate arrangmnt maint. (purch., sales)", "RefUrl": "/notes/400432 "}, {"RefNumber": "458695", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Man. correction f. customer BillDoc income w/ partial settl.", "RefUrl": "/notes/458695 "}, {"RefNumber": "315769", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of bus.volume, price determtn goods receipt", "RefUrl": "/notes/315769 "}, {"RefNumber": "202036", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Termination TSV_TNEW_PAGE_ALLOC_FAILED when you settle", "RefUrl": "/notes/202036 "}, {"RefNumber": "383600", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income updating from settlement document not executed", "RefUrl": "/notes/383600 "}, {"RefNumber": "441313", "RefComponent": "LO-MD-RPC", "RefTitle": "Doc index generation: Variable key wrong for specific values", "RefUrl": "/notes/441313 "}, {"RefNumber": "457035", "RefComponent": "LO-AB-RS", "RefTitle": "Incorrect doc. status if document not relevant to accounting", "RefUrl": "/notes/457035 "}, {"RefNumber": "316290", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance of document index recompilation", "RefUrl": "/notes/316290 "}, {"RefNumber": "368273", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income not updated for total income 0", "RefUrl": "/notes/368273 "}, {"RefNumber": "452056", "RefComponent": "LO-AB", "RefTitle": "Error handling when posting agency documents", "RefUrl": "/notes/452056 "}, {"RefNumber": "425033", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem extending arrangements if arrangement contains EURO", "RefUrl": "/notes/425033 "}, {"RefNumber": "480984", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currency conversion, message MN 471", "RefUrl": "/notes/480984 "}, {"RefNumber": "396955", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/396955 "}, {"RefNumber": "382749", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Org. data of an arrangement not copied during creation", "RefUrl": "/notes/382749 "}, {"RefNumber": "328142", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - part 1", "RefUrl": "/notes/328142 "}, {"RefNumber": "445331", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Currencies can be deleted in the mass maintenance", "RefUrl": "/notes/445331 "}, {"RefNumber": "438324", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Addition to Note 400432", "RefUrl": "/notes/438324 "}, {"RefNumber": "422649", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in validity display (agrmt maintenance)- deletion", "RefUrl": "/notes/422649 "}, {"RefNumber": "437429", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message SG105 List settlement documents, setting up incomes", "RefUrl": "/notes/437429 "}, {"RefNumber": "353614", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Messge MN582 displayed in rebate arrangmnt maintenance twice", "RefUrl": "/notes/353614 "}, {"RefNumber": "324686", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN145 during rebate arrangement settlement", "RefUrl": "/notes/324686 "}, {"RefNumber": "306173", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect specification of period-specific condition", "RefUrl": "/notes/306173 "}, {"RefNumber": "212327", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement simulation conditions on header level incorrect", "RefUrl": "/notes/212327 "}, {"RefNumber": "201492", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance improvement list output recompilation", "RefUrl": "/notes/201492 "}, {"RefNumber": "195730", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN684 in rebate arrangement settlement", "RefUrl": "/notes/195730 "}, {"RefNumber": "190902", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Income always 0 (incorrect rounding)", "RefUrl": "/notes/190902 "}, {"RefNumber": "184340", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Euro Workbench: Incorrect input check of arrangements", "RefUrl": "/notes/184340 "}, {"RefNumber": "169531", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN223 together with MN277", "RefUrl": "/notes/169531 "}, {"RefNumber": "150738", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error messages MN607 or MN597", "RefUrl": "/notes/150738 "}, {"RefNumber": "146739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Condition rate/percentage rate in document condtns incorrect", "RefUrl": "/notes/146739 "}, {"RefNumber": "113240", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Alternative UM for qty-dependent materl volume rebate: VK070", "RefUrl": "/notes/113240 "}, {"RefNumber": "324705", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msgs MN781 or MN164 with settlement of rebate arrangement", "RefUrl": "/notes/324705 "}, {"RefNumber": "319666", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Deadlock with subsequent business volume recompilation", "RefUrl": "/notes/319666 "}, {"RefNumber": "200188", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message NAA234: Rebate arrangement maintenance", "RefUrl": "/notes/200188 "}, {"RefNumber": "186247", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorr. inc. distribution with recompilation / vend.bill.doc", "RefUrl": "/notes/186247 "}, {"RefNumber": "135737", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Posting provisions for accrued income w/ settled arrangemts", "RefUrl": "/notes/135737 "}, {"RefNumber": "394673", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Cond. record status incorrectly set when creating with ref.", "RefUrl": "/notes/394673 "}, {"RefNumber": "355952", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Updating simultn causes multiple updating of business volume", "RefUrl": "/notes/355952 "}, {"RefNumber": "327921", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect condition granter accepted in arrangmnt maintnance", "RefUrl": "/notes/327921 "}, {"RefNumber": "305806", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Provisions for income in price determination", "RefUrl": "/notes/305806 "}, {"RefNumber": "305388", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent business volume update in wrong period", "RefUrl": "/notes/305388 "}, {"RefNumber": "216051", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN543, update agency business", "RefUrl": "/notes/216051 "}, {"RefNumber": "204337", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN514, business volume update goods receipt", "RefUrl": "/notes/204337 "}, {"RefNumber": "197417", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message 06167 when settling an arrangement", "RefUrl": "/notes/197417 "}, {"RefNumber": "175341", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Multiple updating income when changing customer billing doc", "RefUrl": "/notes/175341 "}, {"RefNumber": "160440", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect update of provisions for accrued income recompiltn", "RefUrl": "/notes/160440 "}, {"RefNumber": "143301", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update with fixed amount calculation rule", "RefUrl": "/notes/143301 "}, {"RefNumber": "142303", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlmnt w/ rebate arrngmnt currncy unequal to 2 dcml places", "RefUrl": "/notes/142303 "}, {"RefNumber": "141623", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseqnt updatng of business volume processes discnt in kind", "RefUrl": "/notes/141623 "}, {"RefNumber": "131316", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in recompilation of incomes", "RefUrl": "/notes/131316 "}, {"RefNumber": "388626", "RefComponent": "MM-PUR-VM", "RefTitle": "Variant cannot be saved for reports", "RefUrl": "/notes/388626 "}, {"RefNumber": "207020", "RefComponent": "MM-PUR-VM", "RefTitle": "Creating document index (RMEBEIN3), restart impossible", "RefUrl": "/notes/207020 "}, {"RefNumber": "320728", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Rebate arrangement partners are not copied", "RefUrl": "/notes/320728 "}, {"RefNumber": "387044", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problems for distribution of income on tax code, plants", "RefUrl": "/notes/387044 "}, {"RefNumber": "333914", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequent settlement: performance collective note", "RefUrl": "/notes/333914 "}, {"RefNumber": "433752", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error when checking for release to accounting", "RefUrl": "/notes/433752 "}, {"RefNumber": "398739", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect conversion of EMU currencies - Part 2", "RefUrl": "/notes/398739 "}, {"RefNumber": "427415", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for proforma documents into LIS", "RefUrl": "/notes/427415 "}, {"RefNumber": "386460", "RefComponent": "LO-AB", "RefTitle": "Pro forma documents: updating cash management data", "RefUrl": "/notes/386460 "}, {"RefNumber": "400898", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation bus.vol. data, incomes: sel. by arrangemt type", "RefUrl": "/notes/400898 "}, {"RefNumber": "394082", "RefComponent": "LO-AB", "RefTitle": "Reference fields are not transferred to accounting", "RefUrl": "/notes/394082 "}, {"RefNumber": "364754", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect income update frm vendor billing docs. f. invoices", "RefUrl": "/notes/364754 "}, {"RefNumber": "400893", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Detailed statement, message MN514", "RefUrl": "/notes/400893 "}, {"RefNumber": "179864", "RefComponent": "MM-PUR-GF-CO", "RefTitle": "PURCHIS - commitments - problems during IR or GR", "RefUrl": "/notes/179864 "}, {"RefNumber": "200859", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Terms of Payment not transferred", "RefUrl": "/notes/200859 "}, {"RefNumber": "413786", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Release notes 4.5 for subsequent settlement incomplete", "RefUrl": "/notes/413786 "}, {"RefNumber": "215716", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Message MN186 (settlement of fixed amounts)", "RefUrl": "/notes/215716 "}, {"RefNumber": "391793", "RefComponent": "LO-AB", "RefTitle": "Tax: inconsistent amounts for TxCd E1, N1 ...", "RefUrl": "/notes/391793 "}, {"RefNumber": "396334", "RefComponent": "MM-PUR-VM", "RefTitle": "Selection according to document date in RMEBEIN3", "RefUrl": "/notes/396334 "}, {"RefNumber": "408521", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "MN495 (update simulation), MN514 (detailed statement)", "RefUrl": "/notes/408521 "}, {"RefNumber": "399118", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Msg NAA045 list output for arrangemts (RWMBON02, Trans.MEB5)", "RefUrl": "/notes/399118 "}, {"RefNumber": "335654", "RefComponent": "MM-PUR-PO", "RefTitle": "Incorr. val.w/ new price determintn for GR (Pric.date cat.5)", "RefUrl": "/notes/335654 "}, {"RefNumber": "316723", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error log for settlement run", "RefUrl": "/notes/316723 "}, {"RefNumber": "381831", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Consignment processing and subsequent settlement", "RefUrl": "/notes/381831 "}, {"RefNumber": "382939", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error in agreement maintenance debit-side settlmt accounting", "RefUrl": "/notes/382939 "}, {"RefNumber": "139148", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Performance problems when deleting PO items", "RefUrl": "/notes/139148 "}, {"RefNumber": "136863", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "New DB secondary index AEB f.table EKBO", "RefUrl": "/notes/136863 "}, {"RefNumber": "300959", "RefComponent": "LO-AB", "RefTitle": "Incorrect LIS update for weights, volume, points", "RefUrl": "/notes/300959 "}, {"RefNumber": "132071", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "No update for collectve purchase order", "RefUrl": "/notes/132071 "}, {"RefNumber": "181562", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Recompilation uses up too much main memory", "RefUrl": "/notes/181562 "}, {"RefNumber": "367295", "RefComponent": "MM-PUR-GF-PR", "RefTitle": "Missing tax code in subsequent settlement", "RefUrl": "/notes/367295 "}, {"RefNumber": "353546", "RefComponent": "LO-AB-RS", "RefTitle": "Settlement of incorrect incomes via vendor billing documents", "RefUrl": "/notes/353546 "}, {"RefNumber": "386157", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Shrt dmp CONVT_OVERFLOW or BCD_FIELD_OVERFLOW durng settlmnt", "RefUrl": "/notes/386157 "}, {"RefNumber": "134822", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Formulas not allowed in calculation schema", "RefUrl": "/notes/134822 "}, {"RefNumber": "200070", "RefComponent": "LO-AB", "RefTitle": "Info rec for variant/generc matl wrongly determined", "RefUrl": "/notes/200070 "}, {"RefNumber": "324328", "RefComponent": "LO-AB", "RefTitle": "Error handling durg document creatn via API methods", "RefUrl": "/notes/324328 "}, {"RefNumber": "392055", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Search help attachment in report RMCE0900", "RefUrl": "/notes/392055 "}, {"RefNumber": "72199", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Problem analysis update business volume data", "RefUrl": "/notes/72199 "}, {"RefNumber": "394619", "RefComponent": "SD-BIL-RB", "RefTitle": "Provisions f income n cancelled if cancelling settlement doc", "RefUrl": "/notes/394619 "}, {"RefNumber": "139018", "RefComponent": "MM-IS", "RefTitle": "Incorrect update with formulas/requirements", "RefUrl": "/notes/139018 "}, {"RefNumber": "385052", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect search help for the 'Rebate arrangement' field", "RefUrl": "/notes/385052 "}, {"RefNumber": "159580", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS - Commitments - IR no update in simulation", "RefUrl": "/notes/159580 "}, {"RefNumber": "154601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subsequnt settlmnt:external data transfr cancellatn", "RefUrl": "/notes/154601 "}, {"RefNumber": "364725", "RefComponent": "LO-AB-RS", "RefTitle": "Cancelling settlements of arrangements", "RefUrl": "/notes/364725 "}, {"RefNumber": "360194", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Compar.of bus.vol.:dump'Raise exceptn' Txtedit ctrl", "RefUrl": "/notes/360194 "}, {"RefNumber": "303047", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "RMCENEUA - Error in selection by info structures", "RefUrl": "/notes/303047 "}, {"RefNumber": "324279", "RefComponent": "SD-BIL-RB", "RefTitle": "Rebate: Displ of change docs f conditions is too slow", "RefUrl": "/notes/324279 "}, {"RefNumber": "184387", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Debtor w/ incorrct account group creates short dump", "RefUrl": "/notes/184387 "}, {"RefNumber": "171902", "RefComponent": "MM-PUR", "RefTitle": "Update of update BV data for return item incorrect", "RefUrl": "/notes/171902 "}, {"RefNumber": "175138", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "PURCHIS-GR w/ generation of dely-volme-based rebate", "RefUrl": "/notes/175138 "}, {"RefNumber": "175160", "RefComponent": "MM-PUR-GF-LIS", "RefTitle": "PURCHIS-GR for several PO items with PO generation", "RefUrl": "/notes/175160 "}, {"RefNumber": "191134", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Subseq. settlmt accoun.:Short dump DATE_AFTER_RANGE", "RefUrl": "/notes/191134 "}, {"RefNumber": "186453", "RefComponent": "LO-AB", "RefTitle": "S111 entries for items missing", "RefUrl": "/notes/186453 "}, {"RefNumber": "162897", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Incorrect detailed statement", "RefUrl": "/notes/162897 "}, {"RefNumber": "151398", "RefComponent": "MM-IS-PU", "RefTitle": "PURCHIS -double update for deliv.cst fr several POs", "RefUrl": "/notes/151398 "}, {"RefNumber": "148665", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Settlement materials cannot be maintained", "RefUrl": "/notes/148665 "}, {"RefNumber": "140022", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Bsnss vol.cmprson:Termination for check of material", "RefUrl": "/notes/140022 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}