{"Request": {"Number": "2654430", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 453, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001104592018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002654430?language=E&token=DE1EFD69EDACAC33E35E699BBF4F034F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002654430", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002654430/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2654430"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 23}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.07.2019"}, "SAPComponentKey": {"_label": "Component", "value": "CRM-MD-BP-IF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Data Exchange CRM Online <-> R/3"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2654430 - Data exchange between the CRM and S4 is not possible as Guid of the Business partners are different in both the systems for the old business partners."}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Customer had activated the CVI, but the badi&#160;CVI_CUSTOM_MAPPER was not implemented in the system.</p>\r\n<p>At that time there was a possibility that the guids of the BUT000 and CVI_CUST_LINK&#160; are different from the CRMKUNNR in ECC.</p>\r\n<p>For the contact person there was a possibility that the guids of the BUT000 and CVI_CUST_CT_LINK&#160; are different from the CRMPARNR in ECC.</p>\r\n<p>For the newly created business partners the guids between&#160;the above tables will be same.</p>\r\n<p>No data exchange will happen for the older business partners as the guids are different.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BUT000, CVI_CUST_LINK,&#160;CRMKUNNR, S4, CVI,&#160;CVI_CUSTOM_MAPPER, Guids,&#160;CVI_CUST_CT_LINK,&#160;CRMPARNR.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">It is a program error.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p id=\"\">Implement the attached correction.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I045084)"}, {"Key": "Processor                                                                                           ", "Value": "D047682"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002654430/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002654430/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2713963", "RefComponent": "XX-SER-MCC", "RefTitle": "FAQ: CVI - Customer Vendor Integration for system conversion to SAP S/4HANA", "RefUrl": "/notes/2713963 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "SAPSCORE", "From": "116", "To": "116", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10008INS4CORE", "URL": "/supportpackage/SAPK-10008INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10106INS4CORE", "URL": "/supportpackage/SAPK-10106INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10204INS4CORE", "URL": "/supportpackage/SAPK-10204INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10302INS4CORE", "URL": "/supportpackage/SAPK-10302INS4CORE"}, {"SoftwareComponentVersion": "SAP_APPL 617", "SupportPackage": "SAPKH61718", "URL": "/supportpackage/SAPKH61718"}, {"SoftwareComponentVersion": "SAP_APPL 618", "SupportPackage": "SAPK-61812INSAPAPPL", "URL": "/supportpackage/SAPK-61812INSAPAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0002654430/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 9, "URL": "/corrins/0002654430/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 11, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 13, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2516471 ", "URL": "/notes/2516471 ", "Title": "Partnerfunctions with employees are not transferred to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2622396 ", "URL": "/notes/2622396 ", "Title": "Initial download of Vendors does not work from S/4 HANA to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2623896 ", "URL": "/notes/2623896 ", "Title": "Issues with BP replication between S/4 a", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2516471 ", "URL": "/notes/2516471 ", "Title": "Partnerfunctions with employees are not transferred to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2622396 ", "URL": "/notes/2622396 ", "Title": "Initial download of Vendors does not work from S/4 HANA to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2623896 ", "URL": "/notes/2623896 ", "Title": "Issues with BP replication between S/4 a", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2654430 ", "URL": "/notes/2654430 ", "Title": "Data exchange between the CRM and S4 is not possible as Guid of the Business partners are different in both the systems for the old business partners.", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2516471 ", "URL": "/notes/2516471 ", "Title": "Partnerfunctions with employees are not transferred to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2622396 ", "URL": "/notes/2622396 ", "Title": "Initial download of Vendors does not work from S/4 HANA to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2623896 ", "URL": "/notes/2623896 ", "Title": "Issues with BP replication between S/4 a", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2632101 ", "URL": "/notes/2632101 ", "Title": "Employee responsible relationship replication from CRM to S/4 HANA ends up in error", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2654430 ", "URL": "/notes/2654430 ", "Title": "Data exchange between the CRM and S4 is not possible as Guid of the Business partners are different in both the systems for the old business partners.", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2654430 ", "URL": "/notes/2654430 ", "Title": "Data exchange between the CRM and S4 is not possible as Guid of the Business partners are different in both the systems for the old business partners.", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "1968132 ", "URL": "/notes/1968132 ", "Title": "Business partner replication between CRM and ECC with active CVI", "Component": "LO-MD-BP-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2006644 ", "URL": "/notes/2006644 ", "Title": "Short dump with the replication of business partners and the table CRMPARNR", "Component": "LO-MD-BP-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2070408 ", "URL": "/notes/2070408 ", "Title": "Incorrect entries in the table CRMKUNNR", "Component": "LO-MD-BP-CM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2283212 ", "URL": "/notes/2283212 ", "Title": "Contact person feedback not sent to CRM after note 1968132", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2288745 ", "URL": "/notes/2288745 ", "Title": "Creation of Contact person replication along with the partner function creation between CRM and ECC", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2421803 ", "URL": "/notes/2421803 ", "Title": "Industry sector data replication issue", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2457899 ", "URL": "/notes/2457899 ", "Title": "The tax number for tax number category GT4 is already maintained", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "617", "Number": "2516471 ", "URL": "/notes/2516471 ", "Title": "Partnerfunctions with employees are not transferred to CRM", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "617", "ValidTo": "618", "Number": "2675525 ", "URL": "/notes/2675525 ", "Title": "Contact Person feedback not sent to CRM during request/ initial download", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2288745 ", "URL": "/notes/2288745 ", "Title": "Creation of Contact person replication along with the partner function creation between CRM and ECC", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2421803 ", "URL": "/notes/2421803 ", "Title": "Industry sector data replication issue", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2457899 ", "URL": "/notes/2457899 ", "Title": "The tax number for tax number category GT4 is already maintained", "Component": "CRM-MD-BP-IF"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "618", "ValidTo": "618", "Number": "2516471 ", "URL": "/notes/2516471 ", "Title": "Partnerfunctions with employees are not transferred to CRM", "Component": "CRM-MD-BP-IF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}