{"Request": {"Number": "648565", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 501, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003406452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000648565?language=E&token=EB6834B476BF49BD907FB1B06A680F46"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000648565", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000648565/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "648565"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.09.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-LO-LIS"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Logistics Information System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistics - General", "value": "BW-BCT-LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistics Information System", "value": "BW-BCT-LO-LIS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-LO-LIS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "648565 - XPRA RMCSBWXP_COM does not generate anything"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Parts of the Business Content for the Logistics BW extraction are missing.This means that the BANFN and BNFPO fields of the MC02M_0SCL extract structure (2LIS_02_SCL DataSource) are missing after a plug-in upgrade with target release 2003.1 or an initial installation of PI 2003.1 on R/3 Release 4.0B or 4.5B.<br />Higher R/3 releases may also be affected.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>PI 2003.1, 2003_1_40B, 2003_1_45B, MC02M_0SCL, 2LIS_02_SCL,<br />RMCSBWXP_COM, upgrade, XPRA, BANFN, BNFPO,<br />2003_1_46B, 2003_1_46C, 2003_1_470<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>With R/3 Release 4.0B and 4.5B, release notes for the PI component are not delivered correctly. Therefore, the extract structure listed above is no longer generated and the structure remains with Version PI 2002.2 in the system.<br />For safety reasons and as a prerequisite for related note 651522, you must also implement this note if your system is higher than R/3 Release 4.5B.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><B>CAUTION!</B><br />Refer also to related note 651522.<br /><br />Postprocessing of the upgrade is required (see also the paragraph on postprocessing in related note 597635).</p> <OL>1. Make sure you meet the prerequisites specified in note 597635 for the upgrade (especially I.4. updates, see also note 328181).</OL> <OL>2. Implement the attached corrections in the system.</OL> <OL>3. Call transaction SE38 (ABAP Workbench) and execute the RMCSBWXP_COM report.</OL> <OL>4. Repeat the previous step until the report runs without errors.You can process any error messages that occur by referring to the long texts and to note 640066.</OL> <p><br />Comments:<br />What happens if you do not <B>carry out postprocessing</B> on R/3 4.0B or 4.5B?<br />In this case, the fields listed above are not part of the extract structure and therefore cannot be extracted to BW.A subsequent development designed to execute reporting using these fields in BW is feasible if you carry out postprocessing later, but requires a <B>new statistical data setup</B> in R/3 (-&gt; <B>downtime</B>) for the affected application and subsequent delta initialization.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-ADDON (Upgrade Add-On Components)"}, {"Key": "Other Components", "Value": "BW-BCT-LE-TRA (BW only - Transport)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-PUR (BW only - Purchase)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR-GT (BW only - Global Trade)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-PP (BW only - Production Planning)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-BW (BW only - MM Content im BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BIL (BW only - Billing)"}, {"Key": "Other Components", "Value": "BC-UPG (Upgrade - general)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BW (BW only - SD Content im BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-PM (BW only - Plant Maintenance)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR-AB (BW only - Agency Business)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-BW (BW only - PP Content in the BW System)"}, {"Key": "Other Components", "Value": "BW-BCT-CS (BW only - Customer Service)"}, {"Key": "Other Components", "Value": "BW-BCT (BW only - Business Content and Extractors)"}, {"Key": "Other Components", "Value": "BW-BCT-ISR (BW only - Retail and Consumer Products)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-SLS (BW only - Sales)"}, {"Key": "Other Components", "Value": "BW-BCT-QM (BW only - Quality Management)"}, {"Key": "Other Components", "Value": "BW-BCT-LE-SHP (BW only - Shipping)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D036721)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D027230)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000648565/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000648565/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000648565/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "701179", "RefComponent": "BW-BCT", "RefTitle": "BW-BCT composite SAP note: PI 2004.1 upgrade or SP", "RefUrl": "/notes/701179"}, {"RefNumber": "651522", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP_COM: Business Content incomplete", "RefUrl": "/notes/651522"}, {"RefNumber": "643166", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP* - activation error stops the upgrade", "RefUrl": "/notes/643166"}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "623411", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Gener. terminates with D0 322 (end phase 002)", "RefUrl": "/notes/623411"}, {"RefNumber": "614603", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Skipping releases during the upgrade (jump upgrade)", "RefUrl": "/notes/614603"}, {"RefNumber": "611115", "RefComponent": "BW-BCT", "RefTitle": "Composite BW-BCT note: PI 2003.1 upgrade", "RefUrl": "/notes/611115"}, {"RefNumber": "597635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/597635"}, {"RefNumber": "489259", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP ... terminates with error message D0 322", "RefUrl": "/notes/489259"}, {"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "328181", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Changes to extract structures in the Customizing Cockpit", "RefUrl": "/notes/328181 "}, {"RefNumber": "701179", "RefComponent": "BW-BCT", "RefTitle": "BW-BCT composite SAP note: PI 2004.1 upgrade or SP", "RefUrl": "/notes/701179 "}, {"RefNumber": "651522", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP_COM: Business Content incomplete", "RefUrl": "/notes/651522 "}, {"RefNumber": "611115", "RefComponent": "BW-BCT", "RefTitle": "Composite BW-BCT note: PI 2003.1 upgrade", "RefUrl": "/notes/611115 "}, {"RefNumber": "640066", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Upgrade: Return code 6 with XPRA RMCSBWXP_COM", "RefUrl": "/notes/640066 "}, {"RefNumber": "643166", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP* - activation error stops the upgrade", "RefUrl": "/notes/643166 "}, {"RefNumber": "614603", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "Skipping releases during the upgrade (jump upgrade)", "RefUrl": "/notes/614603 "}, {"RefNumber": "623411", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "LBWE: Gener. terminates with D0 322 (end phase 002)", "RefUrl": "/notes/623411 "}, {"RefNumber": "489259", "RefComponent": "BW-BCT-LO-LIS", "RefTitle": "XPRA RMCSBWXP ... terminates with error message D0 322", "RefUrl": "/notes/489259 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI", "From": "2003_1_40B", "To": "2003_1_470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI 2003_1_40B", "SupportPackage": "SAPKIPZH14", "URL": "/supportpackage/SAPKIPZH14"}, {"SoftwareComponentVersion": "PI 2003_1_45B", "SupportPackage": "SAPKIPZH24", "URL": "/supportpackage/SAPKIPZH24"}, {"SoftwareComponentVersion": "PI 2003_1_46B", "SupportPackage": "SAPKIPZH34", "URL": "/supportpackage/SAPKIPZH34"}, {"SoftwareComponentVersion": "PI 2003_1_46C", "SupportPackage": "SAPKIPZH44", "URL": "/supportpackage/SAPKIPZH44"}, {"SoftwareComponentVersion": "PI 2003_1_470", "SupportPackage": "SAPKIPZH54", "URL": "/supportpackage/SAPKIPZH54"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI", "NumberOfCorrin": 2, "URL": "/corrins/0000648565/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}