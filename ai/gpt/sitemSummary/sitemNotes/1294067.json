{"Request": {"Number": "1294067", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 360, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016707642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001294067?language=E&token=DA2A230B00BC6F6A28AE8C59DD5CC085"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001294067", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001294067/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1294067"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.01.2009"}, "SAPComponentKey": {"_label": "Component", "value": "LO-SPM-X"}, "SAPComponentKeyText": {"_label": "Component", "value": "Cross-Application Topics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Parts Management", "value": "LO-SPM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross-Application Topics", "value": "LO-SPM-X", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-SPM-X*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1294067 - Activation of enhancements for SPM"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In the Implementation Guide (IMG) for service parts management (SPM), you find the activity \"Activate Enhancements for Service Parts Management\".<br /><br />There is a lack of clarity regarding when you have to activate this switch during the introduction of SPM and the effects that the activation has.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>/SPE/CTRL_SW, service parts management (SPM), /SPE/CL_CTRL<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to missing documentation.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The IMG switch (specified in the following SPM switch) was originally designed to control the program flow for processes in SPM in a particular way. However, many features of the processes result from the fact that an Extended Warehouse Management (EWM) system is provided for warehouse management in SPM. Therefore, in the context of several corrections, the check for activating the SPM switch for SPM has been scaled back (see Notes 976716, 990782 and 989611 for more information). Wherever it was possible, the EWM-specific process flows are now controlled using the properties of warehouse management.<br /><br />However, you still have to activate the SPM switch if you want to use some special processes of SPM:</p> <UL><LI>Redirection of scheduling agreement releases in the goods receipt process</LI></UL> <UL><LI>SPM-specific calculation of the cumulative delivered quantities</LI></UL> <p><br />In the specified cases, the processes cannot be controlled using the properties of the warehouse number because the warehouse number is still unknown or does not play a role.<br /><br />If you only want to use an EWM system as a warehouse management system and are not going to use any of the specific processes mentioned, as of Support Package SAPKH60008 and in Releases 6.02, 6.03 and 6.04, you are <B>no longer</B> required to activate the switch for SPM.<br /><br />If you want to use a CRM system with ERP, do not activate the SPM switch; instead, activate the special switch \"Logistics Execution -&gt; Service Parts Management (SPM) -&gt; Integrate SPM with other Components -&gt; Activate Processes Using SAP CRM\" (this activity is available as of Support Package SAPKH60010 and in Releases 6.02, 6.03 and 6.04, see Note 1057459). The following processes are supported:</p> <UL><LI>Sales order management in CRM without replication of the sales order in ERP (system configuration for service parts management with unchecked deliveries)</LI></UL> <UL><LI>Third-party order processing (TPOP) with CRM sales orders</LI></UL> <UL><LI>Complaints and returns with CRM sales orders<br /></LI></UL> <p>If your business processes require the SPM switch to be activated, you can do so just before the first document is created. The switch is not required for the configuration of the system. The SPM switch can be deactivated if required, if you do not want to use any more SPM processes and the documents in question are complete.<br /><br />There are constraints if you use SPM, an EWM system or the direct delivery scenario along with CRM. Before implementation, talk to your SAP consultant, and discuss whether there will be any constraints for the business processes that you are planning to use.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D024439)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D029353)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001294067/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001294067/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "990782", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control Framework for SPM functions in deliveries", "RefUrl": "/notes/990782"}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611"}, {"RefNumber": "976716", "RefComponent": "LO-SPM", "RefTitle": "Defining EWM warehouses without active SPM", "RefUrl": "/notes/976716"}, {"RefNumber": "821101", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/821101"}, {"RefNumber": "1423321", "RefComponent": "LE-IEW", "RefTitle": "Recommendations for using ERP-EWM integration", "RefUrl": "/notes/1423321"}, {"RefNumber": "1346360", "RefComponent": "SCM-EWM-WOP", "RefTitle": "EWM: Notes for implementation (composite SAP Note)", "RefUrl": "/notes/1346360"}, {"RefNumber": "1057459", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control of CRM-related functionality in ERP 2005", "RefUrl": "/notes/1057459"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1423321", "RefComponent": "LE-IEW", "RefTitle": "Recommendations for using ERP-EWM integration", "RefUrl": "/notes/1423321 "}, {"RefNumber": "1346360", "RefComponent": "SCM-EWM-WOP", "RefTitle": "EWM: Notes for implementation (composite SAP Note)", "RefUrl": "/notes/1346360 "}, {"RefNumber": "821101", "RefComponent": "LO-SPM", "RefTitle": "Service Parts Management: Prerequisites and restrictions", "RefUrl": "/notes/821101 "}, {"RefNumber": "989611", "RefComponent": "LO-SPM-INB", "RefTitle": "Inbound control framework switch redesign", "RefUrl": "/notes/989611 "}, {"RefNumber": "990782", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control Framework for SPM functions in deliveries", "RefUrl": "/notes/990782 "}, {"RefNumber": "976716", "RefComponent": "LO-SPM", "RefTitle": "Defining EWM warehouses without active SPM", "RefUrl": "/notes/976716 "}, {"RefNumber": "1057459", "RefComponent": "LO-SPM-OUT", "RefTitle": "Control of CRM-related functionality in ERP 2005", "RefUrl": "/notes/1057459 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}