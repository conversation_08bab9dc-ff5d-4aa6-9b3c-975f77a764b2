{"Request": {"Number": "2364938", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 670, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014222092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002364938?language=E&token=85FAD1AFA3346F6AFF2AA360BF432441"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002364938", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002364938/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2364938"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 101}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BC-ABA-LA"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,  <PERSON>time"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Runtime Environment - ABAP Language Issues Only", "value": "BC-ABA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>time", "value": "BC-ABA-LA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-ABA-LA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2364938 - Downport of the infrastructure of the SAP S/4HANA readiness checks"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to use the SAP S/4HANA readiness checks in your system.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This SAP Note contains infrastructure sections of the SAP S/4HANA readiness checks.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Import the specified Support Package or implement the attached correction instructions.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D002050)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026681)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002364938/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002364938/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2787392", "RefComponent": "BC-ABA-LA", "RefTitle": "ASSERTION_FAILED when running ATC using variant S4HANA_READINESS*", "RefUrl": "/notes/2787392 "}, {"RefNumber": "2736934", "RefComponent": "BC-DWB-TOO-ATF", "RefTitle": "\"Tool failures possibly caused incomplete result\" error happens in the ATC result", "RefUrl": "/notes/2736934 "}, {"RefNumber": "2436688", "RefComponent": "BC-DWB-CEX-CCM", "RefTitle": "Recommended SAP Notes for Using S/4HANA Custom Code Checks in ATC or Custom Code Migration App", "RefUrl": "/notes/2436688 "}, {"RefNumber": "2865234", "RefComponent": "BC-DWB-CEX", "RefTitle": "SAP S/4HANA custom code checks show different number of findings in different check runs", "RefUrl": "/notes/2865234 "}, {"RefNumber": "2866977", "RefComponent": "BC-DWB-CEX", "RefTitle": "Quick Fixes for SAP S/4HANA custom code checks", "RefUrl": "/notes/2866977 "}, {"RefNumber": "2738251", "RefComponent": "BC-DWB-CEX", "RefTitle": "Quick Fixes for the S/4HANA Custom Code Checks", "RefUrl": "/notes/2738251 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "751", "To": "757", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75115INSAPBASIS", "URL": "/supportpackage/SAPK-75115INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75114INSAPBASIS", "URL": "/supportpackage/SAPK-75114INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75116INSAPBASIS", "URL": "/supportpackage/SAPK-75116INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75113INSAPBASIS", "URL": "/supportpackage/SAPK-75113INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75112INSAPBASIS", "URL": "/supportpackage/SAPK-75112INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75117INSAPBASIS", "URL": "/supportpackage/SAPK-75117INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75103INSAPBASIS", "URL": "/supportpackage/SAPK-75103INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75105INSAPBASIS", "URL": "/supportpackage/SAPK-75105INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75104INSAPBASIS", "URL": "/supportpackage/SAPK-75104INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75110INSAPBASIS", "URL": "/supportpackage/SAPK-75110INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75111INSAPBASIS", "URL": "/supportpackage/SAPK-75111INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75106INSAPBASIS", "URL": "/supportpackage/SAPK-75106INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75101INSAPBASIS", "URL": "/supportpackage/SAPK-75101INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75102INSAPBASIS", "URL": "/supportpackage/SAPK-75102INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75109INSAPBASIS", "URL": "/supportpackage/SAPK-75109INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75118INSAPBASIS", "URL": "/supportpackage/SAPK-75118INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 751", "SupportPackage": "SAPK-75108INSAPBASIS", "URL": "/supportpackage/SAPK-75108INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75212INSAPBASIS", "URL": "/supportpackage/SAPK-75212INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75209INSAPBASIS", "URL": "/supportpackage/SAPK-75209INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75213INSAPBASIS", "URL": "/supportpackage/SAPK-75213INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75210INSAPBASIS", "URL": "/supportpackage/SAPK-75210INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75208INSAPBASIS", "URL": "/supportpackage/SAPK-75208INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75201INSAPBASIS", "URL": "/supportpackage/SAPK-75201INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75204INSAPBASIS", "URL": "/supportpackage/SAPK-75204INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75205INSAPBASIS", "URL": "/supportpackage/SAPK-75205INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75211INSAPBASIS", "URL": "/supportpackage/SAPK-75211INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75207INSAPBASIS", "URL": "/supportpackage/SAPK-75207INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75203INSAPBASIS", "URL": "/supportpackage/SAPK-75203INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75202INSAPBASIS", "URL": "/supportpackage/SAPK-75202INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75214INSAPBASIS", "URL": "/supportpackage/SAPK-75214INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 752", "SupportPackage": "SAPK-75206INSAPBASIS", "URL": "/supportpackage/SAPK-75206INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75310INSAPBASIS", "URL": "/supportpackage/SAPK-75310INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75309INSAPBASIS", "URL": "/supportpackage/SAPK-75309INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75308INSAPBASIS", "URL": "/supportpackage/SAPK-75308INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75307INSAPBASIS", "URL": "/supportpackage/SAPK-75307INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75311INSAPBASIS", "URL": "/supportpackage/SAPK-75311INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75301INSAPBASIS", "URL": "/supportpackage/SAPK-75301INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75303INSAPBASIS", "URL": "/supportpackage/SAPK-75303INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75306INSAPBASIS", "URL": "/supportpackage/SAPK-75306INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75312INSAPBASIS", "URL": "/supportpackage/SAPK-75312INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75302INSAPBASIS", "URL": "/supportpackage/SAPK-75302INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 753", "SupportPackage": "SAPK-75304INSAPBASIS", "URL": "/supportpackage/SAPK-75304INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75408INSAPBASIS", "URL": "/supportpackage/SAPK-75408INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75409INSAPBASIS", "URL": "/supportpackage/SAPK-75409INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75410INSAPBASIS", "URL": "/supportpackage/SAPK-75410INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75407INSAPBASIS", "URL": "/supportpackage/SAPK-75407INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75406INSAPBASIS", "URL": "/supportpackage/SAPK-75406INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75405INSAPBASIS", "URL": "/supportpackage/SAPK-75405INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75404INSAPBASIS", "URL": "/supportpackage/SAPK-75404INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75402INSAPBASIS", "URL": "/supportpackage/SAPK-75402INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75403INSAPBASIS", "URL": "/supportpackage/SAPK-75403INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 754", "SupportPackage": "SAPK-75401INSAPBASIS", "URL": "/supportpackage/SAPK-75401INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75505INSAPBASIS", "URL": "/supportpackage/SAPK-75505INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75504INSAPBASIS", "URL": "/supportpackage/SAPK-75504INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75506INSAPBASIS", "URL": "/supportpackage/SAPK-75506INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75503INSAPBASIS", "URL": "/supportpackage/SAPK-75503INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75502INSAPBASIS", "URL": "/supportpackage/SAPK-75502INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75508INSAPBASIS", "URL": "/supportpackage/SAPK-75508INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 755", "SupportPackage": "SAPK-75501INSAPBASIS", "URL": "/supportpackage/SAPK-75501INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 756", "SupportPackage": "SAPK-75603INSAPBASIS", "URL": "/supportpackage/SAPK-75603INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 756", "SupportPackage": "SAPK-75602INSAPBASIS", "URL": "/supportpackage/SAPK-75602INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 756", "SupportPackage": "SAPK-75604INSAPBASIS", "URL": "/supportpackage/SAPK-75604INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 756", "SupportPackage": "SAPK-75601INSAPBASIS", "URL": "/supportpackage/SAPK-75601INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 756", "SupportPackage": "SAPK-75606INSAPBASIS", "URL": "/supportpackage/SAPK-75606INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 757", "SupportPackage": "SAPK-75701INSAPBASIS", "URL": "/supportpackage/SAPK-75701INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 757", "SupportPackage": "SAPK-75702INSAPBASIS", "URL": "/supportpackage/SAPK-75702INSAPBASIS"}, {"SoftwareComponentVersion": "SAP_BASIS 757", "SupportPackage": "SAPK-75704INSAPBASIS", "URL": "/supportpackage/SAPK-75704INSAPBASIS"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 170, "URL": "/corrins/0002364938/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 170, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2375392 ", "URL": "/notes/2375392 ", "Title": "Downport of Code Inspector remote checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2442045 ", "URL": "/notes/2442045 ", "Title": "Field extension check - operator CORRESPONDING", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2494150 ", "URL": "/notes/2494150 ", "Title": "Adjustments to message titles in Code Inspector field length extensions check", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "751", "ValidTo": "751", "Number": "2626913 ", "URL": "/notes/2626913 ", "Title": "Tool failure corrections", "Component": "BC-DWB-TOO-ATF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2527903 ", "URL": "/notes/2527903 ", "Title": "Remote analysis (for check system)", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "752", "ValidTo": "752", "Number": "2904867 ", "URL": "/notes/2904867 ", "Title": "Memory saving for some Code Inspector tests/ATC tests and error correction for an ASSERTION_FAILED", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2656251 ", "URL": "/notes/2656251 ", "Title": "Performance and memory improvements for cl_ci_test_no_order_by", "Component": "BC-DWB-TOO-ATF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2689471 ", "URL": "/notes/2689471 ", "Title": "ASSERTION_FAILED in cl_ci_test_no_order_by", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2712310 ", "URL": "/notes/2712310 ", "Title": "Quick fix for ambiguous SELECT SINGLE", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2738503 ", "URL": "/notes/2738503 ", "Title": "Downport of quick fix Infrastructure", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2738746 ", "URL": "/notes/2738746 ", "Title": "Adjustment of ATC checks in line with new quick fix infrastructure", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "753", "ValidTo": "753", "Number": "2904867 ", "URL": "/notes/2904867 ", "Title": "Memory saving for some Code Inspector tests/ATC tests and error correction for an ASSERTION_FAILED", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2824538 ", "URL": "/notes/2824538 ", "Title": "S/4HANA: Field length extension check: Referenced object type and application component are empty", "Component": "BC-DWB-CEX"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2879493 ", "URL": "/notes/2879493 ", "Title": "Improved SQL analysis for ATC (II)", "Component": "BC-DWB-TOO-ATF"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "754", "ValidTo": "754", "Number": "2904867 ", "URL": "/notes/2904867 ", "Title": "Memory saving for some Code Inspector tests/ATC tests and error correction for an ASSERTION_FAILED", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "755", "ValidTo": "755", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "756", "ValidTo": "756", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "757", "ValidTo": "757", "Number": "2364938 ", "URL": "/notes/2364938 ", "Title": "Downport of the infrastructure of the SAP S/4HANA readiness checks", "Component": "BC-ABA-LA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}