{"Request": {"Number": "853169", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 413, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000004814312017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000853169?language=E&token=C28D9E2D97DA2C98F4D7853D7FEB5C59"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000853169", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000853169/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "853169"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.07.2005"}, "SAPComponentKey": {"_label": "Component", "value": "CO-OM-CCA-F"}, "SAPComponentKeyText": {"_label": "Component", "value": "Period-end Closing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Controlling", "value": "CO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Overhead Cost Controlling", "value": "CO-OM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-OM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cost Center Accounting", "value": "CO-OM-CCA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-OM-CCA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Period-end Closing", "value": "CO-OM-CCA-F", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CO-OM-CCA-F*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "853169 - ALLOCATION: Actual cycles can only be executed periodically"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>As of Release 6.40 (ECC 5.0), actual cycles can only be executed periodically because the derivation of the SEGMENT characteristic can be changed periodically.<br />The system issues error message FCOM_SERVICE 000 Transaction \"&amp;1\" not possible across periods.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Transactions: KSU5, KSUB, KSV5, KSVB, KSW5, KSWB, KSC5, KSCB, KEU5, KEUB , GA15, GA35, GA2B, GA4B, CPP5, CPPB, CPC5, CPCB<br />Program name: ALxxyyyR, MKGALFO4,<br />SUBST_RFIELDS_EXIT<br />SEGMENT, 'FCOM_RECEIVER_DERIVE'<br />Assessment<br />Distribution<br />Periodic reposting<br />Reposting<br />Indirect activity allocation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is due to a design problem.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached program corrections in your system. Following these corrections, you can once again start the actual cycles for more than one period.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D022188)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022188)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000853169/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000853169/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "931078", "RefComponent": "FI-GL", "RefTitle": "NEWGL: Error message FAGL_LEDGER_CUST 023", "RefUrl": "/notes/931078"}, {"RefNumber": "893099", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATIONS:Inconsistencies w/ reconciliation ledger posting", "RefUrl": "/notes/893099"}, {"RefNumber": "880539", "RefComponent": "CO-OM", "RefTitle": "No warning message (KS096) when you create a cost center without a profit center", "RefUrl": "/notes/880539"}, {"RefNumber": "877981", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Funds, Function and Grant are not filled", "RefUrl": "/notes/877981"}, {"RefNumber": "851684", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Plan cycles can only be executed by period", "RefUrl": "/notes/851684"}, {"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357 "}, {"RefNumber": "893099", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATIONS:Inconsistencies w/ reconciliation ledger posting", "RefUrl": "/notes/893099 "}, {"RefNumber": "931078", "RefComponent": "FI-GL", "RefTitle": "NEWGL: Error message FAGL_LEDGER_CUST 023", "RefUrl": "/notes/931078 "}, {"RefNumber": "877981", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Funds, Function and Grant are not filled", "RefUrl": "/notes/877981 "}, {"RefNumber": "880539", "RefComponent": "CO-OM", "RefTitle": "No warning message (KS096) when you create a cost center without a profit center", "RefUrl": "/notes/880539 "}, {"RefNumber": "851684", "RefComponent": "CO-OM-CCA-F", "RefTitle": "ALLOCATION: Plan cycles can only be executed by period", "RefUrl": "/notes/851684 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50009", "URL": "/supportpackage/SAPKH50009"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60001", "URL": "/supportpackage/SAPKH60001"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/0000853169/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "764841 ", "URL": "/notes/764841 ", "Title": "No profit center derivation for active flexible GL", "Component": "RE-FX"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}