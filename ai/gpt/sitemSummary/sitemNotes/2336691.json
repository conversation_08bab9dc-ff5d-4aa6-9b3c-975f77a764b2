{"Request": {"Number": "2336691", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 242, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013784152017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002336691?language=E&token=D13E13FDA2C3DD64D9E5A4652691C47F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002336691", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002336691/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2336691"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.08.2016"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP-PF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Planning Functions and Planning Sequences"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning Functions and Planning Sequences", "value": "BW-PLA-IP-PF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP-PF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2336691 - Execution of FOX formulas in PAK"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Not all FOX formulas can be executed on SAP HANA DB. This SAP Note provides a function that makes the reasons for this transparent. The report RSPLS_PLANNING_ON_HDB_ANALYSIS outputs the reasons why a FOX formula cannot be executed on the HDB. In transaction RSPLAN, there is a &#x2018;Check HDB&#x2019; button that you can use to display corresponding messages during formula maintenance. This SAP Note corrects the fact that the execution on SAP HANA DB cannot be executed if variables are used in FOREACH IN INFOPROVIDER loops if the InfoProviders are different.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>PAK, 0RSPL_FORMULA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You use PAK on SAP HANA DB.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li><strong><span style=\"text-decoration: underline;\">SAP BW 7.40</span></strong><br />Import Support Package 16 for SAP BW 7.40 (SAPKW74016) into your BW system. The Support Package is available once <strong>SAP Note 2298815</strong>&#x00A0;(\"SAPBWNews NW BW 7.40 ABAP SP16\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<ul>\r\n<li><strong><span style=\"text-decoration: underline;\">SAP BW 7.50</span></strong><br />Import Support Package 5 for SAP BW 7.50 (SAPK-75005INSAPBW) into your BW system. The Support Package is available once <strong>SAP Note 2318815</strong>&#x00A0;(\"SAPBWNews NW BW 7.50 ABAP SP5\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p>&#x00A0;</p>\r\n<p>In urgent cases, you can implement the correction instructions as an advance correction.</p>\r\n<p><strong>You must first read SAP Notes 1668882 and 2248091, which provide information about transaction SNOTE.</strong></p>\r\n<p>To provide information in advance, the SAP Note(s) specified above might already be available before the Support Package is released. In this case, however, the short text of the SAP Note still contains the term \"preliminary version\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D003259)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D022158)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002336691/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002336691/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2335867", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.11 ABAP SP 17", "RefUrl": "/notes/2335867"}, {"RefNumber": "2318815", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.50 ABAP SP 05", "RefUrl": "/notes/2318815"}, {"RefNumber": "2300807", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.31 ABAP SP 19", "RefUrl": "/notes/2300807"}, {"RefNumber": "2298815", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.40 ABAP SP 16", "RefUrl": "/notes/2298815"}, {"RefNumber": "2292375", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.30 ABAP SP 16", "RefUrl": "/notes/2292375"}, {"RefNumber": "2277367", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.02 ABAP SP 19", "RefUrl": "/notes/2277367"}, {"RefNumber": "2273996", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.01 ABAP SP 19", "RefUrl": "/notes/2273996"}, {"RefNumber": "2272230", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews BW 7.00 ABAP SP 36", "RefUrl": "/notes/2272230"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74016", "URL": "/supportpackage/SAPKW74016"}, {"SoftwareComponentVersion": "SAP_BW 750", "SupportPackage": "SAPK-75005INSAPBW", "URL": "/supportpackage/SAPK-75005INSAPBW"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 3, "URL": "/corrins/0002336691/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": " <code>   <P><br/><br/>------------------------------------------------------------------------<br/>|Manual Post-Implement.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;|<br/>------------------------------------------------------------------------<br/>|VALID FOR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>|Software Component&nbsp;&nbsp; SAP_BW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Business Inform...|<br/>| Release 740&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPKW74008 - SAPKW74015&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>| Release 750&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SAPK-75004INSAPBW - SAPK-75004INSAPBW&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; |<br/>------------------------------------------------------------------------<br/><br/>In transaction SE91, create the following messages in the message class RSPLFOX:<br/><br/>250 Variable &amp;1 with interval selection on compound InfoObject &amp;2<br/>251 Variable &amp;1 cannot be reused in FOREACH IN INFOPROVIDER -&gt; Use different<br/>252 No Iteration on KEYFIGURE_NAME in FOREACH IN INFOPROVIDER statement<br/>253 &amp;1 Statement not supported<br/>254 INFOPROVIDER must have fields to address<br/>255 Variable &amp;1 with more than 1 val. for comp. characteristics not supported<br/>256 Type &amp;1 not supported<br/>257 Hierarchies not supported<br/>258 Formula has no limitations for execution on HDB<br/>259 Transitive attribute &amp;1 not supported<br/>260 Characteristic &amp;1 with HANA views not supported<br/>261 Access to DSO &amp;1 not supported -&gt; use aggregation level<br/></P> <br/>  </code> "}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisites", "value": 40, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2028499 ", "URL": "/notes/2028499 ", "Title": "Planning functions (PAK): Using a customer-specific namespace", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2035193 ", "URL": "/notes/2035193 ", "Title": "Error in the program RSPLS_PLANNING_ON_HDB_ANALYSIS", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2046381 ", "URL": "/notes/2046381 ", "Title": "FOX: Access to InfoProvider data with variable key figure name", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2052122 ", "URL": "/notes/2052122 ", "Title": "FOX: Insufficient reference data", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2068431 ", "URL": "/notes/2068431 ", "Title": "Formulas: Syntax error if characteristic value is the name of a key figure.", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2075117 ", "URL": "/notes/2075117 ", "Title": "FOX: InfoProvider statement", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2075774 ", "URL": "/notes/2075774 ", "Title": "FOX formulae: Incorrect cursor position for data types and operands", "Component": "BW-PLA-IP-PMD"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2081802 ", "URL": "/notes/2081802 ", "Title": "FOX: Internal table DELETE statement", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2084538 ", "URL": "/notes/2084538 ", "Title": "FOX: FOREACH UNORDERED", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2095182 ", "URL": "/notes/2095182 ", "Title": "Formulas: Use of InfoObjects in customer namespace", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2113405 ", "URL": "/notes/2113405 ", "Title": "TMVL function with own time characteristics", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2121256 ", "URL": "/notes/2121256 ", "Title": "HDB: Use of TMVL with characteristics that reference time characteristics", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2130125 ", "URL": "/notes/2130125 ", "Title": "Assignment of number sign to string", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2138517 ", "URL": "/notes/2138517 ", "Title": "HDB: Access to master data characteristics outside of aggregation level", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2145611 ", "URL": "/notes/2145611 ", "Title": "Internal tables in FOX formulas for execution on SAP HANA database", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2156155 ", "URL": "/notes/2156155 ", "Title": "FOX: Access to InfoProvider data with fixed and variable characteristic value restrictions", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2160427 ", "URL": "/notes/2160427 ", "Title": "Formulas: Reference data selection determined incorrectly", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2161101 ", "URL": "/notes/2161101 ", "Title": "Dump in RSPLS_PLANNING_ON_HDB_ANALYSIS", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2164994 ", "URL": "/notes/2164994 ", "Title": "Planning function type \"Formula\": Internal tables", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2192954 ", "URL": "/notes/2192954 ", "Title": "HDB: Variable values of compound characteristics for VARI, VARC-> PE error", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2198881 ", "URL": "/notes/2198881 ", "Title": "FOX formulas: Use of local providers", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2200331 ", "URL": "/notes/2200331 ", "Title": "FOX: Internal tables and HDB", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2205392 ", "URL": "/notes/2205392 ", "Title": "FOX: CLEAR statement", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2224373 ", "URL": "/notes/2224373 ", "Title": "FOX: Function call - dump during syntax check", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2225085 ", "URL": "/notes/2225085 ", "Title": "HDB: VARI and VARC in FOX formulas", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2230937 ", "URL": "/notes/2230937 ", "Title": "FOX: Function ATRV( ) and reference data", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2233058 ", "URL": "/notes/2233058 ", "Title": "HDB: VARC, VARI, VARV in FOX formulas -> PE error 38.001: Condition 'getCalcScenario()->saveScenario()'", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2234156 ", "URL": "/notes/2234156 ", "Title": "FOX: Executing formulas with external aggregation levels on the SAP HANA database", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2240056 ", "URL": "/notes/2240056 ", "Title": "HDB: FOX - IF statement with pattern", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2241643 ", "URL": "/notes/2241643 ", "Title": "Executing FOX formulas with external aggregation levels in HDB", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2243067 ", "URL": "/notes/2243067 ", "Title": "FOX: FOREACH KYF IN INFOPROVIDER.", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2250563 ", "URL": "/notes/2250563 ", "Title": "FOX: DECFLOAT34 as parameter for function modules", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2255558 ", "URL": "/notes/2255558 ", "Title": "FOX: Referenced objects in customer namespace", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2262901 ", "URL": "/notes/2262901 ", "Title": "FOX: ATRV and commands that use compound master data", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2279526 ", "URL": "/notes/2279526 ", "Title": "FOX: Namespace and InfoProvider statement", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2287934 ", "URL": "/notes/2287934 ", "Title": "HDB: Attributes for Time Characteristics FOX Script check error", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2324519 ", "URL": "/notes/2324519 ", "Title": "FOX: Various problems", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2328366 ", "URL": "/notes/2328366 ", "Title": "PAK: PE error for IF expression", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2334110 ", "URL": "/notes/2334110 ", "Title": "FOX: Variable and characteristic value with the same name are not differentiated", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "740", "ValidTo": "740", "Number": "2336691 ", "URL": "/notes/2336691 ", "Title": "Execution of FOX formulas in PAK", "Component": "BW-PLA-IP-PF"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "750", "ValidTo": "750", "Number": "2336691 ", "URL": "/notes/2336691 ", "Title": "Execution of FOX formulas in PAK", "Component": "BW-PLA-IP-PF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}