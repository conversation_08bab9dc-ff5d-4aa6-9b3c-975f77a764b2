{"Request": {"Number": "438337", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 433, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015077852017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000438337?language=E&token=55667516FDEC9A0BD1BF3A17CD32E392"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000438337", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000438337/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "438337"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.07.2002"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-TEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "In Case of LiveCache Problems: Please use SCM-APO-LCA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "In Case of LiveCache Problems: Please use SCM-APO-LCA", "value": "SCM-TEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-TEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "438337 - APO Support Package 16 for APO Release 3.0A"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes how to import APO Support Package 16 (SAPKY30A16) for APO Release 3.0A into your system.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>APO Support Package, APO patch, APO Release 3.0A, COM routines, liveCache, optimizer, Support Release, Support Package, release strategy<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>CAUTION!!<br /><B>Due to an incorrect delivery, a new SP16 package was made available on October 10, 2001.</B><B></B><br />InfoObject descriptions are deleted when you import the old SP16 package.<br />You can check whether you have imported the old Support Package or the new, correct Support Package 16 in your system as follows:<br />Select transaction 'SEPS' -&gt; menu option 'Goto' -&gt; Inbox<br />to get a tabular display. The first three columns indicate a unique package number for the 'SAPKY30A16' subject (4th column).Note 442764 solves the aforementioned problem and is available for customers who have imported the old SP16 package.The new, correct SP16 package has the number CSN01200615320010183.PAT<br />For customers who imported the old SP16 package, <B>note 442764</B> is available to solve the above described problem.<br />========================================================================<br /></p> <b>1. IMPORTANT: Information on SP16</b><br /> <p></p> <UL><LI> <B>Release restrictions</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to note 427957. It contains known release restrictions as of Support Release 3 (Support Package 14).<br /></p> <UL><LI> <B>Importing requirements for APO Support Package 16 (SAPKY30A16)</B></LI></UL> <UL><UL><LI>Before you import APO Support Package 16, it is absolutely <B>imperative that</B> you import the following components as a minimum requirement into your system:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> - </B><B>BASIS 23 (SAPKB46C23)</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- <B> ABA 23 (SAPKA46C23)</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- <B> BW 17 (SAPKW20B17)</B><B></B></p> <UL><LI> <B>New recommended procedure with transaction SPAM</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of the version level APO Support Release 2 (SR2 contains APO Support Packages 1 - 8), we recommend that you always import several APO Support Packages (from Support Package 9 to the current Support Package) in a queue.<br /></p> <UL><LI> <B>Prerequisite for SAP R/3 back end</B></LI></UL> <UL><UL><LI>PlugIn 2001.1 (SP01) or higher</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Also refer to:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 415244 (PlugIn 2001.1)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note 449677 (PlugIn 2001.2)<br /></p> <UL><LI> <B>liveCache:</B></LI></UL> <UL><UL><LI>Current liveCache for Support Package <B>16 =&gt; 7.2.5 build 7</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 379051 Importing a liveCache version &gt;= 7.2.4 B15</p> <UL><LI> <B>COM routines:</B></LI></UL> <UL><UL><LI>current COM routines for Support <B>Package 16 =&gt; build 24</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 439181 SAPAPO 3.0 COM object build 24<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; <B>liveCache Version 7.2.5 build 7 is absolutely necessary for COM build 24!</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 326494 List of the SAPAPO COM object builds for APO 3.0</p> <UL><LI> <B>SAP R/3 kernel for Release 4.6D:</B></LI></UL> <UL><UL><LI> <B>If you use liveCache &gt;= 7.2.5 build 4 patch level &gt;= 579 is required!</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 406248 liveCache connect problems</p> <UL><LI> <B>Optimizer:</B></LI></UL> <UL><UL><LI>Support Package 16 contains new versions for the following optimizers:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CTM, PP/DS, SNP, ND, and VSR.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 435833 APO 3.0 optimizer Support Package 16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 300930 APO 3.0 Importing an optimizer version</p> <UL><LI> <B>Refer to the following notes and implement these if they are relevant for you:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>Number Component Priority Validity</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;435902 APO-FCS Demand Planning High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;441950 APO-MD-VM Version management High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;442090 APO-PPS-HEU-PP PP heuristics High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;442747 APO-MD-VM Version management High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;435471 APO-INT-PPS Prod. &amp; Det.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;439550 APO-SNP-DPL Deployment High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;of SNP plan High SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;505308 BC-DB-LCA High SP01 - SP19<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;515120 APO-FCS-EXTR high SP01 - SP20<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;511867 APO-MD-LO high SP01 - SP05<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;533134 BC-DB-LCA high SP16 - SP20<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;428551 APO-FCS-BF Basic functions Medium SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;442220 APO-SNP-INS Interactive SNP Medium SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;483479 BC-DB-LCA Medium SP16 - SP18<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;440809 APO-FCS Demand planning Low SP16<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;</p> <UL><LI> <B>Special features for APO Support Package &lt;= 6</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have an <B>APO Support Package Release 6 and</B> lower in your APO system, read and follow the following notes:</p> <UL><UL><LI>=&gt; Note:314218 conversion of the movement data between the Support Packages</LI></UL></UL> <UL><UL><LI>=&gt; Note: 361635 liveCache upgrade APO 3.0A SP7</LI></UL></UL> <b>2. IMPORT THE COMPONENTS FOR SUPPORT PACKAGE 16</b><br /> <p></p> <UL><LI> <B>Support Packages</B></LI></UL> <UL><UL><LI> <B>SPAM Update</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start importing the Support Packages, you need to update the SPAM Manager <B>to the latest </B>status.For more information, select the 'i' button on the initial screen of transaction SPAM (online documentation: Help -&gt; Application help).</p> <UL><UL><LI> <B>Import the components BASIS, ABA, BW and APO</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you start to import these components with the SPAM transaction, we recommend that:<br />1. no system activities are running or occurring in parallel<br />2. no other batch jobs are running<br />3. a checkpoint was written with the program '/SAPAPO/OM_CHECKPOINT_WRITE'<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import these Support Package components separately from each other in the following sequence:<br /><B>BASIS 23 (SAPKB46C23) -&gt; ABA (SAPKA46C23) -&gt; BW 17 (SAPKW20B17)</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that BW Support <B>Packages may not be imported in a queue!</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 484015 Importing BW Support Packages<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 328237 Importing patches into a BW 2.0B system</p> <UL><LI> <B>Binary Patches</B></LI></UL> <UL><UL><LI> <B>COM routines - liveCache</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommended that you always import the latest COM <B>object with the liveCache build currently</B> released for it.In this way, you ensure that you can always use the latest corrections (such as performance improvements).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 157265 Exchanging COM objects for liveCache in APO 3.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 326494 List of the SAPAPO COM object builds for APO 3.0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 379051 Importing a liveCache version &gt;= 7.2.4 B15<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;After completing the liveCache update, refer to the following<br />=&gt; Note: 424886 parameter values as of liveCache version 7.2.5<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Note also that a new liveCache version may require a suitable 'DBADASLIB' library.</B><br />=&gt; Note: 325402 dbadaslib: How do I install a patch?<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Additional sources of information for the UNIX liveCache and COM routines can be found in the following<br />=&gt; Note: 391746 COM routines and liveCache versions on UNIX<br /></p> <UL><UL><LI> <B>Kernel, optimizer and frontend</B></LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you always import the latest 4.6D kernel, optimizer version and front end.You therefore ensure that you can always use the latest corrections (such as performance improvements and so on). being able to be.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 300930 APO 3.0 Importing an optimizer version<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=&gt; Note: 422446 APO frontend patch<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> Note also that a new R/3 kernel (disp+work) may require a suitable 'DBADASLIB' library!</B><br />=&gt; Note: 325402 dbadaslib: How do I install a patch?</p> <b>3. GENERAL INFORMATION</b><br /> <p></p> <b>3.1 General information on APO Support Packages and outbound release versions</b><br /> <UL><LI> <B>Importing sequence</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you import the Support Package components in the following sequence: <B>BASIS -&gt; ABA -&gt; BW -&gt; APO</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of the version level APO Support Release 2 (SR2 contains APO Support Packages 1 - 8), we recommend that you always import several APO Support Packages (from Support Package 9 to the current Support Package) in a queue.</p> <UL><LI> <B>Initial releases</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>APO Support Package 19 (SAPKY30A19) for APO Release 3. 0A requires a complete installation/upgrade (delivery from May 15, 2000).</B><B> </B><B>In addition, you must import all APO Support Packages from Support Package 1 to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> If the system was built with Support Release 1 for APO Release 3.0A, you must also import all Support Packages from Support Package 5 to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> If the system was built with Support Release 2 for APO Release 3.0A, you must also import all Support Packages from Support Package 9 to the current version.</B><B></B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B> If the system was built with Support Release 3 for APO Release 3.0A, you must also import all Support Packages from Support Package 15 to the current version.</B><B></B></p> <UL><LI> <B>Languages supported for APO Release 3.0</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;German, English, French, Spanish, Danish, Finnish, Hungarian, Italian, Japanese, Korean, Dutch, Norwegian, Polish, Portuguese, Swedish, Russian, Czech.<br /><br /></p> <b>3.2 Download areas</b><br /> <UL><LI> <B>Service Marketplace:</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The new SAP Service Marketplace offers customers a new alternative to obtain SAP software at a central location.In time, the Service Marketplace will enable all SAP application components to be downloaded, thereby relieving both the download areas described above (sapservX, equity warrant) and the old SAPNet download area (http://sapnet.sap.de/ocs-download)!<br />Currently the components 'SAP BASIS', 'SAP ABA', 'SAP BW','SAP APO', 'SAP Frontend', 'SAP Kernel' and 'SAP Optimizer' can already be downloaded via the Service Marketplace at the following address:<br /><B>http://SERVICE.SAP.COM/SWCENTER-MAIN</B><br /><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The <B>binary patches for updating the 'COM routines' and the 'liveCache'</B> are not yet available on the Service <B>Marketplace!</B><br /></p> <UL><LI> <B>sapservX (binary patches):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- <B>COM routines</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/specific/apo/apo30/&lt;SPn&gt;/SAPCOM30_&lt;Build&gt;.SAR<br />SPn = Support Package number (for example, SP16)<br />Build = build number of the COM routines (for example, 24)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />- ftp://sapservX/specific/apo/apo30/sp16/SAPCOM30_23_NT.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>- </B><B>liveCache</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/general/3rdparty/sapdb/LC_VERSIONS/&lt;Build&gt;/ SAPDB-SERVER-&lt;OS&gt;-&lt;32/64-Bit&gt;-&lt;PA&gt;-&lt;Build&gt;.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Build = build number of the liveCache (for example, 72.05.07.full)<br />OS = operating system (for example, NT, WIN)<br />PA = processor architecture (for example, i386)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example: SAPDB-SERVER-WIN-32BIT-&lt;i386-7_2_5_7.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<B>- </B><B>Optimizer</B><br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ftp://sapservX/specific/apo/apo30/sp13/SAPAPO_n.SAR<br />n = number of the Support Package (for example, 16)<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Example:<br />- ftp://sapservX/specific/apo/apo30/sp16/SAPAPO_16.SAR</p> <UL><LI> <B>Online service system = OSS (Support Packages):</B></LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can download the following components via the SPAM transaction in the Online Service System (OSS):<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- BASIS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- ABA<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- BW<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;- APO<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS (Online Corr. Support (Tools:Supp.-Pack., Addon Install/Upgr))"}, {"Key": "Responsible                                                                                         ", "Value": "D000325"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037664)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000438337/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000438337/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076"}, {"RefNumber": "445082", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Short dump due to duplicate records in the database", "RefUrl": "/notes/445082"}, {"RefNumber": "442764", "RefComponent": "SCM-TEC", "RefTitle": "SAPKY30A16 deletes texts for InfoObjects", "RefUrl": "/notes/442764"}, {"RefNumber": "442747", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Version deletion does not work for confirmed orders", "RefUrl": "/notes/442747"}, {"RefNumber": "442220", "RefComponent": "SCM-APO-SNP-INS", "RefTitle": "SNP94(2) View shows incorrect lower grid values", "RefUrl": "/notes/442220"}, {"RefNumber": "442090", "RefComponent": "SCM-APO-PPS-HEU-PP", "RefTitle": "Short-dump during the planning with conti-IO", "RefUrl": "/notes/442090"}, {"RefNumber": "441950", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Planning version copy ignores non-working hours", "RefUrl": "/notes/441950"}, {"RefNumber": "440809", "RefComponent": "SCM-APO-FCS", "RefTitle": "/SAPAPO/TSM 252: InfoObject APODPDANT does not exist", "RefUrl": "/notes/440809"}, {"RefNumber": "439550", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Correction for note 433895", "RefUrl": "/notes/439550"}, {"RefNumber": "435902", "RefComponent": "SCM-APO-FCS", "RefTitle": "Correction after you import Support Package 16", "RefUrl": "/notes/435902"}, {"RefNumber": "435833", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 16", "RefUrl": "/notes/435833"}, {"RefNumber": "435471", "RefComponent": "SCM-APO-INT-PPS", "RefTitle": "Insert operation ==> schedule deviation APO-><-R/3", "RefUrl": "/notes/435471"}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027"}, {"RefNumber": "428551", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Drill down is unnecessary", "RefUrl": "/notes/428551"}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957"}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886"}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002"}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248"}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746"}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047"}, {"RefNumber": "361635", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/361635"}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222"}, {"RefNumber": "353197", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/353197"}, {"RefNumber": "352844", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/352844"}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237"}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402"}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218"}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265"}, {"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519"}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "147519", "RefComponent": "BC-FES-GUI", "RefTitle": "Maintenance strategy / deadlines for SAP GUI for Windows / SAP GUI for Java", "RefUrl": "/notes/147519 "}, {"RefNumber": "410002", "RefComponent": "BC-DB-LVC", "RefTitle": "Setting for MAXCPU as of liveCache 7.2.5 Build 4", "RefUrl": "/notes/410002 "}, {"RefNumber": "424886", "RefComponent": "BC-DB-LVC", "RefTitle": "Parameter values as of liveCache Version 7.2.5", "RefUrl": "/notes/424886 "}, {"RefNumber": "445082", "RefComponent": "SCM-APO-SNP-OPT", "RefTitle": "Short dump due to duplicate records in the database", "RefUrl": "/notes/445082 "}, {"RefNumber": "442747", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Version deletion does not work for confirmed orders", "RefUrl": "/notes/442747 "}, {"RefNumber": "442090", "RefComponent": "SCM-APO-PPS-HEU-PP", "RefTitle": "Short-dump during the planning with conti-IO", "RefUrl": "/notes/442090 "}, {"RefNumber": "441950", "RefComponent": "SCM-APO-MD-VM", "RefTitle": "Planning version copy ignores non-working hours", "RefUrl": "/notes/441950 "}, {"RefNumber": "439550", "RefComponent": "SCM-APO-SNP-DPL", "RefTitle": "Correction for note 433895", "RefUrl": "/notes/439550 "}, {"RefNumber": "435902", "RefComponent": "SCM-APO-FCS", "RefTitle": "Correction after you import Support Package 16", "RefUrl": "/notes/435902 "}, {"RefNumber": "373047", "RefComponent": "BC-ABA-SC", "RefTitle": "Error in field input processing", "RefUrl": "/notes/373047 "}, {"RefNumber": "428551", "RefComponent": "SCM-APO-FCS-BF", "RefTitle": "Drill down is unnecessary", "RefUrl": "/notes/428551 "}, {"RefNumber": "406248", "RefComponent": "BC-DB-LCA", "RefTitle": "liveCache Connect problems", "RefUrl": "/notes/406248 "}, {"RefNumber": "325402", "RefComponent": "BC-DB-LVC", "RefTitle": "dbadaslib/dbsdbslib: How do I apply a patch?", "RefUrl": "/notes/325402 "}, {"RefNumber": "361222", "RefComponent": "BC-FES-INS", "RefTitle": "SapPatch: Importing GUI Patches", "RefUrl": "/notes/361222 "}, {"RefNumber": "328237", "RefComponent": "BW-SYS", "RefTitle": "Importing Support Packages into a BW 2.0B system", "RefUrl": "/notes/328237 "}, {"RefNumber": "432027", "RefComponent": "BC-UPG-OCS", "RefTitle": "Strategy for using SAP Support Packages", "RefUrl": "/notes/432027 "}, {"RefNumber": "391746", "RefComponent": "SCM-TEC", "RefTitle": "COM Routines and liveCache Versions on UNIX", "RefUrl": "/notes/391746 "}, {"RefNumber": "442764", "RefComponent": "SCM-TEC", "RefTitle": "SAPKY30A16 deletes texts for InfoObjects", "RefUrl": "/notes/442764 "}, {"RefNumber": "427957", "RefComponent": "SCM-APO", "RefTitle": "Release restrictions for APO 3.0A Support Release 3", "RefUrl": "/notes/427957 "}, {"RefNumber": "533076", "RefComponent": "BC-DB-LVC", "RefTitle": "Importing a liveCache version as of 7.2.5 B18", "RefUrl": "/notes/533076 "}, {"RefNumber": "157265", "RefComponent": "BC-DB-LVC", "RefTitle": "Exchanging COM objects for liveCache in APO 3.0", "RefUrl": "/notes/157265 "}, {"RefNumber": "147218", "RefComponent": "SCM-APO", "RefTitle": "SAP APO Demand Planning - datamart ==> BW patches", "RefUrl": "/notes/147218 "}, {"RefNumber": "442220", "RefComponent": "SCM-APO-SNP-INS", "RefTitle": "SNP94(2) View shows incorrect lower grid values", "RefUrl": "/notes/442220 "}, {"RefNumber": "314218", "RefComponent": "BC-DB-LCA", "RefTitle": "Transaction data conversion between SPs for APO 3.0", "RefUrl": "/notes/314218 "}, {"RefNumber": "435833", "RefComponent": "SCM-APO-OPT", "RefTitle": "APO 3.0 Optimizer Support Package 16", "RefUrl": "/notes/435833 "}, {"RefNumber": "435471", "RefComponent": "SCM-APO-INT-PPS", "RefTitle": "Insert operation ==> schedule deviation APO-><-R/3", "RefUrl": "/notes/435471 "}, {"RefNumber": "440809", "RefComponent": "SCM-APO-FCS", "RefTitle": "/SAPAPO/TSM 252: InfoObject APODPDANT does not exist", "RefUrl": "/notes/440809 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}