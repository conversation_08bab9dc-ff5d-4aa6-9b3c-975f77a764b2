{"Request": {"Number": "385099", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 832, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001691542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000385099?language=E&token=65C33BD4F814E6ABC849E6E32EC5853F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000385099", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000385099/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "385099"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.01.2002"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-LO"}, "SAPComponentKeyText": {"_label": "Component", "value": "BW only - Logistics - General"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Logistics - General", "value": "BW-BCT-LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "385099 - Serialized data transfer into BW system"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>If the ODS is used in the BW system so that it is not updated additively but so that also data is overwritten, data must always be transferred to the BW system in the correct order.<br />That means, if a document is changed several times prior to a data transfer into the BW system and all changes are transferred separately, these changes must be transmitted to the BW system in the correct order so that the last (current) value always exists in the fields that are overwritten.<br />However, this cannot be ensured due to the existing transfer method of the logistics data although it is extremely improbable that a case occurs in which the last change to a document is transferred prior to another change to the same document.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>LBWE</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The basis technology of the collective update could not do this.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>You can Implement the attached source code corrections. However, the order (with the accuracy described in Note 384211) can only be guaranteed with Note 384211.<br />Bear in mind that separate correction instructions are available for application '17' (Plant maintainance) and '18' (Customer Service).<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BCT-PP (Production Planning and Control)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-IM (BW only - Inventory Management)"}, {"Key": "Other Components", "Value": "BW-BCT-MM-PUR (BW only - Purchase)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-SLS (BW only - Sales)"}, {"Key": "Other Components", "Value": "BW-BCT-MM (BW only - Materials Management)"}, {"Key": "Other Components", "Value": "BW-BCT-PP-PP (BW only - Production Planning)"}, {"Key": "Other Components", "Value": "BW-BCT-SD-BIL (BW only - Billing)"}, {"Key": "Other Components", "Value": "BW-BCT-QM (BW only - Quality Management)"}, {"Key": "Other Components", "Value": "BW-BCT-LE (Logistics Execution)"}, {"Key": "Other Components", "Value": "BW-BCT-SD (BW only - Sales and Distribution)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027201)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027201)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000385099/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000385099/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "384211", "RefComponent": "BC-CST-UP", "RefTitle": "Sorting collective run updates", "RefUrl": "/notes/384211"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "384211", "RefComponent": "BC-CST-UP", "RefTitle": "Sorting collective run updates", "RefUrl": "/notes/384211 "}, {"RefNumber": "586163", "RefComponent": "BW-BCT-MM-IM", "RefTitle": "Composite SAP Note for SAP ERP Inventory Management in SAP BW", "RefUrl": "/notes/586163 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "1999_1_40B", "To": "1999_1_46B", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2000_1_40B", "To": "2000_1_46C", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2000_2_40B", "To": "2000_2_46C", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "1999_1_40B", "To": "1999_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2000_1_40B", "To": "2000_1_45B", "Subsequent": ""}, {"SoftwareComponent": "PI-A", "From": "2000_2_40B", "To": "2000_2_45B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "PI-A 2000_2_40B", "SupportPackage": "SAPKINCD24", "URL": "/supportpackage/SAPKINCD24"}, {"SoftwareComponentVersion": "PI-A 2000_2_45B", "SupportPackage": "SAPKINCE24", "URL": "/supportpackage/SAPKINCE24"}, {"SoftwareComponentVersion": "PI 2000_2_46B", "SupportPackage": "SAPKIPZC34", "URL": "/supportpackage/SAPKIPZC34"}, {"SoftwareComponentVersion": "PI 2000_2_46C", "SupportPackage": "SAPKIPZC44", "URL": "/supportpackage/SAPKIPZC44"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "PI-A", "NumberOfCorrin": 4, "URL": "/corrins/0000385099/52"}, {"SoftwareComponent": "PI", "NumberOfCorrin": 4, "URL": "/corrins/0000385099/48"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "PI", "ValidFrom": "2000_1_40B", "ValidTo": "2000_1_46C", "Number": "302509 ", "URL": "/notes/302509 ", "Title": "BW-Plugin 2000.1: Preparing change to V3 update", "Component": "BW-BCT-SD"}, {"SoftwareComponent": "PI-A", "ValidFrom": "2000_1_40B", "ValidTo": "2000_1_45B", "Number": "302509 ", "URL": "/notes/302509 ", "Title": "BW-Plugin 2000.1: Preparing change to V3 update", "Component": "BW-BCT-SD"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}