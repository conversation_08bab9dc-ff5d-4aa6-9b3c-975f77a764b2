{"Request": {"Number": "2226096", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 445, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018180142017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002226096?language=E&token=803D8D6D5BE1046F7F6E09A759E90096"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002226096", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002226096/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2226096"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "25.09.2019"}, "SAPComponentKey": {"_label": "Component", "value": "LO-CMM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Commodity Management in Logistics"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Commodity Management in Logistics", "value": "LO-CMM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-CMM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2226096 - Simplification List: Commodity Management in SAP S/4HANA OP"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><span style=\"font-size: 12pt; font-family: 'Arial',sans-serif; line-height: 107%; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">Commodity Management is activated in your system and you are planning to migrate to SAP&#160;S/4HANA. A pre-check of the migration preparation indicates, that you are not able to convert to SAP S/4HANA with this installation. </span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Commodity Management, Commodity Pricing Engine (CPE),&#160;Commodity Procurement, Commodity Sales, Commodity Risk Management, Commodity Derivatives, Commodity Dervative Contract Specification (DCS), Commodity Configurable Parameters and Formulas (CPF)</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><strong>Target Release SAP S/4HANA, on-premise edition 1511 and SAP S/4HANA 1610:</strong></p>\r\n<p style=\"padding-left: 30px;\">If SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\r\n<p style=\"padding-left: 30px;\">If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\r\n<ul style=\"padding-left: 30px;\">\r\n<li style=\"padding-left: 30px;\">LOG_CPE_SD_MM</li>\r\n<li style=\"padding-left: 30px;\">LOG_CPE_FA_FE</li>\r\n<li style=\"padding-left: 30px;\">LOG_MM_COMMODITY</li>\r\n<li style=\"padding-left: 30px;\">LOG_MM_COMMODITY_02</li>\r\n<li style=\"padding-left: 30px;\">LOG_MM_COMMODITY_03</li>\r\n<li style=\"padding-left: 30px;\">LOG_MM_COMMODITY_04</li>\r\n<li style=\"padding-left: 30px;\">LOG_MM_COMMODITY_05</li>\r\n<li style=\"padding-left: 30px;\">LOG_SD_COMMODITY</li>\r\n<li style=\"padding-left: 30px;\">LOG_SD_COMMODITY_02</li>\r\n<li style=\"padding-left: 30px;\">LOG_SD_COMMODITY_03</li>\r\n<li style=\"padding-left: 30px;\">LOG_SD_COMMODITY_04</li>\r\n<li style=\"padding-left: 30px;\">LOG_SD_COMMODITY_05</li>\r\n<li style=\"padding-left: 30px;\">ISR_GLT_CMMINTEG</li>\r\n<li style=\"padding-left: 30px;\">ISR_GLT_CMMINTEG_2</li>\r\n<li style=\"padding-left: 30px;\">ISR_GLT_IFW</li>\r\n<li style=\"padding-left: 30px;\">LOG_TRM_INT_1</li>\r\n<li style=\"padding-left: 30px;\">LOG_TRM_INT_2</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM_2</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM_3</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM_4</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM_5</li>\r\n<li style=\"padding-left: 30px;\">FIN_TRM_COMM_RM_6</li>\r\n<li style=\"padding-left: 30px;\">COMMODITY_PROC_SALES_01</li>\r\n<li style=\"padding-left: 30px;\">COMMODITY_MANAGEMENT_01</li>\r\n<li style=\"padding-left: 30px;\">COMMODITY_MANAGEMENT_02</li>\r\n<li style=\"padding-left: 30px;\">COMMODITY_MANAGEMENT_03</li>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">These Business Functions&#160;support the following main business processes and features:</p>\r\n<ul style=\"padding-left: 30px;\">\r\n<li style=\"padding-left: 30px;\">Commodity Management Integration in Sales E2E Process and Documents</li>\r\n<li style=\"padding-left: 30px;\">Commodity Management Integration in Procurement E2E Process and Documents</li>\r\n<li style=\"padding-left: 30px;\">Commodity Management Integration in Global Trade Management</li>\r\n<li style=\"padding-left: 30px;\">Commodity Pricing / Commodity Pricing Engine (CPE) / Configurable Parameters &amp; Formulas (CPF)</li>\r\n<li style=\"padding-left: 30px;\">Basis &amp; Future Pricing</li>\r\n<li style=\"padding-left: 30px;\">Differential / Final Invoicing &amp; Billing</li>\r\n<li style=\"padding-left: 30px;\">Commodity master data</li>\r\n<li style=\"padding-left: 30px;\">Market data management for commodity prices and price curves</li>\r\n<li style=\"padding-left: 30px;\">Exposure Management for commodity price risk exposures</li>\r\n<li style=\"padding-left: 30px;\">Financial transaction management for commodity derivatives</li>\r\n<li style=\"padding-left: 30px;\">Market Risk Analyzer functionality for commodity derivatives and commodity price risk exposures</li>\r\n<li style=\"padding-left: 30px;\">Day End / Month End Processing</li>\r\n<li style=\"padding-left: 30px;\">Commodity Analytics (End of Day, Position Reporting, Mark to Market, Profit and Loss)</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong>Target Release SAP S/4HANA 1709 (on premise):</strong></p>\r\n<blockquote>\r\n<p><strong>FPS0:</strong></p>\r\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\r\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP&#160;S/4HANA:</p>\r\n<ul>\r\n<li>ISR_GLT_CMMINTEG</li>\r\n<li>ISR_GLT_CMMINTEG_2</li>\r\n<li>ISR_GLT_IFW</li>\r\n<li>LOG_TRM_INT_1</li>\r\n<li>LOG_TRM_INT_2</li>\r\n<li>FIN_TRM_COMM_RM</li>\r\n<li>FIN_TRM_COMM_RM_2</li>\r\n<li>FIN_TRM_COMM_RM_3</li>\r\n<li>FIN_TRM_COMM_RM_4</li>\r\n<li>FIN_TRM_COMM_RM_5</li>\r\n<li>FIN_TRM_COMM_RM_6</li>\r\n<li>COMMODITY_MANAGEMENT_01</li>\r\n<li>COMMODITY_MANAGEMENT_02</li>\r\n<li>COMMODITY_MANAGEMENT_03</li>\r\n<li>COMMODITY_MANAGEMENT_04</li>\r\n</ul>\r\n<p>A&#160;conversion is possible if only Business Function of the following list are activated:</p>\r\n<ul>\r\n<li>LOG_CPE_SD_MM</li>\r\n<li>LOG_CPE_FA_FE</li>\r\n<li>LOG_MM_COMMODITY</li>\r\n<li>LOG_MM_COMMODITY_02</li>\r\n<li>LOG_MM_COMMODITY_03</li>\r\n<li>LOG_MM_COMMODITY_04</li>\r\n<li>LOG_MM_COMMODITY_05</li>\r\n<li>LOG_SD_COMMODITY</li>\r\n<li>LOG_SD_COMMODITY_02</li>\r\n<li>LOG_SD_COMMODITY_03</li>\r\n<li>LOG_SD_COMMODITY_04</li>\r\n<li>LOG_SD_COMMODITY_05</li>\r\n<li>COMMODITY_PROC_SALES_01</li>\r\n</ul>\r\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\r\n<ul>\r\n<li>2461007&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified Data Model</li>\r\n<li>2461004&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified CPE Activation</li>\r\n<li>2461021&#160;&#160;&#160;&#160;&#160;&#160;&#160;S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\r\n<li>2461014&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\r\n<li>2460737&#160;&#160;&#160;&#160;&#160; &#160;S4TWL - CPE Simplified Market Data</li>\r\n</ul>\r\n<p><strong>FPS1:</strong></p>\r\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\r\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP S/4HANA:</p>\r\n<ul>\r\n<li>ISR_GLT_CMMINTEG</li>\r\n<li>ISR_GLT_CMMINTEG_2</li>\r\n<li>ISR_GLT_IFW</li>\r\n</ul>\r\n<p>A conversion is possible if only Business Function of the following list are activated:</p>\r\n<ul>\r\n<li>LOG_CPE_SD_MM</li>\r\n<li>LOG_CPE_FA_FE</li>\r\n<li>LOG_MM_COMMODITY</li>\r\n<li>LOG_MM_COMMODITY_02</li>\r\n<li>LOG_MM_COMMODITY_03</li>\r\n<li>LOG_MM_COMMODITY_04</li>\r\n<li>LOG_MM_COMMODITY_05</li>\r\n<li>LOG_SD_COMMODITY</li>\r\n<li>LOG_SD_COMMODITY_02</li>\r\n<li>LOG_SD_COMMODITY_03</li>\r\n<li>LOG_SD_COMMODITY_04</li>\r\n<li>LOG_SD_COMMODITY_05</li>\r\n<li>COMMODITY_PROC_SALES_01</li>\r\n<li>LOG_TRM_INT_1</li>\r\n<li>LOG_TRM_INT_2</li>\r\n<li>FIN_TRM_COMM_RM</li>\r\n<li>FIN_TRM_COMM_RM_2</li>\r\n<li>FIN_TRM_COMM_RM_3</li>\r\n<li>FIN_TRM_COMM_RM_4</li>\r\n<li>FIN_TRM_COMM_RM_5</li>\r\n<li>FIN_TRM_COMM_RM_6</li>\r\n<li>COMMODITY_MANAGEMENT_01</li>\r\n<li>COMMODITY_MANAGEMENT_02</li>\r\n<li>COMMODITY_MANAGEMENT_03</li>\r\n<li>COMMODITY_MANAGEMENT_04</li>\r\n</ul>\r\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\r\n<ul>\r\n<li>2461007&#160;&#160;&#160; &#160;&#160;&#160;S4TWL - CM: CPE Simplified Data Model</li>\r\n<li>2461004&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified CPE Activation</li>\r\n<li>2461021&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\r\n<li>2461014&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\r\n<li>2460737&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CPE Simplified Market Data</li>\r\n<li>2551943&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified Commodity Curves</li>\r\n<li>2551960&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified DCS Access</li>\r\n<li>2556089&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification in Position Reporting for Financial Transactions</li>\r\n<li>2547347&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\r\n<li>2547343&#160;&#160; &#160;&#160;&#160; S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\r\n<li>2556106&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Hedge Management for Logistics Transactions</li>\r\n<li>2556134&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Agri-Specific Commodity Position Queries</li>\r\n<li>2560298&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\r\n<li>2555990&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\r\n<li>2556063&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\r\n<li>2556164&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:&#160; DCS ID Extension</li>\r\n<li>2556220&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\r\n<li>2556176&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\r\n<li>2554440&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplified Commodity Management Data Model / Master Data</li>\r\n<li>2556103&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Mark-to-Market (MtM) and Profit/Loss (P&amp;L) Reporting</li>\r\n<li>2551942&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration to GTM not supported</li>\r\n</ul>\r\n<p><strong>FPS2:</strong></p>\r\n<p>The following simplification items have to be observed in addition to the list for OP1709 FPS1. See the following notes:</p>\r\n<ul>\r\n<li>2591598&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\r\n<li>2596369&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\r\n<li>2608098&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark to Market Accounting Changes</li>\r\n</ul>\r\n<p>Note 2556103 becomes obsolete.</p>\r\n</blockquote>\r\n<p><strong>Target Release SAP S/4HANA 1809 (on premise):</strong><strong>&#160;</strong></p>\r\n<blockquote>\r\n<p><strong>FPS0:</strong></p>\r\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\r\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP&#160;S/4HANA:</p>\r\n<ul>\r\n<li>ISR_GLT_CMMINTEG_2</li>\r\n<li>ISR_GLT_IFW</li>\r\n</ul>\r\n<p>A conversion is possible if only Business Functions of the following list are activated:</p>\r\n<ul>\r\n<li>ISR_GLT_CMMINTEG</li>\r\n<li>LOG_CPE_SD_MM</li>\r\n<li>LOG_CPE_FA_FE</li>\r\n<li>LOG_MM_COMMODITY</li>\r\n<li>LOG_MM_COMMODITY_02</li>\r\n<li>LOG_MM_COMMODITY_03</li>\r\n<li>LOG_MM_COMMODITY_04</li>\r\n<li>LOG_MM_COMMODITY_05</li>\r\n<li>LOG_SD_COMMODITY</li>\r\n<li>LOG_SD_COMMODITY_02</li>\r\n<li>LOG_SD_COMMODITY_03</li>\r\n<li>LOG_SD_COMMODITY_04</li>\r\n<li>LOG_SD_COMMODITY_05</li>\r\n<li>COMMODITY_PROC_SALES_01</li>\r\n<li>LOG_TRM_INT_1</li>\r\n<li>LOG_TRM_INT_2</li>\r\n<li>FIN_TRM_COMM_RM</li>\r\n<li>FIN_TRM_COMM_RM_2</li>\r\n<li>FIN_TRM_COMM_RM_3</li>\r\n<li>FIN_TRM_COMM_RM_4</li>\r\n<li>FIN_TRM_COMM_RM_5</li>\r\n<li>FIN_TRM_COMM_RM_6</li>\r\n<li>COMMODITY_MANAGEMENT_01</li>\r\n<li>COMMODITY_MANAGEMENT_02</li>\r\n<li>COMMODITY_MANAGEMENT_03</li>\r\n<li>COMMODITY_MANAGEMENT_04</li>\r\n</ul>\r\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\r\n<ul>\r\n<li>2461007&#160;&#160;&#160; &#160;&#160;&#160;S4TWL - CM: CPE Simplified Data Model</li>\r\n<li>2461004&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified CPE Activation</li>\r\n<li>2461021&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\r\n<li>2461014&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\r\n<li>2460737&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CPE Simplified Market Data</li>\r\n<li>2551943&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified Commodity Curves</li>\r\n<li>2551960&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified DCS Access</li>\r\n<li>2556089&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification in Position Reporting for Financial Transactions</li>\r\n<li>2547347&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\r\n<li>2547343&#160;&#160; &#160;&#160;&#160; S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\r\n<li>2556106&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Hedge Management for Logistics Transactions</li>\r\n<li>2556134&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Agri-Specific Commodity Position Queries</li>\r\n<li>2560298&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\r\n<li>2555990&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\r\n<li>2556063&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\r\n<li>2556164&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:&#160; DCS ID Extension</li>\r\n<li>2556220&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\r\n<li>2556176&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\r\n<li>2554440&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplified Commodity Management Data Model / Master Data</li>\r\n<li>2591598&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\r\n<li>2596369&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\r\n<li>2608098&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark to Market Accounting Changes</li>\r\n<li>2671009&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\r\n<li>2672696&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\r\n<li>2671010&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Functionalities on roadmap for CM-GTM Integration</li>\r\n<li>2710843 &#160; &#160; &#160;&#160;S4TWL &#8211; Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)</li>\r\n</ul>\r\n<p>Note 2551942 becomes obsolete.</p>\r\n<p><strong>FPS1:</strong></p>\r\n<p>If the following SAP Commodity Management Business Functions are active in your system, you cannot immediately convert to SAP S/4HANA. A conversion pre-check checks the use of these active Business Functions and prevents the conversion.</p>\r\n<p>If one or more of the following Business Functions are active in your system, you cannot migrate to SAP&#160;S/4HANA:</p>\r\n<ul>\r\n<li>ISR_GLT_IFW</li>\r\n</ul>\r\n<p>A conversion is possible if only Business Functions of the following list are activated:</p>\r\n<ul>\r\n<li>ISR_GLT_CMMINTEG</li>\r\n<li>ISR_GLT_CMMINTEG_2</li>\r\n<li>LOG_CPE_SD_MM</li>\r\n<li>LOG_CPE_FA_FE</li>\r\n<li>LOG_MM_COMMODITY</li>\r\n<li>LOG_MM_COMMODITY_02</li>\r\n<li>LOG_MM_COMMODITY_03</li>\r\n<li>LOG_MM_COMMODITY_04</li>\r\n<li>LOG_MM_COMMODITY_05</li>\r\n<li>LOG_SD_COMMODITY</li>\r\n<li>LOG_SD_COMMODITY_02</li>\r\n<li>LOG_SD_COMMODITY_03</li>\r\n<li>LOG_SD_COMMODITY_04</li>\r\n<li>LOG_SD_COMMODITY_05</li>\r\n<li>COMMODITY_PROC_SALES_01</li>\r\n<li>LOG_TRM_INT_1</li>\r\n<li>LOG_TRM_INT_2</li>\r\n<li>FIN_TRM_COMM_RM</li>\r\n<li>FIN_TRM_COMM_RM_2</li>\r\n<li>FIN_TRM_COMM_RM_3</li>\r\n<li>FIN_TRM_COMM_RM_4</li>\r\n<li>FIN_TRM_COMM_RM_5</li>\r\n<li>FIN_TRM_COMM_RM_6</li>\r\n<li>COMMODITY_MANAGEMENT_01</li>\r\n<li>COMMODITY_MANAGEMENT_02</li>\r\n<li>COMMODITY_MANAGEMENT_03</li>\r\n<li>COMMODITY_MANAGEMENT_04</li>\r\n</ul>\r\n<p>If the requirements for a migration are fulfilled, then additional simplification items are relevant, please see also the notes:</p>\r\n<ul>\r\n<li>2461007&#160;&#160;&#160; &#160;&#160;&#160;S4TWL - CM: CPE Simplified Data Model</li>\r\n<li>2461004&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified CPE Activation</li>\r\n<li>2461021&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\r\n<li>2461014&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\r\n<li>2460737&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CPE Simplified Market Data</li>\r\n<li>2551943&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified Commodity Curves</li>\r\n<li>2551960&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified DCS Access</li>\r\n<li>2556089&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification in Position Reporting for Financial Transactions</li>\r\n<li>2547347&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\r\n<li>2547343&#160;&#160; &#160;&#160;&#160; S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\r\n<li>2556106&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Hedge Management for Logistics Transactions</li>\r\n<li>2556134&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Agri-Specific Commodity Position Queries</li>\r\n<li>2560298&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\r\n<li>2555990&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\r\n<li>2556063&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\r\n<li>2556164&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:&#160; DCS ID Extension</li>\r\n<li>2556220&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\r\n<li>2556176&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\r\n<li>2554440&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplified Commodity Management Data Model / Master Data</li>\r\n<li>2591598&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\r\n<li>2596369&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\r\n<li>2608098&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark to Market Accounting Changes</li>\r\n<li>2671009&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\r\n<li>2672696&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\r\n<li>2710843 &#160; &#160; &#160;&#160;S4TWL &#8211; Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)</li>\r\n</ul>\r\n<p>Note 2671010 (S4TWL - Functionalities on roadmap for CM-GTM Integration) becomes obsolete.</p>\r\n<p><strong>FPS2:</strong></p>\r\n<p>No SAP Commodity Management Business Function blocks the conversion to SAP S/4 HANA beginning with target release S/4 HANA OP 1809 FPS2.</p>\r\n<p>The following&#160;simplification items are furtheron relevant and have to be observed:</p>\r\n<ul>\r\n<li>2461007&#160;&#160;&#160; &#160;&#160;&#160;S4TWL - CM: CPE Simplified Data Model</li>\r\n<li>2461004&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified CPE Activation</li>\r\n<li>2461021&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplified Formula Assembley &amp; Formula Evaluation</li>\r\n<li>2461014&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: CPE Simplification of Routines &amp; Rules</li>\r\n<li>2460737&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CPE Simplified Market Data</li>\r\n<li>2551943&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified Commodity Curves</li>\r\n<li>2551960&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Simplified DCS Access</li>\r\n<li>2556089&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification in Position Reporting for Financial Transactions</li>\r\n<li>2547347&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data</li>\r\n<li>2547343&#160;&#160; &#160;&#160;&#160; S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting</li>\r\n<li>2556106&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Hedge Management for Logistics Transactions</li>\r\n<li>2556134&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Agri-Specific Commodity Position Queries</li>\r\n<li>2560298&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Commodity Pricing in MM Contracts and Scheduling Agreements</li>\r\n<li>2555990&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Unification of Technologies for Analytical Data Provision</li>\r\n<li>2556063&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Commodity Price Risk Determination from Schedule Lines</li>\r\n<li>2556164&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:&#160; DCS ID Extension</li>\r\n<li>2556220&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions</li>\r\n<li>2556176&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics</li>\r\n<li>2554440&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Simplified Commodity Management Data Model / Master Data</li>\r\n<li>2591598&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - CM: Mark-to-Market &amp; Profit-and-Loss for logistics transactions</li>\r\n<li>2596369&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark-to-Market &amp; Profit-and-Loss Reporting for Derivatives</li>\r\n<li>2608098&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Mark to Market Accounting Changes</li>\r\n<li>2671009&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Pricing and Payment Events in GTM with Commodity Management</li>\r\n<li>2672696&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported</li>\r\n<li>2671010&#160;&#160;&#160;&#160;&#160;&#160; S4TWL - Functionalities on roadmap for CM-GTM Integration</li>\r\n<li>2757768 &#160; &#160; &#160;&#160;Deletion of business function ISR_GLT_IFW</li>\r\n</ul>\r\n<p>Note 2710843 (S4TWL &#8211; Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)) becomes obsolete.</p>\r\n</blockquote>\r\n<div><strong>Target Release SAP S/4HANA 1909 (on premise) and following:</strong></div>\r\n<blockquote>\r\n<p>Observe the section for release SAP S/4HANA OP1809 FPS2.</p>\r\n<blockquote></blockquote>\r\n</blockquote>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Further Commodity Management functionality is on the roadmap for S/4 HANA.</p>\r\n<p>For further information please contact us on <a target=\"_blank\" href=\"http://www.sap.com/products/commodity-management.html\">www.sap.com/products/commodity-management.html</a>&#160;or write&#160;an email to <a target=\"_blank\" href=\"mailto:<EMAIL>\"><EMAIL></a></p>\r\n<p>&#160;</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FIN-FSCM-TRM-CRM (Financial Risk Management for Commodities)"}, {"Key": "Other Components", "Value": "MM-PUR-GF-CPE (CPE in MM)"}, {"Key": "Other Components", "Value": "CA-GTF-PR-CPF (Configurable Parameters and Formulas)"}, {"Key": "Other Components", "Value": "LO-GT-CMM (Commodity Management in Logistics Integration)"}, {"Key": "Other Components", "Value": "CA-GTF-CPE (Commodity Pricing Engine)"}, {"Key": "Other Components", "Value": "SD-BF-CPE (CPE in SD)"}, {"Key": "Other Components", "Value": "LO-INT-TRM (Commodity Management - Logistics Integration into TRM)"}, {"Key": "Responsible                                                                                         ", "Value": "RALF MUELLER (D019691)"}, {"Key": "Processor                                                                                           ", "Value": "RALF MUELLER (D019691)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002226096/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002226096/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2672696", "RefComponent": "LO-GT-CMM", "RefTitle": "S4TWL – Integration of Contract Processing Monitor(CPM) and Logistical Options(LOP) with GTM not supported", "RefUrl": "/notes/2672696"}, {"RefNumber": "2671010", "RefComponent": "LO-GT-CMM", "RefTitle": "S4TWL – Functionalities on roadmap for CM-GTM Integration", "RefUrl": "/notes/2671010"}, {"RefNumber": "2671009", "RefComponent": "LO-GT-CMM", "RefTitle": "S4TWL – Integration of Pricing and Payment Events in GTM with Commodity Management", "RefUrl": "/notes/2671009"}, {"RefNumber": "2608098", "RefComponent": "LO-CMM", "RefTitle": "S4 PreChecks: Conversion Report of Mark-to-Market Accounting for transition from Business Suite to S/4 HANA", "RefUrl": "/notes/2608098"}, {"RefNumber": "2596369", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives", "RefUrl": "/notes/2596369"}, {"RefNumber": "2591598", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Mark-to-Market & Profit-and-Loss for logistics transactions", "RefUrl": "/notes/2591598"}, {"RefNumber": "2560298", "RefComponent": "CA-GTF-CPE", "RefTitle": "S4TWL - Commodity Pricing in Purchasing Contracts and Scheduling Agreements", "RefUrl": "/notes/2560298"}, {"RefNumber": "2556220", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions", "RefUrl": "/notes/2556220"}, {"RefNumber": "2556176", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics Calculator", "RefUrl": "/notes/2556176"}, {"RefNumber": "2556164", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension", "RefUrl": "/notes/2556164"}, {"RefNumber": "2556134", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Agri-Specific Commodity Position Queries", "RefUrl": "/notes/2556134"}, {"RefNumber": "2556106", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Hedge Management for Logistics Transactions", "RefUrl": "/notes/2556106"}, {"RefNumber": "2556103", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Mark-to-Market (MtM) and Profit/Loss (P&L) Reporting", "RefUrl": "/notes/2556103"}, {"RefNumber": "2556089", "RefComponent": "FIN-FSCM-TRM-CRM-TM", "RefTitle": "S4TWL - Simplification in Position Reporting for Financial Transactions", "RefUrl": "/notes/2556089"}, {"RefNumber": "2556063", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Commodity Price Risk Determination from Schedule Lines", "RefUrl": "/notes/2556063"}, {"RefNumber": "2555990", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Unification of Technologies for Analytical Data Provision", "RefUrl": "/notes/2555990"}, {"RefNumber": "2554440", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplified Commodity Management Data Model / Master Data", "RefUrl": "/notes/2554440"}, {"RefNumber": "2551960", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL - CM: Simplified DCS Access", "RefUrl": "/notes/2551960"}, {"RefNumber": "2551943", "RefComponent": "LO-CMM-BF", "RefTitle": "S/4HANA: Commodity Management - Simplified Commodity Curves", "RefUrl": "/notes/2551943"}, {"RefNumber": "2551942", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2551942"}, {"RefNumber": "2547347", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Commodity Position Reporting on Versioned Pricing Data", "RefUrl": "/notes/2547347"}, {"RefNumber": "2547343", "RefComponent": "LO-CMM-ANL", "RefTitle": "S4TWL - CM: Simplified Data Flow of Logistics Data for Commodity Position Reporting", "RefUrl": "/notes/2547343"}, {"RefNumber": "2461021", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL -  CM: CPE Simplified Formula Assembley & Formula Evaluation", "RefUrl": "/notes/2461021"}, {"RefNumber": "2461014", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL -  CM: CPE Simplification of Routines & Rules", "RefUrl": "/notes/2461014"}, {"RefNumber": "2461007", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL -  CM: CPE Simplified Data Model", "RefUrl": "/notes/2461007"}, {"RefNumber": "2461004", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL -  CM: CPE Simplified CPE Activation", "RefUrl": "/notes/2461004"}, {"RefNumber": "2460737", "RefComponent": "LO-CMM-BF", "RefTitle": "S4TWL -  CPE Simplified Market Data", "RefUrl": "/notes/2460737"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2200407", "RefComponent": "MM-PUR-REQ", "RefTitle": "MM-Transition to S/4: Business Functions", "RefUrl": "/notes/2200407 "}, {"RefNumber": "2556164", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplification of DCS-Based Market Data Mngmt for Financial Transactions:  DCS ID Extension", "RefUrl": "/notes/2556164 "}, {"RefNumber": "2556220", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplification of DCS-Based Market Data Management for Financial Transactions", "RefUrl": "/notes/2556220 "}, {"RefNumber": "2556176", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Deprecation of Non-DCS-Based Market Data for Financial Transactions 2: VaR, Market Data Shifts and Scenarios, Statistics Calculator", "RefUrl": "/notes/2556176 "}, {"RefNumber": "2596369", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives", "RefUrl": "/notes/2596369 "}, {"RefNumber": "2556231", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S/4HANA: Commodity Management - Simplified Data Model/Master Data", "RefUrl": "/notes/2556231 "}, {"RefNumber": "2554440", "RefComponent": "FIN-FSCM-TRM-CRM", "RefTitle": "S4TWL - Simplified Commodity Management Data Model / Master Data", "RefUrl": "/notes/2554440 "}, {"RefNumber": "2534268", "RefComponent": "IS-OIL-DS", "RefTitle": "S4TWL IS_OIL & Commodity Pricing Engine Integration 1709", "RefUrl": "/notes/2534268 "}, {"RefNumber": "2270469", "RefComponent": "LO-CMM", "RefTitle": "S4TWL - Commodity Risk Management", "RefUrl": "/notes/2270469 "}, {"RefNumber": "2267741", "RefComponent": "LO-CMM", "RefTitle": "S4TWL - Commodity Management Procurement", "RefUrl": "/notes/2267741 "}, {"RefNumber": "2267352", "RefComponent": "LO-CMM", "RefTitle": "S4TWL - Commodity Management Sales", "RefUrl": "/notes/2267352 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}