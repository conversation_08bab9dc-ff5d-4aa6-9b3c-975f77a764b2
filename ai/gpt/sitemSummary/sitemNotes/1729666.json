{"Request": {"Number": "1729666", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 330, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017455472017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001729666?language=E&token=F3E57B13E371128E437E83C5FF487BE9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001729666", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001729666/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1729666"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CCM-PRN"}, "SAPComponentKeyText": {"_label": "Component", "value": "Print and Output Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Computer Center Management System (CCMS)", "value": "BC-CCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Print and Output Management", "value": "BC-CCM-PRN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CCM-PRN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1729666 - Enhanced print options for ADS printing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You can use transaction SPAD to set the print options and print tickets for the output device in the output device definition (print options and print tickets tab pages).</p>\r\n<ul>\r\n<li>During form printing, the print options are available on the screen on which you define print parameters when choosing the button for additional options.</li>\r\n</ul>\r\n<ul>\r\n<li>You can choose &quot;Print Tickets&quot; to give a name to a set of print options that you can then pass to the form interface in the print program (for example, using the parameter OPTIONS-PRINTTICKET in SAPscript when calling the function module OPEN_FORM). You can also specify a standard print ticket that the system uses if you did not select any of the other advanced print options.</li>\r\n</ul>\r\n<p><br />Up to now, the advanced print options worked only for forms of the type SAPScript and Smart Forms. Customers also wanted them to be available for SAP Interactive Forms by Adobe.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>POSS, Print Options</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>In order to use the advanced print options for printing Interactive Forms, the following conditions must be met:</p>\r\n<ul>\r\n<li>Import the Support Package that is assigned to this SAP Note.</li>\r\n</ul>\r\n<ul>\r\n<li>The ADS must support this function (see SAP Note 1806471).</li>\r\n</ul>\r\n<p><br /><br />For this, note the following:</p>\r\n<ul>\r\n<li>The required print parameters are determined from print commands that are defined in the device type. Accordingly, the advanced print options are available only if the device type matches the XDC file (the report RSPO0022 is used for the assignment between device type and XDC file). If device type and XDC file use different print control languages, you cannot use the advanced print options.</li>\r\n</ul>\r\n<ul>\r\n<li>As a consequence, the advanced print options are available only for PCL and PostScript device types. For example, if a PCL-XDC file is assigned to a Prescribe device type, you cannot select advanced print options.</li>\r\n</ul>\r\n<ul>\r\n<li>A further consequence is that a print option is available only if there is a standard print command specified for this option in the device type. If the device type uses a printer-internal command, this print option is available only for SAPScript and Smart Forms printing.</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Therefore:</p>\r\n<ul>\r\n<ul>\r\n<li>Not all the print options that can be used for SAPScript and Smart Forms printing can also be used for the Interactive Forms.</li>\r\n</ul>\r\n<li>Since the connection to the ADS is unencrypted, you can principally not select password options.</li>\r\n<li>The following print options are always available for the Interactive Forms (even if you cannot select them for SAPScript or Smart Forms printing):</li>\r\n<ul>\r\n<li>Print mode (simplex, duplex, tumble)</li>\r\n<li>Paper tray (if specified in the TRY... print controls default print commands)</li>\r\n<li>If you used the report RSPO0022 to define a different XDC file for black and white print and color print, you can select the color print. Depending on your selection, the system uses the corresponding XDC file for printing.</li>\r\n</ul>\r\n<li>Print tickets can be passed to the function module FP_JOB_OPEN using the parameter PRINTTICKET of the structure SPFOUTPUTPARAMS.</li>\r\n</ul>\r\n<p><br /><br /><span style=\"text-decoration: underline;\">The sequence in which the system uses the advanced print options:</span><br /><br />You can select print options in different places. The system uses the following sequence for this:</p>\r\n<ul>\r\n<li>If you chose the button for additional print options in the print dialog box, and if you selected options here (also using default settings), the system uses these.</li>\r\n</ul>\r\n<ul>\r\n<li>You can use transaction SPAD to define print tickets in the output device definition. A print ticket is a print option record that you have given a description. If you did not select print options in the print dialog box, the system uses the print ticket that was transferred to the form interface by the print program.</li>\r\n</ul>\r\n<ul>\r\n<li>Caution: The following steps are carried out only if you used the report RSPO0021 to activate the parameter SPAD_TRAY_IFBA (incompatible change).</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If no print ticket was transferred, the system uses the default print ticket that you can specify in the output device.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If there is no default print ticket, the system uses the paper tray and the print mode that you set in the output device definition in transaction SPAD on the output attributes tab page.</li>\r\n</ul>\r\n</ul>\r\n<p><br /><br /><span style=\"text-decoration: underline;\">Transaction SPAD:</span><br /><br />By default, transaction SPAD displays only the print options in the output device definition that are available for SAPScript and Smart Forms printing. The reason for this is that SPAD does not know whether the ADS supports the advanced print options or not.<br /><br />In order to display in transaction SPAD which print options are available for the Interactive Forms, and to be able to define print tickets, you must use report RSPO0021 to activate the parameter  IFBA_SUPP_POSS. This parameter influences only transaction SPAD.<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-FP (Forms Processing)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D062450)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D035727)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001729666/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001729666/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1806471", "RefComponent": "BC-SRV-FP", "RefTitle": "IFbA: Additional print options", "RefUrl": "/notes/1806471"}, {"RefNumber": "1806440", "RefComponent": "BC-CCM-PRN", "RefTitle": "Output attributes of an output device in transaction SPAD", "RefUrl": "/notes/1806440"}, {"RefNumber": "1777740", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1777740"}, {"RefNumber": "1049778", "RefComponent": "BC-CCM-PRN", "RefTitle": "Additional print options for SAPscript/Smart Forms", "RefUrl": "/notes/1049778"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2009322", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing to Specific Output Bins", "RefUrl": "/notes/2009322 "}, {"RefNumber": "1565113", "RefComponent": "BC-CCM-PRN", "RefTitle": "Duplex Printing", "RefUrl": "/notes/1565113 "}, {"RefNumber": "2254208", "RefComponent": "BC-CCM-PRN", "RefTitle": "IFbA: Tray control for output attributes of output device", "RefUrl": "/notes/2254208 "}, {"RefNumber": "1435810", "RefComponent": "BC-CCM-PRN", "RefTitle": "Changes to XDC files", "RefUrl": "/notes/1435810 "}, {"RefNumber": "2111085", "RefComponent": "BC-CCM-PRN", "RefTitle": "Tray control in SAP", "RefUrl": "/notes/2111085 "}, {"RefNumber": "685571", "RefComponent": "BC-CCM-PRN", "RefTitle": "Printing SAP Interactive Forms by Adobe", "RefUrl": "/notes/685571 "}, {"RefNumber": "1806471", "RefComponent": "BC-SRV-FP", "RefTitle": "IFbA: Additional print options", "RefUrl": "/notes/1806471 "}, {"RefNumber": "1049778", "RefComponent": "BC-CCM-PRN", "RefTitle": "Additional print options for SAPscript/Smart Forms", "RefUrl": "/notes/1049778 "}, {"RefNumber": "1806440", "RefComponent": "BC-CCM-PRN", "RefTitle": "Output attributes of an output device in transaction SPAD", "RefUrl": "/notes/1806440 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "702", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70214", "URL": "/supportpackage/SAPKB70214"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73010", "URL": "/supportpackage/SAPKB73010"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73107", "URL": "/supportpackage/SAPKB73107"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1849754", "RefTitle": "Printing options not changeable with changed color flag", "RefUrl": "/notes/0001849754"}]}}}}}