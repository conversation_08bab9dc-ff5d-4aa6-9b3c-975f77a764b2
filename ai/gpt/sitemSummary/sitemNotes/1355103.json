{"Request": {"Number": "1355103", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 329, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007980082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001355103?language=E&token=219047E3B6E0A1808A5FCE68489FD11B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001355103", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001355103/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1355103"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "2010-06-18"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED-ORD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Management i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Management i.s.h.med", "value": "XX-PART-ISHMED-ORD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED-ORD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1355103 - Radiology: Findings Work Station - Previewer Missing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The prefindings viewer is missing in the radiology findings work station.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>This problem is caused by a program error.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Before you carry out the source code corrections, you must make the following manual changes (before importing with SNOTE):</p> <b></b><br /> <b>Create report</b><br /> <UL><LI>Call transaction SE38.</LI></UL> <UL><LI>Enter RN1PATORG_PREFNDNGS as the program and select \"Create\".</LI></UL> <UL><LI>Enter 'Patient Organizer Selection Variants Prefindings' as the title.</LI></UL> <UL><LI>Select the following attributes:</LI></UL> <UL><UL><LI>Type: Executable program</LI></UL></UL> <UL><UL><LI>Status: Productive SAP standard program</LI></UL></UL> <UL><UL><LI>Application: Hospital</LI></UL></UL> <UL><LI>Save and activate the report.</LI></UL> <p></p> <b>Create text elements in report:</b><br /> <UL><LI>Call transaction SE38.</LI></UL> <UL><LI>Enter RN1PATORG_PREFNDNGS as the object type and select 'Text Elements' under Subobjects.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>Add the following new text elements:</LI></UL> <UL><UL><LI>005: Empl. Resp. (length 15)</LI></UL></UL> <UL><UL><LI>006: Changed By (length 15)</LI></UL></UL> <UL><LI>Save and activate the text elements.</LI></UL> <p></p> <b>Enhance text elements in class:</b><br /> <UL><LI>Call transaction SE24.</LI></UL> <UL><LI>Enter CL_ISHMED_PATORG as the object type and select 'Change'.</LI></UL> <UL><LI>Choose #Goto -&gt; Text Elements# in the menu.</LI></UL> <UL><LI>Add the following new text elements at the end:</LI></UL> <UL><UL><LI>T88: Load Study (length 15)</LI></UL></UL> <UL><UL><LI>T89: W/o Service Ref. (length 25)</LI></UL></UL> <UL><UL><LI>T90: Accident Number (length 15)</LI></UL></UL> <UL><UL><LI>T91: W/o Accident Number (length 25)</LI></UL></UL> <UL><LI>Save and activate the text elements.</LI></UL> <p></p> <b>Enhance a structure:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select #Data Type# and&#x00A0;&#x00A0;enter the value RN1PO_PROCESS_REPRESENTATION in the input field.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>At the end of the structure add the following new components:</LI></UL> <UL><UL><LI>SORT_DATE_01, component type N1PODATE</LI></UL></UL> <UL><UL><LI>SORT_TIME_01, component type N1POTIME</LI></UL></UL> <UL><UL><LI>SORT_DATE_02, component type N1PODATE</LI></UL></UL> <UL><UL><LI>SORT_TIME_02, component type N1POTIME</LI></UL></UL> <UL><LI>Save and activate the structure.</LI></UL> <p></p> <b>Enhance a structure:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select \"Data type\" and enter the value N1POPAKTE in the input field.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>At the end of the structure enter the new component UNFNR with the component type UNFNR.</LI></UL> <UL><LI>Save and activate the structure.</LI></UL> <p></p> <b>Enhance a structure:</b><br /> <UL><LI>Call transaction SE11.</LI></UL> <UL><LI>Select #Data Type# and&#x00A0;&#x00A0;enter the value RN1PO_SUPPLY in the input field.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>In the component '.INCLUDE&#x00A0;&#x00A0;N1POPAKTE' enter the group 'N1POPAKTE'.</LI></UL> <UL><LI>In the component '.INCLUDE&#x00A0;&#x00A0;RN1PO_PROCESS_REPRESENTATION' enter the group 'RN1PO_PROCESS_REPRESENTATION'.</LI></UL> <UL><LI>Save and activate the structure.</LI></UL> <p><br />See attachments<br /><br />Implement the source code corrections.<br /><br />After you carry out the source code corrections and attachments, you must make the following manual changes (after importing with SNOTE):<br /></p> <b>Enhance selection texts for report:</b><br /> <UL><LI>Call transaction SE38.</LI></UL> <UL><LI>Enter RN1PATORG_PREFNDNGS as the object type and select 'Text Elements' under Subobjects.</LI></UL> <UL><LI>Choose 'Change'.</LI></UL> <UL><LI>Choose the 'Selection Texts' tab.</LI></UL> <UL><LI>Enter the following texts:</LI></UL> <UL><UL><LI>L_DODAT: Document Date</LI></UL></UL> <UL><UL><LI>L_DOKST: Document Status</LI></UL></UL> <UL><UL><LI>L_DTID: Document Category</LI></UL></UL> <UL><UL><LI>L_DTVERS: Version</LI></UL></UL> <UL><UL><LI>L_EINRI: Institution</LI></UL></UL> <UL><UL><LI>L_ERDAT: Created on</LI></UL></UL> <UL><UL><LI>L_MITARB: Employee Responsible</LI></UL></UL> <UL><UL><LI>L_ORGDO: Document.OU</LI></UL></UL> <UL><UL><LI>L_UPDAT: Changed on</LI></UL></UL> <UL><UL><LI>L_UPUSR: Changed by</LI></UL></UL> <UL><UL><LI>P_SERBOE: Service-Performing OU</LI></UL></UL> <UL><UL><LI>P_SFALNR: Case Number</LI></UL></UL> <UL><UL><LI>P_SUNFNR: Accident Number</LI></UL></UL> <UL><LI>Save and activate the seletion texts.</LI></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED (Clinical System i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5001669"}, {"Key": "Processor                                                                                           ", "Value": "C5019872"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001355103/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001355103/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1355103_604.ZIP", "FileSize": "100", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000315592009&iv_version=0002&iv_guid=FC8E7E4E9886B9459B6E8A62DC651185"}, {"FileName": "HW1355103_604_2.ZIP", "FileSize": "22", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000315592009&iv_version=0002&iv_guid=F03EA02B787EEA44BFD9335A7AC06BCF"}, {"FileName": "HW1355103_604_3.ZIP", "FileSize": "16", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000315592009&iv_version=0002&iv_guid=A3FF9C5959B9B845AE44B81C4D2FCB6B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 604", "SupportPackage": "SAPK-60406INISH", "URL": "/supportpackage/SAPK-60406INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 1, "URL": "/corrins/0001355103/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1242900 ", "URL": "/notes/1242900 ", "Title": "Patient Organizer/Patient Viewer: Variant for Each Instituti", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1265399 ", "URL": "/notes/1265399 ", "Title": "Patient Organizer/Patient Viewer: Aspects Across All Clients", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1293101 ", "URL": "/notes/1293101 ", "Title": "Patient Organizer/Patient Viewer: Technical adaptation", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1312809 ", "URL": "/notes/1312809 ", "Title": "Patient organizer/patient viewer: N1PO_TIMESEL", "Component": "IS-H-ACM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1341350 ", "URL": "/notes/1341350 ", "Title": "Patient organizer/patient viewer: N1PO_TIMESEL", "Component": "IS-H-ACM"}, {"SoftwareComponent": "IS-H", "ValidFrom": "604", "ValidTo": "604", "Number": "1345687 ", "URL": "/notes/1345687 ", "Title": "Patient Organizer/Patient Viewer: Case Aspect Display", "Component": "IS-H-ACM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}