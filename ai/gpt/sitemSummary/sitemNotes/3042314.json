{"Request": {"Number": "3042314", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 268, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000485172021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003042314?language=E&token=98C4202151C16EB496B830D2BB10DF4E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003042314", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003042314/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3042314"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "18.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "XX-S4C-OPR-INC"}, "SAPComponentKeyText": {"_label": "Component", "value": "S/4HANA Cloud Availability, Performance and Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud", "value": "XX-S4C", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud Main entry for Operations", "value": "XX-S4C-OPR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C-OPR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "S/4HANA Cloud Availability, Performance and Administration", "value": "XX-S4C-OPR-INC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-S4C-OPR-INC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3042314 - Hotfix Collection deployment for RISE with SAP S/4HANA Cloud"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are planning and coordinating operations related tasks, and/or are responsible for Business Configuration, and/or Extensibility for SAP S/4HANA Cloud and are looking for information on the Hotfix Collection process.</p>\r\n<p>This note provides information on changes related to the Hotfix patching for SAP S/4HANA Cloud. This SAP Note is subject to change, please mark it as a favorite and please check it regularly for updates.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p id=\"\">RISE with SAP S/4HANA Cloud, SAP S/4HANA Cloud, essentials edition, Hotfix, Transport</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p id=\"\">This applies to all customers subscribed to RISE with SAP S/4HANA Cloud, formerly known as SAP S/4HANA Cloud, essentials edition or SAP S/4HANA Cloud.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>With release 2105 Hotfix Collection 04, we introduce&#160;<strong>Blue-Green Software Deployment model</strong>&#160;(see&#160;<a target=\"_blank\" href=\"/notes/3024158\">Note 3024158</a>) for the productive systems.</p>\r\n<p>This note refers to the&#160;<strong>standard deployment model</strong> for the Quality and other non-productive system types.</p>\r\n<p>Please adapt your planning for activities related to configuration, extensibility and transports as outlined in this note below.</p>\r\n<p>SAP is utilizing SPAM Deployment for the biweekly hotfix maintenance procedure.</p>\r\n<p>In order to preserve the integrity of customers&#8217; systems, SAP will rely on proactive safeguarding locks during execution.</p>\r\n<p>Therefore, SAP recommends to refrain from scheduling any activities into development sprints such as Custom Form changes, development for Custom Business Logic or Objects, CDS views, ATO transports during those lock phases as these will not be possible.</p>\r\n<p>For business configuration (Manage Your Solution app), SSCUIs are &#8216;read-only&#8217; during the procedure, so customizing changes cannot be saved in this timeframe.</p>\r\n<p>Additionally, SAP recommends careful project planning for other activities such as Scope Extension, Add New Country, Expert Configuration and Time-Dependent Tax migration etc. to run outside of the 24 hours preceding Hotfix Patching.</p>\r\n<p>Business transactions will not be impacted by this. The above purely relates to changes in configuration and extensibility. Maintenance periods published in the Maintenance planner and Service Level Agreement for Cloud Services still apply.</p>\r\n<p><strong>Biweekly Contractual Maintenance Periods (CMP) and&#160;<strong>Limitations for customer activities during Hotfix Collection deployments (*)</strong>:</strong></p>\r\n<p>Please refer to the maintenance schedule (<a target=\"_blank\" href=\"https://www.sap.com/documents/2017/01/867629d8-a27c-0010-82c7-eda71af511fa.html\">here</a>) to be aware of the upcoming downtimes.</p>\r\n<p>For Q (Quality), SE (Starter) and Partner Demo Systems*</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\"><colgroup><col width=\"72\" /><col width=\"507\" /><col width=\"239\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\">Phase</td>\r\n<td class=\"oa1\" width=\"507\">\r\n<p>Uptime</p>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>Downtime**</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa1\" height=\"36\" width=\"72\">Limitations</td>\r\n<td class=\"oa1\" width=\"507\">\r\n<p>No release of&#160;Transports Requests</p>\r\n<p>No In-APP Extensibility (Extensibility Explorer)</p>\r\n<p>No customizing activity (Manage Your Solution)</p>\r\n<p>or other activities such as Scope Extension, Add New Country, Expert Configuration</p>\r\n</td>\r\n<td class=\"oa1\" width=\"239\">\r\n<p>No logon to system</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>MENA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>THU 14:00 UTC to THU 17:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>THU 17:00 UTC to FRI 05:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>APJ</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>FRI 10:00 UTC to FRI 13:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>FRI 13:00 UTC to SAT 01:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"29\" width=\"72\">\r\n<p>EMEA</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>FRI 17:00 UTC to FRI 20:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>FRI 20:00 UTC to SAT 08:00 UTC</p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td class=\"oa2\" height=\"28\" width=\"72\">\r\n<p>AMER</p>\r\n</td>\r\n<td class=\"oa2\" width=\"507\">\r\n<p>FRI 23:00 UTC to SAT 02:00 UTC</p>\r\n</td>\r\n<td class=\"oa2\" width=\"239\">\r\n<p>SAT 02:00 UTC to 14:00 UTC</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong>* Depending on duration of pre-import phase and size of Hotfix Collection, safeguarding locks may occur earlier or later throughout the maintenance window.</strong></p>\r\n<p><strong>**&#160;In case maintenance activities don&#8216;t require complete business downtime window, systems are released earlier.&#160;</strong></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D043592)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D043592)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003042314/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3024158", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "Blue-Green Deployment for Hotfix Collection for RISE with SAP S/4HANA Cloud", "RefUrl": "/notes/3024158"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2538700", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration Cockpit - File/Staging (Cloud / SAPSCORE)", "RefUrl": "/notes/2538700 "}, {"RefNumber": "3024158", "RefComponent": "XX-S4C-OPR-INC", "RefTitle": "Blue-Green Deployment for Hotfix Collection for RISE with SAP S/4HANA Cloud", "RefUrl": "/notes/3024158 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}