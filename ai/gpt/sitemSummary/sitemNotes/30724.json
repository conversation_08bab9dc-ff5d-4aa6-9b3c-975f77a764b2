{"Request": {"Number": "30724", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 792, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000030724?language=E&token=BDE02198CEE135C519B8DFA272EB4012"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000030724", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000030724/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "30724"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 32}, "Recency": {"_label": "Currentness", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations/Additional Information"}, "Status": {"_label": "Release Status", "value": "To Be Checked"}, "ReleasedOn": {"_label": "Released On", "value": "00.00.0000"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SEC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Security - Read KBA 2985997 for Subcomponents"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Security - Read KBA 2985997 for Subcomponents", "value": "BC-SEC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SEC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "30724 - Data protection and security in SAP systems"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=30724&TargetLanguage=EN&Component=BC-SEC&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/30724/D\" target=\"_blank\">/notes/30724/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Questions about data protection in SAP systems, in particular in R/3.<br /></p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>The R/3 System processes partially highly sensitive personal data. R/3 is used in many countries worldwide and must map different national laws, particularly in the areas of Financial Accounting and Human Resources.<br />The EU data protection directives apply to the European Economic Area, the business processing of personal data is regulated in Germany in the Federal Data Protection Act (BDSG).<br />The purpose of the law is to protect the personal right of the individual.<br />The Data Protection Officer (DPO) must work towards compliance with the law and other regulations on data protection.<br /></p><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><p>Based on strict German security and data protection regulations, R/3 had to include security functions in the sense of data protection right from the start. A large number of security mechanisms for the confidentiality and integrity of the data are provided, such as: Authentication, authorization concept, logging, data compression and encryption, as well as secure integration into different operating systems, databases and networks.<br /><br />This creates the prerequisites for adapting an SAP R/3 system to the company&#39;s security requirements. These requirements must be defined by the persons responsible for security and data protection at the start of the project and implemented for the individual systems. Normally, a test system with test data will have a lower level of security than a production system with real HR data.<br /><br />The book &quot;Security and Data Protection with SAP Systems&quot; (see the bibliography in the appendix) introduces the security functions in SAP systems and their interaction. Security is seen in close connection with data protection and data security. In particular, the embedding of practical measures in the company security policy is in the foreground.<br /></p> <UL><LI>Security Guide</LI></UL> <p><br /><br />The security of the overall system is an essential prerequisite for data protection.<br />The question of whether a business application is secure and ensures data protection cannot be answered theoretically, but only in practical use. A high level of responsibility lies here with project management and administrators, who have to implement and monitor the options offered by the system in accordance with the security requirements of the company (security concept).<br />For this purpose, there is the &#39;SAP Security Guide R/3&#39;, which is delivered to the system administrators. It deals mainly with the following chapters:<br /><br />           <br />Databases                            117 *<br />           Operating Systems                        39<br />           Communication Methods                  22<br />           Network Infrastructure                   19<br />           Communication Interfaces            22<br />           Internet                                23<br />           Authorization Concept                    27<br />           User Authentication               24<br />           Logging and Check             17<br />           Encryption                        10<br />           Protecting the Production System             20<br /><br /><br />           <br /> (*: Number of pages in version 3.0 from April 11, 2001)<br /><br /><br /><br />How much security depends on administration is explained using the example of user authentication using password design. The specifications of federal or state data protection officers can be implemented in the R/3 System as follows:<br /><br /> - Each person receives their own user ID<br /><br /> - The user ID is only valid for a certain period of time<br />      The validity period is longer than the from-to date for each user<br />      adjustable, e.g. for maintenance or in case of emergency.<br /><br /> - Display of last logon<br />      Date and time of last logon are in system status<br />      included.<br /><br /> - Minimum password length of 6 characters<br />      Allowed setting: 3 to 8;<br /><br /> - no first names or trivial passwords<br />      All forbidden passwords can be listed in table USR40<br />      are. Furthermore, the first three characters must not be filled with the<br />      User names match or are identical (e.g. AAA).<br /><br /> - Password change requirement<br />      Specified in number of days<br /><br /> - Prevent the use of old passwords<br />      Do not use the last five passwords.<br /><br /> - Protection of the password file<br />      Passwords are saved as hash values, a reset<br />      is not possible. The data is transferred from the front end.<br />      compressed. When using Secure Network Communication (SNC)<br />      the password no longer has to be transmitted over the network.<br />      The original password is not known and will not be<br />      saved.<br /><br /> - Restriction of logon attempts with locking of the user<br />      The number of invalid logon attempts until the end of the session<br />      or up to the user lock is between 1 and 99<br />      customizable.<br /><br /> - Logging failed attempts<br />      Unsuccessful login attempts and. User locks are stored in the system log<br />      recorded. In addition, in the Security Audit Log, you can choose between the following:<br />      All successful and unsuccessful logons with user name<br />      and ID of the terminal.<br /><br /> - No logon using function key<br />      Client, user name, and password are only then not<br />      required if SNC authenticates itself. The<br />      normal logon process can then be locked.<br /><br /> - Automatic logoff after a long period of non-use<br />      The maximum duration of inactivity must be specified in seconds.<br /><br />The administrator should note that a distribution of the security guide in the company enables persons in other areas to check the implementation of the guide.<br /></p> <UL><LI>Working group guides</LI></UL> <p><br /><br />The Data Protection Working Group developed the &quot;Data Protection Guide for SAP ERP 6.0&quot; within the Working Group Revision of the DSAG.<br /><br />It is divided into the following chapters:<br /></p> <UL><UL><LI>Implementation Process</LI></UL></UL> <p></p> <UL><UL><LI>Tasks of the DPO</LI></UL></UL> <p></p> <UL><UL><LI>Rights of the persons concerned and of everyone</LI></UL></UL> <p></p> <UL><UL><LI>Implementation of the technical and organizational measures</LI></UL></UL> <p></p> <UL><UL><LI>Commissioned data processing and intercompany data exchange</LI></UL></UL> <p></p> <UL><UL><LI>Special topics, such as Audit Information System and SAP GRC</LI></UL></UL> <p><br /><br />The current &quot;Guide for Data Protection and Privacy SAP ERP 6.0&quot; (Version 30 May 2008) can be displayed on the Internet on the page of the &#39;Working Group on Data Protection in the Working Group on Audit/Risk Management&#39; of the DSAG at the following address:<br /><br /> http://www.dsag.de/dsaghome/arbeitskreise/uebersicht/details/arbeitskreis/ag-datenschutz-im-ak-revisionrisikomanagement.html<br /><br /> or<br /><br /> http://www.dsag.de/fileadmin/media/Leitfaeden/080909_Datenschutz-Leitfaden.pdf<br /><br />Further guides on data protection and privacy for SAP BW and SAP CRM are available for download at:<br /><br />    http://www.sap.de/revis<br /><br />               or<br /><br /> http://www.sap.com/germany/company/revis/infomaterial/index.epx<br /><br />The DSAG Working Group on Auditing prepared several audit guides. The R/3 FI SAP Audit Guide contains, for example, the following important chapters in the system section for persons responsible for data protection and security:<br /></p> <UL><UL><LI>System Overview</LI></UL></UL> <p></p> <UL><UL><LI>Security and Access Protection</LI></UL></UL> <p></p> <UL><UL><LI>Workbench Organizer and Transport System</LI></UL></UL> <p></p> <UL><UL><LI>Table Access and Logging</LI></UL></UL> <p><br /><br />The purchase order number is 5001 4633 (German or English). The guide can be displayed on the Internet at the above address.<br /><br />The following data protection-relevant points from the guides are highlighted:<br /></p> <UL><UL><LI>The Audit Information System for auditors consists of a report tree that can also be used for data protection, especially in the areas of system configuration, repository, security configuration, and authorizations.<br /></LI></UL></UL> <UL><UL><LI>The PC download can generally be forbidden via authorization object or restricted by application and user via an exit.<br /></LI></UL></UL> <UL><UL><LI>separation of systems into development, quality assurance and production;</LI></UL></UL> <p><br /></p> <UL><UL><LI>Division of authorization administration tasks into user administrators, activation administrators, and authorization administrators.</LI></UL></UL> <p><br /></p> <UL><UL><LI>Logging of all security-relevant events in the security audit log, such as logon and logoff, transaction start, report start, RFC call, change authorization, download.</LI></UL></UL> <p><br /><br />The &quot;Logging of user actions&quot; is described in detail in SAP Note 139418.<br /><br />Reports for information from the data subjects about the storage of their data can be taken from the AIS, for example: RFDKVZ00 and RFKKVZ00 in the commercial, balance-sheet-oriented audit for customers and vendors, in HR using RPPSTM00 (HR master data sheet), or transaction PA10 (personnel file). In IS-H, the patient data is listed with report RNLAUS00. We recommend that you contact the user departments here.<br /><br />As mentioned above, data protection in an overall system can only be guaranteed if the system (operating system, database, network, R/3 application) offers the appropriate support and the necessary measures can be implemented in the project. The weakness in a system component is sufficient to endanger the entire system.<br /></p> <UL><LI>Remote Service</LI></UL> <p><br />SAP uses remote services to provide services for customers who require a remote connection. SAP’s security measures for physical and logical connections, network security with encryption and authentication, and the measures to be taken by customers can be found in SAP Note 46902 and in the brochure &quot;Security of Customer Connections&quot;, order number 5002 7583 (English: 5002 8244).<br />In SAP Service Marketplace, you can access the brochure directly using the alias /remoteconnection:<br />    http://service.sap.com/remoteconnection<br /><br />It should be emphasized here that the customer always has control over the connection, he opens the session and sets up the user with the password and the access authorizations. The customer decides whether personal data can be accessed in the production system. In most cases, this access is not necessary; a reproduction in the test system is sufficient.<br />In the case of the remote service in the production system, it is not the goal to transfer personal data to SAP using an order, to store it there, and to process it for a specific purpose. Rather, there is a use due to the display, the processing takes place at most on the customer computer. The technical and organizational measures remain on the customer side. This includes the control and logging of maintenance.<br />For more information about the extensive certification measures of SAP development and maintenance, see Alias /certificates:<br />     http://service.sap.com/certificates<br /><br />In addition to the certificates for fulfilling legal requirements in financial accounting and HR payroll, ISO certificates for global development and corporate support, you can also find the ITSEC security certificate of the BSI there.<br /></p> <UL><LI>Practical implementation</LI></UL> <p><br />The practical recommendations on data protection must be weighted differently for each company.<br /><br />A contact person for data protection should exist in the company. He should be under the authority of the management and be free of instruction. His tasks also include educating employees, checking programs, authorizations, data avoidance, and submissions. He is the contact person for inquiries and complaints.<br />In Germany, a data protection officer whose tasks are defined in the German Federal Data Protection Act (Bundesdatenschutzgesetz) must be appointed. The EU directives also provide for the DPO as an alternative to self-regulation in the company. Reporting obligations can thus be simplified or avoided.<br /><br />Project management should also define persons responsible for the individual applications and systems. The system owners are responsible for the security of the overall system (see Security Guide), the application owners are the contact persons for the data protection officer. You must have the necessary R/3 training to perform your tasks.<br /><br />Like the employee representative body (works council or personnel council in Germany), the DPO should be integrated into the individual projects that accompany the processing of personal data. The project plan should provide for appropriate basic training.<br /><br />However, it is not necessary for the DPO to become an SAP specialist. He relies on the collegiate help and expertise of his contacts, administrators, and SAP consultants.<br /><br />Using the example of the BDSG, proposals are presented as to how the tasks of a data protection officer can be performed in the R/3 System.<br /><br />Examination and training (§ 4g)<br /><br />1. Monitoring the proper application of programs that:<br />   process personal data<br /><br />The R/3 standard software is subject to regular audits by respected, international audit firms. In Germany, the following are used as a benchmark for the assessment of the software:<br /></p> <UL><UL><LI>Generally Accepted Accounting Principles (GAAP)</LI></UL></UL> <p></p> <UL><UL><LI>Opinions of the Technical Committee on Modern Billing Systems (FAMA)</LI></UL></UL> <p></p> <UL><UL><LI>Principles of proper DV-based accounting systems (GoBS)</LI></UL></UL> <p></p> <UL><UL><LI>Tax regulations<br /></LI></UL></UL> <p><br />The audit guides of the Audit Working Group provide auditors with recommendations for the procedure for audits in the R/3 System.<br />Internal Audit can check the proper application of the programs, in particular the modifications and enhancements, based on the audit guides and procedure overviews (see below).<br /><br />The type of audit will differ depending on the time available and the scope of the data to be audited in the companies.<br />The variants can be diverse:<br /><br />      - Data Entry Control<br />           form defaults, dynpro<br /><br />      - Transaction-Oriented Evaluation<br />           PA10: Personnel file displays the contents of the infotypes with<br />           converted key figures.<br /><br />      - Table, field, and program analysis with the ABAP Dictionary<br />           The key figures must be interpreted in the check tables<br />          .<br /><br /><br />Rules for modifications and custom development should be in place and adhered to. The DPO must be informed in advance of any planned data processing projects. This is the only way to check whether aspects relevant to data protection law are affected.<br /><br />You can use the SAP authorization concept to protect any functions or objects in the SAP system. The programmer determines where and how the check is performed, the user administrator determines who is allowed to execute a function or access an object.<br /><br />User administration should be carried out in the triple division mentioned above. It creates the user names and initial passwords. It checks whether the new user is aware of his or her responsibility in the processing of personal data or has been obliged to keep the data secret.<br />A general obligation of all users of the system is recommended, even if they are denied access to personal data.<br /><br />This central body may, where appropriate, require further obligations:<br /></p> <UL><UL><LI>Preservation of business and trade secrets in the Group and third parties<br /></LI></UL></UL> <UL><UL><LI>Special obligation of administrators and emergency users who have extensive rights both in R/3 and in the system bypass. In particular, logon misuse must be prevented (secure passwords).<br /></LI></UL></UL> <UL><UL><LI>Confidential handling of all information related to the access and handling of the system, such as password, addresses, structure of data, programs and documentation<br /><br />In the HR system, you can monitor the use of the program, including the parameter variants used. This enables misuse and problems in authorization assignment to be detected.<br /></LI></UL></UL> <p>2. Training of persons involved in the processing of personal data<br /><br />In the HR system, the corresponding data protection training courses can be recorded for each person in infotype 0022. In infotype 0035, you can also save the obligation of data secrecy as an instruction.<br /><br /><br />Overviews (§§ 4e and 4g)<br /><br />To get an overview of the processing of personal data, it seems useful to create overviews. You should record the systems and SAP components in which personal data is processed. These overviews are required for checking, preliminary checks, and information.<br /><br />In the Working Group on Data Protection, we recommended that you divide the overviews into a public notification register and a company-internal list of procedures:<br /></p> <UL><UL><LI>The public reporting register is the basis for reporting to the supervisory authority and providing information to everyone. The messages should be short and understandable.</LI></UL></UL> <p><br /></p> <UL><UL><LI>The internal directory of procedures, on the other hand, serves the DPO to perform its tasks. For the preliminary check, it may have to contain details down to the field level. In addition, access authorizations and security measures must be included.</LI></UL></UL> <p><br />According to the BDSG, the overviews are to be made available by the data controller.<br /><br />To fulfill the obligation, the responsible department must have at least good knowledge of table processing, the Repository Information System, the ABAP Dictionary, and the authorization concept. The DPO should acquire basic knowledge in this area through the specialist department.<br /><br />Since the DPO should be included in the projects from the start, the DPO can decide in Customizing which data is to be entered and saved. At this point, the requirements for the following points in the overviews must be discussed:<br /></p> <OL>1. Purpose of data collection, processing or use</OL> <p><br /><br />Examples:<br />mySAP HR: Personnel Administration, Payroll, Time Management, Organizational Management, Recruitment, Personnel Support<br />mySAP FI: Maintenance and Service for Customers and Vendors<br />mySAP CRM: Telemarketing, Contact Management, Service Management, Resource Planning, Internet Customer Self-Service<br /></p> <OL>2. Description of the person groups and the corresponding data</OL> <p><br />For the public reporting register, lump sum information is sufficient, e.g.<br /><br />Persons: employees, applicants, customers, suppliers<br /><br />Data: Employee, applicant, customer, vendor data according to standard mySAP HR,  mySAP FI and mySAP CRM;<br /><br />The DPO requires the following for the internal procedure directory:<br />more extensive information. In the R/3 System, you have the option of tracking the personal data down to the individual field of a table or program. The data protection officers in the company must define the depth to which they require information in order to carry out their tasks.<br /><br />It seems useful that only those overviews are created that are required for the admissibility check. These do not necessarily have to be in paper form, but can be generated dynamically from R/3. Comprehensive registers for the filing cabinet are quickly outdated and superfluous.<br />Note that ABAP Workbench provides tools that support dynamic analysis in the system:<br />In list displays, further selections or where-used lists of objects can be made. The search icons enable an automatic search for terms. You can use the Data Browser to determine specific vendors or personnel numbers using table keys.<br /><br /><br />Since the BDSG  Ü b e r s i c h t e n  requires, the requirement of the data protection consultant (see bibliography) for manageable directories for standard software should also be taken into account:<br /></p> <UL><UL><LI>Especially in the case of complex standard software, there is a risk of creating a data cemetery whose practical benefits are highly doubtful.<br /></LI></UL></UL> <UL><UL><LI>The respective application should be documented in such a way that one can check the admissibility, recognizes the data flow and the system administrator is known for details and documentation;<br /></LI></UL></UL> <UL><UL><LI>As payroll, accounts receivable, accounts payable and travel expenses involve both personal and financial data, the company should follow the principles of proper DV-based accounting systems (GoBS);<br /></LI></UL></UL> <UL><UL><LI>The DPO keeps its registers concise and clear and refers them to those responsible for documentation in the sense of GoBS.</LI></UL></UL> <p><br /><br />Another approach can be seen in the management of an audit-oriented overview of the particularly important data of the company. This could contain, for example, prohibited data that is particularly worthy of protection and that is to be deleted on time. In practice, the check would focus on this data.<br /><br />In the check itself, you can access the individual table field dynamically. The ABAP Dictionary provides the following information here  (transaction SE11):<br /></p><UL><UL><LI>Structure of Tables with Fields and Domains</LI></UL></UL> <p></p> <UL><UL><LI>Where-used lists for tables, fields, and domains on screens, programs, dynpros, and tables</LI></UL></UL> <p></p> <UL><UL><LI>Online Documentation of Tables and Fields</LI></UL></UL> <p></p> <UL><UL><LI>Value tables for the permitted values of a domain</LI></UL></UL> <p></p> <UL><UL><LI>Table Contents</LI></UL></UL> <p></p> <UL><UL><LI>Number of Currently Occupied Entries in the Table</LI></UL></UL> <p></p> <UL><UL><LI>Versions of the table definition</LI></UL></UL> <p><br /></p> <OL>3. Recipients or categories of recipients</OL> <p><br />Recipients of personal data are, for example: Bank, tax office, social insurance agency, health insurance funds, insurance companies, building insurance funds, pension institutions<br /></p> <OL>4. Regulatory periods for the deletion of data</OL> <p><br />Instructions about the periodic deletion of data can be mentioned here, for example, after the retention regulations have expired.<br />Detailed lists of deadlines can be found at the following Internet address:<br /><br />http://www.heilbronn.ihk.de/dokumente/aufbewahrung.htm<br /></p> <OL>5. Planned data transfer to third countries</OL> <p><br />In particular, recipients in countries outside the EU with a low level of data protection must be included here.<br />Permissions are consent, the need to fulfil a contract, or special contracts establishing a high level of protection. Due to the bureaucratic approval procedure laid down in Article 26 of the EU Directive, the standard contractual clauses recommended by the EU should be used:<br /><br />http://europa.eu.int/comm/internal_market/en/dataprot/news/clauses.htm<br /></p> <OL>6. General Description of Safety Measures</OL> <p><br /><br />The assessment of the measures depends in particular on the sensitivity of the data. In addition to securing access by firewalls, user access lists and routers, the recommended measures of the security guide must be checked, in particular authentication, user administration, authorization concept, authorization, operating system, database, network and security software.<br /></p> <OL>7. Persons authorized to access</OL> <p><br />With the R/3 authorization concept, individuals or Employee groups that allow access to transactions and programs.<br /><br />Example HR:<br />Authorizations, for example, for transactions, programs, infotypes, and infotypes determine the access options. There are authorizations for reading, locking writing, and releasing blocked entries (dual control principle).<br /><br />To meet all security requirements from the system view and the application view, a comprehensive authorization concept is required.<br />It is not sufficient to delegate certain review tasks to the system environment, such as the table check of the database. Furthermore, the cluster and pool single tables are only known to R/3, the database itself only knows the container cluster pool or table pool.<br /><br />In a business application, for example, the user has various activity characteristics to process business objects such as display, create, change, flag, activate, and delete, which as such cannot be transferred to a database. In addition, the authorizations of the users must be regulated with the user department from a business point of view: The user may only access selected areas of a table in certain program-technical constellations, for example, reading in a specific transaction, under a predefined program constellation, in plant 0001, for the purchasing group ABC.<br /><br />The role concept provides administration and the user department with a tool that makes it much easier to maintain authorizations. Depending on the job description, users are assigned different roles that contain the individual authorization objects in generated profiles.<br /><br />The DPO and Internal Audit should also use direct testing with current roles to determine whether the described and desired access protection is observed in practice. A list of theoretical access options is provided for documentation purposes only.<br /><br /></p> <UL><LI>Appendix:     Data Analysis Tools</LI></UL> <p><br /><br />Basic knowledge of the R/3 table structure is a prerequisite here. Terms such as transparent table, pooled table, cluster table, structure, field name, data element, domain, dynpro should be known.<br /><br />The ABAP Dictionary can be used to determine information about tables, structures, field names, data elements, and domains, as well as their use in programs, screens, and tables.<br /><br />The role SAP_CA_AUDITOR_DS is intended for the data protection auditor. It contains the required read authorizations.<br /><br />How can the data controller create a data overview for the DPO or branch to the individual field?<br /><br />Due to legal requirements, an employer must store more than 200 individual details about the employee. In HR, the master data is processed in more than 300 tables (infotypes) with more than 10,000 fields. Tables PA0000 to PA0999 are reserved for the master data (structure P0000 to P0999).<br /><br />In a table overview (see point 1 below), a<br />Short description of infotypes or tables.<br /><br />Examples:<br />                                    Number of tables<br />    HRPA*    Personnel Planning Data        99  (in 4.6C Test System)<br />    PA0*    HR Master Data          468<br />    PA2*    Time Data                    13<br />    PB0*    Applicant Master Data          34<br />    PB4*    Applicant data                 6<br />    KN*      Customer Master                  30<br />    LF*      Vendor Master            18<br />    SADR*    Address Management            19<br /><br />      (PA0*: all tables starting with PA0)<br /><br />Tables with a person reference are not named as such in the SAP system, but can be determined using personnel domains (see point 3 below).<br /><br /><br />In Customizing, the person responsible for the project determines which infotypes are used in the company. The system also checks which fields are required. If the DPO is included in the project, you can perform a field analysis for individual tables in the ABAP Dictionary.<br /><br /><br />In the ABAP Dictionary and in the AIS, functions are available for branching to the individual fields of tables.<br />The table content and the number of entries can also be determined from the Dictionary display. Furthermore, the generated lists can be stored on the PC for further processing (download authorization required).<br /><br />The menu paths can differ slightly in the individual R/3 releases. In this case, status 4.6C is listed as an example:<br /><br />1. Table Overview<br /><br />   menu path: -> Tools -> ABAP Workbench -> Development<br />            -> ABAP Dictionary (SE11) -> Database table &#39;PA0*&#39;<br />             -> Display -> Print<br /><br /><br />2. Display or Printing tables and fields (also as<br />   table manual can be printed with report RSSDOCTB)<br /><br />   menu path: -> Tools -> ABAP Workbench -> Overview -> Information System<br />            -> ABAP Dictionary -> Fields -> Structure Fields  &#39;P000*&#39;<br />                Print (SE84)<br /><br />3. Where-Used List of Tables, Data Elements, and Domains<br /><br />   menu path: -> Tools -> ABAP Workbench -> Development<br />            -> Dictionary with selection -> Domains &#39;KUNNR&#39; for<br />                Customer number -> Utilities -> Where-used lists<br />            -> Indirect use -> Tables (or programs,<br />                Screens (SE11))<br /><br /><br />The generated lists can be stored via PC as an Excel, Word, or text file and, if necessary, used for a separate tab:<br /><br />   menu path: -> System -> List -> Save -> Local File<br />            -> unconverted or spreadsheet or rich text<br />                Format or HTML format<br /><br /><br />4. Audit Information System (AIS)<br /><br />The AIS provides transactions, reports, and variants that make it possible to create a comprehensive data register &quot;at the touch of a button&quot;.<br /><br />   menu path: -> Information Systems -> Audit Info System -> System Audit<br />             -> Human Resource Audit / Data Protection Audit<br />             -> File Register for Personal Data<br /><br />Among other things, there are: Variants for applicants, customers, suppliers, employees, administrators, and users. When you start the report RSCRDOMA, you can choose to evaluate only tables that are also filled. The report provides a list of all tables in which the domains of the variants occur. The individual table fields can be displayed in this list. This can then be used to generate a where-used list of tables for reports or screens.<br /></p> <UL><LI>Literature:<br /></LI></UL> <UL><UL><LI>Bergmann/Möhrle/Herb: Privacy law Hand comment. Richard Boorberg Verlag, Stuttgart 2008<br /></LI></UL></UL> <UL><UL><LI>Dammann, Ulrich; Simitis, Spiros: EU Privacy Policy. Nomos Verlagsgesellschaft, 6. Expl. Baden-Baden 2006.<br /></LI></UL></UL> <UL><UL><LI>GDD: Data protection in the company. Working aid for business practice. 6. Regulation. Association for Data Protection and Data Protection, Bonn 2003<br /></LI></UL></UL> <UL><UL><LI>Gliss Hans: The register according to § 37 BDSG and the duties of the data protection officer. Data Protection Advisor 4/1998, Verlagsgruppe Handelsblatt GmbH<br /></LI></UL></UL> <UL><UL><LI>Hornberger Werner; Schneider Jürgen: Security and Data Protection with SAP Systems. Galileo Press GmbH, Bonn 2000 -->  www.galileo-press.de English translation: Security and Data Protection with SAP Systems. Addison Wesley, London, 2001.<br /></LI></UL></UL></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "D001837"}, {"Key": "Processor                                                                                          ", "Value": "D001837"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000030724/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "827573", "RefComponent": "IS-CWM", "RefTitle": "Security Guide: Catch Weight Management", "RefUrl": "/notes/827573"}, {"RefNumber": "77503", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS)", "RefUrl": "/notes/77503"}, {"RefNumber": "77462", "RefComponent": "BC-SEC", "RefTitle": "ITSEC certification of R/3", "RefUrl": "/notes/77462"}, {"RefNumber": "700659", "RefComponent": "SCM-TEC", "RefTitle": "Security Guide: mySAP Supply Chain Management", "RefUrl": "/notes/700659"}, {"RefNumber": "693220", "RefComponent": "BC-SEC", "RefTitle": "ITS Services Security Recommendations", "RefUrl": "/notes/693220"}, {"RefNumber": "66687", "RefComponent": "BC-SEC-SNC", "RefTitle": "Using Network Security Products", "RefUrl": "/notes/66687"}, {"RefNumber": "46902", "RefComponent": "XX-SER-NET-RCP", "RefTitle": "Security Aspects of Remote Access", "RefUrl": "/notes/46902"}, {"RefNumber": "39267", "RefComponent": "BC-SEC", "RefTitle": "Availability of the SAP Security Guide", "RefUrl": "/notes/39267"}, {"RefNumber": "35493", "RefComponent": "BC-SEC", "RefTitle": "Commitment to data protection and confidentiality", "RefUrl": "/notes/35493"}, {"RefNumber": "23611", "RefComponent": "BC-SEC", "RefTitle": "Collective Note: Security in SAP Products", "RefUrl": "/notes/23611"}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of User Actions (ABAP Server)", "RefUrl": "/notes/139418"}, {"RefNumber": "1393432", "RefComponent": "EHS-BD-TLS", "RefTitle": "EHS: Authorization Checks for File Transfer", "RefUrl": "/notes/1393432"}, {"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193"}, {"RefNumber": "1253263", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1253263"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1261193", "RefComponent": "BC-SEC-AUT", "RefTitle": "Composite SAP Note : HCM Authorizations Documentation", "RefUrl": "/notes/1261193 "}, {"RefNumber": "77503", "RefComponent": "FI-GL-IS", "RefTitle": "Audit Information System (AIS)", "RefUrl": "/notes/77503 "}, {"RefNumber": "1393432", "RefComponent": "EHS-BD-TLS", "RefTitle": "EHS: Authorization Checks for File Transfer", "RefUrl": "/notes/1393432 "}, {"RefNumber": "66687", "RefComponent": "BC-SEC-SNC", "RefTitle": "Using Network Security Products", "RefUrl": "/notes/66687 "}, {"RefNumber": "39267", "RefComponent": "BC-SEC", "RefTitle": "Availability of the SAP Security Guide", "RefUrl": "/notes/39267 "}, {"RefNumber": "46902", "RefComponent": "XX-SER-NET-RCP", "RefTitle": "Security Aspects of Remote Access", "RefUrl": "/notes/46902 "}, {"RefNumber": "827573", "RefComponent": "IS-CWM", "RefTitle": "Security Guide: Catch Weight Management", "RefUrl": "/notes/827573 "}, {"RefNumber": "700659", "RefComponent": "SCM-TEC", "RefTitle": "Security Guide: mySAP Supply Chain Management", "RefUrl": "/notes/700659 "}, {"RefNumber": "23611", "RefComponent": "BC-SEC", "RefTitle": "Collective Note: Security in SAP Products", "RefUrl": "/notes/23611 "}, {"RefNumber": "139418", "RefComponent": "BC-SEC", "RefTitle": "Logging of User Actions (ABAP Server)", "RefUrl": "/notes/139418 "}, {"RefNumber": "77462", "RefComponent": "BC-SEC", "RefTitle": "ITSEC certification of R/3", "RefUrl": "/notes/77462 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=30724&TargetLanguage=EN&Component=BC-SEC&SourceLanguage=DE&Priority=06\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/30724/D\" target=\"_blank\">/notes/30724/D</a>."}}}}