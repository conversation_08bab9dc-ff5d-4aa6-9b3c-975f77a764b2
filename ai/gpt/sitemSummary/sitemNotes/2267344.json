{"Request": {"Number": "2267344", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 285, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018242822017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002267344?language=E&token=63A54B59EC7DEB149B6F71EA4CAAD7F8"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002267344", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002267344/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2267344"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.12.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-RB"}, "SAPComponentKeyText": {"_label": "Component", "value": "Rebate Processing"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Rebate Processing", "value": "SD-BIL-RB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-RB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2267344 - S4TWL - Optimization of SD Rebate Processing for TPM Customers"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion to SAP S/4HANA. In this scenario, the following SAP S/4HANA Transition Worklist item applies.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><strong><span style=\"color: #000000;\">Business Value</span></strong></strong></p>\r\n<p><span style=\"color: #000000;\"><span style=\"color: #000000;\">Redundant index data, to determine relevant documents, was removed. This leads to a&#160;significantly r<span style=\"color: #000000;\"><span style=\"color: #000000;\">educed database footprint</span></span>.<br />Additional information can be found in note 2200691 and the documentation/notes for business function LOG_SD_REBATE_INDEX.</span></span></p>\r\n<p><strong>Description</strong></p>\r\n<p>Customers with an active CRM TPM or Trade Management license can continue using SD Rebate Processing, even though a successor&#160;is already being provided by Settlement Management&#160;(cp. simplification item SD Rebate Processing replaced by Settlement Management).</p>\r\n<p><strong>Business Process-Related information</strong></p>\r\n<p>For more information, see note <a target=\"_blank\" href=\"/notes/2226380\">2226380</a>.</p>\r\n<p>For these customers,&#160;the existing SD Rebate functionality has been optimized&#160;with regard to the database footprint. For this purpose, from a technical viewpoint, the rebate index table VBOX is not available.</p>\r\n<p>Licensed customers can optionally enable&#160;the creation of&#160;rebate agreements directly in sales&#160;via removal of the blacklist entries for VB01 (Note <a target=\"_blank\" href=\"/notes/2249880\" title=\"https://launchpad.support.sap.com/#/notes/2249880\">2249880</a>)<em>.</em>&#160;However, for the TPM process this is not necessary at all, since for this business process, rebate agreements are created&#160;by CRM TPM.<em> </em></p>\r\n<p>Regardless of any TPM licences the usage right of SD Rebates in SAP S/4HANA will end with the end of standard or extended maintenance of SAP ERP/ECC.</p>\r\n<p>The SD Rebate solution is only provided in the current scope.&#160;Upcoming legal requirements and additional funcitionalities will&#160;<strong>only</strong>&#160;be covered in the Settlement Management solution.</p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<ul>\r\n<li>If the customer is using the extended SD rebate processing, a rebuild of the S469 content is required after the upgrade.</li>\r\n<li>The customer has to maintain new customizing settings.</li>\r\n</ul>\r\n<p><strong><span style=\"color: #000000;\">How to Determine Relevancy</span></strong></p>\r\n<p><span style=\"color: #000000;\">This Transition Worklist Item is only relevant if the customer has an active Trade Promotion Management or Trade Management license.<br /></span></p>\r\n<p>The S/4HANA Readiness Check determines this Transition Worklist Item as relevant if SD Rebates have been used in the customer system (table KONA has entries with field ABTYP Equal to 'A'). <br />But in fact only those customers are affected which have an active Trade Promotion or Trade Management license.</p>\r\n<p>The general changes for SD Rebates are described in note<br /><a target=\"_blank\" href=\"/notes/2267377\">2267377 - S4TWL - SD Rebate Processing Replaced by Settlement Management</a></p>\r\n<p><strong>Related SAP Notes</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"0\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td valign=\"top\" width=\"302\">\r\n<p>Custom code-related information</p>\r\n</td>\r\n<td valign=\"top\" width=\"302\">\r\n<p>SAP Note: <a target=\"_blank\" href=\"/notes/2200691\">2200691</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D025004)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D025004)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002267344/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267344/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2267377", "RefComponent": "SD-BIL-RB", "RefTitle": "S4TWL - SD Rebate Processing Replaced by Settlement Management", "RefUrl": "/notes/2267377"}, {"RefNumber": "2226380", "RefComponent": "SD-BIL-RB", "RefTitle": "S/4 HANA: Deprecation of SD Rebate Processing", "RefUrl": "/notes/2226380"}, {"RefNumber": "2200691", "RefComponent": "SD-BIL-RB", "RefTitle": "SD rebate: Simplifications in SAP S/4HANA", "RefUrl": "/notes/2200691"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2815535", "RefComponent": "LO-GT-CCS", "RefTitle": "FAQ: Condition Contract Management by Settlement Management in SAP S/4HANA", "RefUrl": "/notes/2815535 "}, {"RefNumber": "2267377", "RefComponent": "SD-BIL-RB", "RefTitle": "S4TWL - SD Rebate Processing Replaced by Settlement Management", "RefUrl": "/notes/2267377 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}