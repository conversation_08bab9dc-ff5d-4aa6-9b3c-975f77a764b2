{"Request": {"Number": "966000", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 343, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016132782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E2713FAAC4FD1FA6CC7DE0CA18AF1D7A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "966000"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05/20/2009"}, "SAPComponentKey": {"_label": "Component", "value": "CA-JVA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Joint Venture and Production Sharing Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Joint Venture and Production Sharing Accounting", "value": "CA-JVA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-JVA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "966000 - mySAP new general ledger and Joint Venture Accounting"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You intend to use the Online Splitting functionality of the New General Ledger alongside with Joint Venture Accounting (JVA).</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>JVA, new G/L, New GL, NewGL, Online Splitter</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>ERP release 2004 or later</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>If a JVA customer uses the New General Ledger <B>without</B> Online splitting logic being active (default), the benefits of the New General Ledger as stated in note 756146 will only be available partly. Moreover, transaction GJ91 will only work if note 998431 is installed.<br /><br /><B>If the New General Ledger Online Splitter is switched on in a company code where JVA is also active, there are several restrictions to be taken into account. Especially, the split results of the New GL Online Splitter and of the JVA integration manager cannot be customized to be identical. </B>In other words, the results of the New GL splitter will not match the JVA split results in many cases. Most importantly, the JVA specific fields are treated differently by New GL and JVA (venture, equity group, recovery indicator). Many derivations and split criteria used by the JVA integration manager are not available in the New GL Online Splitter (and cannot be customized).<br /><br /><B>Note that the New GL splitter was NOT created and provided by SAP to replace Joint Venture Accounting. This means that while both the New GL splitter and JVA can be run parallely, the results are not identical and cannot be made identical.</B><br /><br />For company codes, where both New GL and JVA are active, the following rules and restrictions apply:<br /><br /><B>(1) It is not possible to customize the New GL splitter in a way that the split results exaclty match the split results of the JVA splitter. Especially, the handling of the JVA specific fields Venture, Equity Group and Recovery Indicator (fields VNAME, EGRUP, and RECID in database table BSEG) is different.</B><br /><br /><B>(2) To avoid issues regarding the different New GL ledgers (e.g. leading legders), install the following two notes: 995812, 1063464.</B><br /><B>Note that the JV ledgers can only reflect the posting made to the leading ledger! In other words: Posting made to non-leading NewGL ledgers are not posted to the JVA tables! So, if there is the requirement to reflect different evalution regulations not only in the New GL ledgers, but also in the JV ledgers, you can not use parallel account for this involving non-leading ledgers, but you have to use different accounts for the different evaluation regulations (\"classical account approach\").</B><br /><br />(3) If there is the requirement that the results of the the New GL Online Splitter should at least partially match the split in JVA documents, the New GL Online Splitter must be customized accordingly:<br /><br />(a) While a GL account line in JVA documents can have a cost object even if the GL account is not defined as cost element, GL account lines in the New GL documents do only have a cost object, if the GL account is defined as cost element. Therfore, all accounts which should be split and carry cost objects (e.g. discount accounts and RXD accounts) must be configured as cost elements. Otherwise the New GL splitter will not save the cost objects on the related lines.<br /><br />(b) Make sure that automatic lines (e.g. discount and RXD lines) are split by cost object in effect.<br /><br />(c) To avoid double splitting by the JVA integration manager, install note 985298.<br /><br />However, even if you have customized the New GL splitter in a way such that some New GL documents match the corresponding JV documents, it is impossible to achieve identical split results in all cases!<br /><br />(4) For company codes, where the New GL splitter is active, handle the JVA transactions GJ90 and GJ91 with great care and make sure that the results produced are correct. Transaction GJ91 can only be run if note 998431 is installed.<br /><br />(5) If the New GL splitter is active, the Cutback process can lead to errors because the New GL introduces stricter posting rules to be followed. To adapt the cutback transaction GJCB accordingly, install note 1267898.<br /><br />(6) It is strongly recommended to intensively test all scenarios which involve both the New GL splitter and JVA, before you move on to production. In especially, test the period end transactions (cutback, cash call, billing, equity change, etc.), if the documents posted are correct. Moreover, check if any reconciliation between JVA split results and New GL split results is needed.<br /><br /><B>Please watch this note for updated versions.</B></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D041872)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D041872)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "995812", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "G4872 when posting an FI document to the leading ledger", "RefUrl": "/notes/995812"}, {"RefNumber": "985298", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/985298"}, {"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357"}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919"}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146"}, {"RefNumber": "1267898", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "GJCB: Zero transaction amount causes error in NewGL splitter", "RefUrl": "/notes/1267898"}, {"RefNumber": "1063464", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "Postings to non-leading ledgers blocked by JV IM", "RefUrl": "/notes/1063464"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "826357", "RefComponent": "EC-PCA", "RefTitle": "Profit Center Accounting and General Ledger Accounting (new) in mySAP ERP", "RefUrl": "/notes/826357 "}, {"RefNumber": "985298", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "Preparing JV integration manager for the New GL splitter", "RefUrl": "/notes/985298 "}, {"RefNumber": "1267898", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "GJCB: Zero transaction amount causes error in NewGL splitter", "RefUrl": "/notes/1267898 "}, {"RefNumber": "995812", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "G4872 when posting an FI document to the leading ledger", "RefUrl": "/notes/995812 "}, {"RefNumber": "1063464", "RefComponent": "CA-JVA-JVA-IF", "RefTitle": "Postings to non-leading ledgers blocked by JV IM", "RefUrl": "/notes/1063464 "}, {"RefNumber": "812919", "RefComponent": "FI-GL-MIG", "RefTitle": "SAP ERP new general ledger: Migration", "RefUrl": "/notes/812919 "}, {"RefNumber": "756146", "RefComponent": "FI-GL", "RefTitle": "SAP ERP new General Ledger: General information", "RefUrl": "/notes/756146 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}