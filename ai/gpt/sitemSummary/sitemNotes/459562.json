{"Request": {"Number": "459562", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 525, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015144732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000459562?language=E&token=572CC7D9B5FC1623C64FD27F6C97061A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000459562", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "459562"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Pilot Release"}, "ReleasedOn": {"_label": "Released On", "value": "08.11.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-RE"}, "SAPComponentKeyText": {"_label": "Component", "value": "Enhancements Real Estate"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enhancements Real Estate", "value": "XX-PROJ-RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "459562 - Quarter days"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In certain countries, agreed annual rents are settled on fixed calendar days (quarter days) in each quarter.<br /><br />This note contains information about the quarter day function and its different calculation methods:</p> <UL><LI><B>Annual calculation:</B><br />Calculations are made on the basis of 365 days, leap years are not considered.<br /><br />Calculation method:<br />Annual amount_/_365 * number of days (for example from March 24, 2003<br />to the date of the condition change, February 29 is not considered)<br /><br />You cannot use \"flexible periods\" for the annual calculation.<br />The cashflow is always calculated from January 01 to December 31 each year.</LI></UL> <UL><LI><B>Calculation based on frequency:</B><br />This calculation method is based on the actual number of days in the period of quarter days and considers 366 days in a leap year.<br /><br />Calculation method:<br />Annual amount_/_periods (4)_/_number of actual days in the period of quarter days (for example 91) * number of days, splitted according to the number of days up to the date of the condition change and several days after this date. This method does not consider February 29.<br /><br />Payment is due in advance or in arrears. Mid-period payment is not possible and is considered as payment in advance.<br />Rounding differences are added to the first period following the condition change.</LI></UL> <p><br />This note contains information about the use of this function.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Quarter day, UK, GB, British, cash flow, due date</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is a special development.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Import the files attached to this note into client 000 of your system. (For additional information about the procedure of preliminary transports to the customer, see Note 13719).</OL> <OL>2. The table contents are entries in Customizing tables. This is why you must transport the following objects to your clients:<br />R3TR CDAT BUPAFLDMOD<br />R3TR CDAT BUPATBZ3A<br />R3TR CDAT BUPA_TBZ3C<br />R3TR CDAT BUPA_TBZ3E<br />R3TR TABU TBE24<br />R3TR TABU TPS34<br />R3TR TABU /CUKREQ/TIQUDAYS<br />R3TR TABU /CUKREQ/TIQUREG<br />R3TR TABU /CUKREQ/TIQUREGT<br /><br />Since key fields for transport objects of type R3TR CDAT cannot be directly qualified in order processing, you must create a new transport of copies (transaction SE01) and copy the objects of the copied transport request to this new request ('Request/task -&gt; Object list -&gt; Include objects... -&gt; Object list from request'). Then delete entries from the object list except for the above entries and import the new transport of copies into your target client.</OL> <OL>3. You may have to generate the container screens of the Business Data Toolset (BDT). For more information, see Note 210705.</OL> <p><br /><B>Note:</B> The user fields function is used to display the additional fields of the rental agreement. The REUSERF customer product is activated through the transport. Furthermore, entries for processes 00700190, 00700191, 00700192, and 00700193 are generated. This may cause problems if you are already using the user fields function. In this case, combine your customer product with customer product REUSERF. To do this, include the functions of screen SAPL/CUKREQ/FVUQ0500 in your customer product.<br />For the general contract, the additional fields are included in screen 'RECN04 agreement: Conditions'. If you do not use this screen, you must include section 'ZQUDAY' in one of your screens.<br /><br />To be able to use the function, your system has to meet the following requirements:</p> <UL><LI>The fixed calendar days must be stored in Customizing table /CUKREQ/TIQUDAYS.</LI></UL> <UL><LI>The quarter day region must be filled in the user fields of the contract.</LI></UL> <UL><LI>The month frequency of the conditions has to be 12.</LI></UL> <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-CF (Cash Flow)"}, {"Key": "Release's validity for upgrade customers checked?", "Value": "checked"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D026407)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D026407)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000459562/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000459562/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SP7K001873.zip", "FileSize": "91", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700011660142001&iv_version=0015&iv_guid=65D72AA24084004990BAFE0D06F63A95"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "904606", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter-Days: Conditions before date of first posting", "RefUrl": "/notes/904606"}, {"RefNumber": "838250", "RefComponent": "RE-CF", "RefTitle": "Quarter days in Classic RE", "RefUrl": "/notes/838250"}, {"RefNumber": "721236", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Redesign", "RefUrl": "/notes/721236"}, {"RefNumber": "657328", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Condition amount aw/ amount/month", "RefUrl": "/notes/657328"}, {"RefNumber": "651954", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date with 'annual basis'", "RefUrl": "/notes/651954"}, {"RefNumber": "651928", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date w/payment form in arrears", "RefUrl": "/notes/651928"}, {"RefNumber": "584155", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Proportional calculation w/ 'Annual basis'", "RefUrl": "/notes/584155"}, {"RefNumber": "583992", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Conditions after end of term", "RefUrl": "/notes/583992"}, {"RefNumber": "573778", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Proportional calculation w/ frequency basis", "RefUrl": "/notes/573778"}, {"RefNumber": "573189", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Incorrect flow type used", "RefUrl": "/notes/573189"}, {"RefNumber": "571324", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Period finish w/ annual basis", "RefUrl": "/notes/571324"}, {"RefNumber": "570651", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: incorrect amounts in the cash flow", "RefUrl": "/notes/570651"}, {"RefNumber": "568652", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Amount incorrect", "RefUrl": "/notes/568652"}, {"RefNumber": "560780", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Changes in non-occupancy contract are ignored", "RefUrl": "/notes/560780"}, {"RefNumber": "541104", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date with rents in arrears", "RefUrl": "/notes/541104"}, {"RefNumber": "447851", "RefComponent": "RE-PR", "RefTitle": "User fields for the RE general contract", "RefUrl": "/notes/447851"}, {"RefNumber": "415749", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days for the UK", "RefUrl": "/notes/415749"}, {"RefNumber": "210705", "RefComponent": "RE", "RefTitle": "Incomplete or shifted interface", "RefUrl": "/notes/210705"}, {"RefNumber": "1824129", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Rental start on February 29", "RefUrl": "/notes/1824129"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1052866", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Information for upgrading", "RefUrl": "/notes/1052866"}, {"RefNumber": "1051402", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Event ISDAT is not defined", "RefUrl": "/notes/1051402"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1824129", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Rental start on February 29", "RefUrl": "/notes/1824129 "}, {"RefNumber": "1052866", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Information for upgrading", "RefUrl": "/notes/1052866 "}, {"RefNumber": "1051402", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days: Event ISDAT is not defined", "RefUrl": "/notes/1051402 "}, {"RefNumber": "583992", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Conditions after end of term", "RefUrl": "/notes/583992 "}, {"RefNumber": "721236", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Redesign", "RefUrl": "/notes/721236 "}, {"RefNumber": "584155", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Proportional calculation w/ 'Annual basis'", "RefUrl": "/notes/584155 "}, {"RefNumber": "904606", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter-Days: Conditions before date of first posting", "RefUrl": "/notes/904606 "}, {"RefNumber": "657328", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Condition amount aw/ amount/month", "RefUrl": "/notes/657328 "}, {"RefNumber": "651954", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date with 'annual basis'", "RefUrl": "/notes/651954 "}, {"RefNumber": "651928", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date w/payment form in arrears", "RefUrl": "/notes/651928 "}, {"RefNumber": "573778", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Proportional calculation w/ frequency basis", "RefUrl": "/notes/573778 "}, {"RefNumber": "573189", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Incorrect flow type used", "RefUrl": "/notes/573189 "}, {"RefNumber": "571324", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Period finish w/ annual basis", "RefUrl": "/notes/571324 "}, {"RefNumber": "570651", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: incorrect amounts in the cash flow", "RefUrl": "/notes/570651 "}, {"RefNumber": "568652", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Amount incorrect", "RefUrl": "/notes/568652 "}, {"RefNumber": "560780", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Changes in non-occupancy contract are ignored", "RefUrl": "/notes/560780 "}, {"RefNumber": "541104", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarterdays: Due date with rents in arrears", "RefUrl": "/notes/541104 "}, {"RefNumber": "447851", "RefComponent": "RE-PR", "RefTitle": "User fields for the RE general contract", "RefUrl": "/notes/447851 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "838250", "RefComponent": "RE-CF", "RefTitle": "Quarter days in Classic RE", "RefUrl": "/notes/838250 "}, {"RefNumber": "415749", "RefComponent": "XX-PROJ-RE", "RefTitle": "Quarter days for the UK", "RefUrl": "/notes/415749 "}, {"RefNumber": "210705", "RefComponent": "RE", "RefTitle": "Incomplete or shifted interface", "RefUrl": "/notes/210705 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}