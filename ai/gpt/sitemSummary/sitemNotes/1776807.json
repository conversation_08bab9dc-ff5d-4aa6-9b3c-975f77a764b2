{"Request": {"Number": "1776807", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 836, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010498922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001776807?language=E&token=BB5CB271A356B160C8F00CBDEDB432B4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001776807", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001776807/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1776807"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "LE-SHP-GI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Goods Issue"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics Execution", "value": "LE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Shipping", "value": "LE-SHP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LE-SHP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Goods Issue", "value": "LE-SHP-GI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LE-SHP-GI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1776807 - Performance improvement for WT parallel processing"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You often post the goods movements for a delivery with reference to a specific material number from the same plant. The system often issues error messages especially at peak times due to various parallel postings (background jobs or dialog). This occurs despite a later material block and therefore, possibly despite various attempts. An example of these error messages is M3 897: \"The plant data of the material &amp; is locked by the user &amp;\". The performance analysis shows for example, that the delivery-relevant message determination is a reason for the blocks that are held for a long time unnecessarily (MARC/MBEW).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>M3 897, M3897, message determination, LIS, VL06G, VL02N</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This concerns the processing sequence of delivery posting.<br /><br />This was not part of the original system design.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached correction instructions. In addition, create the new form routine USER_EXIT_EARLY_UNLOCK_MM at the end of the include MV50AFZ1. Use the attached programming example for this to set the changing parameter CP_EARLY_UNL to X.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "LE-SHP-GF-OC (Output Determination Delivery)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D037211)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D037211)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001776807/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001776807/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1865642", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "OIL Adjustment note for core note 1776807", "RefUrl": "/notes/1865642"}, {"RefNumber": "1737609", "RefComponent": "MM-IM-GF", "RefTitle": "Changing the update time of the goods movement", "RefUrl": "/notes/1737609"}, {"RefNumber": "1723759", "RefComponent": "MM-IM-GF-LOCK", "RefTitle": "Change from exclusive block to shared when using S-Price", "RefUrl": "/notes/1723759"}, {"RefNumber": "1685136", "RefComponent": "MM-IM-GF-MISC", "RefTitle": "VL01N/VL31N: Field XBLNR_MKPF not filled in MM document item", "RefUrl": "/notes/1685136"}, {"RefNumber": "1673670", "RefComponent": "MM-IM-GF", "RefTitle": "Supplement to SAP Note 1668550", "RefUrl": "/notes/1673670"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1856641", "RefComponent": "LE-SHP-GI", "RefTitle": "Material document shows \"Archived\" in document flow.", "RefUrl": "/notes/1856641 "}, {"RefNumber": "3167335", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Subcontracting Inbound Delivery - Oil fields not filled for components in material document , reversal effected", "RefUrl": "/notes/3167335 "}, {"RefNumber": "2690967", "RefComponent": "LE-SHP-GI", "RefTitle": "no HU data for output processing during goods issue", "RefUrl": "/notes/2690967 "}, {"RefNumber": "2062855", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "Error M7299 at loading or delivery confirmation", "RefUrl": "/notes/2062855 "}, {"RefNumber": "1985306", "RefComponent": "MM-IM-GF-LOCK", "RefTitle": "Performance guide for goods movements", "RefUrl": "/notes/1985306 "}, {"RefNumber": "1673670", "RefComponent": "MM-IM-GF", "RefTitle": "Supplement to SAP Note 1668550", "RefUrl": "/notes/1673670 "}, {"RefNumber": "1865642", "RefComponent": "IS-OIL-DS-TD", "RefTitle": "OIL Adjustment note for core note 1776807", "RefUrl": "/notes/1865642 "}, {"RefNumber": "1723759", "RefComponent": "MM-IM-GF-LOCK", "RefTitle": "Change from exclusive block to shared when using S-Price", "RefUrl": "/notes/1723759 "}, {"RefNumber": "1737609", "RefComponent": "MM-IM-GF", "RefTitle": "Changing the update time of the goods movement", "RefUrl": "/notes/1737609 "}, {"RefNumber": "1685136", "RefComponent": "MM-IM-GF-MISC", "RefTitle": "VL01N/VL31N: Field XBLNR_MKPF not filled in MM document item", "RefUrl": "/notes/1685136 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "616", "To": "616", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "617", "To": "617", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "618", "To": "618", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "107", "To": "107", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "108", "To": "108", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50029", "URL": "/supportpackage/SAPKH50029"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60024", "URL": "/supportpackage/SAPKH60024"}, {"SoftwareComponentVersion": "SAP_APPL 602", "SupportPackage": "SAPKH60214", "URL": "/supportpackage/SAPKH60214"}, {"SoftwareComponentVersion": "SAP_APPL 603", "SupportPackage": "SAPKH60313", "URL": "/supportpackage/SAPKH60313"}, {"SoftwareComponentVersion": "SAP_APPL 604", "SupportPackage": "SAPKH60414", "URL": "/supportpackage/SAPKH60414"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60511", "URL": "/supportpackage/SAPKH60511"}, {"SoftwareComponentVersion": "SAP_APPL 606", "SupportPackage": "SAPKH60608", "URL": "/supportpackage/SAPKH60608"}, {"SoftwareComponentVersion": "SAP_APPL 616", "SupportPackage": "SAPKH61602", "URL": "/supportpackage/SAPKH61602"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 5, "URL": "/corrins/0001776807/1"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0001776807/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 6, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 6, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "782447 ", "URL": "/notes/782447 ", "Title": "Runtime error SAPSQL_ARRAY_INSERT_DUPREC when posting GIs", "Component": "LE-SHP-GI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "1081640 ", "URL": "/notes/1081640 ", "Title": "Parcel tracking data is not always saved", "Component": "LE-TRA-XSI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "500", "Number": "1309475 ", "URL": "/notes/1309475 ", "Title": "Post Goods Issue Button truncated in Delivery Processing", "Component": "LE-SHP-DL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "500", "ValidTo": "604", "Number": "1777374 ", "URL": "/notes/1777374 ", "Title": "Material documents missing sporadically for delivery", "Component": "LE-SHP-GI"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "989611 ", "URL": "/notes/989611 ", "Title": "Inbound control framework switch redesign", "Component": "LO-SPM-INB"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1309475 ", "URL": "/notes/1309475 ", "Title": "Post Goods Issue Button truncated in Delivery Processing", "Component": "LE-SHP-DL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "600", "ValidTo": "605", "Number": "1589704 ", "URL": "/notes/1589704 ", "Title": "Runtime problems when you set delivery completed indicatorII", "Component": "LO-SPM-INB"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1309475 ", "URL": "/notes/1309475 ", "Title": "Post Goods Issue Button truncated in Delivery Processing", "Component": "LE-SHP-DL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1309475 ", "URL": "/notes/1309475 ", "Title": "Post Goods Issue Button truncated in Delivery Processing", "Component": "LE-SHP-DL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "604", "ValidTo": "604", "Number": "1309475 ", "URL": "/notes/1309475 ", "Title": "Post Goods Issue Button truncated in Delivery Processing", "Component": "LE-SHP-DL"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "605", "ValidTo": "605", "Number": "1777374 ", "URL": "/notes/1777374 ", "Title": "Material documents missing sporadically for delivery", "Component": "LE-SHP-GI"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2420700", "RefTitle": "batch split with GR leads to wrong EKES update", "RefUrl": "/notes/0002420700"}, {"RefNumber": "2690967", "RefTitle": "no HU data for output processing during goods issue", "RefUrl": "/notes/0002690967"}]}}}}}