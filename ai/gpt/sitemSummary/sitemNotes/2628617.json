{"Request": {"Number": "2628617", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 409, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000763102018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=19604CFF8B386DBA1E9961DA5540FDFF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2628617"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "CA-FLE-AMT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Amount Field Extension"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Cross Application Field Lenght Extension", "value": "CA-FLE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Amount Field Extension", "value": "CA-FLE-AMT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-FLE-AMT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2628617 - Amount Field Length Extension: Adaptations for ALE/BDBG Generation"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Selected currency amount&#160;field lengths and related data types&#160;have been extended. See SAP Note <a target=\"_blank\" href=\"/notes/2628654\">2628654&#160;</a>for motivation and scope.</p>\r\n<p>This is relevant, if you are converting from SAP ERP ECC60 or upgrading to SAP S/4HANA On-Premise 1809 or higher.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Amount Field Length Extension, AFLE, BAPI, BDBG, ALE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>In SAP S/4HANA,&#160;currency amount fields with a field length between 9-22 including 2 decimals have been extended to 23 digits including 2 decimals. In addition to currency amount fields, selected data elements of DDIC type DEC, CHAR, and NUMC with varying lengths and decimal places&#160;that may hold amounts have been affected. This feature is available in SAP S/4HANA, on-premise edition 1809 and higher releases.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Introduction</strong></p>\r\n<p>Amount field&#160;extensions were mainly realized via domain exchanges of existing data-elements, which guarantee that most of the affected development artifacts like ABAP datatypes, structures, and database tables within the system are also adjusted consistently. With this approach, the ABAP code that uses the affected types is still syntactically correct in almost all cases.&#160;This also applies to your code that references&#160;the extended data elements. For more background information about the amount field length extensions in general, see SAP Note <a target=\"_blank\" href=\"/notes/2628040\">2628040 </a>and SAP Note <a target=\"_blank\" href=\"/notes/2628654\">2628654</a>. Nevertheless, there are some artifacts that may require manual adjustments.</p>\r\n<p><span style=\"text-decoration: underline;\">ALE Interface for BAPIs</span></p>\r\n<p>For more information, see <a target=\"_blank\" href=\"https://help.sap.com/saphelp_nw73/helpdata/en/4a/644ebc4b752b1ae10000000a42189c/frameset.htm\">SAP Help </a>.</p>\r\n<p>To allow the asynchronous calls of BAPI&#160;in ALE business processes, create a BAPI-ALE interface for the BAPI. The integration of BAPIs and ALE makes it easier for you to develop your own ALE business processes for distribution.</p>\r\n<p>BAPIs are methods of SAP business objects. They are defined in the Business Object Repository (BOR) and are subject to strict design guidelines. The SAP Business Objects and their BAPIs provide an open, object-oriented view of the business processes and data in an SAP System. BAPIs are implemented as RFC-enabled function modules in the SAP system.</p>\r\n<p>When you maintain an ALE interface for a BAPI, the following&#160;objects are generated:</p>\r\n<ul>\r\n<li>\r\n<p>Message type</p>\r\nIDoc type &amp; IDoc segment types: An IDoc type is generated for the BOR method specified on the initial screen of BDBG. The IDoc type would be an assembly of segment types generated as per the parameters of the BAPI. Only relevant input parameters are considered for segments types. The parameter should be a parameter for the BOR method. The segment types consist of a header segment for all single value scalar parameters and segments per reference structure.</li>\r\n<li>ALE outbound function module:<br />A function module that is called on the outbound side, acting as a proxy for sending the IDoc. It uses the BAPI data to generate the IDoc and dispatches it.</li>\r\n<li>ALE inbound function module:<br />A function module that calls the BAPI with the IDoc data on the inbound processing side.</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\">Amount Field Length Extension</span></p>\r\n<p>&#160;For more&#160;information about the amount field length extensions in general, see the&#160;SAP Note <a target=\"_blank\" href=\"/notes/2628654\">2628654</a>.</p>\r\n<p><strong>Solution step 1: Add an additional parameter to your BOR method for the LONG parameter</strong></p>\r\n<p>This step is only needed if the extended field is a scalar parameter of the BAPI and a LONG scalar parameter has been added to the BAPI function module&#8217;s signature. It is not needed if extended fields only occur in structured parameters.</p>\r\n<p>As explained above, for generation of segment types only those input BAPI parameters are considered which are maintained as &#8220;Import&#8221; BOR method parameter.&#160;This is particularly important for amount field length extension. As part of field length extension, we add long parameters in BAPI for all scalar parameters extended. If the existing scalar parameter was considered for IDoc header segment field the added long parameter should also be considered for IDoc segment field.</p>\r\n<p>For BOR object methods&#160;delivered by SAP the needed adaptions to the BOR method parameters have already been done.</p>\r\n<p>If the extended scalar parameter is an importing parameter of the BOR method,&#160;just maintain the LONG parameter also as BOR method parameter.</p>\r\n<p><strong>Solution</strong><strong> step 2: Repair IDoc segments for IDocs that you&#160;already generated before conversion to S/4HANA </strong></p>\r\n<p><strong>This step and the subsequent steps are relevant for scalar parameters as well as structured parameters.</strong></p>\r\n<p>The segment definition in table EDSAPPL contains the reference to the currency data elements (Example: DMBTR) in case the ALE interface was already generated before. In the BAPIs delivered by SAP this has been changed to currency data element *_XX_2 (Example: DMBTR_13_2, Refer to the SAP Note <a target=\"_blank\" href=\"/notes/2628040\">2628040 </a>for details on Decoupling).&#160;For custom generated IDoc segments this still needs to be done.</p>\r\n<p>To repair the IDoc segment follow these steps:</p>\r\n<ol>\r\n<li>Execute transaction IDoc</li>\r\n<li>Go to <em>Repair Segments</em></li>\r\n<li>Select<em> Change data element for field</em></li>\r\n<li>Choose Execute</li>\r\n<li>Select<em> Segment Type, Field Name and extended data element</em></li>\r\n<li>Choose Execute&#160;</li>\r\n</ol>\r\n<div>\r\n<div>\r\n<div>\r\n<p><strong>Solution step 3: Regenerate the ALE interfaces</strong></p>\r\n<p>Trigger regeneration in transaction BDBG.</p>\r\n<p>You usually do not need to change any values on the subsequent dialogs.</p>\r\n<p>Further steps will&#160;be just the same that you usually do in&#160;BDBG:&#160;inbound, outbound function module and all affected segments are regenerated. If you&#160;did not generate function modules before for the specific interface, you can skip this step&#160;by canceling the confirmation dialog for function module generation. You should finally check the generated interface if the entities are contained as before with the additional fields added.</p>\r\n<div>\r\n<p><strong>Solution step 4:&#160;&#160;Adapt your coding that calls the generated ALE outbound function module </strong></p>\r\n<p>In case you&#160;call the generated ALE outbound module to send an IDoc to a BAPI in a system that has not been enabled for&#160;the S/4HANA&#160;amount field length&#160;extension (example: an ECC system), make sure that the original short fields are also filled by the generated ALE outbound function module. Theoretically it would be possible to provide both short and long field for the amounts in each call to the generated outbound module, however this is error-prone. The other option is to use the mapper class already used for the BAPI implementations and embed the necessary mapping calls in the generated module. The latter is the recommended approach and will be described in the following.</p>\r\n<p>Change all calls to the generated outbound modules&#160;to fill the&#160;long amount fields in case&#160;amount field&#160;length extension&#160;shall be supported.</p>\r\n<p>To ensure both the short and long fields are filled compatibly, you can use the method &#8216;bapi_struct_outbound&#8217; of the class&#160;&#8216;cl_afle_chk_mapper&#8217; for filling the short fields. For more information see the class documentation.</p>\r\n<div>\r\n<div>\r\n<div>\r\n<div>\r\n<div>\r\n<div>\r\n<p>Example: FM: ALE_ACC_MANUAL_ALLOC_POST</p>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n</div>\r\n<div>\r\n<div>\r\n<div>*&#160;begin-of-insertion<br />*&#160;AFLE<br />&#160;&#160;DATA&#160;lt_fnames&#160;TYPE&#160;cl_afle_chk_mapper=&gt;tt_amt_bapi_fname.<br />&#160;&#160;lt_fnames&#160;=&#160;VALUE&#160;#(<br />&#160;&#160;&#160;&#160;(&#160;short&#160;=&#160;'VALUE_TCUR'&#160;long&#160;=&#160;'VALUE_TCUR_LONG'&#160;)<br />&#160;&#160;&#160;&#160;).<br /><br />&#160;&#160;LOOP&#160;AT&#160;DOC_ITEMS&#160;ASSIGNING&#160;FIELD-SYMBOL(&lt;ls_doc_item&gt;).<br /><br />&#160;&#160;&#160;&#160;CALL&#160;METHOD&#160;cl_afle_chk_mapper=&gt;bapi_struct_outbound<br />&#160;&#160;&#160;&#160;&#160;&#160;EXPORTING<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;it_fnames&#160;&#160;=&#160;lt_fnames<br />&#160;&#160;&#160;&#160;&#160;&#160;IMPORTING<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;return&#160;&#160;&#160;&#160;&#160;=&#160;DATA(ls_return)<br />&#160;&#160;&#160;&#160;&#160;&#160;CHANGING<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;cs_amt_ext&#160;=&#160;&lt;ls_doc_item&gt;.<br /><br />&#160;&#160;&#160;&#160;IF&#160;ls_return-type&#160;=&#160;'E'.<br />&#160;&#160;&#160;&#160;&#160;&#160;MESSAGE&#160;ID&#160;ls_return-id<br />&#160;&#160;&#160;&#160;&#160;&#160;TYPE&#160;ls_return-type<br />&#160;&#160;&#160;&#160;&#160;&#160;NUMBER&#160;ls_return-number<br />&#160;&#160;&#160;&#160;&#160;&#160;WITH&#160;ls_return-message_v1&#160;ls_return-message_v2<br />&#160;&#160;&#160;&#160;&#160;&#160;ls_return-message_v3&#160;ls_return-message_v4<br />&#160;&#160;&#160;&#160;&#160;&#160;RAISING&#160;ERROR_CREATING_IDOCS.<br />&#160;&#160;&#160;&#160;ENDIF.<br /><br />&#160;&#160;ENDLOOP.<br />*&#160;end-of-insertion&#160;</div>\r\n</div>\r\n</div>\r\n<div>\r\n<div>\r\n<div>\r\n<p>You should do this in a similar way as described in&#160;SAP Note&#160;<a target=\"_blank\" href=\"/notes/2628724\">2628724&#160;</a>for remote calls to other systems, although&#160;the call to the generated ALE function module is local. This is because the generated ALE outbound module is a proxy for calling a BAPI asynchronously in a remote system. In this way it is an asynchronous remote function call.</p>\r\n<p>Note that this mapping has not only to be done for added scalar parameters but also for extended fields in structure and table parameters.</p>\r\n<div>\r\n<div>\r\n<div>\r\n<p>To protect the changes from getting lost during re-generation of the ALE inbound function modules we advise to follow the approach described in SAP Note <a target=\"_blank\" href=\"/notes/513785\">513785</a>.</p>\r\n</div>\r\n</div>\r\n</div>\r\n&#160;</div>\r\n</div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D048317)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D048317)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "513785", "RefComponent": "BC-MID-ALE", "RefTitle": "UTI: Extension of source code generated by BDBG/BDFG", "RefUrl": "/notes/513785"}, {"RefNumber": "2628724", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: Code Adaptations/Usages of BAPIs and RFCs", "RefUrl": "/notes/2628724"}, {"RefNumber": "2628654", "RefComponent": "CA-FLE-AMT", "RefTitle": "S4TWL: Amount Field Length Extension", "RefUrl": "/notes/2628654"}, {"RefNumber": "2628040", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: General Information", "RefUrl": "/notes/2628040"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2628699", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: Code Adaptations for Compatibly Enhanced Local Function Modules", "RefUrl": "/notes/2628699 "}, {"RefNumber": "2628641", "RefComponent": "CA-FLE-AMT", "RefTitle": "Amount Field Length Extension: IDoc Interface/ALE Adaptations", "RefUrl": "/notes/2628641 "}, {"RefNumber": "2628654", "RefComponent": "CA-FLE-AMT", "RefTitle": "S4TWL: Amount Field Length Extension", "RefUrl": "/notes/2628654 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}