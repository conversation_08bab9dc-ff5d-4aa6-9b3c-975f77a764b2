{"Request": {"Number": "1178006", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001178006?language=E&token=B70CACF399EEFA5DB793C588306BEE44"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001178006", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001178006/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1178006"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Modification"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "05.05.2011"}, "SAPComponentKey": {"_label": "Component", "value": "PY-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "PY-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1178006 - IN86 - Retroactive calculation and wage type amount signs"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The IN86 report (HBRIN860) does not consider the differences of retroactive calculations to create the records 4.8.1.<br /><br />Besides that, the negative amounts of the wage types are considered in the creation of records 4.8.1, but the minus sign is not reported in the file. Therefore, these amounts are reported without sign, indicating they are positive values.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IN86, HBRIN860, retroactive calculation, retrocalculation, negative sign, sign, minus sign</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>IMPORTANT: This note will not be release via Support Package.<br /><br />The IN86 report was modified to support the retroactive calculation values and the minus signs during the creation of records 4.8.1.<br /><br />From now on, the differences generated by retroactive calculations are reported in the period in which they are paid to the employee.<br /><br />In addition, the signs of the wage type amount reported in the records 4.8.1 have been included to the TemSe file. There is a column that is filled with the sign \"+\" (plus) or \"-\" (minus), according to the customizing done on Evaluation Class 15 for the respective wage type.<br /><br />For example, if you want the IN86 report to consider the sign of the wage type amount, you have to customize the Evaluation Class 15 for this wage type the values:<br />- P: for a gain wage type<br />- DR: for a discount wage type<br /><br /><B>STEPS SUMMARIZE</B><br /><br />1) Install the attached Advanced Delivery file for the respective system's release<br /><br />2) Apply the correction instructions for the respective system's release (customer at Release 4.6C only need to install the Advanced Delivery file)<br /><br />An Advanced Delivery is available in the attached files according to the following list (\"xxxxxx\" means numbers):<br /><br />- L7DKxxxxxx_600.CAR - Release 600 (ERP 2005)<br />- L6DKxxxxxx_500.CAR - Release 500 (ERP 2004)<br />- L6BKxxxxxx_470.CAR - Release 4.70 (Enterprise)<br />- L9CKxxxxxx_46C.CAR - Release 4.6C<br /><br />For more details about Advance Delivery installation procedure please read the notes listed in \"Related Notes\".<br /><br />Important: Be aware of an Advance Delivery delivers the last version of the object. It means that if you do not have the last HR Support Package installed in your system you could get errors, either Syntax Errors or process errors. In this case the only option is to undo the changes from Advance Delivery and do the code changes manually according to the Correction Instructions available in this note.<br /><br /> <br />The following objects were changed:<br /><br />- Report source<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; HBRIN860<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; PCN86BR0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; PCN86BRD<br /><br />- Table definition<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; HBRIN86481<br /><br />- Type group source code<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; PBR37<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I811676)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I811676)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001178006/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1129568", "RefComponent": "PY-BR", "RefTitle": "Report HBRIN860 should not include wage type without value", "RefUrl": "/notes/1129568"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1129568", "RefComponent": "PY-BR", "RefTitle": "Report HBRIN860 should not include wage type without value", "RefUrl": "/notes/1129568 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HRCBR", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 1, "URL": "/corrins/0001178006/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "951740 ", "URL": "/notes/951740 ", "Title": "HR-BR: IN86 - Job description missing", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1109752 ", "URL": "/notes/1109752 ", "Title": "HR-BR: Splitting TEMSE includes", "Component": "PY-BR"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "1129568 ", "URL": "/notes/1129568 ", "Title": "Report HBRIN860 should not include wage type without value", "Component": "PY-BR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}