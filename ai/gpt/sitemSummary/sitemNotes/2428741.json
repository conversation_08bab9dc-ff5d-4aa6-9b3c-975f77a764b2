{"Request": {"Number": "2428741", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 418, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018548262017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=5DF52919E8E3B3345555D6518B6E7478"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2428741"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.09.2020"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2428741 - Universal Journal FAQ"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want basic information on the Universal Journal in SAP S/4HANA Finance, on its effects on the accounting architecture and on changes in the functionality.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>journal entry, accounting document</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>General Questions</strong></p>\r\n<p><em>What is the Universal Journal?</em></p>\r\n<p>In SAP S/4HANA Finance, the Universal Journal captures all accounting-relevant transactions in Financial Accounting (FI) and Controlling (CO) as journal entries. It thus represents the single source of truth for both financial accounting and management accounting. The result is a fully integrated accounting system in which all line items from business transactions, regardless of where they occur, are located in one place. The Universal Journal contains all fields (columns) required by the business processes and the individual components. The first release of the Universal Journal was SAP Simple Finance 2.0. SAP Simple Finance has since been renamed SAP S/4HANA&#174; Finance.</p>\r\n<p><em>Why was the Universal Journal introduced?</em></p>\r\n<p>The Universal Journal was developed in order to guarantee the integrity of financial data, eliminate redundancy and reconciliation effort between FI and CO, and provide significantly higher levels of performance, transparency, and financial insight. Combining the data structures of the different components into a single line item table (ACDOCA) results in a single source of truth that replaces the previously separate physical tables.</p>\r\n<p><em>What application areas within FI and CO are integrated with the Universal Journal?</em></p>\r\n<p>Every business transaction in one of the following application components generates a journal entry:</p>\r\n<ul>\r\n<li>General Ledger Accounting (FL-GL)</li>\r\n<li>Asset Accounting (FI-AA)</li>\r\n<li>Controlling (CO)</li>\r\n<li>Profitability Analysis (CO-PA), except costing-based*</li>\r\n<li>Material Ledger (CO-PA-ACT)</li>\r\n</ul>\r\n<p>*Only account-based CO-PA is fully integrated with the universal journal. Costing-based CO-PA can nevertheless be used in parallel.</p>\r\n<p><em>How does the Universal Journal affect Controlling?</em></p>\r\n<p>A major change in Controlling is that cost elements are no longer stored as separate master data in CO. They are now a special type of G/L account, maintained as master records within General Ledger.</p>\r\n<p><em>How many parallel currencies can the Universal Journal handle?</em></p>\r\n<p>The Universal Journal in SAP S/4HANA can handle up to 10 currency fields. Two of them are preconfigured, and 8 are freely-definable currencies.<br />The preconfigured currencies are company code currency and controlling area currency. These two currencies cannot be changed. Controlling area currency is only available if you use the Controlling application component. <br />You can use the freely-defined currencies to configure further local currencies and to map transfer prices, for example.<em> <br /></em>Each posting fills all currency fields according to the configured currency conversion rules.</p>\r\n<p><em>What happened to New General Ledger Accounting (also known as \"New G/L\" or \"FI-GL (new)\")?</em></p>\r\n<p>The New General Ledger Accounting functions (which are still available for SAP ERP) were at the time introduced to enable parallel accounting as well as planning and reporting on the basis of profit centers, segments, and business units. These functions are now available using the Universal Journal which combines FI and CO within table ACDOCA.</p>\r\n<p><em>Doesn&#8217;t the real-time integration of FI and CO exist anymore?</em></p>\r\n<p>Statements such as &#8220;the FI-CO real-time integration is obsolete&#8221; may sound confusing, since real-time integration of the two application components FI and CO is essential in an integrated accounting system. But such statements only mean that the technical communication technique between the two components is not needed anymore because they are inherently integrated within the same table ACDOCA. Since the two components are no longer separated, the data is always available in real time.</p>\r\n<p><em>Have all Accounting-relevant tables been replaced by table ACDOCA?</em></p>\r\n<p>No. Many tables have been replaced by table ACDOCA but not all. <br />For example, the former &#8220;G/L-only&#8221; table BSEG still exists as it is needed to store the source documents that serve as the basis for journal entries into table ACDOCA. In addition, it stores entries relating to open item management.<br />Also, table BKPF that stores the header for journal entries remains unchanged.</p>\r\n<p>The entries of the following tables are now completely contained in table ACDOCA:</p>\r\n<ul>\r\n<li>The tables making up New General Ledger Accounting known in classical ERP:<br />FAGLFLEXA, FAGLFLEXT, JVGLFLEXA, JVGLFLEXT,&#160;FMGLFLEXA, FMGLFLEXT</li>\r\n<li>The actual data of the Controlling tables: COEP, COSS, COSP<br />The data for statistical postings and target data remain in the tables COEP, COSS, and COSP.</li>\r\n<li>The Material Ledger table: MLIT</li>\r\n<li>The Fixed Assets table: ANEP<br />(Please note that this list is not exhaustive and only lists some of the most important tables.)</li>\r\n</ul>\r\n<p><em>Why is there no primary key on the database for table ACDOCA?</em></p>\r\n<p>In order to reduce its memory footprint and allow flexible partitioning as described in Note 2289491, table ACDOCA is created without a primary key on the database.</p>\r\n<p>Although fields .RCLNT to DOCLN are marked as primary keys in SE11, there is a special setting in the DDIC that prevents creation of the primary key on the database. To verify that there is no inconsistency (that is, the primary key does not exist on the database), you can use <em>Database object -&gt; Check</em> in transactions SE11 or SE14.</p>\r\n<p>The primary key of table BKPF is always inserted in the same transaction when new journal entries are posted. This prevents the unintended insertion of duplicate document numbers in table ACDOCA.</p>\r\n<p><strong>Terminology</strong></p>\r\n<p><em>What is the difference between journal entries and accounting documents?</em></p>\r\n<p>In SAP ERP, accounting documents (also known as G/L account documents, G/L documents, or simply documents) represented the Financial Accounting view of business transactions. They were complemented by CO documents which represented the Controlling view. It was not possible to navigate directly between an accounting document and the corresponding CO document as they were stored in different parts of the system.</p>\r\n<p>With SAP Simple Finance, On-Premise Edition 1503 (SAP S/4HANA&#174; Finance), accounting documents and CO documents have been superseded by journal entries.</p>\r\n<p><strong>Documentation</strong></p>\r\n<p>General information on the Universal Journal is available on the Help Portal in the Application Help for SAP Simple Finance, On-Premise Edition under Financial Accounting (FI) &gt; General Ledger Accounting (FI-GL) (New) &gt; Business Transactions &gt; Posting &gt; Document &gt; Universal Journal.</p>\r\n<p>For detailed information on cost elements, see the Application Help for SAP Simple Finance, On-Premise Edition under Controlling (CO) &gt; Cost Center Accounting (CO-OM-CCA) &gt; Master Data in Cost Center Accounting (CO-OM-CCA) &gt; Cost Elements.</p>\r\n<p>For an overview of the changes introduced by the Universal Journal, see the Help Portal for SAP Simple Finance, On-Premise Edition under What's New - Release Notes &gt;&#160;SAP Simple Finance, On-Premise Edition &gt;&#160;Initial Shipment &gt; SAP Accounting powered by SAP HANA &gt; Universal Journal Entry.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D020843"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023393)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3002739", "RefComponent": "XX-TRANSL-JA", "RefTitle": "Collective note: Japanese translation of terms changed in SAP S/4HANA", "RefUrl": "/notes/3002739 "}, {"RefNumber": "2527020", "RefComponent": "FI-GL-GL", "RefTitle": "G/L accounts with the G/L account type \"Secondary Costs\" in general ledger accounting", "RefUrl": "/notes/2527020 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": "X"}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}