{"Request": {"Number": "593041", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 341, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003006552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000593041?language=E&token=B9AFF7CF82F2AE7B502105E9B98E9465"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000593041", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000593041/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "593041"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Exit added"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.11.2006"}, "SAPComponentKey": {"_label": "Component", "value": "CA-DSG"}, "SAPComponentKeyText": {"_label": "Component", "value": "Digital Signature"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Digital Signature", "value": "CA-DSG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-DSG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "593041 - DSAL: Digital Signature Logs - BAdI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You entered digital signatures in your applications. You use transaction DSAL to display the log entries for the signatures that have been executed. During this process you note the following:</p> <OL>1. You cannot display the comment for a digital signature from the message.</OL> <OL>2. When you print digital signatures, the system does not print information about, for example, page number, date of the printout, and so on.</OL> <p></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Digital signature, Digital Signature Logs, DSAL, comment, BAdI, BADI, CJ, DSIGAL, CJ_DSAL_FUNC</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This problem is caused by a missing or incomplete function.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Remark:<br />As of Note 994317, the relevant function for displaying comments is available in the SAP standard system (as of SAP_ABA Release 6.20).<br /><br />Create the CJ_DSAL_FUNC BAdI in package CJ.<br />To implement the BAdI, proceed as follows:</p> <OL>1. Use transaction SE18 to call the BAdI Builder. Create the BAdI with definition name CJ_DSAL_FUNC.</OL> <UL><LI>Choose the Attributes tab page:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Definition short text: BAdI for DSAL for the inclusion of customer functions<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type: Do not select 'Within SAP', 'Multiple use' or 'Filter-Depend.' for this BAdI.</p> <OL>2. Change to the Interface tab page. Transfer the name proposed by the system (IF_EX_CJ_DSAL_FUNC).</OL> <OL>3. Create the methods of the BAdI interface. To do this, double-click the IF_EX_CJ_DSAL_FUNC interface name. The system prompts you to save the BAdI definition. Assign the BAdI definition to package CJ when you save it. The system displays the view of the methods for the IF_EX_CJ_DSAL_FUNC interface. Maintain the following data:</OL> <UL><LI>Method: GET_MULTI&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Level: Instance method</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Information about several selected lines</p> <UL><LI>Method: GET_SINGLE&#x00A0;&#x00A0;&#x00A0;&#x00A0; Level: Instance method</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Information about one selected line</p> <UL><LI>Method: SET_BUTTON&#x00A0;&#x00A0;&#x00A0;&#x00A0; Level: Instance method</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Set customer-defined pushbuttons in DSAL</p> <OL>4. Now maintain the data for the parameters of the methods:</OL> <OL><OL>a) Method GET_MULTI:</OL></OL> <UL><UL><LI>Parameter: IM_FUNCTION&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type  Associated Type: UI_FUNC</LI></UL></UL> <UL><UL><LI>Parameter: IM_LOG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type  Associated Type: BAL_T_LOGH</LI></UL></UL> <UL><UL><LI>Parameter: IM_MSG&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type  Associated Type: BAL_T_MSGH</LI></UL></UL> <OL><OL>b) Method: GET_SINGLE:</OL></OL> <UL><UL><LI>Parameter: IM_FUNCTION&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type  Associated Type: UI_FUNC</LI></UL></UL> <UL><UL><LI>Parameter: IM_HDR_CONTEXT_TABNAME&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0; Typing Method: Type&#x00A0;&#x00A0;Associated Type: BALTABNAME</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Header data: Table name of the original context</p> <UL><UL><LI>Parameter: IM_HDR_CONTEXT&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type Associated Type: ANY</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Header data: Context</p> <UL><UL><LI>Parameter: IM_MSG_CONTEXT_TABNAME&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0; Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: BALTABNAME<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Message data: Table name of the original context</p> <UL><UL><LI>Parameter: IM_MSG_CONTEXT&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0;&#x00A0;&#x00A0;Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: ANY<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Message data: Context</p> <UL><UL><LI>Parameter: IM_HDR&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0; Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: BAL_S_LOG<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Header data</p> <UL><UL><LI>Parameter: IM_MSG&#x00A0;&#x00A0; Type: Importing&#x00A0;&#x00A0; Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: BAL_S_MSG<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Message data</p> <OL><OL>c) Method SET_BUTTON:</OL></OL> <UL><UL><LI>Parameter: EX_BUTTONS&#x00A0;&#x00A0; Type: Exporting&#x00A0;&#x00A0;&#x00A0;&#x00A0; Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: TTB_BUTTON<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Include new pushbuttons</p> <UL><UL><LI>Parameter: EX_EXCLUDE_FUNCTIONS&#x00A0;&#x00A0; Type: Exporting&#x00A0;&#x00A0;Typing Method: Type</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Associated Type: UI_FUNCTIONS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Description: Exclude existing pushbuttons</p> <OL>5. Save your entries. Activate the interface.</OL> <OL>6. Check, whether the CL_EX_CJ_DSAL_FUNC BAdI class was generated when you saved and activated the interface.</OL> <p></p> <OL>7. Now call transaction SE80. Create the new include LDSIGALF04 in the DSIGAL function group. Use menu path Goto -&gt; Attributes to make the following entries:</OL> <OL><OL>a) Title: BAdI CJ_DSAL_FUNC</OL></OL> <OL><OL>b) Status: SAP standard production program</OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Save and activate your entries. <OL>8. Then implement the correction instructions. They contain the source codes for the BAdI methods. Save and activate your entries.</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D026022)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D038137)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000593041/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000593041/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "994317", "RefComponent": "CA-DSG", "RefTitle": "Displaying comments for individual signatures with DSAL", "RefUrl": "/notes/994317"}, {"RefNumber": "827417", "RefComponent": "CA-DSG", "RefTitle": "Signature tool: Output of signed documents using DSAL", "RefUrl": "/notes/827417"}, {"RefNumber": "594010", "RefComponent": "CA-DSG", "RefTitle": "DSAL: Example implementations for BADI CJ_DSAL_FUNC", "RefUrl": "/notes/594010"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "594010", "RefComponent": "CA-DSG", "RefTitle": "DSAL: Example implementations for BADI CJ_DSAL_FUNC", "RefUrl": "/notes/594010 "}, {"RefNumber": "994317", "RefComponent": "CA-DSG", "RefTitle": "Displaying comments for individual signatures with DSAL", "RefUrl": "/notes/994317 "}, {"RefNumber": "827417", "RefComponent": "CA-DSG", "RefTitle": "Signature tool: Output of signed documents using DSAL", "RefUrl": "/notes/827417 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C43", "URL": "/supportpackage/SAPKH46C43"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47008", "URL": "/supportpackage/SAPKH47008"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 2, "URL": "/corrins/0000593041/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}