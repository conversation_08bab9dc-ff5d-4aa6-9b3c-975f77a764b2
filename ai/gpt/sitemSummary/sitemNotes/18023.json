{"Request": {"Number": "18023", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 346, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014338082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000018023?language=E&token=F50758F5C0B878B30F8A51D046AA68B5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000018023", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000018023/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "18023"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.07.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB"}, "SAPComponentKeyText": {"_label": "Component", "value": "ABAP Workbench, Java IDE and Infrastructure"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "18023 - Jobs EU_INIT, EU_REORG, EU_PUT"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>What are the jobs EU_INIT (program SAPRSEUC), EU_REORG (program SAPRSLOG), and EU_PUT (program SAPRSEUT), which are automatically scheduled by the ABAP Workbench, used for?</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>SE80, Object Navigator, Repository Browser, Repository Information System</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The EU jobs are used to reconstruct or update the indexes (where-used lists, navigation indexes, object lists) that are important for the ABAP Workbench.<br /><br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>When you start transaction SE80 (Repository Browser) for the first time, the three EU jobs are automatically created and, if the user has sufficient authorizations, released: EU_INIT (single start), EU_REORG (periodically each night), and EU_PUT (periodically each night). Alternatively, You can also schedule the three jobs by manually executing program SAPR<PERSON>UJ.<br />If the user does not have a release authorization, the jobs remain in the status \"scheduled\". In this state, they are not executed until they are eventually manually released. Therefore, in this case it is required that a system administrator interferes.<br /><br />Short description of the individual jobs:<br /></p> <UL><LI>EU_INIT:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;EU_INIT is used to completely rebuild the indexes and therefore has a correspondingly long runtime. It starts program SAPRSEUI. All customer-defined programs (selection according to the naming convention) are analyzed and indexes are created that are used in the ABAP Workbench for the where-used lists of function modules, error messages, reports, and so on. These indexes are automatically updated in dialog mode.<br />The job can be repeated at any time. After a termination, the job is automatically scheduled for the next day; it then starts at the point of termination. (EU_INIT can therefore be terminated deliberately, if it disturbs other activities in the system. See also Note 759407.)</p> <UL><LI>EU_REORG:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As mentioned above, the indexes are automatically updated online by the ABAP Workbench tools. To keep the effort for updating these indexes as low as possible, only the changes are logged, which means a reorganization of the entire index for each program is required from time to time. So that this reorganization does not interfere with the online system, the EU_REORG job runs every night and performs this task. If the EU_REORG job did not run one night, this simply means that the reorganization takes place more often online.</p> <UL><LI>EU_PUT:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The EU_PUT job also runs every night. It starts program SAPRSEUT. This program checks whether customer-defined development objects have been transported into the SAP system with the SAP transport system, and generates or updates the indexes described above whenever required.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;<br />To create the indexes, the EU jobs analyze the program sources of the development objects. Faulty ABAP programs (sources with grave syntax errors, for example, a literal that is too long because a concluding inverted comma is missing) are skipped. The relevant job continued the analysis with the next program and issues the names of all programs with errors in a list.<br />After correcting the faulty programs, you can update the object lists of the relevant programs in the Repository Browser. To do this, proceed as follows:</p> <UL><UL><LI>Up to Release 6.10: Choose \"Update\".</LI></UL></UL> <UL><UL><LI>As of Release 6.20: In the tree display for the object list, go to the context menu \"Other functions\"-&gt;\"Rebuild object list\".</LI></UL></UL> <UL><UL><LI></LI></UL></UL> <UL><UL><LI></LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB-UTL-INR (Repository Infosystem)"}, {"Key": "Other Components", "Value": "BC-DWB-UTL-BRR (Repository Browser, Object Navigator)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D032407)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D032407)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000018023/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000018023/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000018023/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "951329", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "ITS objects, where-used list, copying", "RefUrl": "/notes/951329"}, {"RefNumber": "785307", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP Query: Where-used list and generated reports", "RefUrl": "/notes/785307"}, {"RefNumber": "759407", "RefComponent": "BC-DWB-UTL", "RefTitle": "EU_INIT job is started repeatedly/you want to stop this job", "RefUrl": "/notes/759407"}, {"RefNumber": "663522", "RefComponent": "BC-DWB-UTL", "RefTitle": "Where-used list: Space required, DBIF_RSQL_SQL_ERROR", "RefUrl": "/notes/663522"}, {"RefNumber": "501868", "RefComponent": "BC-DWB-TOO", "RefTitle": "Forward navigation does not work", "RefUrl": "/notes/501868"}, {"RefNumber": "495859", "RefComponent": "BC-DWB-UTL", "RefTitle": "Incorrect where-used list for dictionary types", "RefUrl": "/notes/495859"}, {"RefNumber": "459122", "RefComponent": "PA-PA-CN", "RefTitle": "Tcode SWO2 and BAPI do not work", "RefUrl": "/notes/459122"}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389"}, {"RefNumber": "389679", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "CS V46C.1A: error in TA SWO2 (SAPLSEM5)", "RefUrl": "/notes/389679"}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022"}, {"RefNumber": "208540", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/208540"}, {"RefNumber": "188790", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of jobs EU_PUT and EU_REORG", "RefUrl": "/notes/188790"}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645"}, {"RefNumber": "1527757", "RefComponent": "BC-DWB-UTL", "RefTitle": "Deleting multiple-use includes: Where-used list", "RefUrl": "/notes/1527757"}, {"RefNumber": "1346740", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Where-used list for component configurations", "RefUrl": "/notes/1346740"}, {"RefNumber": "118481", "RefComponent": "BC-DWB-TOO-BOB", "RefTitle": "Component Hierarchy doesn't show all components", "RefUrl": "/notes/118481"}, {"RefNumber": "107510", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "User definable fields in IS-Oil SOCs (prerelease)", "RefUrl": "/notes/107510"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2801600", "RefComponent": "BC-DWB-UTL", "RefTitle": "How to stop the Where-Used List", "RefUrl": "/notes/2801600 "}, {"RefNumber": "2541906", "RefComponent": "BC-DWB-UTL", "RefTitle": "Function modules are missing from Where-Used list.", "RefUrl": "/notes/2541906 "}, {"RefNumber": "2516597", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Support troubleshooting guide: where-used-list - Internal Use Only", "RefUrl": "/notes/2516597 "}, {"RefNumber": "2833335", "RefComponent": "BC-DWB-AIE-DIC", "RefTitle": "DDLS object missing in the where-used result list", "RefUrl": "/notes/2833335 "}, {"RefNumber": "2234970", "RefComponent": "BC-DWB-UTL", "RefTitle": "Job EU_INIT", "RefUrl": "/notes/2234970 "}, {"RefNumber": "2145213", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Update search index properly for imported objects", "RefUrl": "/notes/2145213 "}, {"RefNumber": "28022", "RefComponent": "BC-DWB-UTL", "RefTitle": "Customer system: Where-used list for SAP Objects", "RefUrl": "/notes/28022 "}, {"RefNumber": "1879635", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Jobs EU_PUT and EU_REORG are created even though they are already scheduled", "RefUrl": "/notes/1879635 "}, {"RefNumber": "1346740", "RefComponent": "BC-WD-ABA", "RefTitle": "WDA: Where-used list for component configurations", "RefUrl": "/notes/1346740 "}, {"RefNumber": "1527757", "RefComponent": "BC-DWB-UTL", "RefTitle": "Deleting multiple-use includes: Where-used list", "RefUrl": "/notes/1527757 "}, {"RefNumber": "759407", "RefComponent": "BC-DWB-UTL", "RefTitle": "EU_INIT job is started repeatedly/you want to stop this job", "RefUrl": "/notes/759407 "}, {"RefNumber": "401389", "RefComponent": "BC-DWB-UTL-INR", "RefTitle": "Where-used list in the Workbench as of Release 6.10", "RefUrl": "/notes/401389 "}, {"RefNumber": "785307", "RefComponent": "BC-SRV-QUE", "RefTitle": "SAP Query: Where-used list and generated reports", "RefUrl": "/notes/785307 "}, {"RefNumber": "501868", "RefComponent": "BC-DWB-TOO", "RefTitle": "Forward navigation does not work", "RefUrl": "/notes/501868 "}, {"RefNumber": "951329", "RefComponent": "BC-DWB-TOO-WAB", "RefTitle": "ITS objects, where-used list, copying", "RefUrl": "/notes/951329 "}, {"RefNumber": "663522", "RefComponent": "BC-DWB-UTL", "RefTitle": "Where-used list: Space required, DBIF_RSQL_SQL_ERROR", "RefUrl": "/notes/663522 "}, {"RefNumber": "459122", "RefComponent": "PA-PA-CN", "RefTitle": "Tcode SWO2 and BAPI do not work", "RefUrl": "/notes/459122 "}, {"RefNumber": "118481", "RefComponent": "BC-DWB-TOO-BOB", "RefTitle": "Component Hierarchy doesn't show all components", "RefUrl": "/notes/118481 "}, {"RefNumber": "495859", "RefComponent": "BC-DWB-UTL", "RefTitle": "Incorrect where-used list for dictionary types", "RefUrl": "/notes/495859 "}, {"RefNumber": "174645", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/174645 "}, {"RefNumber": "389679", "RefComponent": "XX-PROJ-CS-BC", "RefTitle": "CS V46C.1A: error in TA SWO2 (SAPLSEM5)", "RefUrl": "/notes/389679 "}, {"RefNumber": "188790", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of jobs EU_PUT and EU_REORG", "RefUrl": "/notes/188790 "}, {"RefNumber": "208540", "RefComponent": "BC-DWB-UTL-BRR", "RefTitle": "Multiple scheduling of the jobs EU_PUT and EU_REORG", "RefUrl": "/notes/208540 "}, {"RefNumber": "107510", "RefComponent": "IS-OIL-DS-BDRP", "RefTitle": "User definable fields in IS-Oil SOCs (prerelease)", "RefUrl": "/notes/107510 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}