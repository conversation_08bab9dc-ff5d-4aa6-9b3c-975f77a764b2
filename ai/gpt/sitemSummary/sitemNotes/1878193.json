{"Request": {"Number": "1878193", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 574, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001878193?language=E&token=A9DA0693B697F2FEC6D8DDB176B0B884"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001878193", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001878193/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1878193"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 25}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "To Be Archived"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1878193 - Central Note - Software Update Manager 1.0 SP10 [lmt_006]"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><br />Errors in the update process; preparations for the update; additional information to the update guides</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p><br />Enhancement package installation, Software Update Manager, SUM, EHP, update, upgrade, Support Package Stack application, SPS update, additional technical usage, maintenance</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>This note applies to <strong>Software Update Manager 1.0 SP10</strong>.<br /><br />Use Software Update Manager 1.0 SP10 for the following maintenance processes:</p>\r\n<ul>\r\n<li>Upgrading to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.4) for the following upgrade paths (SAP Business Suite systems are considered below):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 4.6C</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 Enterprise 4.70 / Extension Set 1.10</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP R/3 Enterprise 4.70 / Extension Set 2.00</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004-based systems (SAP ERP 2004, SAP SCM 4.1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based systems (SAP ERP 6.0 and higher, SAP SCM 5.0 and higher, SAP SRM 5.0 and higher, SAP CRM 5.0 and higher)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.4) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2010 (SAP ERP 6.0 EHP5, SAP CRM 7.0 EHP1, SAP SCM 7.0 EHP1, SAP SRM 7.0 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2011 (SAP ERP 6.0 EHP6, SAP CRM 7.0 EHP2, SAP SCM 7.0 EHP2, SAP SRM 7.0 EHP2)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 2 for SAP CRM 7.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP enhancement package 2 for SAP SCM 7.0, version for SAP HANA</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.3 including enhancement package 1) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2013 (based on SAP NetWeaver 7.3) for the following update/EHP installation paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.4 / 7.4 SR1 for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 (with SP 14 or higher) or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver AS ABAP 7.1 (for banking services from SAP 7.0 and 8.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 or SAP NetWeaver 7.3 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have SAP Business Suite 7i2013 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have SAP Business 7i2013 applications installed on top</li>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>\r\n<p>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.4)</p>\r\n</li>\r\n<li>\r\n<p>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.4)</p>\r\n</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.4)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\">Note that depending on whether your system is ABAP-based or Java-based, \"updating\" here means performing a release upgrade or enhancement package installation (on the technical level, this is handled differently on ABAP vs Java stack). <br />SAP NetWeaver 7.4 Support Release 1 (SR1) is based on SPS05 and&#160;a direct upgrade/update path is supported to this release, whereas this is <strong>the recommended</strong> release now.</p>\r\n<ul>\r\n<li>Upgrading to SAP NetWeaver 7.3 including enhancement package 1 for the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004 (except for dual-stack systems)</li>\r\n</ul>\r\n</ul>\r\n<p>For Java standalone systems: this upgrade path is only supported for pure portal systems</p>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 (with SP 14 or higher) or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver BW 7.3 on SAP HANA DB</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.3 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP NetWeaver 7.3 for the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Process Integration 7.1 or SAP NetWeaver Process Integration 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 - Process Integration, SAP NetWeaver Process Integration 7.1, or SAP NetWeaver Process Integration 7.1 including enhancement package 1, each with installed PI Adapters (SWIFT, BCONS, ELSTER) on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Mobile 7.1 or SAP NetWeaver Mobile 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver Composition Environment 7.1, SAP NetWeaver Composition Environment 7.1 including enhancement package 1 or SAP NetWeaver Composition Environment 7.2 (production edition)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 2004</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0 or SAP NetWeaver 7.0 including enhancement package 1 or 2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2010 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based Java hub systems which have Business Suite 7i2011 applications installed on top (enablement on SAP NetWeaver 7.3)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP Solution Manager 7.1 from the following upgrade paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Solution Manager 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Solution Manager 7.0 EHP1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 6 for SAP ERP 6.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP ERP 6.0 and higher (ABAP components only)</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 2 for SAP SCM 7.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP SCM 7.0 and higher</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP enhancement package 2 for SAP CRM 7.0, version for SAP HANA (based on SAP NetWeaver Application Server ABAP 7.4) from SAP CRM 7.0 (ABAP components only)</li>\r\n</ul>\r\n<ul>\r\n<li>Upgrading to SAP Business Suite 7 Innovation 2011 for the following upgrade paths (SAP Business Suite systems are considered below):</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Basis 4.6C-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Basis 4.6D-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP Web AS ABAP 6.20-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 04-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Upgrading from SAP NetWeaver 7.0-based systems</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2011 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 Innovation 2010 (SAP ERP 6.0 EHP5, SAP CRM 7.0 EHP1, SAP SCM 7.0 EHP1, SAP SRM 7.0 EHP1)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7i2010 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7i2010 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP Business Suite 7 Innovation 2010 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP Business Suite 7 (SAP ERP 6.0 EHP4, SAP CRM 7.0, SAP SCM 7.0, SAP SRM 7.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3 EHP1 Java Hub systems which have Business Suite 7 applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP ERP 6.0 including enhancement package 4 for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP ERP 6.0 including enhancement package 1, 2, or 3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.3 including enhancement package 1 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.3-based Java hub systems which have SAP Business Suite applications installed on top</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Composition Environment 7.2 from the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver Composition Environment 7.1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP NetWeaver Composition Environment 7.1 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Composition Environment 7.1 EHP1 from SAP NetWeaver Composition Environment 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Process Integration 7.1 including enhancement package 1 (enhancement package installation) from SAP NetWeaver Process Integration 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver Mobile 7.1 including enhancement package 1 (enhancement package installation) from SAP NetWeaver Mobile 7.1</li>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.0 including enhancement package 2 (enhancement package installation) for the following update paths:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Updating from SAP NetWeaver 7.0 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Updating to SAP NetWeaver 7.0 including enhancement package 1 from SAP NetWeaver 7.0 (enhancement package installation)</li>\r\n</ul>\r\n<ul>\r\n<li>Applying Support Packages Stacks to the following systems:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.4 (including Business Suite applications on SAP HANA on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 1 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 2 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.0 including enhancement package 3 (including Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.1 and SAP NetWeaver 7.1 including enhancement package 1 (CE, PI, Mobile, banking services from SAP 7.0 and SAP 8.0)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver Composition Environment 7.2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.3 (including systems with PI adapters SWIFT, BCONS, ELSTER on top and Java Hub systems with Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Systems based on SAP NetWeaver 7.3 including enhancement package 1 (including systems with PI adapters SWIFT, BCONS, ELSTER on top and Java Hub systems with Business Suite applications on top)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Solution Manager 7.0 including enhancement package 1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAP Solution Manager 7.1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Installing additional Java usage types / technical usages on top of existing Java systems</li>\r\n</ul>\r\n<ul>\r\n<li>Updating and patching single software components (not supported for ABAP only systems)</li>\r\n</ul>\r\n<p>See the attached document SUM_SP10_paths.pdf for a graphical representation of the supported update and upgrade paths.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>This is the central SAP Note for Software Update Manager 1.0 SP10.</strong><br /><strong>CAUTION:</strong><br />This note is updated regularly!<br />Therefore, you must read it again immediately before starting the tool.<br /><br /><br /><strong>Contents</strong><br />Part A: Update Notes and Keywords<br />Part B: SUM Version, Maintenance Strategy and Documentation<br />Part C: General Problems / Information<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;I/.......Important General Information<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;II/ .... Corrections to the Guide<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;III/ ....Preparing the Update<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;IV/..... Problems During the Update Phases<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;V/ ..... Problems After the Update<br />Part D: Chronological Summary<br /><br /><br /><br /><br /><strong>Part A: Update Notes and Keywords</strong><br /><br />Keyword for Phase KEY_CHK: 1743651<br /><br />For more information about the update of your specific application, see the following Notes:<br /><br />SAP NetWeaver 7.4.......................................1751237<br />SAP Solution Manager 7.1................................1781833<br />SAP NetWeaver 7.3 EHP1..................................1609441<br />SAP NetWeaver 7.3.......................................1390477<br />EHP1 for SAP NetWeaver 7.1..............................1162299<br /><br />ERP on HANA 1.0 - Release Information Note..............1730095<br />CRM on HANA 1.0 - Release Information Note..............1730098<br />SCM on HANA 1.0 - Release Information Note..............1730175<br /><br />SAP ERP 6.0 EHP 7 - Release Information Note..........1737650<br />SAP CRM 7.0 EHP 3 - Release Information Note.........1737725<br />SAP SCM 7.0 EHP 3 - Release Information Note.........1737723<br />SAP SRM 7.0 EHP 3 - Release Information Note.........1818517<br /><br />SAP ERP 6.0 EHP 6:......................................1496212<br />SAP CRM 7.0 EHP 2:......................................1497032<br />SAP SCM 7.0 EHP2:......................................1497083<br />SAP SRM 7.0 EHP 2:......................................1582094<br /><br /><br />In addition, you need the SAP Note for your database or operating system:<br /><br />Operating system.....................................Note number<br />_________________________________________________________________<br />IBM i ......................................................1932447<br /><br />Database ............................................Note number<br />_________________________________________________________________<br />SAP MaxDB ..............................................1878252<br />IBM DB2 for Linux, UNIX and Windows .........1878296<br />IBM DB2 for z/OS .......................................1878395<br />MS SQL Server ..........................................1879293<br />Oracle .....................................................1878198<br />SAP ASE..................................................1878241<br />SAP HANA DB.............................................1878295<br />IBM DB2 for i .............................................1932447<br />_________________________________________________________________<br /><br /><br /><br /><strong>Part B: SUM Version, Maintenance Strategy and Documentation</strong></p>\r\n<p><strong>SUM Version</strong><br />This note applies to <strong>Software Update Manager 1.0 SP10</strong>.<br />Before you start the update, check if there is a newer tool version available on SAP Service Marketplace.<br />SAP recommends that you download the latest tool version as only this version contains the latest corrections and is updated regularly.<br /><br />The Software Update Manager is available on the SAP Service Marketplace at: http://service.sap.com/sltoolset -&gt; Software Logistics Toolset 1.0 -&gt; table with the Software Logistics tools -&gt; Software Update Manager &lt;version&gt; -&gt; Download Link.<br /><br /><strong>Maintenance Strategy</strong><br />The Software Update Manager 1.0 SP10 is currently in <strong>\"patch on request\" </strong><strong>mode</strong>.<br />The maintenance strategy for the Software Update Manager and the other tools delivered with SL Toolset 1.0 SP10 is described in the document attached to the SL Toolset 1.0 SPS10 Release Note 1871178.<br /><br /><strong>Documentation<br /></strong>Before you start using the SUM tool, make sure you have the latest version of the corresponding document, which is available on the SAP Service Marketplace at: http://service.sap.com/sltoolset -&gt; Software Logistics Toolset 1.0 -&gt; Documentation -&gt; System Maintenance -&gt; Updating SAP Systems Using Software Update Manager 1.0 SP10.</p>\r\n<p><br />----------------------------------------------------------------------<br /><strong>Part C: General Problems / Information</strong><br /><br /><strong>I/ Important General Information</strong><br /><br />a) General</p>\r\n<p>--------------------&lt; I036200 DEC/13/2013 &gt;-----------------------------<br /><strong>Solution Manager only: SPS update of ABAP or Java stack independently is supported</strong><br />If you want to update the ABAP or Java stack of a dual-stack Solution Manager system to the latest SPS independently, use SUM for updating the Java stack and transaction SPAM/SAINT for updating the ABAP stack.<br />SUM supports this scenario for the Java stack but requires a special command on startup.<strong> <br /></strong>Detailed information is available in the Release Information Notes for SP Stacks of SAP Solution Manager, which are listed in SAP Note <a target=\"_blank\" href=\"/notes/1595736\">1595736</a>.<br /><br /></p>\r\n<p><br />-----------------------&lt; I36200 JUL/26/2013 &gt;--------------------------<br /><strong>Update of shared Wily AutoProbe connectors not supported</strong><br />Performing updates in a system that uses a Wily AutoProbe connector shared with other systems is not supported. For example, if you have multiple systems on the same host, all systems use the same AutoProbe connectors, and you upgrade one of the systems, starting production operation of the remaining systems might fail.<br /><br /><br />---------------------&lt; D024828 MAY/17/2013 &gt;--------------------------<br /><strong>SAP NetWeaver 7.4: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Note:</p>\r\n<ul>\r\n<li>Note 1730102 - Release Restrictions for SAP NetWeaver 7.4</li>\r\n</ul>\r\n<p>Be aware that upgrade/update to SAP NetWeaver 7.4 is not supported for <strong>dual-stack </strong>NetWeaver systems. The only exception to this rule is SAP NetWeaver Process Integration on a traditional database system, which still requires a dual-stack implementation. For more information about the discontinuation of dual-stack deployments, see SAP Community Network at: <a target=\"_blank\" href=\"http://scn.sap.com/docs/DOC-33703\">http://scn.sap.com/docs/DOC-33703</a>.<br /><br />---------------------&lt; D031178 MAY/17/2013 &gt;--------------------------<br /><strong>SAP Business Suite Powered by SAP HANA: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Note 1774566: SAP Business Suite Powered by SAP HANA - Restrictions</li>\r\n</ul>\r\n<ul>\r\n<li>Note 1830894: Availability of SAP NetWeaver 7.4 on IBM i</li>\r\n</ul>\r\n<p>Be aware that upgrade/update to SAP Business Suite 7 Innovation 2013 and higher is not supported for <strong>dual-stack</strong> SAP Business Suite systems. For more information, see <a target=\"_blank\" href=\"/notes/1816819\">SAP Note 1816819</a>.</p>\r\n<p><br />---------------------&lt; D038245 MAR/25/2013 &gt;--------------------------<br /><strong>SUM requires SAP kernel 7.20 or higher for target release</strong><br />The Software Update Manager requires target release kernel 7.20 or higher for all platforms.<br />We recommend to always check before starting the update or upgrade whether there is a newer version of the SAP kernel available on SAP Service Marketplace and download it to the download directory.<br />Lower target release kernel versions are no no longer supported and may lead to an error during the update or upgrade.<br /><br /><br />-----------------------&lt; D029945 NOV/16/12 &gt;--------------------------<br /><strong>Built-in Capabilities of SUM to include customer transport requests</strong><br />As of version 1.0 SP06, the Software Update Manager 1.0 SP06 is enhanced with a new feature to reduce the downtime by including customer transport requests to the update phase.<br />However, this feature is not generally released yet. We offer interested customers and partners to use this new feature on an 'availability on request' basis.<br />The procedure to follow in order to make use of the inclusion of customer transport requests of the SUM is described in SAP Note 1759080 (Conditions for SUM including customer transport requests).<br /><br /><br />----------------- &lt; D041206 March/26/2013 &gt; -------------------------<br /><strong>Built-in capability near-zero downtime maintenance (nZDM/SUM)</strong><br />The Built-in capability near-zero downtime maintenance (nZDM/SUM) to run the main import almost in the shadow during uptime is generally available without any restrictions since SUM 1.0 SP7. nZDM/SUM is a main feature to optimize the downtime and is available in the advanced pre-configuration of SUM.<br />Further release information about nZDM/SUM is described in the note 1678565.<br /><br /><br />-----------------------&lt; D035496 APR/18/12 &gt;--------------------------<br /><strong>Applying Support Package 02 to Solution Manager 7.1 </strong><br />When applying Support Package stack 02 to SolMan 7.1 with the Software Update Manager, an error occurs for component BI_CONT.<br />If you want to import Support Package queues that contain this Support Package stack, you either use the Support Package Manager (SPAM) or you include at least Support Package stack 05 as well.<br /><br /><br />-----------------------&lt; I053650 DEC/19/11 &gt;--------------------------<br /><strong>Java, ABAP+Java: Check for sapstartsrv Patch Level on the Source System</strong><br />To guarantee that the Java or dual-stack SAP system can be correctly manipulated during the update/upgrade process, the Software Update Manager checks whether the patch level of SAP start service (sapstartsrv) on the source system is appropriate. This check is performed in the beginning of the SUM process so that known problems, related to system detection as well as proper stop and start of the SAP system, will be prevented. For more information, see SAP Note <strong>1656036</strong>.<br /><br /><br />------------------&lt; updated D029128 MAY/31/12 &gt;-----------------------<br />-----------------------&lt; D041112 NOV/22/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.3 EHP1: Release Restrictions and Limitations</strong><br />Before the update/upgrade, you must familiarize with the following SAP Note:<br />Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3 - Note 1522700<br /><br /><br />-----------------------&lt; D038722 OCT/28/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.0 EHP3: Release Restrictions and Limitations</strong><br />Before the update/upgrade, you must familiarize yourself with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Release Restrictions for EHP3 for SAP NetWeaver 7.0 - Note 1557825</li>\r\n<li>Installation of SAP EHP3 for SAP NetWeaver 7.0 - Note 1637366</li>\r\n<li>No Process Integration in SAP EHP3 FOR SAP NETWEAVER 7.0 - Note 1637629</li>\r\n</ul>\r\n<p><br />-----------------------&lt; D025095 APR/13/11 &gt;--------------------------<br /><strong>SAP NetWeaver 7.3: Release Restrictions and Limitations</strong><br />Before the update, you must familiarize with the following important SAP Notes:</p>\r\n<ul>\r\n<li>Release Restrictions for SAP NetWeaver 7.3 - Note 1407532</li>\r\n<li>SAP Business Suite PI Adapters for SAP NetWeaver PI 7.3 - Note 1570738</li>\r\n</ul>\r\n<p><br />----------------------&lt; I031257 SEP/08/09 &gt;---------------------------<br /><strong>JSPM NOT to be Used During the Update Process</strong><br />You must not run the tool Java Support Package Manager (JSPM) when running the Software Update Manager. This is critical for the successful completion of the process.<br /><br /><br />b) ABAP<br /><br />/<br /><br /><br />c) Java</p>\r\n<p>-----------------------&lt; D039661 OCT/28/2013 &gt;--------------------------<br /><strong>Installing SAP Business Suite Usage Types on Top of an Existing SAP NetWeaver Java System</strong><br />This installation scenario is described in the document <em>SAP Solution Manager: Special Cases in Installation and Upgrade</em> available at <a target=\"_blank\" href=\"http://service.sap.com/mopz\">http://service.sap.com/mopz</a>, section <em>How-Tos and Guides</em>. Follow the specific steps described in the chapter <em>Add Installation of SAP Business Suite Usage Types to Existing NW System</em>.</p>\r\n<p><br /><br /><strong>II/ Corrections to the Guide</strong><br /><br />a) General</p>\r\n<p>-------------------------&lt; I069357 JUN/03/2014 &gt;---------------------------</p>\r\n<p><strong>HP-UX and Solaris Operating System Support</strong></p>\r\n<p>The Software Update Manager supports operating in HP-UX- and Solaris-based systems for all database platforms, with the <strong>only exception&#160;for IBM DB2 for z/OS database</strong>. All versions of the guide for UNIX-based systems, except the IBM DB2 for z/OS guides, are also valid for HP-UX and Solaris.</p>\r\n<p>------------------------&lt; I036200 APR/30/2014 &gt;----------------------------<br /><strong>Java /&#160;Dual-Stack Systems: Additional Info for \"Reentering Passwords\" Section<br /></strong>Note that the user credentials of the shadow schema database user utilized during the update process are copied from the standard schema database user&#160;in the&#160;secure store.</p>\r\n<p>------------------------&lt; I036200 APR/2/2014 &gt;----------------------------<br /><strong>Windows Server 2012 (R2) procedure validity<br /></strong>Throughout the guide, all manual procedures valid for Windows Server 2012 are also valid for Windows Server 2012 (R2).</p>\r\n<p>----------------------&lt; I069357 MAR/21/2014 &gt;--------------------------<br /><strong>Navigation paths for Installing or Updating the SAP Host Agent<br /></strong>The navigation paths to the documentation on <a target=\"_blank\" href=\"http://help.sap.com/\">http://help.sap.com</a> <em>-&gt; SAP NetWeaver -&gt; &lt;Release&gt;</em> describing the installation or update of SAP Host Agent are:</p>\r\n<p>For SAP NetWeaver 7.0, 7.0 including EHP 1 or 3 - <em>Application Help</em> -&gt; <em>Function-Oriented View &lt;Language&gt;</em> -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">SAP NetWeaver by Key Capability -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">Solution Life Cycle Management by Key Capability -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">SAP Host Agent -&gt;&#160; </em></em></em>in the<em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"> <em class=\"SAPXDPScreenElement\" title=\"Object name\">Related Information</em> </em></em></em>section<em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\">, </em></em></em>chapters<em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"> <em class=\"SAPXDPScreenElement\" title=\"Object name\">SAP Host Agent Installation</em>&#160;</em></em></em>or<em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"><em class=\"SAPXDPScreenElement\" title=\"Object name\"> <em class=\"SAPXDPScreenElement\" title=\"Object name\">SAP Host Agent Upgrade .</em></em></em></em></p>\r\n<p>For SAP NetWeaver 7.0 including EHP2 - <em>Application Help</em> -&gt; <em>Function-Oriented View &lt;Language&gt;</em> -&gt; <em>S</em><em class=\"SAPXDPScreenElement\" title=\"Object name\">AP NetWeaver by Key Capability -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">Solution Life Cycle Management by Key Capability -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">Solution Monitoring -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">Monitoring in the CCMS -&gt; <em class=\"SAPXDPScreenElement\" title=\"Object name\">SAP Host Agent, </em></em></em></em></em>sections <em>SAP Host Agent Installation</em> or <em>SAP Host Agent Upgrade .</em></p>\r\n<p><br /><br />-----------------------&lt; D037517 Aug/09/2013 &gt;-------------------------<br /><strong>Manual steps for non-central instances after the upgrade (profile merge)</strong><br />As of SAP kernel version 710, SAP systems no longer use separate profiles to configure the sapstart service and the instance started by the sapstart service.<br />The upgrade procedure from SAP systems to version 710 or higher might not convert and reconfigure all installed services to this single profile structure.<br />Therefore see note 1528297 for Windows OS (also valid for ERS and dialog instances) and note 1898687 for Linux/Unix OS.<br /><br /><br />-----------------------&lt; I046284 JUN/07/2012 &gt;-------------------------<br /><strong>User Management Engine on a remote instance host</strong><br />During the upgrade or update of an SAP system that has a User Management Engine(UME) located on a remote instance host, make sure that this instance is operational.<br /><br /><br />b) ABAP<br /><br />-----------------------&lt;&#160;D019500 MAY/22/2014 &gt;-------------------------<br /><strong>Addendum to ABAP Workbench Locking (Phase REPACHK_CLONE)</strong><br />(5.Running the Software Update Manager -&gt; Making Entries for the Preprocessing Roadmap Step -&gt; ABAP Workbench Locking (Phase REPACHK_CLONE))<br />SUM checks in this phase also for inactive development objects. During this check, it can occur that SUM reports inactive objects which can not be found in the SAP system. For information about removing these inconsistencies, see SAP Note 538167.<br /><br /><br />-----------------------&lt; D019500 DEC/17/2013 &gt;-------------------------<br /><strong>Customer implementations to SAP exit function modules are disabled on the shadow instance</strong><br />The list, which is to be noted in the introductory text of section \"5. Running the Software Update Manager\", is enhanced with the following item:<br />Enhancement package installation and SPS Update: All customer implementations to SAP exit function modules within the SAP BASIS will be disabled on the shadow instance at the beginning of the update procedure. They will be re-enabled at the end of the downtime so that they can be used productively again in the updated system.<br />The reason for this is, that SAP exit function modules with access to tables can be implemented within customer enhancement projects (R3TR CMOD). However, this could lead to errors during the update because these tables do not exist in the shadow system.<br /><br /><br /><br />c) Java<br /><br /><br />-----------------------------------------------------------------------<br /><br /><strong>III/ Preparing the Update</strong></p>\r\n<p>a) General</p>\r\n<p>-----------------------&lt; D001330 AUG/18/14 &gt;--------------------------<br /><strong>Deactivate IOT feature for certain tables</strong><br />The SUM tool does not support the (Oracle) IOT feature for certain tables because the SAP dictionary does not officially support the IOT feature. The tables are in DBDIFF exception table for index differences between DB and SAP dictionary. The solution or workaround is to deactivate the IOT feature for such tables before the SUM run. After finishing the SUM run, the IOT feature may be activated again.<br /><br /><br />------------------------&lt;&#160;I044400&#160;APR/9/2014 &gt;---------------------------<br /><strong>Preventing 'osexecute' errors</strong></p>\r\n<p>It is <strong>mandatory</strong> that you&#160;perform&#160;the update process as the &lt;sapsid&gt;adm user.&#160;Switching to another user during the \"Specify Credentials\" roadmap step, even if that user has sufficient rights to operate the process, might cause errors in the 'osexecute' module. If such errors occur, you might not be able to switch back to the &lt;sapsid&gt;adm user to correct them.</p>\r\n<p>Note that&#160;although the \"Accesscheck\" functionality of SAP NetWeaver&#160;states that&#160;other OS users can execute 'osexecute' commands, many 'osexecute' operations are specifically linked to the &lt;sapsid&gt;adm user.</p>\r\n<p><br />-----------------------&lt; D033486 OCT/14/10 &gt;--------------------------<br /><strong>Apply SAP Note 1910464 before the import of certain SPs</strong><br />If your system is on one of the following Support Package levels<br />SAPKB70029<br />SAPKB70114<br />SAPKB70214<br />SAPKB71017<br />SAPKB71112<br />SAPKB73010<br />SAPKB73109<br />SAPKB74004<br />and you want to import the next Support Packages, apply SAP Note 1910464 before the import.<br /><br /><br />----------------&lt; Update&#160;D023890 OCT/13/2014 &gt;-----------------------<br />---------------------&lt; D035061 AUG/22/2012 &gt;--------------------------<br /><strong>No \"Single System\" mode on dual stack systems</strong><br />On dual stack systems, do not use the preconfiguration mode \"Single System\". Use preconfiguration mode \"Standard\" or \"Advanced\" instead.<br />In the case of&#160;a Solution Manager system, see also the&#160;coment &#8220;Solution Manager only: SPS update of ABAP or Java stack independently is supported&#8221; in section \"I/ Important General Information\" -&gt; \"a) General\".<br /><br /><br />---------------------&lt; D035061 AUG/22/2012 &gt;--------------------------<br /><strong>Implement note 1720495 before you start transaction SPAU <strong>or include the related Support Package</strong></strong><br />Make sure to implement SAP note 1720495 immediately after a Support Package import, an enhancement package installation, or an upgrade and before making any adjustments in transaction SPAU. Alternatively, you can include the related Support Package for your target release.<br />Otherwise you can observe after an EHP installation or a Support package import notes with status 'SAP note &lt;no.&gt; obsolete; de-implementation necessary' in transaction SPAU, when adjusting the note, and in the Snote. If you proceed in transaction SPAU and adjust this kind of notes, the de-implementation of the note can destroy the code of the support package and can make the system inconsistent.<br />The SAP note 1720495 corrects the status of the notes and avoids wrong deimplementation of obsolete notes.<br /><br /><br />---------------------&lt; D035061 JUL/23/2012 &gt;--------------------------<br /><strong>Check platform-specific requirements for the 7.20 EXT kernel</strong><br />In case you want to install the 7.20 EXT kernel, or you want to change to this kernel, check beforhand the platform-specific requirements.<br />See SAP note 1553301 for the 7.20 EXT kernel requirements, especially section \"Platform-Specific Information\".<br />If the requirements are not met, errors might occur in phase TOLVERSION_EXTRACT.<br /><br /><br />---------------------&lt; D035061 JUL/23/2012 &gt;--------------------------<br /><strong>Server Group SAP_DEFAULT_BTC must include Primary Application Server Instance</strong><br />If you have defined a batch server group SAP_DEFAULT_BTC according to SAP Note 786412, make sure that the primary application server instance is included in this server group.<br /><br /><br />-----------------------&lt; D029385 MAR/08/11 &gt;--------------------------<br /><strong>Upgrade on Windows only: Check registration of sapevents.dll</strong><br />During the installation of your source release system, the installation procedure might have inadvertently registered the sapevents.dll from the central DIR_CT_RUN directory instead of from the local DIR_EXECUTABLE directories of the instance. This may lead to errors with locked kernel files during or after the upgrade. Therefore check the registration of sapevents.dll as described in SAP Note 1556113.<br /><br /><br />-----------------------&lt; D037517 08/DEC/10 &gt;--------------------------<br /><strong>Adjust Start Profile</strong><br />Before you start the update, add the SAPSYSTEM parameter to the start profile of your system. The entry should look like:<br />SAPSYSTEM = &lt;instance number&gt;<br /><br /><br />----------------------&lt; D053561 20/OCT/10 &gt;---------------------------<br /><strong>Apply SAP Note to Avoid Error During Reset</strong><br />We recommend to apply SAP Note 1518145 before starting the Software Update Manager (if it is not yet in your system as part of the corresponding Support Package). This note prevents an error that can occur when you reset the update procedure during the Preprocessing roadmap step. Without this note, some tables that need to be deleted during the reset cannot be deleted.<br /><br />--------------------&lt; D033899 31/MAY/10 &gt;-----------------------------<br /><strong>Preventing Errors with Table \"SATC_MD_STEP\"</strong><br />To avoid problems during the update due to conflicting unique indexes, apply the correction described in SAP Note <strong>1463168</strong> or apply the corresponding Support Package.<br /><br /><br /><br />b) ABAP<br /><br />-----------------------&lt; D035956 JUL/03/2014 &gt;--------------------------<br /><strong>SFW activation: Prevent automatic activation of BC-Sets in all clients<br /></strong>In particular if table SFWPARAM contains an entry: NAME = 'SBCSETS_ACTIVATE_IN_CLIENTS' and VALUE = 'X', follow the steps described in SAP Note 2035728.<br /><br /><br />-----------------------&lt; D023536 MAR/11/14 &gt;-------------------------<br /><strong>SAP_FIN 700: Do not use the Single System Mode</strong><br />If you are installing the component SAP_FIN 700, do not use the Single System Mode. Otherwise SUM stops with errors in phase MVNTAB_TRANS due to missing fields.<br /><br /><br />-----------------------&lt; D024828 FEB/28/2014 &gt;--------------------------<br /><strong>Consider SAP Note 1983758 while preparing the update</strong><br />When you prepare the update/upgrade, consider SAP Note 1983758 to make sure that BW clients are not set wrongly by a previous update. Otherwise BW-specific error messages might occur during the follow-up activities.<br /><br /><br />-----------------------&lt; D037517 DEC/13/2013 &gt;--------------------------<br /><strong>Exclude Z languages</strong><strong><br /></strong>If you have installed your own languages (Z languages ) in the SAP system, you must exclude them from the upgrade or update process.<br />For this, you maintain the Z languages in the SAP system before the upgrade or update as follows:</p>\r\n<ol>\r\n<li>Logon to the SAP system</li>\r\n<li>Choose transaction SE16 and open table T002C</li>\r\n<li>Choose all Z language rows (max. Z1 &#8211; Z9) for editing</li>\r\n<li>Remove the &#8220;x&#8221; (if exists) from field &#8220;can be installed&#8221; (fieldname LAINST</li>\r\n</ol>\r\n<p><br /><br />--------------------&lt; D035061 FEB/12/2013 &gt;---------------------------<br /><strong>Remove usages of customer-developed objects before you start SUM</strong><br />The Software Update Manager applies the following security notes during the Extraction roadmap step by overwriting or deleting the related objects without further notice:<br />1668465, 1631124, 1631072, 1628606, 1584549, 1584548, 1555144, 1526853, 1514066, 1453164<br />If you have not installed those notes yet, check if any customer-developed objects make use of deleted or disabled objects and remove those usages before you start the Software Update Manager.<br /><br /><br />----------------&lt; Update d023536 15/JUL/14 &gt;--------------------------<br />--------------------&lt; D053561 13/DEC/12 &gt;-----------------------------<br /><strong>See SAP Note 1413569 for table SMSCMAID<br /></strong>Before you start the Software Update Manager, check table SMSCMAID for duplicate records concerning field SCHEDULERID. Otherwise the upgrade might fail during downtime in phase PARCONV_UPG because a unique index cannot be created due to duplicate records in the table. See SAP Note 1413569 for more details.<br /><br /><br />--------------------&lt; D040050 19/SEP/12&gt;-----------------------------<br /><strong>NTsystems: Install latest sapstartsrv before the update</strong><br />If you use an NT-system with the &lt;SID&gt;ADM as domain user, make sure that you apply the SAP Note 1756703 before you start the update. Otherwise, if you enter during the update in phase PREP_PRE_CHECK/PROFREAD the &lt;SID&gt;ADM name and password as domain user, the phase PREP_INPUT/INSTANCELIST_PRE will not accept the &lt;SID&gt;ADM password, and the SUM stops.<br /><br /><br />----------------------- &lt;D020214 30/MAY/11 &gt;--------------------------<br /><strong>Preventing Errors Due to Unicode Length</strong><br />A structure exists that uses a table type with a structure in at least<br />one component. When You call DDIF_FIELDINFO_GET, the system sometimes automatically selects the system Unicode length instead of the UCLEN<br />Unicode length that is specified in DDIF_FIELDINFO_GET. This can lead to an RFC error in phase PREP_INIT/SPAM_CHK_INI during the upgrade or update. To prevent this error, see SAP Note 1029444 and implement the attached correction instruction.<br /><br /><br />----------------------- &lt;D003550 15/DEC/08 &gt;--------------------------<br /><strong>Preventing Activation Errors</strong><br />Under certain circumstances, information of runtime objects might get lost during the activation. This can lead to unwanted conversions of cluster tables. To prevent this error, see SAP Note <strong>1283197</strong> and implement the attached correction instruction.<br /><br /><br />c) Java<br /><br />----------------------&lt;I077286 29/APR/2013&gt;---------------------------<br /><strong>Setting the instance profile parameter AutoStart to '0'</strong><br />Before an upgrade, in each instance profile you must manually set the vaule of the AutoStart parameter to 0. This must be done for each application server instance. Proceed as follows:</p>\r\n<ol>1. Log on to the instance.</ol><ol>2. Navigate to the &lt;drive&gt;:\\user\\sap\\&lt;SID&gt;\\SYS\\profile\\ folder.</ol><ol>3. Edit the &lt;SID&gt;_&lt;Instance Name&gt;_&lt;host name&gt; profile, and change the value of the AutoStart parameter to 0, that is,</ol>\r\n<p><strong>AutoStart = 0 </strong>.</p>\r\n<p>This must be done to prevent unexpected start of instances, which might lead to connection errors.<br /><br />----------------------&lt; I030727 21/SEP/2012 &gt;------------------------<br /><strong>Upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 Based PowerPC System on Linux: Update Your Instance Profile</strong><br />Before starting an upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 based PowerPC system on Linux, you need to add the following parameter to your instance profile:<br /><br />jstartup/native_stack_size = 2097152<br /><br />----------------------&lt; I035760 30/JAN/09 &gt;----------------------------<br /><strong>Cleaning Up the Profile Directory</strong><br />Before starting the Software Update Manager, you need to clean up the profile directory. Remove any old, unused profiles and move any backup copies to another directory. The profile directory must only contain active profile files. By default, it is located in the central file share:<br />For UNIX: /sapmnt/&lt;SAPSID&gt;<br />For Windows: &lt;Drive&gt;:\\usr\\sap\\&lt;SAPSID&gt;\\SYS<br />For IBM i: /sapmnt/&lt;SID&gt;<br /><br /><br /><br />-----------------------------------------------------------------------<br /><br /><strong>IV/ Problems During the Update Roadmap Steps</strong><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under very specific circumstances.<br /><br />a) General<br /><br /></p>\r\n<p>--------------------&lt; D050889 05/JUN/2014 &gt;------------------------------<br /><strong>DB connect problem with SUM for systems on ORACLE or SAP ASE database<br /></strong>Symptom: A source system with SAP Kernel 7.41 and an individual key mode for <em>Secure Storage in File System</em> (SSFS) leads to DB connect issues in different SUM phases (READCVERS_DUMP, INITPUT_PRE, SQLDB*, SQLSELEXE*).<br />Reason: The SUM tool applies a DBSL version that is not ready for the new SSFS key mode.<br />Solution: To continue the update, you need to reset the SSFS key mode to default. Enter the following commands in the command line tool:</p>\r\n<p>rsecssfx pf=&lt;INSTANCE_PROFILE&gt; changekey 000000000000000000000000000000000000000000000000<br />mv DIR_GLOBAL/security/rsecssfs/key/SSFS_K41.KEY DIR_GLOBAL/security/rsecssfs/key/SSFS_K41.KEY.SAV</p>\r\n<p><br /><br />--------------------&lt; D037517 21/MAR/2014 &gt;------------------------------<br /><strong>Reset not possible during update of dual-stack systems</strong><br />If you perform an update or upgrade with DMO on dual-stack systems, it may occur that the option to reset the update/upgrade in the SUM is not possible.<br />If you need to reset the update or upgrade on a dual-stack systems, use the following work-around:</p>\r\n<ol>\r\n<li>Stop all SUM processes (SDT, SAPJup and SAPup)</li>\r\n<li>Change to directory ../SUM/abap/bin</li>\r\n<li>Call \"SAPup reset prepare\"</li>\r\n<li>Delete the SUM directory</li>\r\n<li>Re-extract the SUM archive</li>\r\n<li>Start from scratch</li>\r\n</ol>\r\n<p><br /><br /></p>\r\n<p>---------------------&lt; I024101 MAR/17/2014 &gt;---------------<br /><strong>Correcting connectivity issues when sapstartsrv operates over HTTPS<br /></strong>If sapstartsrv is configured to operate by using the HTTPS communications protocol, during the downtime phase SUM might fail. In such cases, in the log file of the current step the following entry might exist:<br />\"The sapcontrol service at $(0) is not&#160;started.\"<br />The issue is caused by SUM not being able to connect to sapstartsrv by using its web service. To correct the issue, see SAP Note <strong>1991911.<br /></strong><br /><br />---------------------&lt;&#160;D022256 FEB/28/2014 &gt;-----------------------------<br /><strong>Windows only: SUM Process Report displays incorrect OS version<br /></strong>During or after the update process, the&#160;SUM Process Report may not display the correct OS version. For example, you are running&#160;an update&#160;on Windows Server 2012 R2, version 6.3 but the Process Report mentions version 6.2 instead. Be aware that this has no negative effect on&#160;the SUM process.</p>\r\n<p>---------------------&lt; I042050 FEB/11/2014 &gt;-----------------------------<br /><strong>Windows only: Correcting Issues in the \"Delete Old Java Content\" Step</strong></p>\r\n<p>During the removal of redundant update process data, SUM might stop with an error stating that a folder under the &lt;DRIVE&gt;:\\usr\\sap\\&lt;SID&gt;\\&lt;Instance_name&gt;\\j2ee\\ path cannot be deleted, for example the \"admin\" folder. This issue might occur when a Windows process named \"conime.exe\" has a handle on the folder, thus preventing it from deletion.</p>\r\n<p>To correct this issue, first you have to end the \"conime.exe\" process (for example, by using the Windows Task Manager) and&#160;then repeat the failed SUM step.<br /><br /><br /></p>\r\n<p>-----------------------&lt; I069357 NOV/11/2013 &gt;--------------------------<br /><strong>Correcting Issues with Resetting the Upgrade</strong><br />After you have reset the upgrade and a new SUM run is started, after using the \"Cleanup and start afresh\" option the buttons \"Next\" and Back\" on the SUM GUI might be inactive. Alternatively, SUM may crash after the reset procedure when selecting 'Exit' or 'Cleanup and start afresh'\".<br />In this case, proceed as follows:</p>\r\n<ul>\r\n<li>For the ABAP part of the upgrade: Check in the log-file SAPup.log whether the phase SAVELOGS_RESET is mentioned in the end, either as started or skipped.</li>\r\n<li>For the Java part of the upgrade: Check in the Java tab of the SUM GUI whether all steps have been completed.</li>\r\n<li>Afterwards extract the SUM archive again to a new directory and start the new SUM run.</li>\r\n</ul>\r\n<p>The above solution is valid when you reset the update by repeatedly choosing the \"Back\" button on the SUM GUI, or if you choose the Update -&gt; Reset Update option.</p>\r\n<p>&#160;</p>\r\n<p>------------------&lt; I077286 23/OCT/2013 &gt;----------------------------<br /><strong>Shadow instance reset during an upgrade</strong><br /><strong>Linux- or Windows-based systems only:</strong> If during a switch upgrade you reset the SUM procedure after the SET_ENGINE_SAFE_MODE step has passed, afterwards SUM might stop at step \"DELETE_SHADOW_DIR\" with the following message:<br /><br />Error while executing Task with input file FsoTasks.xml and task<br />DELETE_SHADOW_DIR. Could not finish Delete operation with parameters<br />file '', directory '&lt;drive:&gt;/usr/sap/&lt;SID&gt;/SUM/sdt/&lt;SID&gt;'. Check whether the<br />current user has sufficient permission to execute this operation. Cannot<br />delete file &lt;drive:&gt;\\usr\\sap\\&lt;SID&gt;\\SUM\\sdt\\&lt;SID&gt;. Cannot delete<br />&lt;drive:&gt;\\usr\\sap\\&lt;SID&gt;\\SUM\\sdt\\&lt;SID&gt; using Windows console operation. 0<br /><br />To correct this issue, proceed as follows:</p>\r\n<ol>1. Stop the Software Update Manager back-end process. To do this, in the GUI menu bar choose Update -&gt; Stop Update. If required, shut down the SUM GUI as well.</ol><ol>2. Start the Software Update Manager back-end process and GUI again.</ol>\r\n<p><strong>NOTE:</strong> After you have started the SUM GUI, <strong>do not</strong> choose Next.</p>\r\n<ol>3. Reset the SUM update. To do this, in the SUM GUI menu bar choose Update -&gt; Reset Update.</ol><ol>4. Wait until the dialog for repeating a failed step appears again.</ol><ol>5. Repeat the step from point of failure.</ol>\r\n<p><br /><br />---------------------&lt; D026178 AUG/22/2013 &gt;--------------------------<br /><strong>Phase XPRAS_UPG: Errors during post-handling \"RS_AFTER_IMPORT\"</strong><br />In phase XPRAS_UPG, the following error can occur:<br />2EEPU133 Errors occurred during post-handling \"RS_AFTER_IMPORT\" for \"WWIB\" \"L\"<br />This after-import method can fail if only SAP_BASIS 740 with SP03 or lower is imported.<br />To continue the upgrade, see SAP Note 1839664 in which a workaround is described.<br />To prevent this issue before starting the upgrade, see SAP Note 1894463.<br /><br />The above procedure is required as temporary data for operating the shadow instance is loclocked and cannot be removed when the SUM procedure needs to do so.<br /><br /><br />---------------------&lt;I067708 23/MAY/2013&gt;---------------------------<br /><strong>Correcting issues with primary application server instance host name connectivity</strong><br />During SUM startup, in the console the following error might appear:<br />'Unable to detect an abap and/or AS java instance of the SAP system &lt;SID&gt; installed on host &lt;host name&gt;' .<br />To correct this error, restart SUM and after the startup command enter the command 'hostname=&lt;host name&gt;', where &lt;host name&gt; is the primary application server instance host. For example:<br />'STARTUP.BAT hostname=wdmlbmd5799' .<br />The error might appear when the host where the instance profile directory is located is not directly accessible due to, for example, a specific customer setup.<br /><br /><br />-----------------------&lt; D035061 05/FEB/13 &gt;--------------------------<br /><strong>Wrong MCOD warning for dual stack systems</strong><br />In case your system is a dual stack system with an independent schema name for the JAVA part, this schema might be detected as MCOD setup. In consequence of this, a number of warnings and tasks might be recommended by the tool and the guides, that apply to real MCOD systems.<br />If your dual stack system is the only system on the database, you can ignore related warnings and skip any MCOD related tasks.<br /><br /><br />-----------------------&lt; D038245 10/NOV/11 &gt;--------------------------<br /><strong>UNIX: Prevent Overwriting sapuxuserchk During the Update/Upgrade</strong><br />In case sapuxuserchk inside instance executable directories gets overwritten during the update/upgrade process, the startup of those instances might fail due to failing authentication for sapcontrol calls executed by SUM.<br />It must be prevented that the set-user-ID bit for sapuxuserchk inside the instance executable directories is overwritten during kernel switch. To do this, apply the solution described in SAP Note <strong>1650797</strong> after the Checks roadmap step and before downtime start.<br /><br /><br />-----------------------&lt; D021970 01/FEB/12 &gt;--------------------------<br /><strong>Dual-Stack System Update: Phase STARTSAP_PUPG Fails</strong><br />In special cases during an update (for example, if you perform manual actions after SAP kernel switch), Java shared memory conflicts might be reported in phase STARTSAP_PUPG.<br />To solve the problem, you need to run the \"cleanipc all\" command and then repeat the failing phase.<br /><br /><br /><br />b) ABAP</p>\r\n<p>---------------------&lt; D023536 APR/11/2014 &gt;--------------------------<br /><strong>P messages in LONGPOST.LOG due to BW object activation</strong><br />It may occur that the file LONGPOST.LOG contains P error messages concerning the activation of BW objects, such as:</p>\r\n<p>2PETK754 Error when creating object directory entry \"R3TR\" \"TABL\" \"/BI0/STCAIFAREA\" <br />A2PERSTCO_UT 003 An error occurred while activating BW object &amp;2\"0TCAIFAREA\" (&lt;(&gt;&amp;&lt;)&gt;1\"IOBJ\")&amp;3\"OM\"</p>\r\n<p>SUM has added these message in phase XPRAS_AIMMRG during the execution of report RS_TCO_ACTIVATION_XPRA. This report installs basic technical content BW objects, but their activation generates in some cases an error. Check the log file to see what BW objects cause problems during the activation. You can execute the report again manually after the upgrade phase. If there are still problems with some objects, try to activate the affected objects using transaction RSOR.<br />You can also skip the execution of report RS_TCO_ACTIVATION_XPRA during the upgrade. In this case, make sure that you execute this report manually after the upgrade phase. See SAP Note 1629923 for more information.<br /><br /><br /></p>\r\n<p>---------------------&lt; D024828 MAR/04/2014 &gt;--------------------------<br /><strong>Updating to SAP Business Suite 7 Innovation 2013 SP3: Endless loop in MAIN_SHDIMP/SUBMOD_SHD2_RUN/RUN_RSGEN</strong><br />In certain cases, the&#160;phase MAIN_SHDIMP/SUBMOD_SHD2_RUN/RUN_RSGEN might loop endlessly. You could identify such a case by inspecting the (temporary) log file &lt;SUM-Directory&gt;/abap/tmp/RSGEN.&lt;SID&gt;.&#160;At the end of this log file there could&#160;be a lot of similar entries such as</p>\r\n<p>A4 ETSGEN 312 Job \"RSPARAGENER8M\"(\"xxxxxxxx\") for machine type \"xxx\" still has \"nn\" object(s) to process&#8221; where nn stays constant over time</p>\r\n<p>If you encounter such a case during your update procedure, report an incident on the component BC-ABA-LA.<br /><br /><br />---------------------&lt; D024828 FEB/28/2014 &gt;--------------------------<br /><strong>Runtime error INSERT_PROGRAM_NAME_BLANK in phase XPRAS_AIMMRG</strong><br />If the update or upgrade stops in phase XPRAS_AIMMRG with the runtime error INSERT_PROGRAM_NAME_BLANK, see SAP Note 1941711 for more information and further instructions.<br /><br /><br />---------------------&lt; D020904 FEB/03/2014 &gt;--------------------------<br /><strong>(Windows only:) Error \"Access denied\" in phase MAIN_UPTRANS/UPCONF</strong><br />During an update or upgrade, the process may stop with the following error message:<br />Severe error(s) occured in phase MAIN_UPTRANS/UPCONF!<br />Last error code set: Cannot kill process 59720: Access is denied.<br />The error may disappear after repeating the phase. If the error occurs again, see SAP Note 1973135 for more information.<br /><br /><br />---------------------&lt; D041506 JAN/14/2014 &gt;--------------------------<br /><strong>DYNPRO_NOT_FOUND during parallel DBCLONE processes<br /></strong>In phase MAIN_SHDCRE/SUBMOD_SHDDBCLONE/DBCLONE, several DBCLONE processes have been started and run in parallel, but some of them stop with ST22 dump DYNPRO_NOT_FOUND. For more information and a solution, see SAP Note 1964350.<br /><br /><br />---------------------&lt; D024828 DEC/16/2013 &gt;--------------------------<br /><strong>Solaris X86_64: Timeout Error during phase MAIN_NEWBAS/STOPSAP_FINAL</strong><br />You use a Solaris X86_64 system and encounter a timeout error during phase MAIN_NEWBAS/STOPSAP_FINAL. A repeat of the phase will solve the error.<br /><br /><br />---------------------&lt; D023536 JUL/16/2013 &gt;--------------------------<br /><strong>Phase ACT_UPG: Certain search helps could not be activated</strong><br />During an update or upgrade, the Software Update Manager displays in phase ACT_UPG error messages such as<br /><br />1EEDO519 \"Srch Help\" \"H_5ITCN\" could not be activated&#8221; or<br />1EEDO519 \"Srch Help\" \"H_5ITTT\" could not be activated&#8221;</p>\r\n<p>You can ignore these errors. See SAP Note 1243014 for more information</p>\r\n<p><br />-----------------------&lt; D035061 OCT/14/10 &gt;--------------------------<br /><strong>Error in Phase MAIN_NEWBAS/SUCCCHK_PROD</strong><br />Symptom:<br />You read in the log-file PHASES.LOG the following line:<br />2EETQ399 Change request 'SAPK-&lt;vers&gt;INCTSPLGN not removed from buffer<br />This message indicates, that the import of a transport request of the CTS Plug-In could not performed.<br /><br />Reason known to date:<br />The system could not create primary indices for tables, if an object with the same name already exists. To solve this issue, drop the tables CL_CTS_REQUEST_REMOTE&#126;&#126;OLD and CTS_REQ_OVERLAPS&#126;&#126;OLD directly in the database.<br /><br />Solution:<br />1. Find out the cause of the import error and resolve it.<br />2. Start the report /CTSPLUG/CTS_ACTIVATION and set the parameter FORCEACT='X'. This report starts the import again.<br />3. Afterwards, check whether the import was successful and the import queue is empty now. If the problem continues to exist, contact the SAP Support.<br />4. If the report has run successfully, repeat the phase<br />MAIN_NEWBAS/SUCCCHK_PROD in the Software Update Manager to continue the upgrade<br /><br /><br />---------------------&lt; D023536 JUL/16/2013 &gt;--------------------------<br /><strong>Phase ACT_UPG: Certain tables cannot be activated</strong><br />During an update or upgrade, the Software Update Manager displays in phase ACT_UPG the error message that the following tables cannot be activated :</p>\r\n<ul>\r\n<li>/SOMO/MA_S_KEYFIG and</li>\r\n</ul>\r\n<ul>\r\n<li>/SOMO/MA_S_MONOBJ</li>\r\n</ul>\r\n<p>You can ignore this error by using the option \"Accept non-severe errors\".<br /><br /><br /></p>\r\n<p>---------------------&lt; D035061 MAY/15/2013 &gt;--------------------------<br /><strong>Errore in phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS</strong><br />During an update or upgrade, the SUM can stop during the phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS with an error message using the following model:<br />Object '&amp;' language '&amp;' not created<br />In this case, you need to apply SAP Note 1866886. Implement manually the correction which corresponds to your target release. Afterwards repeat the affected phase in the SUM.<br />To avoid the error proactively, select a target Support Package level that covers that note.<br /><br /><br />---------------------&lt; d001330 MAY/13/2013 &gt;--------------------------<br /><strong>Phase XPRAS_UPG: Problems in FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C</strong><br />If you encounter a problem during the upgrade in phase XPRAS_UPG in method FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C that requires a code correction, see SAP Note 1357207 for further information and consider the solution in the note.<br /><br /><br />-----------------------&lt; D051861 APR/24/13 &gt;--------------------------<br /><strong>View EPIC_V_BRS_BSEG not activated</strong><br />The activation of view EPIC_V_BRS_BSEG fails during an upgade or update. In the log file of the ABAP Dictionary activation, you see the following message: View \"EPIC_V_BRS_BSEG\" was not activated<br />To solve this issue, see SAP Note 1846998.<br /><br /><br />----------------------&lt; D035061 25/MAR/13 &gt;--------------------------<br /><strong>RFC_COMMUNICATION_FAILURE during phase CHECKSYSSTATUS</strong><br />When additional instances are installed but currently stopped, the phase CHECKSYSSTATUS stops with an RFC_COMMUNICATION_FAILURE error regarding these instances.<br />In this case, start all installed instances during this phase so that their status can be checked. After the phase has passed successfully, you can stop the installed instances again if required. Alternatively, the services of the stopped instances need to be stopped as well, followed by a reset of SUM and start from scratch.<br /><br /><br />-----------------------&lt; D053561 OCT/10/10 &gt;--------------------------<br /><strong>tp 212 error in MAIN_NEWBAS/TABIM_UPG: left-over semaphore</strong><br />The SUM stops during phase TABIM_UPG in module MAIN_NEWBAS with the following error message:<br /><br />Calling &lt;path to&gt; tp.exe failed with return code 212, check &lt;path to&gt; SAPup.ECO for details.<br /><br />This stop can occur when tp does not finish correctly due to one or more semaphores that haven't been removed correctly after the end of r3trans. In general, it can happen in each phase where tp is started that semaphores are<br />left over. Note that the semaphores have to be kept if the tp or R3trans process of them still exists.<br /><br />If you encounter the error, use the operating system first to check whether the corresponding tp is still active, for example, whether the process still exists. For more information about semaphores in tp and how to solve the problem, see SAP Note 12746.<br /><br /><br />---------------------&lt; I065580 11/SEP/12 &gt;---------------------------<br /><strong>MAIN_NEWBAS/STARTSAP_TBUPG:System start failed on NT IA64</strong><br />During the MAIN_NEWBAS/STARTSAP_TBUPG phase the upgrade stops with the error message:<br />Checks after phase MAIN_NEWBAS/STARTSAP_TBUPG were negative! (...) System start failed<br />A possible solution can be note 1696517. To prevent the error, select a target kernel version that includes the related correction.<br /><br /><br />---------------------&lt; D035061 13/AUG/12 &gt;---------------------------<br /><strong>Error while sapcpe copies vcredist_x64.msi</strong><br />While using the Software Update Manager for updates or upgrades from systems based on NetWeaver 7.1, errors might occur in phase MOD_INSTNR_POST when sapcpe tries to copy the file vcredist_x64.msi.<br />In this case, enable the write access on this file and repeat the phase.<br /><br /><br />---------------------&lt; D028310 05/JUL/11 &gt;---------------------------<br /><strong>Missing tables during DB02 check after reset of update</strong><br />You have reset an update and before you restart this update, you run a DB02 check (Missing Tables and Indexes). This check comes to the result that two tables (CLU4 and VER_CLUSTR) are missing in the database.<br />You can ignore these missing tables and continue with the upgrade. The nametabs, which caused this DB02 check result, will be deleted after the update has been finished.<br /><br /><br />-------------------- &lt; D037517 18/MAY/11 &gt; -------------------------<br /><strong>Phase STARTSAP_PUPG: System start of the dialog instances failed</strong><br />Checks after phase MAIN_NEWBAS/STARTSAP_PUPG were negative! Last error code set: Unknown dialogue variable 'INSTLIST' System start failed.<br />Solution: Install the latest version of the Visual C++ Runtime on the host for the dialogue instances. Repeat the upgrade step afterwards.<br /><br /><br />--------------------- &lt; D035061 17/MAY/11 &gt; -------------------------<br /><strong>Phase ACT_UPG phase during EHP installation</strong><br />In this phase, either the following short dump can occur:<br />Runtime Errors.........TSV_TNEW_PAGE_ALLOC_FAILED<br />or this phase can take unusually long time.<br />In this case refer to note 1387739 and follow the instructions there.<br /><br /><br />--------------------&lt; D031901 22/OCT/10 &gt;-----------------------------<br /><strong>Preprocessing:ERROR: Found pattern \"R3load:...</strong><br />You have reset the update in the Preprocessing roadmap step and now, when running the Software Update Manager again, you get the following error message:<br />\"ERROR: Found pattern \"R3load: job completed\" 0 times, but expected 1! Analyze the log file for further error messages or program abort.\"<br />This error occurs if you did not apply SAP Note 1518145 before the reset. To solve the problem, repeat the phase in which the error occured.<br /><br /><br />------------------------&lt; D030559 07/JUN/10 &gt;--------------------------<br /><strong>Preventing long runtime of SUSR_AFTER_IMP_PROFILE</strong><br />During the update, the after-import method SUSR_AFTER_IMP_PROFILE can have a long runtime, and there may be a longer system downtime as a result.<br />For information about how to prevent long runtimes of this method, see SAP Note 821496.<br /><br /><br />----------------------&lt; D035061 12/MAY/09 &gt;---------------------------<br /><strong>Resetting the Update When Using the Incremental Table Conversion</strong><br />If you use the incremental table conversion (ICNV) for a table which has the character '_' (underscore) at the sixth position and you need to reset the Software Update Manager using \"Reset Update\", check if there is an index QCM1&lt;abcde&gt;_QCM left on the database after the reset. &lt;abcde&gt; stands for the first 5 characters of the table.<br />You need to drop that index manually using database means before you restart the update. For more information, see SAP Note 1337378.<br /><br /><br />--------------------&lt; D028597 29/APR/08 &gt;----------------------------<br /><strong>Modification Adjustment with SPDD</strong><br />If you adjust modifications during the enhancement package installation using transaction SPDD and mark the change request with the \"Select for transport\" function, you are asked whether the modification adjustment is performed for an Upgrade or for a Support Package update. Choose \"Upgrade\".<br /><br /><br />-----------------&lt; updated D023536 14/OCT/09 &gt;-------------------------<br />----------------------&lt; D023536 19/SEP/08 &gt;----------------------------<br /><strong>Activation Error</strong><br />An activation error for domains can occur if your source release system has a Support Package level of SAP NetWeaver 7.1 SP3, SP4, SP5 or SP6. The issue can be solved by repeating the failed phase. For more information, see SAP Note 1242867. If you have already implemented SAP Note 1242867 in your system, the error does not occur.<br /><br />-------------------- &lt; C5003135 26/AUG/08 &gt; ---------------------------<br /><strong>Phase MAIN_SHDIMP/SHDUNINST_DB</strong><br />Description: In this phase, the following error might occur<br />ERROR: Error by drop user SAP&lt;SID&gt;SHD<br />In the detailed log files, you may find the error, that the user is still connected to the database. For example, for SAP MaxDB, the error message in the file XCMDOUT.LOG is as follows:<br />-7048,DROP/ALTER USER not allowed while user is connected<br />Solution: Wait for about 15 minutes and then repeat the phase. You can also repeat the phase, if you cannot clearly identify in the detailed log whether the above mentioned error occured, since repeating the phase is harmless. You can also repeat the phase several times.<br /><br /><br />c) Java</p>\r\n<p>-------------------------&lt; I044400 25/MAY/14 &gt;---------------------------</p>\r\n<p><strong>HP-UX systems only: Correcting JVM core dump errors</strong></p>\r\n<p>During the update, SUM might stop with a JVM core dump error similar to the following:</p>\r\n<p># A fatal error has been detected by SAP Java Virtual Machine:</p>\r\n<p>#&#160; SIGILL (0x4) at pc=0x1fffffffec0989b1, pid=24369, tid=290806</p>\r\n<p># JRE version: 6.0_31</p>\r\n<p># Java VM: SAP Java Server VM (6.1.038 19.1-b02 &lt;Date and time&gt;)</p>\r\n<p>[1]&#160;&#160; Segmentation fault&#160;&#160; ./STARTUP (core dumped)</p>\r\n<p>To correct such errors, you have to update the SAPJVM utilized by SUM to at least SAPJVM 6 patch 41. Proceed as follows:</p>\r\n<ol>\r\n<li>Go to <a target=\"_blank\" href=\"http://service.sap.com/support\">http://service.sap.com/support</a>&#160;-&gt; Software Downloads -&gt; Search for Software Downloads.</li>\r\n<li>In the Search Term field, type \"SAPJVM6\" and&#160;start your search.</li>\r\n<li>Select and download&#160;the version relevant for your operating system.</li>\r\n<li>Extract the archive and place it&#160;in the&#160;/&lt;SUM directory&gt;/jvm folder.</li>\r\n<li>Repeat the failed step of the procedure.</li>\r\n</ol>\r\n<p>-------------------------&lt; I024107 27/FEB/14&#160;&gt;-------------------------</p>\r\n<p><strong>SAP NetWeaver 7.3 and higher only: Preventing&#160;refusal issues caused by column drop of NZDM_VALUE2</strong></p>\r\n<p>While updating your SAP system,&#160;an error similar to the following might appear:</p>\r\n<p>&#160;E R R O R ******* (DbDeployConfig dev &lt;Date&gt; &lt;Time&gt;)</p>\r\n<p>&lt;Time&gt; &lt;Date&gt; dbs-Error:&#160; Refuse due to drop column NZDM_VALUE2</p>\r\n<p>The issue is caused by an additional column added facilitate the near-Zero Downtime Maintenance Java tool. To correct this issue, proceed as described in SAP Note <strong>1978632</strong>.</p>\r\n<p><br /><br />-----------------------&lt; I071217 28/MAR/13 &gt;--------------------------<br /><strong>Preventing Issues with Wily AutoProbe connectors</strong><br />The Software Update Manager may not be able to rebuild the Wily AutoProbe connectors of the Wily Introscope Agent. To prevent this, ensure the following:</p>\r\n<ol>1. The &lt;SID&gt;adm user executing the update process must have full administrator rights for the directories containing a Wily AutoProbe connector</ol><ol><ol><ol>2. Verify that the name of each used Wily AutoProbe connector jar file is</ol></ol></ol>\r\n<p><strong>connector.jar</strong></p>\r\n<ol><ol>. If the name of a connector is AutoProbeConnector.jar, you&#160;have to&#160;rename it to connector.jar.</ol></ol>\r\n<p>----------------------&lt; I036707 JUL/28/11 &gt;--------------------------<br /><strong>UNIX: Remote AAS Instance startup fails in phase START-SYSTEM</strong><br />For updates from SAP NetWeaver 7.1 (or higher) to SAP NetWeaver 7.3 on UNIX, it might happen that any remote additional application server instance fails to start in phase START-SYSTEM.<br />In this case, proceed as described in SAP Note <strong>1613445</strong>.<br /><br /><br />-----------------------&lt; D025792 MAY/19/11 &gt;--------------------------<br /><strong>UNIX only: Apply SAP Note 995116</strong><br />If your source release has a lower level than one of the following:</p>\r\n<ul>\r\n<li>SAP Basis 6.40 Kernel patch level 169 or SP 20</li>\r\n</ul>\r\n<ul>\r\n<li>SAP Basis 7.00 Kernel patch 96 or SP 12</li>\r\n</ul>\r\n<p>you have to apply the solution in Note <strong>995116</strong> before starting a release upgrade. Otherwise, the Software Update Manager is unable to start and stop your SAP system as it uses sapcontrol for this.<br /><br />----------------------&lt; I024101 MAY/12/11 &gt;--------------------------<br /><strong>Error in Phase DETECT_START_RELEASE_COMPONENTS</strong><br />When running an upgrade on a NetWeaver 2004-based system, the phase DETECT_START_RELEASE_COMPONENTS might fail with the following error message:<br /><br />The software component &lt;component name&gt; could not be read from the BC_COMPVERS table; the table schema may be outdated. Fix it and then repeat the phase from the beginning.<br /><br />In this case, apply SAP Note <strong>873624</strong>.<br /><br />----------------------&lt; I056573 SEP/15/09 &gt;--------------------------<br /><strong>IBM Databases: Error in Phase DEPLOY_ONLINE_DEPL</strong><br />The following error can occur in the DEPLOY_ONLINE_DEPL phase:<br />\"Table WCR_USERSTAT Conversion currently not possible.\"<br />For the solution, see SAP Note <strong>1156313</strong>.<br /><br /><br />---------------------&lt; I031257 NOV/19/08 &gt;----------------------------<br /><strong>Phase DEPLOY_ONLINE_DEPL: Timeout During AS Java Restart</strong><br />On slow/loaded systems, the DEPLOY_ONLINE_DEPL phase might fail due to a timeout during AS Java restart. This restart can take several hours as during this time portal applications are updated. If this restart takes longer than expected (3h), the Software Update Manager stops with an error.<br />To complete the enhancement package installation, wait for the AS Java restart in SAFE mode to finish and then resume SUM. If the system is still in SAFE mode after the update process completes, apply SAP Note <strong>1267123</strong>.<br /><br /><br />------------------------&lt; D030182 20/Jun/08 &gt;--------------------------<br /><strong>Error in Phase DEPLOY_ONLINE_DEPL (IBM DB2 for z/OS)</strong><br />You may encounter an error in phase DEPLOY_ONLINE_DEPL and the following error message can be found in the log file:<br />===<br />Info: E R R O R ******* (DbModificationManager)<br />Info: ... dbs-Error: Table KMC_WF_TEMPLATES: Conversion currently not possible<br />===<br />See SAP Note <strong>989868</strong> for the solution.<br />Additionally, you may need to increase the heap size of your Java VM. For more information, see SAP Note <strong>1229300</strong>.<br /><br /><br />----------------------------------------------------------------------<br /><br /><br /><strong>V/ Problems After the Update</strong><br />This section addresses known problems that cannot be avoided using preventive measures. These problems will only occur under specific circumstances.<br /><br />a) General<br /><br />/<br /><br /><br />b) ABAP<br /><br />---------------------&lt;&#160;D023536 APR/22/2014 &gt;------------------------<br /><strong>SAP_BASIS 740 SP4: Missing Views in the database</strong><br />After an update to SAP_BASIS 740 SP4, the views V_DSH_ASSIGNED and V_DSH_TF_DETAIL are missing in the database. These views were delivered accidentally and cannot be created on a system based on SAP_BASIS 740 SP4. You can ignore this inconsistency as the views are not needed by the system.&#160;This issue&#160;will be solved with SUM version 1.0 SP12.<br /><br /><br />---------------------&lt; D024828 FEB/25/2014 &gt;------------------------<br /><strong>Ignorable short dumps LOAD_NOT_FOUND_AFTER_GEN in Phase MAIN_NEWBAS/XPRAS_AIMMRG</strong><br />After the upgrade you might find short dumps LOAD_NOT_FOUND_AFTER_GEN that happenend (from a time perspective) during phase MAIN_NEWBAS/XPRAS_AIMMRG if your system was upgraded to Kernel Version 7.41 PL 23 or lower.<br />You can ignore these short dumps. The problem is fixed with Kernel 7.41 PL 24.<br /><br /><br />---------------------&lt; D028310 DEC/16/2013 &gt;------------------------<br /><strong>Error: DDIC_ILLEGAL_KEY_COMP_NAME</strong><br />After an upgrade or update, you encounter the following error message:<br />DDIC_ILLEGAL_KEY_COMP_NAME<br />You can ignore this error.<br /><br /><br />---------------------&lt; D057512 DEC/12/2013 &gt;------------------------<br /><strong>Warnings and messages in work process trace files<br /></strong>After a successful update or upgrade using near-zero downtime maintenance (nZDM/SUM) capability of SUM, you see in the trace files of the work processes (dev_w*) warnings similar to the following:<br />C head_p=ffff80ffbfffa710: dbsl err=103=DBSL_ERR_DBOBJECT_UNKNOWN -&gt;<br />dbds err=512=DS_DBOBJECTUNKNOWN, dbdsoci.c:962<br />C *** ERROR =&gt; ^^ Ds_exec_imm() -&gt; err=512=DS_DBOBJECTUNKNOWN<br />[dbdsoci.c 976]<br />You can ignore these warnings. They are caused by the automatic deletion of temporary objects that were used in the shadow system only.<br /><br /><br />-------------------&lt; D047912 OCT/21/2012 &gt;--------------------------<br /><strong>Message: DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM</strong><br />After an successful upgrade or update, you see in the system log using<br />transaction SM21 the error message:<br />DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM<br />with the following details: A destination has been deleted in SM59,<br />destination maintenance of RFC. Problems can occur if this destination<br />is still used in existing ABAP programs.<br />You can ignore this error message.<br /><br /><br />-------------------&lt; D035061 03/Apr/2013 &gt;--------------------------<br /><strong>SAP_BASIS 731 SP05 to 740: Some tables remain on database</strong><br />After an update from SAP_BASIS 731 SP05 or higher to SAP_BASIS 740, the tables DDREPLIAPPLI and DDREPLIAPPLIT remain on the database. You can remove these tables using transaction SE14 or database commands.<br /><br /><br />-------------------&lt; D033123 17/Nov/2012 &gt;--------------------------<br /><strong>LONGPOST.LOG: Error message GI747</strong><br />SUM has added in phase XPRAS_UPG the error message GI747 (Error during creation ofstructure GLU1\"RECID\"\"TEXT4\"\"JV_RECIND\") to the LONGPOST.LOG file. See SAP note 1779102 and check, if this note solves the problem.<br /><br /><br />------------------&lt; Update D035061 10/JUL/2013 &gt;----------------------<br />-----------------------&lt; D026178 OCT/10/10 &gt;--------------------------<br /><strong>Ignorable P messages in LONGPOST.LOG</strong><br />Problem: SUM has added in phase ACT_UPG the following message to the LONGPOST.LOG file:<br />4PDDH176 Parameter list of append search help \"RDM_WKBK\" differs from appending one.<br />In phase CHK_POSTUP, you will be prompted as a result to check that message.<br />Solution: You can ignore this P message. It was written during ACT_UPG, but no followup action is required.<br /><br /><br />---------------&lt; Update D023536 16/JUL/2013 &gt;-----------------------<br />-------------------&lt; D053561 10/FEB/2012 &gt;--------------------------<br /><strong>LONGPOST.LOG: Tables without DDIC reference or not existing in DDIC</strong><br />After an upgrade or update, the file LONGPOST.LOG contains lines that indicate that a table has no DDIC reference. It can be either the following message:<br /><br />1PEPU203X &gt; Messages extracted from log file \"RDDNTPUR.&lt;SID&gt;\"<br />&lt;3PETG447 Table and runtime object \"NAVERS2\" exist without DDIC reference (\"Transp. table\")<br /><br />or a message such as<br /><br />4 ETG003 Table \"NCVERS2\" does not exist in the ABAP Dictionary<br /><br />The following tables can be be affected from this error message:</p>\r\n<p>/1SAP1/CCE_RUN01<br />CRR_CONFDT<br />CRR4TABLES<br />CRRPARAMETERS<br />CRRRTI<br />CRRRTIT<br />CRRSCENARIOS<br />CRRTASKHIST<br />CRRTASKINFO<br />CRRTCONFID<br />NAVERS2<br />NCVERS2<br />PATALLOWED<br />SUBST_SLANA_HDR<br />SUBST_SLANA_POS</p>\r\n<p>Note: We know of the list above, and the mentioned tables can safely be deleted in the database. If there are additional tables mentioned in the LONGPOST.LOG, please check the relevance for your system before you remove them.</p>\r\n<p>Proceed as follows to solve this issue:</p>\r\n<ol>1. Choose transaction SE11 to display the mentioned tables</ol><ol>2. If the tables don't exist in the transaction, you can remove the tables from the database.</ol><ol>3. In addition, you delete the runtime objects that belong to these tables:</ol><ol><ol>a) Choose transaction SE37.</ol></ol><ol><ol>b) Check if the function module DD_SHOW_NAMETAB contains runtime objects that belong to these tables.</ol></ol><ol><ol>c) Delete the runtime objects of the tables using the function module DD_NAMETAB_DELETE.</ol></ol>\r\n<p>The following tables might appear in upgrades from 702 to 730 and can safely be removed in this case. See SAP Note 824971 for more information.</p>\r\n<p>GENSETM<br />RSJOBTABM<br />SERVTYPEM</p>\r\n<p>If they appear in other updates, please contact the SAP support and report an incident under component BC-UPG-TLS-TLA.</p>\r\n<p><br /><br />c) Java</p>\r\n<p>--------------------------------&lt; D057755 28/FEB/14&gt;-------------------------------------</p>\r\n<p><strong>Correcting issues with the SAP MC applet digital signature</strong></p>\r\n<p>If you open the SAP Management Console (SAP MC) by using its web interface after a SUM update procedure has been completed, a warning about the digital signature of the applet archives may appear. The reason for this is that these archives are self-signed. To correct this, acquire the archives relevant for your kernel as follows:</p>\r\n<ol>\r\n<li>Go to the SAP Software Distribution Center at: <a target=\"_blank\" href=\"http://service.sap.com/swdc\">http://service.sap.com/swdc</a> and logon with your SAP Service Marketplace ID.</li>\r\n<li>In the navigation bar, choose SAP Software Download Center&#160;-&gt;&#160;Support Packages and Patches&#160; -&gt; &#160;Browse our Download Catalog&#160;-&gt; &#160;Additional Components&#160;-&gt;&#160;SAP Kernel&#160; -&gt; SAP KERNEL <var>&lt;OS&gt;</var> -&gt;&#160;SAP KERNEL <var>&lt;Release&gt;</var> &#160;-&gt;&#160;Database independent.</li>\r\n<li>Select the appropriate sapmc_&lt;version&gt;.sararchive from the &#8220;Download&#8221; tab.</li>\r\n</ol>\r\n<p><strong>Note:</strong> Make sure that the SAPCAR tool is available in the host where you want to update the SAP Management Console. For more information about acquiring and using the SAPCAR tool, see SAP Note <strong>212876</strong>.</p>\r\n<ol>\r\n<li>Open a command prompt or PowerShell and extract the sapmc_&lt;version&gt;.sar archive to a local directory by executing the following command:</li>\r\n</ol>\r\n<p>-&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; for Windows:&#160; SAPCAR.exe &#8211;xvf sapmc_&lt;version&gt;.sar</p>\r\n<p>-&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; For UNIX and IBM-i:&#160; ./SAPCAR -xvf sapmc_&lt;version&gt;.sar</p>\r\n<ol>\r\n<li>Replace the existing sapmc directory with the sapmc directory that was extracted during the previous step.</li>\r\n</ol>\r\n<p>For more information about installing and updating the SAP MC, see SAP Note <strong>1014480</strong>.</p>\r\n<p>&#160;</p>\r\n<p><br />--------------------&lt; D001712 AUG/28/09 &gt;------------------------------<br /><strong>Changing the start profile</strong><br />Include the following entry in your start profile:</p>\r\n<ul>\r\n<li>Unix:</li>\r\n</ul>\r\n<p>DIR_LIBRARY = $(DIR_LIBRARY)<br />PATH = $(DIR_EXECUTABLE):%(PATH)</p>\r\n<ul>\r\n<li>Windows:</li>\r\n</ul>\r\n<p>PATH = $(DIR_EXECUTABLE);%(PATH)<br />This prevents a problem with the jmon.dll file.<br /><br /><br /><strong>Part D Chronological Summary</strong><br /><br />Date........Topic...Short description<br />-----------------------------------------------------------------------<br />OCT/13/14...III..No \"Single System\" mode on dual-stack systems<br />AUG/18/14...III..Deactivate IOT feature for certain tables<br />JUL/03/14....III..SFW activation: Prevent automatic activation of BC-Sets in all clients<br />JUN/03/14....IV..DB connect problem with SUM for systems on ORACLE or SAP ASE database<br />JUN/03/14....II..HP-UX and Solaris Operating System Support<br />MAY/22/14...II...Addendum to ABAP Workbench Locking (Phase REPACHK_CLONE)<br />MAY/22/14...IV..HP-UX systems only: Correcting JVM core dump errors<br />APR/30/14....II...Java / Dual-Stack Systems: Additional Info for \"Reentering Passwords\" Section<br />APR/22/14....V...SAP_BASIS 740 SP4: Missing Views in the database<br />APR/11/14....IV..P messages in LONGPOST.LOG due to BW object activation<br />APR/09/14....III..Preventing 'osexecute' errors<br />APR/02/14....II...Windows Server 2012 (R2) procedure validity<br />MAR/21/14....II..Navigation paths for Installing or Updating SAP Host Agent<br />MAR/21/14...IV..Reset not possible during update of dual-stack systems<br />MAR/17/14...IV..Correcting connectivity issues when sapstartsrv operates over HTTPS<br />MAR/11/14...III..SAP_FIN 700: Do not use the Single System Mode<br />MAR/04/14...IV..Updating to SAP Business Suite 7 Innovation 2013 SP3: Endless loop in MAIN_SHDIMP/SUBMOD_SHD2_RUN/RUN_RSGEN<br />FEB/28/14....IV..Windows only: SUM Process Report displays incorrect OS version<br />FEB/28/14....V...Correcting issues with the SAP MC applet digital signature<br />FEB/28/14....IV..Runtime error INSERT_PROGRAM_NAME_BLANK in phase XPRAS_AIMMRG<br />FEB/28/14....III..Consider SAP Note 1983758 while preparing the update<br />FEB/27/14....IV..SAP NetWeaver 7.3 and higher only: Preventing refusal issues caused by column drop of NZDM_VALUE2<br />FEB/25/14....V...Ignorable short dumps LOAD_NOT_FOUND_AFTER_GEN in Phase MAIN_NEWBAS/XPRAS_AIMMRG<br />JAN/10/18....II...Manual steps for non-central instances after the upgrade (profile merge)<br />FEB/11/14....IV..Windows only: Correcting Issues in the \"Delete Old Java Content\" Step<br />FEB/03/14....IV..(Windows only:) Error \"Access denied\" in phase MAIN_UPTRANS/UPCONF<br />JAN/14/14....IV..DYNPRO_NOT_FOUND during parallel DBCLONE processes<br />DEC/16/13...II...Customer implementations to SAP exit function modules are disabled on the shadow instance<br />DEC/16/13...V...Error message DDIC_ILLEGAL_KEY_COMP_NAME<br />DEC/16/13...IV...Solaris X86_64: Timeout Error during phase MAIN_NEWBAS/STOPSAP_FINAL<br />DEC/13/13...III..Exclude Z languages<br />DEC/13/13...I....Solution Manager only: SPS update of ABAP or Java stack independently is supported<br />NOV/16/13...V...Warnings and messages in work process trace files<br />NOV/11/13...IV...Correcting Issues with Resetting the Upgrade<br />NOV/05/13...IV...Phase ACT_UPG: Certain search helps could not be activated<br />OCT/29/13...I....Software Update Manager 1.0 SP09 Release Restrictions<br />OCT/28/13...I...Installing SAP Business Suite Usage Types on Top of an Existing SAP NetWeaver Java System<br />OCT/23/13...IV..Shadow instance reset during an upgrade<br />OCT/21/13...V....Message: DDIC deletes RFC destination SAP_UPGRADE_SHADOW_SYSTEM<br />OCT/14/13...IV...Error in Phase MAIN_NEWBAS/SUCCCHK_PROD<br />OCT/14/13...III..Apply SAP Note 1910464 before the import of certain SPs<br />AUG/16/13...IV...Phase XPRAS_UPG: Errors during post-handling \"RS_AFTER_IMPORT\"<br />JUL/26/13....I....Update of shared Wily AutoProbe connectors<br />JUL/15/13...IV...Phase ACT_UPG: Certain tables cannot be activated<br />JUL/15/13....V....LONGPOST.LOG: Tables without DDIC reference or not existing in DDIC<br />JUN/25/13....IV...Errore in phases XPRAS_SHD_AIMMRG, XPRAS_AIMMRG, OR XPRAS_TRANS<br />JUN/17/13....V....Tables without DDIC reference: Message ETG447 in LONGPOST.LOG<br />MAY/23/13...IV...Correcting issues with primary application server instance host name connectivity<br />MAY/14/13...IV...Phase XPRAS_UPG: Problems in FDT_AFTER_IMPORT or FDT_AFTER_IMPORT_C<br />MAY/02/13...IV...ICNVREQ: Incremental conversion in special namespaces<br />APR/29/13...III..Setting the instance profile parameter AutoStart to '0'<br />APR/24/13...IV...View EPIC_V_BRS_BSEG not activated<br />APR/03/13...V....SAP_BASIS 731 SP05 to 740: Some tables remain on database<br />MAR/28/13...IV...Preventing Issues with Wily AutoProbe connectors<br />MAR/26/13...I....Built-in capability near-zero downtime maintenance (nZDM/SUM)<br />MAR/25/13...IV...RFC_COMMUNICATION_FAILURE during phase CHECKSYSSTATUS<br />MAR/25/13...I....SUM requires SAP kernel 7.20 or higher for target release<br />FEB/11/13...III..Remove usages of customer-developed objects before you start SUM<br />FEB/05/13...IV...Wrong MCOD warning for dual stack systems<br />DEC/13/12...III..See SAP Note 1413569 for table SMSCMAID<br />NOV/17/12...V....LONGPOST.LOG: Error message GI747<br />NOV/16/12...I....Build-in Capabilities of SUM to include customer transport requests<br />OCT/10/12...IV...Ignorable P messages in LONGPOST.LOG<br />OCT/10/12...IV...tp 212 error in MAIN_NEWBAS/TABIM_UPG: left-over semaphore<br />SEP/18/12...III..Upgrade from SAP NetWeaver 2004 to SAP NetWeaver 7.3 EHP1 Based PowerPC System on Linux: Update Your Instance Profile<br />SEP/18/12...III..NTsystems: Install latest sapstartserv before the update<br />SEP/09/12...IV...MAIN_NEWBAS/STARTSAP_TBUPG: System start failed on NT IA64<br />AUG/22/12...III..Implement note 1720495 before you start transaction SPAUAUG/13/12...IV...Error while sapcpe copies vcredist_x64.msi<br />AUG/01/12...III..Check platform-specific requirements for the 7.20 EXT kernel<br />JUL/23/12...III..Server Group SAP_DEFAULT_BTC must include Primary Application Server Instance<br />JUN/07/12...II...User Management Engine on a remote instance host<br />FEB/01/12...IV...Dual-Stack System Update: Phase STARTSAP_PUPG Fails<br />NOV/22/11...I....SAP NetWeaver 7.3 EHP1: Release Restrictions and Limitations<br />NOV/10/11...IV...UNIX: Prevent Overwriting sapuxuserchk During Update<br />OCT/28/11...I....SAP NetWeaver 7.0 EHP3: Release Restrictions and Limitations<br />JUL/28/11...IV...UNIX: Remote AASI startup fails in phase START-SYSTEM<br />JUL/05/11...IV...Missing tables during DB02 check after reset of update<br />MAY/30/11...III..Preventing Errors Due to Unicode Length<br />MAY/19/11...IV...UNIX only: Apply SAP Note 995116<br />MAY/18/11...IV...Phase STARTSAP_PUPG: System start of the dialog instances failed<br />MAY/17/11...IV...Phase ACT_UPG phase during EHP installation<br />MAY/12/11...IV...Error in Phase DETECT_START_RELEASE_COMPONENTS<br />APR/13/11...I....SAP NetWeaver 7.3: Release Restrictions and Limitations<br />MAR/08/11...III..Windows only: Check registration of sapevents.dll<br />DEC/08/10...III..Adjust Start Profile<br />NOV/25/10...III..No Automatic Update of SAPCryptolib<br />NOV/23/10...III..Support Package Stack Update only<br />OCT/22/10...IV...Preprocessing:ERROR: Found pattern \"R3load:..<br />OCT/20/10...III..Apply SAP Note 1518145 to Avoid Error During Reset<br />JUN/07/10...IV...Preventing long runtime of SUSR_AFTER_IMP_PROFILE<br />MAY/31/10...IV...Preventing Errors with Table \"SATC_MD_STEP\"<br />SEP/15/09...IV...IBM databases: Error in DEPLOY_ONLINE_DEPL phase<br />SEP/08/09...I....JSPM NOT to be Used During the Update Process<br />AUG/28/09...V....Changing the Start Profile<br />MAY/12/09...IV...Resetting the Installation When Using ICNV<br />APR/29/09...IV...Modification Adjustment<br />JAN/30/09...III..Cleaning Up the Profile Directory<br />DEC/15/08...III..Preventing Activation Errors<br />NOV/20/08...IV...File HUGETABS.LST<br />NOV/19/08...IV...Phase DEPLOY_ONLINE_DEPL:Timeout During AS Java Restart<br />NOV/13/08...IV...\"/ISQC/S_UT_REF\" could not be activated<br />SEP/19/08...IV...Activation Error<br />AUG/26/08...IV...Phase: MAIN_SHDIMP/SHDUNINST_DB<br />AUG/20/08...IV...Phase XPRAS_UPG: COMPUTE_INT_PLUS_OVERFLOW</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-SLC (Software Logistics Controller)"}, {"Key": "Other Components", "Value": "BC-UPG-TLS-TLJ (Upgrade tools for Java)"}, {"Key": "Other Components", "Value": "BC-EHP-INS (Please use BC-UPG-TLS*)"}, {"Key": "Other Components", "Value": "BC-UPG-TLS-TLA (Upgrade tools for ABAP)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D031330)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001878193/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SUM_SP10_paths.pdf", "FileSize": "332", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000488992013&iv_version=0025&iv_guid=9765E9B390EBEF4584CA97E27DA0E988"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116"}, {"RefNumber": "873624", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/873624"}, {"RefNumber": "824971", "RefComponent": "BC-UPG", "RefTitle": "Message ETG447 for /1SAP1/CCE_RUN01 in LONGPOST.LOG", "RefUrl": "/notes/824971"}, {"RefNumber": "821496", "RefComponent": "BC-SEC-USR", "RefTitle": "Runtime of after-import method SUSR_AFTER_IMP_PROFILE", "RefUrl": "/notes/821496"}, {"RefNumber": "797147", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Installation for SAP Customers", "RefUrl": "/notes/797147"}, {"RefNumber": "786412", "RefComponent": "BC-CCM-BTC", "RefTitle": "Determination of execution server for jobs without target server", "RefUrl": "/notes/786412"}, {"RefNumber": "538167", "RefComponent": "BC-DWB-DIC", "RefTitle": "Active objects are displayed as inactive", "RefUrl": "/notes/538167"}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "The new archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "2035728", "RefComponent": "BC-DWB-TOO-SFW", "RefTitle": "SFW activation: Prevent automatic activation of BC-Sets in all clients", "RefUrl": "/notes/2035728"}, {"RefNumber": "1991911", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SUM fails to connect to sapstartsrv with enabled SSL", "RefUrl": "/notes/1991911"}, {"RefNumber": "1983758", "RefComponent": "BW-WHM-MTD", "RefTitle": "BW client is incorrectly set in prepare phase of upgrade", "RefUrl": "/notes/1983758"}, {"RefNumber": "1978632", "RefComponent": "BC-UPG-DTM-TLJ", "RefTitle": "NZDM Java: Refuse due to drop column", "RefUrl": "/notes/1978632"}, {"RefNumber": "1973135", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1973135"}, {"RefNumber": "1964350", "RefComponent": "BC-ABA-SC", "RefTitle": "DYNPRO_NOT_FOUND during parallel DBCLONE processes", "RefUrl": "/notes/1964350"}, {"RefNumber": "1941711", "RefComponent": "BC-DWB-CEX-BAD", "RefTitle": "Dump: INSERT_PROGRAM_NAME_BLANK during After-Import processing of SXCI / SXSD", "RefUrl": "/notes/1941711"}, {"RefNumber": "1910464", "RefComponent": "BC-DWB-CEX", "RefTitle": "Processing of SPDD-relevant note corrections", "RefUrl": "/notes/1910464"}, {"RefNumber": "1898687", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Merge start profile with instance profile (Linux/Unix OS)", "RefUrl": "/notes/1898687"}, {"RefNumber": "1879293", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1879293"}, {"RefNumber": "1878395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878395"}, {"RefNumber": "1878296", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878296"}, {"RefNumber": "1878295", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878295"}, {"RefNumber": "1878252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878252"}, {"RefNumber": "1878241", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878241"}, {"RefNumber": "1878198", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878198"}, {"RefNumber": "1871178", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1871178"}, {"RefNumber": "1866886", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Message CD837 in transport log", "RefUrl": "/notes/1866886"}, {"RefNumber": "1846998", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "View EPIC_V_BRS_BSEG not activated", "RefUrl": "/notes/1846998"}, {"RefNumber": "1830894", "RefComponent": "BC-DB-DB4", "RefTitle": "Availability of SAP NetWeaver 7.4 on IBM i", "RefUrl": "/notes/1830894"}, {"RefNumber": "1789659", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP Netweaver AS ABAP 7.40", "RefUrl": "/notes/1789659"}, {"RefNumber": "1781833", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. About Upgrading to SAP Solution Manager 7.1", "RefUrl": "/notes/1781833"}, {"RefNumber": "1779102", "RefComponent": "FI-GL", "RefTitle": "RGZZGLUX: Error GU093 or GI747 because of field RECID", "RefUrl": "/notes/1779102"}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566"}, {"RefNumber": "1759080", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prerequisites and restrictions of Customer Transport Integration with SUM", "RefUrl": "/notes/1759080"}, {"RefNumber": "1756703", "RefComponent": "BC-CST-STS", "RefTitle": "sapstartsrv: instance property is missing for ERS", "RefUrl": "/notes/1756703"}, {"RefNumber": "1751237", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the update/upgrade to SAP NetWeaver 7.4 (incl. SPs and SRs)", "RefUrl": "/notes/1751237"}, {"RefNumber": "1730175", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 FOR SAP SCM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730175"}, {"RefNumber": "1730102", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP NetWeaver 7.4", "RefUrl": "/notes/1730102"}, {"RefNumber": "1730098", "RefComponent": "CRM-BF", "RefTitle": "EHP2 FOR SAP CRM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730098"}, {"RefNumber": "1730095", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 FOR SAP ERP 6.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730095"}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495"}, {"RefNumber": "1696517", "RefComponent": "BC-CCM-MON", "RefTitle": "Memory alignment problem in sapstartsrv.exe process", "RefUrl": "/notes/1696517"}, {"RefNumber": "1678565", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prerequisites and restrictions of nZDM (near-Zero Downtime Maintenance) for ABAP-based solutions", "RefUrl": "/notes/1678565"}, {"RefNumber": "1668465", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Directory traversal in RSUPG_COPY_SHD_VIEWS", "RefUrl": "/notes/1668465"}, {"RefNumber": "1656036", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SUM functionality for checking sapstartsrv patch level", "RefUrl": "/notes/1656036"}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797"}, {"RefNumber": "1637629", "RefComponent": "BC-XI", "RefTitle": "No Process Integration in SAP EHP3 FOR SAP NETWEAVER 7.0", "RefUrl": "/notes/1637629"}, {"RefNumber": "1637366", "RefComponent": "BC-INS", "RefTitle": "Installation of SAP EHP3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1637366"}, {"RefNumber": "1633876", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1633876"}, {"RefNumber": "1631124", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RDDCP6TB", "RefUrl": "/notes/1631124"}, {"RefNumber": "1631072", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RDDNT4DL", "RefUrl": "/notes/1631072"}, {"RefNumber": "1628606", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Potential modification of persisted data by RSTRESNC", "RefUrl": "/notes/1628606"}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463"}, {"RefNumber": "1613445", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Remote AAS instance startup fails during SUM update on UNIX", "RefUrl": "/notes/1613445"}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441"}, {"RefNumber": "1595736", "RefComponent": "SV-SMG-INS", "RefTitle": "Solution Manager: Overview on Release Information Notes", "RefUrl": "/notes/1595736"}, {"RefNumber": "1584549", "RefComponent": "BC-UPG", "RefTitle": "Directory traversal in function group SUGPEWA", "RefUrl": "/notes/1584549"}, {"RefNumber": "1584548", "RefComponent": "BC-UPG", "RefTitle": "Directory traversal in RSUPGSUM", "RefUrl": "/notes/1584548"}, {"RefNumber": "1577595", "RefComponent": "BC-DWB-DIC", "RefTitle": "Index P of table SFW_PACKAGE is not deleted", "RefUrl": "/notes/1577595"}, {"RefNumber": "1570738", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite PI adapters for SAP NetWeaver PI 7.3", "RefUrl": "/notes/1570738"}, {"RefNumber": "1557825", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "Release Restrictions for SAP EHP 3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1557825"}, {"RefNumber": "1556113", "RefComponent": "BC-CST-STS", "RefTitle": "sapevents.dll in DIR_CT_RUN is locked", "RefUrl": "/notes/1556113"}, {"RefNumber": "1555144", "RefComponent": "BC-UPG", "RefTitle": "Potential information disclosure relating to file system", "RefUrl": "/notes/1555144"}, {"RefNumber": "1528297", "RefComponent": "BC-OP-NT", "RefTitle": "Merge start profile with instance profile", "RefUrl": "/notes/1528297"}, {"RefNumber": "1526853", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Protection of upgrade tools against acts of sabotage", "RefUrl": "/notes/1526853"}, {"RefNumber": "1522700", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1522700"}, {"RefNumber": "1518145", "RefComponent": "BC-DWB-DIC", "RefTitle": "FM DD_DB_MISSING_OBJECTS returns incorrect no. of objects", "RefUrl": "/notes/1518145"}, {"RefNumber": "1514066", "RefComponent": "BC-UPG", "RefTitle": "Overwriting of files in upgrade or EHPI", "RefUrl": "/notes/1514066"}, {"RefNumber": "1497083", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP SCM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497083"}, {"RefNumber": "1497032", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP CRM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497032"}, {"RefNumber": "1496212", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 for SAP ERP 6.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1496212"}, {"RefNumber": "1489787", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1489787"}, {"RefNumber": "1468467", "RefComponent": "BC-DWB-DIC", "RefTitle": "Sequence problem during DROP/CREATE of indexes", "RefUrl": "/notes/1468467"}, {"RefNumber": "1463168", "RefComponent": "BC-DWB-TOO-ATF", "RefTitle": "Tables \"SATC_MD_STEP\" and \"SATC_AC_CHK\" and upgrade tools", "RefUrl": "/notes/1463168"}, {"RefNumber": "1453164", "RefComponent": "BC-UPG", "RefTitle": "Missing authorization check in module of upgrade", "RefUrl": "/notes/1453164"}, {"RefNumber": "1413569", "RefComponent": "CA-GTF-SCM", "RefTitle": "Index for SMSCMAID table for Performance and Upgrade", "RefUrl": "/notes/1413569"}, {"RefNumber": "1407532", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP NetWeaver 7.3", "RefUrl": "/notes/1407532"}, {"RefNumber": "1390477", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1390477"}, {"RefNumber": "1387739", "RefComponent": "BC-UPG-RDM", "RefTitle": "Out of memory errors during shadow system operation", "RefUrl": "/notes/1387739"}, {"RefNumber": "1357207", "RefComponent": "BC-SRV-BR", "RefTitle": "Implementation of coding corrections during phase XPRAS_UPG", "RefUrl": "/notes/1357207"}, {"RefNumber": "1337378", "RefComponent": "BC-UPG-TLS", "RefTitle": "ICNV: Form oidx_cre:Operation DB_CREATE_INDEX failed", "RefUrl": "/notes/1337378"}, {"RefNumber": "1283197", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Cluster tables: Unnecessary conversion", "RefUrl": "/notes/1283197"}, {"RefNumber": "12746", "RefComponent": "BC-CTS", "RefTitle": "WARN <file> is already in use (), I'm waiting 5 sec", "RefUrl": "/notes/12746"}, {"RefNumber": "1267123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1267123"}, {"RefNumber": "1264734", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Error message for reference table and reference fields", "RefUrl": "/notes/1264734"}, {"RefNumber": "1243486", "RefComponent": "BC-SRV-BR", "RefTitle": "Dump COMPUTE_INT_PLUS_OVERFLOW during background cleanup", "RefUrl": "/notes/1243486"}, {"RefNumber": "1243014", "RefComponent": "PA-PA-IT", "RefTitle": "Search Help H_5ITCD and H_5ITTT Not Activated in ACT_700", "RefUrl": "/notes/1243014"}, {"RefNumber": "1242867", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Domain append structure is not activated", "RefUrl": "/notes/1242867"}, {"RefNumber": "1162299", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1162299"}, {"RefNumber": "1162171", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC: Texts of domain fixed values are lost after transport", "RefUrl": "/notes/1162171"}, {"RefNumber": "1161733", "RefComponent": "BC-UPG", "RefTitle": "SQL Server: SAP EHPI for SAP NetWeaver 7.0", "RefUrl": "/notes/1161733"}, {"RefNumber": "1160166", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1160166"}, {"RefNumber": "1156313", "RefComponent": "EP-PIN-SPT", "RefTitle": "Activity Report Upgrade with IBM Databases (DB2, DB4, DB6)", "RefUrl": "/notes/1156313"}, {"RefNumber": "1142632", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for Enhancement Package installer: MaxDB", "RefUrl": "/notes/1142632"}, {"RefNumber": "1127815", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions EhP installer / SUM", "RefUrl": "/notes/1127815"}, {"RefNumber": "1109375", "RefComponent": "BC-DWB-DIC", "RefTitle": "Values from fixed value append are missing in the domain", "RefUrl": "/notes/1109375"}, {"RefNumber": "1029444", "RefComponent": "BC-DWB-DIC", "RefTitle": "DDIF_FIELDINFO_GET:Prob with UCLEN <> system Unicode length", "RefUrl": "/notes/1029444"}, {"RefNumber": "1025085", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "How to manually patch the SAPJVM", "RefUrl": "/notes/1025085"}, {"RefNumber": "1014480", "RefComponent": "BC-CCM-MC", "RefTitle": "SAP Management Console (SAP-MC)", "RefUrl": "/notes/1014480"}, {"RefNumber": "1009759", "RefComponent": "BC-UPG-TLS", "RefTitle": "Incremental conversion in special namespaces", "RefUrl": "/notes/1009759"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1991638", "RefComponent": "BC-DB-HDB-POR", "RefTitle": "Supplementary SAP Note to SAP Note 1990894", "RefUrl": "/notes/1991638 "}, {"RefNumber": "1730175", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 FOR SAP SCM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730175 "}, {"RefNumber": "1730098", "RefComponent": "CRM-BF", "RefTitle": "EHP2 FOR SAP CRM 7.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730098 "}, {"RefNumber": "1497083", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP SCM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497083 "}, {"RefNumber": "1730095", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 FOR SAP ERP 6.0 ON HANA - Release Information Note", "RefUrl": "/notes/1730095 "}, {"RefNumber": "1496212", "RefComponent": "XX-SER-REL", "RefTitle": "EHP6 for SAP ERP 6.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1496212 "}, {"RefNumber": "1910464", "RefComponent": "BC-DWB-CEX", "RefTitle": "Processing of SPDD-relevant note corrections", "RefUrl": "/notes/1910464 "}, {"RefNumber": "1656036", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "SUM functionality for checking sapstartsrv patch level", "RefUrl": "/notes/1656036 "}, {"RefNumber": "1730102", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP NetWeaver 7.4", "RefUrl": "/notes/1730102 "}, {"RefNumber": "1609441", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info about the upgrade to SAP NetWeaver 7.3 EHP1", "RefUrl": "/notes/1609441 "}, {"RefNumber": "1720495", "RefComponent": "BC-UPG-NA", "RefTitle": "Invalid deimplementation of obsolete notes by Snote tool", "RefUrl": "/notes/1720495 "}, {"RefNumber": "1774566", "RefComponent": "XX-SER-REL", "RefTitle": "SAP Business Suite - Restrictions", "RefUrl": "/notes/1774566 "}, {"RefNumber": "797147", "RefComponent": "XX-PART-WILY", "RefTitle": "Introscope Installation for SAP Customers", "RefUrl": "/notes/797147 "}, {"RefNumber": "1557825", "RefComponent": "XX-SER-SAPSMP-CON", "RefTitle": "Release Restrictions for SAP EHP 3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1557825 "}, {"RefNumber": "1407532", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP NetWeaver 7.3", "RefUrl": "/notes/1407532 "}, {"RefNumber": "1522700", "RefComponent": "XX-SER-GEN", "RefTitle": "Release Restrictions for SAP EHP1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1522700 "}, {"RefNumber": "1127815", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions EhP installer / SUM", "RefUrl": "/notes/1127815 "}, {"RefNumber": "1615463", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite 7i 2010 for SAP NetWeaver 7.3 hub systems", "RefUrl": "/notes/1615463 "}, {"RefNumber": "824971", "RefComponent": "BC-UPG", "RefTitle": "Message ETG447 for /1SAP1/CCE_RUN01 in LONGPOST.LOG", "RefUrl": "/notes/824971 "}, {"RefNumber": "1387739", "RefComponent": "BC-UPG-RDM", "RefTitle": "Out of memory errors during shadow system operation", "RefUrl": "/notes/1387739 "}, {"RefNumber": "1789659", "RefComponent": "XX-SER-REL", "RefTitle": "Release Restrictions for SAP Netweaver AS ABAP 7.40", "RefUrl": "/notes/1789659 "}, {"RefNumber": "1009759", "RefComponent": "BC-UPG-TLS", "RefTitle": "Incremental conversion in special namespaces", "RefUrl": "/notes/1009759 "}, {"RefNumber": "1866886", "RefComponent": "BC-SRV-ASF-CHD", "RefTitle": "CD: Message CD837 in transport log", "RefUrl": "/notes/1866886 "}, {"RefNumber": "1357207", "RefComponent": "BC-SRV-BR", "RefTitle": "Implementation of coding corrections during phase XPRAS_UPG", "RefUrl": "/notes/1357207 "}, {"RefNumber": "1413569", "RefComponent": "CA-GTF-SCM", "RefTitle": "Index for SMSCMAID table for Performance and Upgrade", "RefUrl": "/notes/1413569 "}, {"RefNumber": "1781833", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. Info. About Upgrading to SAP Solution Manager 7.1", "RefUrl": "/notes/1781833 "}, {"RefNumber": "1497032", "RefComponent": "XX-SER-REL", "RefTitle": "EHP2 for SAP CRM 7.0 SP stacks - Release & Information Note", "RefUrl": "/notes/1497032 "}, {"RefNumber": "821496", "RefComponent": "BC-SEC-USR", "RefTitle": "Runtime of after-import method SUSR_AFTER_IMP_PROFILE", "RefUrl": "/notes/821496 "}, {"RefNumber": "1650797", "RefComponent": "BC-UPG-RDM", "RefTitle": "Prevent overwriting of sapuxuserchk during update/upgrade", "RefUrl": "/notes/1650797 "}, {"RefNumber": "1779102", "RefComponent": "FI-GL", "RefTitle": "RGZZGLUX: Error GU093 or GI747 because of field RECID", "RefUrl": "/notes/1779102 "}, {"RefNumber": "1577595", "RefComponent": "BC-DWB-DIC", "RefTitle": "Index P of table SFW_PACKAGE is not deleted", "RefUrl": "/notes/1577595 "}, {"RefNumber": "1025085", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "How to manually patch the SAPJVM", "RefUrl": "/notes/1025085 "}, {"RefNumber": "1756703", "RefComponent": "BC-CST-STS", "RefTitle": "sapstartsrv: instance property is missing for ERS", "RefUrl": "/notes/1756703 "}, {"RefNumber": "1696517", "RefComponent": "BC-CCM-MON", "RefTitle": "Memory alignment problem in sapstartsrv.exe process", "RefUrl": "/notes/1696517 "}, {"RefNumber": "1570738", "RefComponent": "BC-UPG", "RefTitle": "SAP Business Suite PI adapters for SAP NetWeaver PI 7.3", "RefUrl": "/notes/1570738 "}, {"RefNumber": "1637366", "RefComponent": "BC-INS", "RefTitle": "Installation of SAP EHP3 for SAP NetWeaver 7.0", "RefUrl": "/notes/1637366 "}, {"RefNumber": "1161733", "RefComponent": "BC-UPG", "RefTitle": "SQL Server: SAP EHPI for SAP NetWeaver 7.0", "RefUrl": "/notes/1161733 "}, {"RefNumber": "1029444", "RefComponent": "BC-DWB-DIC", "RefTitle": "DDIF_FIELDINFO_GET:Prob with UCLEN <> system Unicode length", "RefUrl": "/notes/1029444 "}, {"RefNumber": "1109375", "RefComponent": "BC-DWB-DIC", "RefTitle": "Values from fixed value append are missing in the domain", "RefUrl": "/notes/1109375 "}, {"RefNumber": "1142632", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements for Enhancement Package installer: MaxDB", "RefUrl": "/notes/1142632 "}, {"RefNumber": "1156313", "RefComponent": "EP-PIN-SPT", "RefTitle": "Activity Report Upgrade with IBM Databases (DB2, DB4, DB6)", "RefUrl": "/notes/1156313 "}, {"RefNumber": "1162171", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "DDIC: Texts of domain fixed values are lost after transport", "RefUrl": "/notes/1162171 "}, {"RefNumber": "1242867", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Domain append structure is not activated", "RefUrl": "/notes/1242867 "}, {"RefNumber": "1243486", "RefComponent": "BC-SRV-BR", "RefTitle": "Dump COMPUTE_INT_PLUS_OVERFLOW during background cleanup", "RefUrl": "/notes/1243486 "}, {"RefNumber": "1264734", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "Error message for reference table and reference fields", "RefUrl": "/notes/1264734 "}, {"RefNumber": "1337378", "RefComponent": "BC-UPG-TLS", "RefTitle": "ICNV: Form oidx_cre:Operation DB_CREATE_INDEX failed", "RefUrl": "/notes/1337378 "}, {"RefNumber": "1463168", "RefComponent": "BC-DWB-TOO-ATF", "RefTitle": "Tables \"SATC_MD_STEP\" and \"SATC_AC_CHK\" and upgrade tools", "RefUrl": "/notes/1463168 "}, {"RefNumber": "1518145", "RefComponent": "BC-DWB-DIC", "RefTitle": "FM DD_DB_MISSING_OBJECTS returns incorrect no. of objects", "RefUrl": "/notes/1518145 "}, {"RefNumber": "1613445", "RefComponent": "BC-UPG-TLS-TLJ", "RefTitle": "Remote AAS instance startup fails during SUM update on UNIX", "RefUrl": "/notes/1613445 "}, {"RefNumber": "1637629", "RefComponent": "BC-XI", "RefTitle": "No Process Integration in SAP EHP3 FOR SAP NETWEAVER 7.0", "RefUrl": "/notes/1637629 "}, {"RefNumber": "786412", "RefComponent": "BC-CCM-BTC", "RefTitle": "Determination of execution server for jobs without target server", "RefUrl": "/notes/786412 "}, {"RefNumber": "995116", "RefComponent": "BC-CST-STS", "RefTitle": "Backward porting of sapstartsrv for earlier releases", "RefUrl": "/notes/995116 "}, {"RefNumber": "1468467", "RefComponent": "BC-DWB-DIC", "RefTitle": "Sequence problem during DROP/CREATE of indexes", "RefUrl": "/notes/1468467 "}, {"RefNumber": "12746", "RefComponent": "BC-CTS", "RefTitle": "WARN <file> is already in use (), I'm waiting 5 sec", "RefUrl": "/notes/12746 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SUM", "From": "1.0", "To": "1.0", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP012", "SupportPackagePatch": "000000", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP012&patch_level=000000"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP011", "SupportPackagePatch": "000007", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP011&patch_level=000007"}, {"SoftwareComponentVersion": "SOFTWARE UPDATE MANAGER 1.0", "SupportPackage": "SP010", "SupportPackagePatch": "000010", "URL": "https://userapps.support.sap.com/sap/support/swdc/notes?cvnr=01200314690200013676&support_package=SP010&patch_level=000010"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}