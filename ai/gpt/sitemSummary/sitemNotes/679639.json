{"Request": {"Number": "679639", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 522, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003620332017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=F7E55FBCE0A53ED8D97579618C6DC312"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "679639"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.04.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "679639 - BP_TR2: Language missing in organizations after conversion"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You have carried out a business partner conversion from the TR BP to the SAP BP. After the conversion, the language field on the 'Address' tab page in the 'Communication data' section is empty for the SAP BP of category 'Organization' or 'Group' even though the language field was filled in the respective TR BP.<br /><br />Refer to Composite SAP Note 724492 on the addres formatting and the correspondence language for SAP business partner Financial Services. The note describes in detail the steps to be carried out in order to correct the data inconsistencies and to correct the program errors that caused the inconsistencies.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>BPAR_P_ADDRESS_READ_INTO_PRINT, BPAR_P_PARTNER_READ_INTO_PRINT, business partner, language, correspondence language, conversion, RFTBUP01, parallel maintenance, TR BP, SAP BP</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>A missing language in the communication data causes errors in organizations and groups during the determination of the form of address key for the written communication.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached correction instructions.<br />Create two additional text elements manually for program RFTBUP99_REPAIR_ADRC:</p> <UL><LI>Call up Transaction SE38.</LI></UL> <UL><LI>Enter the program name, select the 'Text elements' checkbox and choose 'Change'.</LI></UL> <UL><LI>Create the following text symbols:</LI></UL> <UL><UL><LI>020 Addresses in DB for Changing (ADRC)&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 31 50</LI></UL></UL><UL><UL><LI>021 Address Change Required for Business Partner&#x00A0;&#x00A0;41 50</LI></UL></UL> <p><br />Select the 'Selection texts' tab page in Transaction SE38 and enter the following text for name PAR_DFLT:<br />Name&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Text<br />PAR_DFLT&#x00A0;&#x00A0;Change Default Address Only<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FIN-FSCM-TRM-BF-BP (Please use component FS-BP!)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033094)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033094)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "724492", "RefComponent": "FS-BP", "RefTitle": "BP_DIA: Correspondence language for SAP business partner", "RefUrl": "/notes/724492"}, {"RefNumber": "689957", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Correction report adjustment for table ADRC", "RefUrl": "/notes/689957"}, {"RefNumber": "656108", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Language missing in organizations after conversion", "RefUrl": "/notes/656108"}, {"RefNumber": "645872", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "RefUrl": "/notes/645872"}, {"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}, {"RefNumber": "724492", "RefComponent": "FS-BP", "RefTitle": "BP_DIA: Correspondence language for SAP business partner", "RefUrl": "/notes/724492 "}, {"RefNumber": "656108", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Language missing in organizations after conversion", "RefUrl": "/notes/656108 "}, {"RefNumber": "645872", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "RefUrl": "/notes/645872 "}, {"RefNumber": "689957", "RefComponent": "FS-BP", "RefTitle": "BP_TR2: Correction report adjustment for table ADRC", "RefUrl": "/notes/689957 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "462_10", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA17", "URL": "/supportpackage/SAPKGPFA17"}, {"SoftwareComponentVersion": "BANK/CFM 462_10", "SupportPackage": "SAPKIPBI17", "URL": "/supportpackage/SAPKIPBI17"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ24", "URL": "/supportpackage/SAPKIPBJ24"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C47", "URL": "/supportpackage/SAPKH46C47"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}, {"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 1, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 2, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "BANK/CFM", "ValidFrom": "462_10", "ValidTo": "462_10", "Number": "645872 ", "URL": "/notes/645872 ", "Title": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "462_10", "ValidTo": "462_10", "Number": "656108 ", "URL": "/notes/656108 ", "Title": "BP_TR1: Language missing in organizations after conversion", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "645872 ", "URL": "/notes/645872 ", "Title": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "Component": "FS-BP"}, {"SoftwareComponent": "BANK/CFM", "ValidFrom": "463_20", "ValidTo": "463_20", "Number": "656108 ", "URL": "/notes/656108 ", "Title": "BP_TR1: Language missing in organizations after conversion", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "645872 ", "URL": "/notes/645872 ", "Title": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "Component": "FS-BP"}, {"SoftwareComponent": "EA-FINSERV", "ValidFrom": "110", "ValidTo": "110", "Number": "656108 ", "URL": "/notes/656108 ", "Title": "BP_TR1: Language missing in organizations after conversion", "Component": "FS-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "645872 ", "URL": "/notes/645872 ", "Title": "BP_TR1: Conversion of form of addr:Corr report for tab. ADRC", "Component": "FS-BP"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "46C", "ValidTo": "46C", "Number": "656108 ", "URL": "/notes/656108 ", "Title": "BP_TR1: Language missing in organizations after conversion", "Component": "FS-BP"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}