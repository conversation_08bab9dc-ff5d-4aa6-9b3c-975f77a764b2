{"Request": {"Number": "2776897", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 199, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002776897?language=E&token=62D9D82A149E623B69BA8176A074CE13"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002776897", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002776897/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2776897"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2776897 - SAP S/4HANA Migration Cockpit:  Collective KBA Migration object \"Purchasing info record\"/ \"Purchasing info record- extend existing record\""}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are using SAP S/4HANA Cloud</p>\r\n<p>You want to migrate data using either Migration Object:</p>\r\n<ul>\r\n<li>\"Migration of Purchasing info records (FILE2S4)\" (SIF_PURCH_INFREC) or</li>\r\n<li>\"Purchasing info record with conditions\" (SIF_PURCH_INF_V2) or</li>\r\n<li>\"Purchasing info record- extend existing record\"&#160;(SIF_PURCH_INF_EXT)</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>SAP S/4HANA</p>\r\n<p>SAP S/4HANA&#160; Cloud</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p>&#160;</p>\r\n<p>SAP S/4HANA Cloud</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr><th class=\"header3\" rowspan=\"2\" style=\"background-color: #87cefa;\">#</th><th class=\"header3\" rowspan=\"2\" style=\"background-color: #87cefa;\">Comment and SAP Note / KBA for a specific Issue</th><th class=\"header3\" rowspan=\"2\" style=\"background-color: #87cefa;\">SAP Note / KBA</th><th class=\"header3\" rowspan=\"2\" style=\"background-color: #87cefa;\">Classification</th><th class=\"header3\" colspan=\"2\" style=\"background-color: #87cefa;\">\r\n<p>Relevant&#160;for the following SAP S/4HANA releases</p>\r\n<p>(for not mentioned releases the issue does not occur or is not relevant)</p>\r\n</th></tr>\r\n<tr><th class=\"header3\" style=\"background-color: #87cefa;\">SAP S/4HANA Cloud</th><th style=\"background-color: #87cefa;\">\r\n<p>SAP S/4HANA (on premise)</p>\r\n</th><th style=\"background-color: #87cefa;\">\r\n<p>&#160;</p>\r\n</th></tr>\r\n<tr>\r\n<td style=\"text-align: center;\">1</td>\r\n<td>\r\n<p>SIF_PURCH_INF_V2</p>\r\n<p>During migration of<strong>&#160;Purchasing info record with conditions</strong> you get error&#160;<strong>ME 083:</strong></p>\r\n<ul>\r\n<li>\"Enter net price\" or</li>\r\n<li>\"Enter order Price Un.\".</li>\r\n</ul>\r\n<p>&#160;</p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p><a target=\"_blank\" href=\"/notes/2679294\">2679294</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/80183\">80183</a></p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p>INFO</p>\r\n</td>\r\n<td style=\"text-align: center;\">All</td>\r\n<td style=\"text-align: center;\">All</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">2</td>\r\n<td>\r\n<p>SIF_PURCH_INFREC</p>\r\n<p>'scale price' cannot be migrated</p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p><a target=\"_blank\" href=\"/notes/2553997\">2553997</a></p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p>INFO</p>\r\n</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1610</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">3</td>\r\n<td>\r\n<p>SIF_PURCH_INFREC</p>\r\n<p>If you have for example,&#160;the same combination of Material and Vendor&#160;inside different files the system will create duplicate purchase order info records with&#160;different Info record numbers.</p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p><a target=\"_blank\" href=\"/notes/2575468\">2575468</a></p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p>FIX</p>\r\n</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1610</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">4</td>\r\n<td>\r\n<p>SIF_PURCH_INF_V2</p>\r\n<p>SAP S/4HANA Database Issue when using Staging Tables for SIF_PURCH_INF_V2.</p>\r\n<p>Using Staging Tables, there will be a constraint issue because the field for long text is marked mandatory. This is not any issue using files.</p>\r\n</td>\r\n<td style=\"text-align: center;\">\r\n<p><a target=\"_blank\" href=\"/notes/2946674\">2946674</a></p>\r\n<p><a target=\"_blank\" href=\"/notes/2946656\">2946656</a></p>\r\n</td>\r\n<td style=\"text-align: center;\">FIX</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1809/1909</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">5</td>\r\n<td>During the migration of Purchase Info record with conditions the error message is raised: Info record &amp; already exists for material group &amp; and vendor &amp; (Message no. CNV_DMC_SIN288)&#160;</td>\r\n<td style=\"text-align: center;\">\r\n<p><a target=\"_blank\" href=\"/notes/2934111\">2934111</a><a target=\"_blank\" href=\"/notes/2946674\"><br /></a></p>\r\n</td>\r\n<td style=\"text-align: center;\">FIX</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1909</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">6</td>\r\n<td>Migrating Purchasing info record with conditions,&#160;scales have no unit of measure in table KONP&#160;</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/2984110\">2984110&#160;</a></td>\r\n<td style=\"text-align: center;\">FIX</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1809/1909/2020</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">7</td>\r\n<td>One condition is overwriting the other conditions when Validity periods are matching, when migrating multiple conditions, with \"<a target=\"_blank\" href=\"https://help.sap.com/viewer/cc9ecc5f5f6b400a98d10e930324f7ad/latest/en-US/fec0958cf12c4c6d970c0185ae45a825.html\">MM - Purchasing info record with conditions</a>\" Object</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/3065947\">3065947&#160;</a></td>\r\n<td style=\"text-align: center;\">INFO</td>\r\n<td style=\"text-align: center;\">All</td>\r\n<td style=\"text-align: center;\">All</td>\r\n</tr>\r\n<tr>\r\n<td style=\"text-align: center;\">8</td>\r\n<td>You need to set number range with external numbering, but during upload, Migration Cockpit assign it to internal</td>\r\n<td style=\"text-align: center;\"><a target=\"_blank\" href=\"/notes/3282992\">3282992&#160;</a></td>\r\n<td style=\"text-align: center;\">FIX</td>\r\n<td style=\"text-align: center;\">n/a</td>\r\n<td style=\"text-align: center;\">1809/1909/2020</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><a target=\"_blank\" href=\"/notes/3282992\">3282992&#160;</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p>Data Migration Template samples: <a target=\"_blank\" href=\"/notes/2470789\">2470789</a></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>ME. 083, 816, ME_INFORECORD_MAINTAIN_MULTI</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "CA-LT-MC (S/4HANA Migration Cockpit)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029057)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002776897/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3282992", "RefComponent": "CA-GTF-MIG", "RefTitle": "MM - Purchasing info record with conditions, external numbering assignment", "RefUrl": "/notes/3282992"}, {"RefNumber": "3065947", "RefComponent": "CA-GTF-MIG", "RefTitle": "One condition is overwriting the other conditions when Validity periods are matching, when migrating multiple conditions.", "RefUrl": "/notes/3065947"}, {"RefNumber": "2984110", "RefComponent": "CA-GTF-MIG", "RefTitle": "Issue with Purchasing info record Scale", "RefUrl": "/notes/2984110"}, {"RefNumber": "2934111", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration cockpit for purchase info records with conditions error Info record & already exists for material group & and vendor & (Message no. CNV_DMC_SIN288)", "RefUrl": "/notes/2934111"}, {"RefNumber": "2755476", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2755476"}, {"RefNumber": "2679294", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration Cockpit: Enter Net Price Error or Enter order Price Un.", "RefUrl": "/notes/2679294"}, {"RefNumber": "2553997", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration of Purchasing Info Records (FILE2S4) does not support ‘Scale price’", "RefUrl": "/notes/2553997"}, {"RefNumber": "80183", "RefComponent": "SD-BF-PR", "RefTitle": "Rounding", "RefUrl": "/notes/80183"}, {"RefNumber": "2575468", "RefComponent": "CA-GTF-MIG", "RefTitle": "Migration Object SIF_PURCH_INFREC leads to duplicate records", "RefUrl": "/notes/2575468"}, {"RefNumber": "2470789", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit (Cloud) - Sample data migration templates", "RefUrl": "/notes/2470789"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP S/4HANA Migration object documentation: Purchasing info record with conditions", "RefUrl": "https://help.sap.com/viewer/d3a3eb7caa1842858bf0372e17ad3909/latest/en-US/fec0958cf12c4c6d970c0185ae45a825.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP S/4HANA Cloud Migration object documentation: Purchasing info record with conditions", "RefUrl": "https://help.sap.com/viewer/cc9ecc5f5f6b400a98d10e930324f7ad/latest/en-US/fec0958cf12c4c6d970c0185ae45a825.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP S/4HANA Migration object: Purchasing info record- extend existing record", "RefUrl": "https://help.sap.com/viewer/d3a3eb7caa1842858bf0372e17ad3909/latest/en-US/89b0a2430ff74f1ca740aa692f1d2723.html"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP S/4HANA Cloud Migration object: Purchasing info record- extend existing record", "RefUrl": "https://help.sap.com/viewer/cc9ecc5f5f6b400a98d10e930324f7ad/latest/en-US/89b0a2430ff74f1ca740aa692f1d2723.html"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2538700", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration Cockpit - File/Staging (Cloud / SAPSCORE)", "RefUrl": "/notes/2538700 "}, {"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP S/4HANA Cloud all versions "}, {"Product": "SAP S/4HANA all versions "}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}