{"Request": {"Number": "1678024", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 292, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017379392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001678024?language=E&token=4E5986BAAC0B0536E76779F446F63E06"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001678024", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001678024/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1678024"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "08.03.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-LNX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Linux"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Linux", "value": "BC-OP-LNX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-LNX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1678024 - Linux: NetWeaver 7.0 EhP 3 with SAP kernel 720"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Newer SAP Products and Enhancement Packages require that you upgrade your Linux operating system to RHEL 6.x or SLES 11 Service Pack 1 because it is the minimum Linux version to use the 720_EXT Kernel variant.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>x86_64, Linux, RHEL5, SLES10, System Copy, Migration, Upgrade, CRM 7.0, SRM X.0, BWX, SCM X.0, PAM, CRM X.0, SLES10, SLES11, RHEL5, RHEL6<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You want to install or upgrade to a new SAP product based on NetWeaver 7.0 EhP 3, like</p> <UL><UL><LI>ERP 6.0 EhP 6</LI></UL></UL> <UL><UL><LI>CRM 7.0 EhP 2</LI></UL></UL> <UL><UL><LI>SRM 7.0 EhP 2</LI></UL></UL> <p><br />Those releases require a 720_EXT SAP kernel which only runs on 64 bit Linux versions starting with Red Hat RHEL 6.x and SUSE SLES 11 Service Pack 1.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><b>Please note:</b><br /> <p><B>This description is for information purposes only, to archive the procedure for systems where the workaround has already been implemented.</B><br /><B>Since the required operating system versions RHEL 6 and SLES 11 SP1 are released for productive use in SAP environments, it is not intended to give out new support exceptions as described below in this note. </B><br /><br />Some customers may not be able to upgrade to RHEL 6 (or SLES 11 SP1) in time to use SAP products which require the 720_EXT kernel. To help our customers, SAP decided to allow&#x00A0;&#x00A0;for Enterprise- and Maxattention-Support customers to use the 720 SAP kernel instead of the 720_EXT SAP kernel for a limited period of time. This additional support statement complements information from the PAM and allows customers to stay on RHEL 5.x (and SLES 10) until the upgrade to RHEL 6.x or SLES 11 SP1 has been finished.<br />Since May 2012 Oracle 11 has been released on RHEL 6.x, so SAP strongly advises customers to install RHEL 6 or SLES 11 SP1 and not follow the path as described in this note. If you anyway want to install your SAP product as described here, please keep in mind to plan the upgrade to RHEL 6.x or SLES 11 SP1 (or higher) as soon as possible.<br />According to the PAM, SAP has tested, validated and released all SAP products which require the 720_EXT kernel on RHEL 6.x and SLES 11 SP1 with the 720_EXT kernel only.<br />Nevertheless, technically it is possible to use the 720 kernel variant as a temporary workaround instead of kernel 720_EXT as long as some manual interaction is done during installation and/or update to a NetWeaver 7.0 EhP 3 based SAP product.<br />For further details on the manual steps read the information below.<br /><br />This additional support statement for the 720 kernel under NetWeaver 7.0Eh P 3 will expire by July 31st 2013. Please make sure that you have upgraded your systems to a fully validated operating system according to the SAP Product Availability Matrix (http://service.sap.com/pam) by then.<br /><br />If you want to apply for this additional support offering to run your SAP system on Linux x86_64 with the 720 kernel instead of the 720_EXT kernel, please open a customer message on component BC-OP-LNX with reference to this note.<br />In this customer message, please specify the following:</p> <UL><LI>Solution release (e.g. ERP 6.0 EhP 6, CRM 7.0 EhP 2 or other NetWeaver 7.0 EhP 3 based)</LI></UL> <UL><LI>Current and future database platform and release (e.g. DB/2 LUW, MaxDB, Oracle, Sybase)</LI></UL> <UL><LI>Current operating system (e.g. RHEL 5)</LI></UL> <UL><LI>Planned target operating system (e.g. RHEL 6, SLES 11 SP1) and planned date for OS upgrade (e.g. Q4/2012)</LI></UL> <UL><LI>Main reason why you request this additional support statement for the 720 kernel.</LI></UL> <UL><LI>Add a short notice that you acknowledge and agree that you will have to update your Linux operating system to a released DB/OS combination according to the PAM until the date specified above.<br /></LI></UL> <p>This note, together with your customer message, then serves as a support statement covering the missing PAM entry.<br />You can also ask additional questions if you feel uncertain about applicability of this note to your planned environment.<br /><br />Important remark: The additional support statement contained in this note allows you to run the respective SAP system under the same conditions on the Linux x86_64 platform with the 720 kernel like for other SAP releases listed in PAM. The process is not usable to obtain maintenance extension for running an SAP release outside of published SAP, Linux operating system and database support timeframes. Please use the communication channels communicated to you by SAP in order to apply for such maintenance extensions.<br /></p> <b>Remarks to implement the workaround</b><br /> <p>We expect that installation and/or upgrade activities are performed by experienced SAP technology consultants capable of resolving problems on their own by adapting existing standard documentation to the described approach.<br />SAP has tested the installation of and the update to NetWeaver 7.0 EhP 3 to run with the 720 SAP kernel on RHEL 5 and Oracle 11.2. We expect that following description can be applied to other products based on NetWeaver 7.0 EhP 3, like ERP 6.0 EhP 6 or CRM 7.0 EhP 1, or other Linux distributions as well. In case you had to adapt the description please let us know, e.g. by a <NAME_EMAIL>.<br /></p> <b>Installation of NetWeaver 7.0 EhP 3 on RHEL 5.x x86_64 and Oracle 11.2 together with SAP kernel 720:</b><br /> <UL><UL><LI>Download the following archives in the 720 version from the Service Marketplace: http://service.sap.com/patches<BR/> -&gt; Browse our Download Catalog<BR/> -&gt; Additional Components<BR/> &#x00A0;&#x00A0;-&gt; SAP Kernel<BR/> &#x00A0;&#x00A0;-&gt; SAP Kernel 64-Bit Unicode<BR/> &#x00A0;&#x00A0; -&gt; SAP Kernel 7.20 64-Bit Unicode<BR/> &#x00A0;&#x00A0; -&gt; Linux on x86_64 64bit<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Database independent<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;- SAPEXE....SAR<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Oracle<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;- DBATL720O10....SAR<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;- SAPEXEDB....SAR<BR/> -&gt; SAP Frontend Components<BR/> &#x00A0;&#x00A0;-&gt; SAP IGS<BR/> &#x00A0;&#x00A0;-&gt; SAP IGS 720<BR/> &#x00A0;&#x00A0; -&gt; Linux on x86_64 64bit<BR/> &#x00A0;&#x00A0; - igsexe....SAR<BR/> &#x00A0;&#x00A0; - igshelper....SAR<BR/> -&gt; SAP Technology Components<BR/> &#x00A0;&#x00A0;-&gt; SAP HOST AGENT<BR/> &#x00A0;&#x00A0; -&gt; SAP HOST AGENT 7.20<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;-&gt; Linux on x86_64 64bit<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0; -&gt; SAPHOSTAGENT....SAR</LI></UL></UL> <UL><UL><LI>Copy the sub-directory K_720_U_LINUX_X86_64 from your 720_EXT kernel medium (e.g. material number 510 419 87) to a local directory</LI></UL></UL> <UL><UL><LI>Exchange the following 720_EXT SAR archives with those from the 720 branch you just downloaded:</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/ORA/DBATOOLS.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/ORA/SAPEXEDB.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/DBINDEP/IGSEXE.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/DBINDEP/IGSHELPER.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/DBINDEP/SAPEXE.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt; K_720_U_LINUX_X86_64&gt;/DBINDEP/SAPHOSTAGENT.SAR<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Ensure to retain the original file name from the DVD (i.e. SAPEXE.SAR instead of SAPEXE_&lt;xx&gt;-&lt;yyy&gt;.SAR)</p> <UL><UL><LI>Start your installation via SAPinst as usual...</LI></UL></UL> <UL><UL><LI>If SAPinst fails running update statistics (taken from note 1642058):</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In the brconnect.log you see this message:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;brconnect: error while loading shared libraries: libclntsh.so.10.1: cannot open shared object file: No such file or directory<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Solution:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Create this link:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ln -s /oracle/client/11x_64 /oracle/client/10x_64<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Now retry the step.<br /></p> <b>Update to NetWeaver 7.0 EhP 3 on RHEL 5.x or SLES 10 x86_64 and Oracle</b><br /> <b>11.2 together with SAP Kernel 720:</b><br /> <p>For systems based on AS-ABAP:</p> <UL><UL><LI>As a prerequisite, patch your NW 7.0 EhP 2 source system to a patchlevel of SAP kernel 720 greater or equal than 200 before starting the update to NW 7.0 EhP 3.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For details where to download the correct archives please have a look at the description of the installation with kernel 720 above.</p> <UL><UL><LI>Prepare your update to NW 7.0 EhP 3 via Maintenance Optimizer until you reach roadmap step 2.9, called \"Select OS/DB-Dependent Files\". Here select only -&gt; Linux on x86_64<BR/> &#x00A0;&#x00A0;-&gt; SUM10SP03....SAR</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Do _not_ select any other binary archive, like SAPEXE.SAR!</p> <UL><UL><LI>Now generate your \"stack file\" (stack.xml) and proceed with the update as usual.</LI></UL></UL> <UL><UL><LI>Before starting SUM, please exchange &lt;path to SUM&gt;/SUM/abap/bin/ConfCheck.xml with the ConfCheck.xml attached to this note.</LI></UL></UL> <UL><UL><LI>Afterwards start SUM and conduct your update as usual.<br /></LI></UL></UL> <p>For systems based on AS-JAVA:</p> <UL><UL><LI>Please ensure that you select the \"HUB landscape pattern\" in Solution Manager when peparing the update, so your Java stack will stay on J2EE Engine 7.02.</LI></UL></UL> <UL><UL><LI>Furthermore ensure that you do only select the 720 kernel variant when downloading binary archives via Maintenance Optimizer.<br /></LI></UL></UL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-OP-LNX-RH (Red Hat Linux)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D036493)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031117)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001678024/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ConfCheck_RHEL5.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000087862012&iv_version=0005&iv_guid=68F53D44E257B8499266BF554BDCC1AF"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887"}, {"RefNumber": "171356", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/171356"}, {"RefNumber": "1642058", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Adapt SAPInst Oracle Client 11.2.0.x", "RefUrl": "/notes/1642058"}, {"RefNumber": "1591607", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel for SAP EhP 3 for SAP NetWeaver 7.0 and SAP EhP 1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1591607"}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102"}, {"RefNumber": "1553301", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1553301"}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2369910", "RefComponent": "BC-OP-LNX", "RefTitle": "SAP Software on Linux: General information", "RefUrl": "/notes/2369910 "}, {"RefNumber": "1496410", "RefComponent": "BC-OP-LNX-RH", "RefTitle": "Red Hat Enterprise Linux 6.x: Installation and Upgrade", "RefUrl": "/notes/1496410 "}, {"RefNumber": "936887", "RefComponent": "BC-OP-LNX", "RefTitle": "End of maintenance for Linux distributions", "RefUrl": "/notes/936887 "}, {"RefNumber": "1563102", "RefComponent": "BC-OP-LNX", "RefTitle": "Linux Requirements for 7.20 EXT and higher kernel", "RefUrl": "/notes/1563102 "}, {"RefNumber": "1642058", "RefComponent": "BC-DB-ORA-SYS", "RefTitle": "Adapt SAPInst Oracle Client 11.2.0.x", "RefUrl": "/notes/1642058 "}, {"RefNumber": "1591607", "RefComponent": "BC-CST", "RefTitle": "SAP Kernel for SAP EhP 3 for SAP NetWeaver 7.0 and SAP EhP 1 for SAP NetWeaver 7.3", "RefUrl": "/notes/1591607 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.21", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021235&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 32-BIT UNICODE", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021236&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021237&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 64-BIT UNICODE", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021238&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021405&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 64-BIT UC", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021406&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021283&V=MAINT"}, {"SoftwareComponentVersion": "SAP KERNEL 7.21 EXT 32-BIT UC", "SupportPackage": "SP002", "SupportPackagePatch": "000002", "URL": "https://me.sap.com/softwarecenter/template/products/_APP=00200682500000001943&_EVENT=DISPHIER&HEADER=Y&FUNCTIONBAR=N&EVENT=TREE&NE=NAVIGATE&ENR=67837800100200021284&V=MAINT"}]}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}