{"Request": {"Number": "49701", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 476, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014454022017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000049701?language=E&token=87D5089E5EC373D7B64045779A28AF1D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000049701", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000049701/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "49701"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 19}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.10.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation and Upgrade AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation and Upgrade AS/400", "value": "BC-INS-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "49701 - iSeries: Info and recommendations for kernel libraries (46D)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Recommendations concerning the R/3 kernel libraries.<br />You have installed more than one R/3 system on AS/400 and you require information about the various useful options available when installing kernel libraries.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS/400, kernel, R3&lt;REL&gt;OPT, library, LODR3KRN, APYR3KRN, RMVR3KRN, APYR3FIX.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><OL>1. <B>What is a kernel?</B><br /><br />An SAP kernel on AS/400 is found in a program library.<br />The library contains programs that are all optimized, and therefore offer the best performance possible. These are also referred to as optimized kernels.<br />In the subdirectory \"opt\" in directory /usr/sap/&lt;SID&gt;/SYS/exe, there are links to the program objects in the library. These links are used by R/3 under AS/400, for example when starting programs.<br /></OL> <OL>2. <B>What do I name the libraries?</B><br /><br />You can define the name of the kernel library freely. Names are suggested according to the template R3&lt;REL&gt;OPT. &lt;REL&gt; stands for the R/3 release, resulting, for example, in 45A with the library name R345FOPT.<br />Template R3&lt;SID&gt;OPT is an alternative to this, but cannot be adhered to in an upgrade, as an &lt;SID&gt; temporarily has 2 kernels available: the one from the start release and the new kernel from the target release. You can then include the release in the name, for example, R3 &lt;SID&gt;O&lt;REL&gt; for the optimized kernel.<br /></OL> <OL>3. <B>Should I only use one kernel for several R/3 systems?</B><br /><br />In both an installation and in an upgrade, it is possible to reuse existing kernel libraries from the same release. Consequently, several R/3 systems use the same kernel (variant A).<br />This saves time during the installation and upgrade, as well as saving disk space. However, changes to kernel objects take immediate effect for all R/3 systems. This is the case if you import program corrections, for example.<br />When you upgrade the systems, there must be two kernels on AS/400 until all of the R/3 systems have the latest release. In the last upgrade, the \"old\" kernel is found to be redundant and is then deleted.<br /></OL> <OL>4. <B>Should I use a separate kernel for each R/3 system?</B><br /><br />In both an installation and in an upgrade, it is possible to install the kernel libraries. Consequently, each R/3 system can use its own kernel (variant B).<br />The import time saved is countered by the extra time taken to import and test program corrections for each R/3 system, without changing the other R/3 systems.<br />When upgrading the systems, the new kernel is imported in each case and the old kernel is deleted, as there is no other R/3 system that uses the old kernel.<br /></OL> <OL>5. <B>For example: Test system TST and production system PRD</B><br /></OL> <UL><UL><LI>Variant A: TST and PRD use separate kernels. We suggest you name the kernel library R3&lt;REL&gt;OPT.</LI></UL></UL> <UL><UL><LI>Variant B: TST and PRD use separate kernels. We suggest you name the kernels R3&lt;SID&gt;O&lt;REL&gt;.<br /></LI></UL></UL> <OL>6. <B>Using APYR3FIX to import kernel corrections</B><br /><br />The command APYR3FIX transfers save files from SAP to the customer system and then uses the commands UPDPGM or UPDSRVPGM to transfer the corrections to your kernel library.<br />Since the transfer may take a long time and the kernel library cannot be used by other jobs on AS/400 while this is happening, you must stop the R/3 system or you have to work with a copy of the kernel library.</OL> <UL><UL><LI><B>Creating a copy:</B> Save the kernel library in a save file (using the command SAVLIB) and, afterwards, use the command LODR3KRN to resave the kernel library under a different name. Other copy methods (using command RSTLIB or CPYLIB) result in the loss of object authorizations, which can, however, be corrected with QSECOFR by calling CALL PGM (&lt;new_lib&gt;/FIXR3OWNS) PARM(&lt;new_lib&gt; *ALL) or, as of Release 4.6B, using command FIXR3OWNS LIB (&lt;new_lib&gt;) OBJ (*ALL).</LI></UL></UL> <UL><UL><LI><B>Importing corrections first to the copy:</B> Enter the name of the copy in the KRNLIB parameter of the APYR3FIX command.</LI></UL></UL> <UL><UL><LI><B>Using the copy for the R/3 system:</B> First use the command RMVR3KRN to remove the original kernel. For this, you have to stop the R/3 system. Now, delete or rename the original and give the copy the required name (RNMOBJ). Use the command APYR3KRN to activate the copied, corrected kernel. Restart the R/3 system. This will take a couple of minutes.<br /></LI></UL></UL> <OL>7. <B>What are the standard names for SAP kernels?</B><br /><br />Many SAP tools for kernel processing require a standard name (for example parameter SAVLIB in the LODR3KRN or APYR3FIX commands). These names follow a simple pattern:<br />R3&lt;rel&gt;OPT&#x00A0;&#x00A0;- when downloading a kernel (LODR3KRN)<br />GEN&lt;rel&gt;OPT - when importing patches (APYR3FIX)<br />For the abbreviation &lt;rel&gt; further rules apply depending on the SAP release. For Releases 31I, 40B and 45B, there is only type EBCDIC. The release name is therefore used without being changed. As of SAP Release 46D, as well as EBCDIC, there is also the ASCII variant. The letters E and A have therefore been added to the release name.<br />Here are some examples:<br />R331IOPT&#x00A0;&#x00A0; - for LODR3KRN in SAP Release 31I<br />GEN40BOPT&#x00A0;&#x00A0;- for APYR3FIX in SAP Release 40B<br />R346DEOPT&#x00A0;&#x00A0;- for LODR3KRN in SAP Release 46D EBCDIC<br />GEN46DAOPT - for APYR3FIX in SAP Release 46D ASCII</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Operating system", "Value": "OS/400"}, {"Key": "Database System", "Value": "DB2/400"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000049701/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000049701/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    DB2/400", "RefUrl": "/notes/99379"}, {"RefNumber": "912575", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Using LODSAPKRN to load a 7.00 kernel", "RefUrl": "/notes/912575"}, {"RefNumber": "904977", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Support Package stacks for SAP Kernel 6.40", "RefUrl": "/notes/904977"}, {"RefNumber": "751132", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How to apply a 640 Kernel", "RefUrl": "/notes/751132"}, {"RefNumber": "751131", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How to apply a kernel patch in releases 6.40/7.00", "RefUrl": "/notes/751131"}, {"RefNumber": "68727", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1G       DB2/400", "RefUrl": "/notes/68727"}, {"RefNumber": "64812", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.0F       DB2/400", "RefUrl": "/notes/64812"}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to 4.6C - DB2/400", "RefUrl": "/notes/202169"}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823"}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6A - DB2/400", "RefUrl": "/notes/162117"}, {"RefNumber": "1310133", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Loading a 7.01 kernel using LODSAPKRN", "RefUrl": "/notes/1310133"}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751"}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637"}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600"}, {"RefNumber": "1097599", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (640)", "RefUrl": "/notes/1097599"}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5A       DB2/400", "RefUrl": "/notes/108377"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "99379", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.0B    DB2/400", "RefUrl": "/notes/99379 "}, {"RefNumber": "108377", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 4.5A       DB2/400", "RefUrl": "/notes/108377 "}, {"RefNumber": "162117", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6A - DB2/400", "RefUrl": "/notes/162117 "}, {"RefNumber": "178823", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to 4.6B - DB2/400", "RefUrl": "/notes/178823 "}, {"RefNumber": "202169", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrade to 4.6C - DB2/400", "RefUrl": "/notes/202169 "}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637 "}, {"RefNumber": "1097751", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Information and recommendations for kernel libraries", "RefUrl": "/notes/1097751 "}, {"RefNumber": "913001", "RefComponent": "BC-OP-AS4", "RefTitle": "Description of tool APYSAP", "RefUrl": "/notes/913001 "}, {"RefNumber": "1310133", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Loading a 7.01 kernel using LODSAPKRN", "RefUrl": "/notes/1310133 "}, {"RefNumber": "68727", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.1G       DB2/400", "RefUrl": "/notes/68727 "}, {"RefNumber": "912575", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Using LODSAPKRN to load a 7.00 kernel", "RefUrl": "/notes/912575 "}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600 "}, {"RefNumber": "1097599", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (640)", "RefUrl": "/notes/1097599 "}, {"RefNumber": "751131", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How to apply a kernel patch in releases 6.40/7.00", "RefUrl": "/notes/751131 "}, {"RefNumber": "904977", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: Support Package stacks for SAP Kernel 6.40", "RefUrl": "/notes/904977 "}, {"RefNumber": "751132", "RefComponent": "BC-OP-AS4", "RefTitle": "iSeries: How to apply a 640 Kernel", "RefUrl": "/notes/751132 "}, {"RefNumber": "64812", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Info: Upgrading to 3.0F       DB2/400", "RefUrl": "/notes/64812 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}