{"Request": {"Number": "1326381", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 457, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016767872017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001326381?language=E&token=0B8088B050DADE7BBF27DD66AA920E5F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001326381", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1326381"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.04.2009"}, "SAPComponentKey": {"_label": "Component", "value": "BC-EHP-INS-TLA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Please use BC-UPG-TLS-TLA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enhancement Package", "value": "BC-EHP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-EHP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use BC-UPG-TLS*", "value": "BC-EHP-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-EHP-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Please use BC-UPG-TLS-TLA", "value": "BC-EHP-INS-TLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-EHP-INS-TLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1326381 - Attribute Change Packages (ACP) do not work in the EHPI"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The calculation of the queue of the packages that are to be included in the Enhancement Package upgrade fails in the Enhancement Package Installer (EHPI) because the implementation attributes are not fulfilled. SAP Support or notes request that you load an Attribute Change Package (ACP) in your system to modify the implementation attributes. Even though the ACP exists, the queue calculation still fails.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Enhancement Package Installer, EHPI, ACP, Attribute Change Package, EHP_INCLUSION<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>In the EPS inbox (in the directory $DIR_EPS_ROOT/in, for example, /usr/sap/trans/EPS/in), several versions (different .PAT files) of an ACP exist. Due to an error in the upload function in the EHPI, the system may load the attributes and data from an old version of an ACP; as a result, the system may not be able to completely correct or modify the implementation attributes.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This error is corrected in a newer version of the EHPI.<br />Until the new version is available, as a workaround, you must delete the old versions of the ACPs from the EPS inbox. Proceed as follows:</p> <UL><LI>Call transaction SEPS and choose \"Goto -&gt; Inbox\" to access the EPS inbox.</LI></UL> <UL><LI>Place the cursor in the \"Subject\" column and choose \"Sort\" to sort the table.</LI></UL> <UL><LI>Scroll through the list and check whether several entries (with the same subject) exist for ACPs. You can identify ACPs by their names (&lt;COMP_ID&gt;=&lt;COMP_REL&gt;, for example, EA-DFPS===600 or SAP_APPL==600; see Note 1119856).</LI></UL> <UL><LI>If several entries exist for an ACP, select all of the entries except for the entry with the highest number in the \"Number\" column.</LI></UL> <UL><LI>Choose \"Delete\" to delete the selected packages; the status then changes to \"D\".</LI></UL> <p><br />After you delete the obsolete ACP versions, you must ensure that the system can perform the upload in the EHPI to ensure that the correct ACP data is loaded. If the queue calculation terminated in the phase EHP_INCLUSION, it is sufficient to simply continue; when the system repeats the phase, it performs the upload automatically. However, if the queue calculation terminated in the phase ADDONQCALC, you must repeat the \"Configuration\" step from the beginning. To do this, choose \"Exit\" to cancel the phase. Choose \"Back\" to return to the end of the previous step and then choose \"Next\" to start the \"Configuration\" step.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-UPG-OCS-SPA (Support package tools for ABAP)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D028597)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D019419)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001326381/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001326381/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1119856", "RefComponent": "BC-UPG-OCS-SPA", "RefTitle": "Description, tips and tricks for Attribute Change Packages", "RefUrl": "/notes/1119856 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}