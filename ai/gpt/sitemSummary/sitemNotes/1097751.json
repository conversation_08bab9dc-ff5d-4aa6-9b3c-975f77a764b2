{"Request": {"Number": "1097751", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2457, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016379542017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001097751?language=E&token=C1F79106E92EB795F9FDF356274B7240"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001097751", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001097751/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1097751"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 20}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.12.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-OP-AS4"}, "SAPComponentKeyText": {"_label": "Component", "value": "IBM AS/400"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operating System Platforms", "value": "BC-OP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IBM AS/400", "value": "BC-OP-AS4", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-OP-AS4*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1097751 - IBM i: Information and recommendations for kernel libraries"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are searching for recommendations concerning the R/3 kernel libraries of your R/3 system.<br />You have installed more than one SAP system on an iSeries, and you require information about the various options to install kernel libraries or load kernels.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AS/400, OS400, System i, i5/OS, iSeries, IBM i</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You require information.<br />Caution: This SAP Note is valid only for kernels as of Version 7.10 and not for older kernels.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>1. What is a kernel?</strong></p>\r\n<p>An SAP kernel on an iSeries is a collection of PASE programs in the IFS (executable AIX binary files in the file system) and an assigned ILE library (with 'classic' IBM i objects such as CMD, PGM, SRVPGM, and so on). All programs of a kernel have been optimized and offer the best possible performance. These are also referred to as optimized kernels.</p>\r\n<p>The kernel objects are saved to the directory /sapmnt/&lt;SID&gt;/exe when the kernel is imported with APYSIDKRN (see the related notes). In this case, the ILE parts are copied from the IFS file ILE_TOOLS to a library (*LIB).</p>\r\n<p>When you activate the SAPCPE procedure (see SAP Note 1632754 for a system with SAP NetWeaver 7.0 and its enhancement packages - and, by default, with NetWeaver 7.1), program SAPCPE transfers the kernel objects to the directory /usr/sap/&lt;SID&gt;/&lt;INSTANCE&gt;/exe during the system start; in the program SAPCPE, the ILE objects are unpacked into an assigned *LIB. The programs in this directory and the assigned *LIB are used by the SAP system on the iSeries only by the relevant instance.</p>\r\n<p><strong>2. How do I name the libraries?</strong></p>\r\n<p>You <strong>cannot</strong> choose any name for the runtime library; it is assigned by the program APYSIDKRN. This name cannot be changed.</p>\r\n<p>The name of the LIB created by APYSIDKRN for the instance-independent directory is SAP&lt;SID&gt;&lt;IND&gt; (for example, SAPQASIND) - in older kernel versions SAP&lt;SID&gt;71 (for example, SAPQAS71) -, where &lt;SID&gt; is the name of the SAP system (for example, QAS). The ILE library created by the program SAPCPE is called SAP&lt;SID&gt;I&lt;INSTNR&gt; (for example, SAPQASI00) - in older kernel versions SAP&lt;SID&gt;71&lt;INSTNR&gt; (for example, SAPQAS7100) -, where &lt;INSTNR&gt; represents the number of the relevant SAP instance (for example, 00).</p>\r\n<p>In this case, kernel library only refers to the name of the LIB that contains the ILE elements of the SAP kernel. These elements are only a small part of the complete SAP kernel.</p>\r\n<p><strong>3. Should I use only one kernel for several SAP systems?</strong></p>\r\n<p>No, this option is not supported by SAP.</p>\r\n<p><strong>4. Should I use a separate kernel for each SAP system?</strong></p>\r\n<p>Only this option is supported by APYSIDKRN because as of NetWeaver 7.10, each instance has its own kernel in the instance directory.</p>\r\n<p><strong>5. Implementing kernel corrections with APYSIDKRN</strong></p>\r\n<p>The command APYSIDKRN transfers SAP archives (SAR files) to the corresponding subdirectory of the central SAP kernel directory of a SAP system (&lt;SID&gt;) /sapmnt/&lt;SID&gt;/exe that were previously copied from a DVD or from SAP Service Marketplace to the IFS. Since the files are not initially transferred to the instance directory when you use the SAPCPE procedure, the SAP kernel can still be used on the instances of the SAP system during the transfer; you do not need to shut down the SAP system during this period. If SAPCPE is not activated (on this topic see Note 1632754), you have to close the instances of the SAP system before you implement the kernel corrections.</p>\r\n<p>You import SAR archives with APYSIDKRN in mode MODE(*ADD). The parts of the archives assigned to the command are added to the existing kernel directory or overwrite older programs with the same names without emptying the kernel directory first. When you apply patches, you must use the parameter MODE(*ADD).</p>\r\n<p>You can use the parameter MODE(*TEST) to test which effects the actual application of a patch using MODE(*ADD) will have.</p>\r\n<p>In addition, when you use the APYSIDKRN procedure to apply patches in the parameter SAVSAR, you can assign the name of a directory into which the current content of the kernel directory is copied prior to applying the patches. If an error occurs, you can import this archive to restore the status to the status prior to applying the patches (see SAP Note 1432807). You can use the following call as an example for the command APYSIDKRN. For more information, use the online help (F1) of the command: ?APYSIDKRN SID(&lt;PRD&gt;)</p>\r\n<p><br />&#x00A0;ARCHIVES('/home/<USER>/&lt;ARCHIVE&gt;')</p>\r\n<p>&#x00A0;CARPATH('/home/<USER>')</p>\r\n<p>&#x00A0;MODE(*ADD)</p>\r\n<p><strong>6. Implementing a backwards compatible kernel (AKK) with APYSIDKRN</strong></p>\r\n<p>The procedure to install, for example, EXT Kernel 7.22 into a system that previously used Kernel 7.21, or Kernel 7.53 into a system that was previously operated with Kernel 7.49, is slightly more elaborate:</p>\r\n<p>a) Log on as a QSECOFR-type user and execute the following commands or actions:</p>\r\n<p>b) Switch to the directory containing the downloaded archive and SAPCAR if SAPCAR cannot be found using the environment variable PATH:</p>\r\n<p>CHGCURDIR '&lt;newkernel&gt;'</p>\r\n<p>c) If the library SAP_TOOLS exists, execute a cleanup: CLRLIB SAP_TOOLS</p>\r\n<p>d) The library SAP_TOOLS is filled with data from the save file ILE_TOOLS using the program \"iletools\". The archive SAPEXE.SAR and the archive ILE.SAR contain this program and the save file. Use the archive with the higher patch level.</p>\r\n<p>You can use copy and paste to copy and execute one of the following commands in your session:</p>\r\n<p>If you use SAPEXE.SAR:</p>\r\n<p>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf SAPEXE.SAR iletools ILE_TOOLS; ./iletools')</p>\r\n<p>If you use ILE.SAR:</p>\r\n<p>CALL QP2SHELL PARM('/QOpenSys/usr/bin/csh' '-c' 'SAPCAR -xvf ILE.SAR iletools ILE_TOOLS; ./iletools')</p>\r\n<p>e) Since this call to QP2SHELL produces no output, check whether the library SAP_TOOLS exists and contains some objects; if it does not, you should Sie use the WRKSPLF command to search for spool files with error messages.</p>\r\n<p>f) You can now use the following commands to set the authorizations of objects in SAP_TOOLS:</p>\r\n<p>If you already use the new user concept ('newuserconcept'), execute the following steps:</p>\r\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('N')</p>\r\n<p>ADDLIBLE SAP_TOOLS</p>\r\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\r\n<p>If you still use the old user concept ('classicuserconcept'), execute the following steps:</p>\r\n<p>ADDENVVAR ENVVAR(CLASSICUSERCONCEPT) VALUE('Y')</p>\r\n<p>ADDLIBLE SAP_TOOLS</p>\r\n<p>FIXSAPOWN *NONE SAP_TOOLS</p>\r\n<p>g) Log on to the system as the user &lt;SID&gt;ADM and execute the following commands:</p>\r\n<p>ADDLIBLE SAP_TOOLS</p>\r\n<p>If you want to test the kernel installation in advance, you can use *TEST mode. Use the following command to test the intended kernel installation:</p>\r\n<p>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*NONE) MODE(*TEST) CHGENV(*NO) UPDAPAR(*NO)</p>\r\n<p>Then, install the new kernel with the following command:</p>\r\n<p>APYSIDKRN SID(&lt;SAPSID&gt;) ARCHIVES('&lt;newkernel&gt;/*') SAVSAR(*STANDARD) MODE(*ADD) CHGENV(*YES) UPDAPAR(*NO)</p>\r\n<p>h) Delete the SQL packages that remain from using the old kernel:</p>\r\n<p>DLTR3PKG SID(&lt;SAPSID&gt;)</p>\r\n<p>i) Log off and log on again as the user &lt;SID&gt;ADM. You now use the new kernel.</p>\r\n<p>The implemented corrections only take effect after you stop the SAP system and restart it. When the system is restarted, all programs in the central directory /sapmnt/&lt;SID&gt;/exe that are newer than the programs in the instance directory are copied here by the program SAPCPE; if necessary, the ILE objects of the assigned *LIB are also created again.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D042520)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (********)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001097751/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001097751/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701"}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173"}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755"}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754"}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807"}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637"}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600"}, {"RefNumber": "1097599", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (640)", "RefUrl": "/notes/1097599"}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3156866", "RefComponent": "BC-CST", "RefTitle": "Using Kernel 7.54 instead of Kernel 7.40, 7.41, 7.42, 7.45, 7.49 or 7.53", "RefUrl": "/notes/3156866 "}, {"RefNumber": "2556153", "RefComponent": "BC-CST", "RefTitle": "Using Kernel 7.53 instead of Kernel 7.40, 7.41, 7.42, 7.45, or 7.49", "RefUrl": "/notes/2556153 "}, {"RefNumber": "2251972", "RefComponent": "BC-CST", "RefTitle": "Using kernel 7.45 instead of kernel 7.40, 7.41, or 7.42", "RefUrl": "/notes/2251972 "}, {"RefNumber": "2128122", "RefComponent": "BC-CST", "RefTitle": "Use of 7.42 kernel instead of 7.40 or 7.41 kernel", "RefUrl": "/notes/2128122 "}, {"RefNumber": "19466", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Downloading SAP kernel patches", "RefUrl": "/notes/19466 "}, {"RefNumber": "1713986", "RefComponent": "BC-CST", "RefTitle": "Installation of kernel 721 (EXT)", "RefUrl": "/notes/1713986 "}, {"RefNumber": "1994690", "RefComponent": "BC-CST", "RefTitle": "Using the 7.41 kernel instead of 7.40", "RefUrl": "/notes/1994690 "}, {"RefNumber": "1636252", "RefComponent": "BC-CST", "RefTitle": "Installing a 7.20 kernel in SAP Web AS 7.00/7.01/7.10/7.11", "RefUrl": "/notes/1636252 "}, {"RefNumber": "1632755", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Description of command APYSIDKRN", "RefUrl": "/notes/1632755 "}, {"RefNumber": "1097637", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Copying an SAP kernel (kernel version 7.10/7.11/7.20)", "RefUrl": "/notes/1097637 "}, {"RefNumber": "1078134", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Distribution of ILE and PASE system components", "RefUrl": "/notes/1078134 "}, {"RefNumber": "1632754", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Changeover to instance-specific directory", "RefUrl": "/notes/1632754 "}, {"RefNumber": "1432807", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Applying a saved kernel using APYSIDKRN", "RefUrl": "/notes/1432807 "}, {"RefNumber": "1687173", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: Collection of notes about the 7.20 kernel", "RefUrl": "/notes/1687173 "}, {"RefNumber": "1097600", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (700)", "RefUrl": "/notes/1097600 "}, {"RefNumber": "1097599", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations on kernel libraries (640)", "RefUrl": "/notes/1097599 "}, {"RefNumber": "49701", "RefComponent": "BC-INS-AS4", "RefTitle": "iSeries: Info and recommendations for kernel libraries (46D)", "RefUrl": "/notes/49701 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "KRNL32NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL32UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64NUC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.10", "To": "7.20", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.20EXT", "To": "7.20EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21", "To": "7.21", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.21EXT", "To": "7.21EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.22EXT", "To": "7.22EXT", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KRNL64UC", "From": "7.53", "To": "7.53", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.10", "To": "7.11", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.20", "To": "7.22", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.40", "To": "7.40", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.41", "To": "7.41", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.42", "To": "7.42", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.45", "To": "7.45", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.49", "To": "7.49", "Subsequent": ""}, {"SoftwareComponent": "KERNEL", "From": "7.53", "To": "7.53", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}