{"Request": {"Number": "2189861", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 344, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018121732017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002189861?language=E&token=24A734FD146ABE8E78207AB3C30FCBC1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002189861", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002189861/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2189861"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.11.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BW-PLA-IP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Integrated Planning"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Planning", "value": "BW-PLA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Integrated Planning", "value": "BW-PLA-IP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-PLA-IP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2189861 - Details and Condition for Planning on native HANA views"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p><em>You want to use&#160;native HANA views modeled&#160;in&#160;VirtualProvider, CompositeProvider&#160;or InfoObject&#160; for planning scenarios</em></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BW-IP, Planning ,&#160;&#160;PAK, HANA view,&#160;Program Error: LCL_IS_EMPTY_HANDLER and FORM 0001 _SYS_PLE:yyyymmddhhmmss_nnnnnnn:DATA no DDIC(BRAIN-299, XXX), BRAIN 154</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are at least on BW 7.4 SP9. You want to use native HANA views together&#160;for planning scenarios either in InfoObjects based own implementation of type HANA view or on virtual provider based on HANA views or with composite provider containing native HANA views.&#160;Since the HANA views are not controlled by <PERSON><PERSON>,&#160;the values are not known on the BW layer and necessary default values or the fact that SID values exist in the corresponding SID tables cannot be ensured. In addition the HANA view itself has to guarantee that an initial value (internal representation)&#160;does exist. This includes also compounded info objects. Also no NULL values&#160;must be returned from the HANA views but have to be converted into the initial values. In general we do not recommend to use this widely as customer because&#160;if those prerequisites are not ensured it can cause errors in planning which are hard to be tracked. In general such scenarios are only adopted by application like SAP simplified financials or retail which work in close relationship with SAP BW to ensure the consistency in those specific scenarios.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>\r\n<li>Ensure that no NULL values are returned from the HANA views&#160;but the type correct initial values (internal representation e.g. '00000000' for DATS, TIMS = '000000' and &#160;NUMC = '000..' with length &lt; 32). In case of errors always use user parameter RSPLS_HDB_PE_TRACE = Y&#160;and RSPLS_HDB_RUNMODE &gt;= 8.</li>\r\n<li>Ensure that each master data view has to add the initial&#160;value for unassigned values '#' by itself. This includes in compounded cases also each combination of an unassigned '#' child and it parents.<br />This can be achieved through having a transparent table in the SAP BW with the following structure:<br />FIELD &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; KEY &#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; DESCRIPTION<br />LANGU&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; X&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Language Key<br />NAME&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; General Name<br />TEXT&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Long Text<br />Fill the table with &#8220;not assigned&#8221; in all relevant languages. e.g. &#8220;E&#8221;, &#8220;not assigned&#8221;, &#160;&#8220;not assigned&#8221;<br />This table needs to be joined as a union in SAP HANA to the corresponding master data view.</li>\r\n<li>The corresponding&#160;SID values&#160;to the master data values of the HANA views must be guaranteed. Depending on your scenario it might be possible to ensure it through the reference data selected.&#160;In this case the provider specific properties of all HANA view based info object in the virtual provider is not flagged as referential integer (flag 'Ref. Integrity (HANA) is empty)&#160;. If the reference data&#160;is read from a HANA view&#160;in the composite provider to on the output tab and&#160;make sure that the flag 'Referential Integrity' is not set&#160;for each InfoObject. In other cases the report RSPLS_CREATE_MISSING_SIDS or similar functionality have to be run frequently to ensure all possible values have corresponding SID values.</li>\r\n<li>See also referenced notes for corrections. We strongly recommend to use at least BW 7.4 SP9 due to larger notes prior SP9.&#160;</li>\r\n<li>The usage of ATRV on HANA views in FOX requires note 2644984 and as minimal revision&#160;&#160;2.00.030.01<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA; mso-ascii-theme-font: minor-latin; mso-hansi-theme-font: minor-latin; mso-bidi-theme-font: minor-bidi;\">.</span></li>\r\n<li>Notes 2731945 and 2735151 are required for unit conversion planning function (0RSPL_CURR_CONV) that uses a unit conversion type of dynamically determined conversion factor by using a reference InfoObject with master data in hana view.</li>\r\n<li>Known limitations are</li>\r\n<ul>\r\n<li>Usage of navigational attributes on info objects based on HANA views in direct update DSO of type planning is not supported. They only work in real time cubes (In 7.5, Note 2424079 is needed<span style=\"font-size: 11pt; font-family: 'Calibri',sans-serif; mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: 'Times New Roman'; mso-ansi-language: DE; mso-fareast-language: EN-US; mso-bidi-language: AR-SA;\">).</span> See also note 2479940 for general restrictions here.</li>\r\n<li>The combination of NO2 key figures and navigational attribute is supported on aDSO from 7.50 SP8 onwards.&#160;Each feature can be only be used separately before.</li>\r\n<li>Attribute Planning on aDSO is&#160;supported with HANA views</li>\r\n<li>Note: Master data&#160;in BW are not fully time dependent. Only attributes and text can be time dependent. Therefore fully time dependent HANA views are not allowed. Or in other words the resulting keys of HANA views returning master data must be stable against time parameter.</li>\r\n</ul>\r\n<li>\r\n<p>To use HANA view with parameters in HCPR for planning purpose, the parameters have to be mandatory and to be replaced by a non-initial value. Planning scenarios using such a HANA view cannot use PAK (Planning Applications Kit).<br />Aggregation levels defined on a HANA CompositeProvider (HCPR) with HANA views as part providers containing optional parameters and other base planning InfoProviders as DataStore-Objects (advanced) or InfoCubes may lead to the following symptoms:<br />&#160;&#160;&#160; Input-ready queries on these aggregation levels cannot be input-ready<br />&#160;&#160;&#160; Planning functions may fail with error BRAIN154.&#65279;</p>\r\n</li>\r\n</ol>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "EPM-BPC-BW4-PLA (BPC/4 - Planning Enginee)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029075)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022583)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002189861/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002189861/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2735151", "RefComponent": "EPM-BPC-BW4-PLA-PF", "RefTitle": "Einheitenumrechnung: PE Fehler bei Hana View Merkmalen", "RefUrl": "/notes/2735151"}, {"RefNumber": "2731945", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Internal error while generating dynamic SQLs - GET_BASE_UOM-01 in Unit Conversion Planning Function in ABAP Mode", "RefUrl": "/notes/2731945"}, {"RefNumber": "2708611", "RefComponent": "BW-PLA-IP", "RefTitle": "Performance: Input-ready query and navigation attributes on basis of SAP HANA views", "RefUrl": "/notes/2708611"}, {"RefNumber": "2707223", "RefComponent": "BW-PLA-IP", "RefTitle": "Performance: CL_RSR_FIPT_NAV_ATTR", "RefUrl": "/notes/2707223"}, {"RefNumber": "2644984", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "FOX functions - execution on SAP HDB - release of features", "RefUrl": "/notes/2644984"}, {"RefNumber": "2479940", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Restrictions for InfoObjects based on SAP HANA model", "RefUrl": "/notes/2479940"}, {"RefNumber": "2424079", "RefComponent": "BW-PLA-IP", "RefTitle": "BW PAK: Infocube with navigation attributes based on HANA views", "RefUrl": "/notes/2424079"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2518331", "RefComponent": "BW-PLA-BPC-TDL", "RefTitle": "BPC Consolidation fails with LCL_IS_EMPTY_HANDLER_TRANSFORM CL_RSR_FIPT_VAL2SID->LOOKUP-01- - BP<PERSON> embedded", "RefUrl": "/notes/2518331 "}, {"RefNumber": "2471784", "RefComponent": "BW-PLA-IP", "RefTitle": "Input ready query hits exception of CL_RSR_FIPT_VAL2SID->LOOKUP-01- when saving data", "RefUrl": "/notes/2471784 "}, {"RefNumber": "2471688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Navigation Attribute based on HANA Model displays as 'not assigned' in a BW query", "RefUrl": "/notes/2471688 "}, {"RefNumber": "2196318", "RefComponent": "BW-PLA-IP", "RefTitle": "Error message CL_RSR_FIPT_VAL2SID->LOOKUP-01-", "RefUrl": "/notes/2196318 "}, {"RefNumber": "2731945", "RefComponent": "BW-PLA-IP-PF", "RefTitle": "Internal error while generating dynamic SQLs - GET_BASE_UOM-01 in Unit Conversion Planning Function in ABAP Mode", "RefUrl": "/notes/2731945 "}, {"RefNumber": "2347000", "RefComponent": "BW-PLA-IP", "RefTitle": "Compounding in SID generation report", "RefUrl": "/notes/2347000 "}, {"RefNumber": "1637199", "RefComponent": "BW-PLA-IP", "RefTitle": "Using the planning applications KIT (PAK)", "RefUrl": "/notes/1637199 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}