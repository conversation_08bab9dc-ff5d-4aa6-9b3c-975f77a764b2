{"Request": {"Number": "2267333", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 345, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018242782017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002267333?language=E&token=4B62D6C9946D7E8C8F6997831F0A5327"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002267333", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002267333/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2267333"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "PS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Project System"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Project System", "value": "PS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2267333 - S4TWL - Selected PS Business Function Capabilities"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are doing a system conversion from SAP&#160;ERP&#160;to SAP&#160;S/4HANA&#160;or an upgrade from a lower to a higher SAP&#160;S/4HANA&#160;release and are using the functionality described in this note. The following SAP&#160;S/4HANA&#160;Simplification Item is applicable in this case.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP&#160;S/4HANA, Compatibility Scope, System Conversion, Upgrade</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Description</strong></p>\r\n<p>Selected PS Business Function capabilities&#160;are part of the SAP&#160;S/4HANA&#160;compatibility scope, which comes with limited usage rights. For more details on the compatibility scope and it&#8217;s expiry date and links to further information please refer to SAP note 2269324. In the compatibility matrix attached to SAP note 2269324,&#160;selected PS Business Function capabilities can be found under the ID 468.</p>\r\n<p>As of SAP S/4HANA 1511 - FPS1 the following business functions are switchable but obsolete:</p>\r\n<ul>\r\n<li>OPS_PS_CI_3</li>\r\n<li>LOG_XPD_EXT_1&#160;</li>\r\n</ul>\r\n<p>The following PS business functions are part of the compatibilty scope</p>\r\n<ul>\r\n<li>OPS_PS_CI_1</li>\r\n<li>OPS_PS_CI_2</li>\r\n</ul>\r\n<p>&#160;The following transactions are hence categorized not to be the target architecture:</p>\r\n<ul>\r\n<li>CN08CI&#160;Config. of Commercial Proj Inception</li>\r\n<li>CNACLD PS ACL&#160;Deletion Program transaction</li>\r\n<li>CNSKFDEF SKF defaults for project elements</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p><strong><em>Basic Information related to Business Functions</em></strong></p>\r\n<p><em>If a business function was switched &#8220;ON&#8221; in the Business Suite start release system, but defined as &#8220;always_off&#8221; in the SAP S/4HANA, on-premise edition target release, then a system conversion is not possible with this release. See SAP Note 2240359 - SAP S/4HANA, on-premise edition 1511: Always-Off Business Functions. If a business function is defined as \"customer_switchable\" in the target release (SAP S/4HANA, on-premise edition 1511 &#8211; FPS1), then the business function status remains unchanged from the status on start release. Business Functions defined as &#8220;obsolete&#8221; cannot be switched &#8220;ON&#8221;</em></p>\r\n<p><strong>Business Process related information</strong></p>\r\n<p>Customer having one or all of these business functions switched &#8220;ON&#8221; in Business Suite start release can execute the system conversion. Customers having none of these business functions switched &#8220;ON&#8221; in Business Suite start release cannot activate the obsolete&#160;business functions after the system conversion to SAP S/4 HANA 1511.</p>\r\n<p>The Business Function&#160;LOG_XPD_EXT_1 was flagged as obsolete as of SAP S/4HANA 1511 - FPS1, the flag got removed with SAP S/4HANA 2022 FPS2. The functionality covered in the business function has been modernized along with Progress Tracking in SAP S/4HANA 2022. With this modernization, the functionality becomes part of the perpetual scope of SAP S/4HANA.</p>\r\n<p>The Business Functions OPS_PS_CI_1 and OPS_PS_CI_2 are available and selectable with SAP S/4HANA 1511.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<p><strong>Modernized topics</strong></p>\r\n<p><strong>Project Builder enhancements</strong></p>\r\n<p>Project Builder enhancements consist of</p>\r\n<ul>\r\n<li>Display of archived projects&#160;(OPS_PS_CI_1)</li>\r\n<li>Intermediate save&#160;(OPS_PS_CI_1)</li>\r\n<li>Display of more than 5 projects in work list&#160;(OPS_PS_CI_1)</li>\r\n<li>Performance Improvements in the Project Builder and Network Transactions (OPS_PS_CI_2)</li>\r\n<li>Collective purchase requisitions&#160;(OPS_PS_CI_1)</li>\r\n<li>Multilanguage short texts&#160;(OPS_PS_CI_1)</li>\r\n</ul>\r\n<div><span style=\"font-size: 14px;\">The topic of Project Builder enhancements has been modernized by</span></div>\r\n<div>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">Display of archived multilanguage short texts in Project Builder (SAP S/4HANA 2022 FPS0)</span></li>\r\n<li><span style=\"font-size: 14px;\">Support of multilanguage short texts in Fiori apps (SAP S/4HANA&#160;2021 FPS2)</span></li>\r\n<li><span style=\"font-size: 14px;\">Support of a new processing layer for creating purchase requisitions from network activities (SAP S/4HANA 2022 FPS1)</span></li>\r\n</ul>\r\n<span style=\"font-size: 14px;\">All of the above features are carved out of the given business function as of SAP S/4HANA 2022 FPS1 and with the modernization are part of the perpetual scope of SAP S/4HANA.</span></div>\r\n<p><strong>Project-Oriented Procurement (ProMan) enhancements</strong></p>\r\n<p>With SAP S/4HANA 2021 Project-Oriented Procurement is enhanced by support for multiple business partner adresses as part of the purchasing integration.&#160;The enhanced&#160;Project-Oriented Procurement is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements to ProMan included in Business Function&#160;OPS_PS_CI_1. See also Note <a target=\"_blank\" href=\"/notes/2267384\">2267384</a>.</p>\r\n<p><span style=\"font-size: 14px;\"><strong>Progress Tracking and Progress Analysis enhancements</strong></span></p>\r\n<ul>\r\n<li>Progress Tracking enhancements</li>\r\n<li>Progress Analysis Workbench enhancements</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">With SAP S/4HANA&#160;2022 FPS0 Progress Tracking and Progress Analysis Workbench are enhanced by support of key-user extensibility.&#160;The enhanced functionality is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements included in Business Function&#160;OPS_PS_CI_1 and&#160;LOG_XPD_EXT_1. See also Note&#160;<a target=\"_blank\" href=\"/notes/2267190\">2267190</a>.&#160;</span></p>\r\n<p><span style=\"font-size: 14px;\"><strong>Cost Calculation</strong></span></p>\r\n<ul>\r\n<li>Easy Cost Planning for Network Activities</li>\r\n<li>Business Add-Ins for Calculation in the Project System</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">With SAP S/4HANA&#160;2022 FPS0&#160;Cost Calculation is modernized by support of ACDOCP.&#160;The enhanced cost calculation is available as part of SAP S/4HANA perpetual scope. This includes also the enhancements included in Business Function&#160;OPS_PS_CI_1. See also Note&#160;<a target=\"_blank\" href=\"/notes/3033658\">3033658</a>.</span></p>\r\n<p><strong>New extractors for BI content</strong></p>\r\n<ul>\r\n<li>0PROJECT_CUST_ATTR Customer Fields for Project Def. (ci_proj)&#160;</li>\r\n<li>0WBS_ELEMT_CUST_ATTR Customer Fields for WBS Element&#160;</li>\r\n<li>0NETWORK_CUST_ATTR Customer Fields for network&#160;</li>\r\n<li>0ACTIVITY_CUST_ATTR Customer Fields for Network Activity&#160;</li>\r\n<li>0ACTY_ELEMT_CUST_ATTR Customer Fields for Activity Element<br />0WBS_ELEMT_USR_ATTR User Fields for WBS Element&#160;</li>\r\n<li>0ACTIVITY_USR_ATTR User Fields for Network Activity&#160;</li>\r\n<li>0ACTY_ELEMT_USR_ATTR User Fields for Activity Element&#160;</li>\r\n<li>0PS_CLM_ECP ECP Data for Claim&#160;</li>\r\n<li>0PS_WBS_ECP ECP for WBS-element&#160;</li>\r\n<li>0PS_NWA_ECP ECP for network activities&#160;</li>\r\n<li>0PS_NAE_ECP ECP for activity elements</li>\r\n<li>0PROJECT_TEXT Project Definition&#160;</li>\r\n<li>0WBS_ELEMT_TEXT Work Breakdown Structure Element&#160;</li>\r\n<li>0NETWORK_TEXT Network Number&#160;<br />the above extractors are modernized by modernization of the related topic, i.e.&#160;</li>\r\n<ul>\r\n<li>Support of key-user extensibility for projects&#160;(SAP S/4HANA 2020 FPS2)</li>\r\n<li>Modernization of user-field display (SAP S/4HANA 2020 FPS0)</li>\r\n<li>Modernization of Easy Cost Planning (SAP S/4HANA 2022 FPS0)</li>\r\n<li>Modernization of multi-languge texts (SAP S/4HANA 2022 FPS0)</li>\r\n</ul>\r\n</ul>\r\n<p style=\"padding-left: 30px;\"><span style=\"font-size: 14px;\">with these modernizations, the extractors above are part of the perpetual scope of SAP S/4HANA.</span></p>\r\n<p><span style=\"font-size: 14px;\"><strong>OPS_PS_CI_1</strong>:</span></p>\r\n<ul>\r\n<li>Change Inherited Settlement Rules<br />the topic of settlement rule is modernized by substitution and validation of WBS element settlement rule parameters (SAP S/4HANA 2022 FPS1). With the modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\r\n<li>Project System Material Component enhancements<br />the topic of project system material components is modernized by support of a new processing layer for creating purchase requisitions from network activities (SAP S/4HANA 2022 FPS1) as well as by the modernization of the Open Catalog Interface&#160;(SAP S/4HANA 2022 FPS1). With these modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\r\n<li>Determine WBS Account Assignment at MRP Area Level<br />the topic of WBS Account Assignment determination at MRP Area Level is modernized by the usage in enhanced Project Manufacturing Management and Optimization (PMMO) scenarios (SAP S/4HANA 2020). With these modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\r\n<li>Performance improvements through allocation of status combinations<br />modernized by support of reading via status combination codes in the OData APIs for Project and Project Network. With this modernization in SAP S/4HANA 2023 the feature becomes part of the perpetual scope.</li>\r\n</ul>\r\n<p><strong>OPS_PS_CI_2</strong>:</p>\r\n<ul>\r\n<li>Display of Nonarchived and Archived Line Items<br />the topic of line item reports is moderined by the new Fiori app Project Cost Line Items (SAP S/4HANA 2022 FPS1), see also note&#160;<a target=\"_blank\" href=\"/notes/2267286\">2267286</a>. With the modernization the functionality becomes part of the perpetual scope of SAP S/4HANA.</li>\r\n<li>Option for the Down Payment Clearing of Billing Plans and Invoicing Plan<br />modernized with SAP S/4HANA 2023 by the modernization of Project Cash Management, see also&#160;<a target=\"_blank\" href=\"/notes/2267182\">2267182</a>.&#160;With this modernization the feature becomes part of the perpetual scope of S/4HANA.&#160;</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\"><strong>Topics that stay in compatibility scope</strong></span></p>\r\n<p>The following features can be used as part of the compatibility scope. There are no plans to modernize the functionality, hence usage rights expire with the expiry date of the compatibility scope.</p>\r\n<p><span style=\"font-size: 14px;\">OPS_PS_CI_1:</span></p>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">Forecast Workbench</span></li>\r\n<li><span style=\"font-size: 14px;\">Access Control Lists</span></li>\r\n<li><span style=\"font-size: 14px;\">Display of statistical key figures on an additional tab page</span></li>\r\n<li><span style=\"font-size: 14px;\">New extractors for BI content</span></li>\r\n<ul>\r\n<li><span style=\"font-size: 14px;\">Network: Statistical Key Figure</span></li>\r\n<li><span style=\"font-size: 14px;\">Network Activity: Statistical Key Figure</span></li>\r\n<li><span style=\"font-size: 14px;\">Netw. Act. Element: Statistical Key Figures</span></li>\r\n</ul>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">OPS_PS_CI_3:&#160;</span></p>\r\n<p><span style=\"font-size: 14px;\">The business function provides an optimization for&#160;Resource-Related Billing (RRB) for the source&#160;Actual costs - line items. As an alternative, consider using the optimization provided with SAP Note&#160;2677564 or evaluate the usage of the new source&#160;Actuals Cost from Univ. Jrnl&#160;(Actuals Cost from Universal Journal) available as of SAP S/4HANA 2022.<em><br /></em></span></p>\r\n<p><span style=\"font-size: 14px;\">&#160;</span></p>\r\n<p><strong>Required and Recommended Action(s)</strong></p>\r\n<p>For the modernized topics, upgrade to the relevant SAP S/4HANA release before expiry date of the compatibility scope.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033100)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033100)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002267333/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002267333/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2318728", "RefComponent": "CA-CPD", "RefTitle": "S4TWL – Launchpad of Gantt Chart and Commercial Project Inception", "RefUrl": "/notes/2318728 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}