{"Request": {"Number": "2499728", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 221, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000019347252017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002499728?language=E&token=C4259E1E625A4ACA359849B0ED4E7F00"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002499728", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002499728/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2499728"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.06.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SCM-BAS-MD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Master Data"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Supply Chain Management", "value": "SCM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SCM Basis", "value": "SCM-BAS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-BAS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Master Data", "value": "SCM-BAS-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SCM-BAS-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2499728 - Restrictions for BW extractors relevant for S/4HANA in the area of Production Planning and Detailed Scheduling"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Several data sources for BW extraction are no longer available with S/4 in the area of PP/DS (Production Planning and Detailed Scheduling) as part of the product version SAP S/4HANA, on-premise edition 1511 and above</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>S/4HANA, BW, Extractors, PPDS, Production Planning and Detailed Scheduling, PP/DS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>See the reasons given below</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following DataSources are not working any longer.</p>\r\n<p>In case it is needed to use extractors, please get in contact with a BW consultant to clarify how to access specific data from PP or S/4 HANA in general.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" style=\"width: 657px;\"><colgroup> <col width=\"212\" /> <col width=\"105\" /> <col width=\"90\" /> <col width=\"250\" /></colgroup>\r\n<tbody>\r\n<tr>\r\n<td height=\"17\" width=\"212\">DataSource</td>\r\n<td width=\"105\">Appl.component</td>\r\n<td width=\"90\">Restriction</td>\r\n<td width=\"250\">Comment</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_BESKZ_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LANE_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCMAP_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCNO_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCNO_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LOCTYP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_LPROD_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MATMAP_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MODEL_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_MODEL_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"34\" width=\"212\">0APO_MSP_PLANNING_TYPE_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_ORDETP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PART_PROD_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PART_PRODT_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLNNR_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLNNR_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLVERS_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PLVERS_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PRDTYP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PROD_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_PROD_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESART_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESNAM_ATTR</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RESNAM_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_RTYPE_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_SUBLOC_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_TRTYPE_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0APO_WHTBOM_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0SCM_BOD_HIER</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">0SCM_PROD2BOD</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td height=\"17\">9ACFM_IDOC</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl65\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_MATGRPTYPE_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_REPTYP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SCRAPIND_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SODISPO_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SOREPLEN_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORETGRP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORFLAG_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SORSN_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n<tr>\r\n<td class=\"xl66\" height=\"17\" width=\"212\">9ASOR_SOTPOP_TEXT</td>\r\n<td class=\"xl66\" width=\"105\">0APO-IO</td>\r\n<td class=\"xl66\" width=\"90\">Deprecated</td>\r\n<td class=\"xl66\" width=\"250\">DS not working - no alternative exists</td>\r\n</tr>\r\n</tbody>\r\n</table></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SCM-APO-PPS (Production Planning and Detailed Scheduling)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I807453)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002499728/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002499728/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2500202", "RefComponent": "XX-SER-REL", "RefTitle": "S4TWL - BW Extractors in SAP S/4HANA", "RefUrl": "/notes/2500202"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2382787", "RefComponent": "SCM-APO-PPS-ERP", "RefTitle": "Restrictions and Implementation Recommendations for Production Planning and Detailed Scheduling for SAP S/4HANA 1610", "RefUrl": "/notes/2382787 "}, {"RefNumber": "2500202", "RefComponent": "XX-SER-REL", "RefTitle": "S4TWL - BW Extractors in SAP S/4HANA", "RefUrl": "/notes/2500202 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}