{"Request": {"Number": "2417298", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 332, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018753552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002417298?language=E&token=890BBF88543DA12A214B16DC1383C81E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002417298", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002417298/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2417298"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2019"}, "SAPComponentKey": {"_label": "Component", "value": "LO-MD-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partners"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Logistics - General", "value": "LO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Logistics Basic Data", "value": "LO-MD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-MD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partners", "value": "LO-MD-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'LO-MD-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2417298 - Integration of Business Partner with Customer and Supplier Roles"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You want to create Business Partner with Customer and Supplier roles in the same system or want to integrate data with various other landscapes.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>CL_MD_BP_MAINTAIN, API for Business Partner, IDOCs, BP SOA Service, S/4 HANA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<ul>\r\n<li>As Business Partner (BP) is a leading object in SAP S/4HANA, it is necessary to replicate all the customers and suppliers as Business Partners.</li>\r\n<li>You need an API to create Business Partner with Customer and Supplier roles.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The following are possible use-cases:</p>\r\n<ol>\r\n<li><strong>Requirement to create business partners in the same system:</strong>&#160;You can use API <strong>CL_MD_BP_MAINTAIN</strong>. Pass the data in CVIS_EI_EXTERN structure format and pass it to the method&#160;VALIDATE_SINGLE. This method validates all the data that is passed; after that, you can use the method&#160;MAINTAIN to create business partners.<br /><br /></li>\r\n<li><strong>Integration of data with various landscapes:&#160;</strong>If you want to integrate BP/Customer/Supplier master data across different systems, you can use following interfaces:<br /><br /><strong>a. IDOCs&#160;<br /><br /></strong>There are two types of IDocs available that can be used:</li>\r\n<ol>\r\n<li><strong>DEBMAS</strong>: If you want to integrate customer (without BP) data between two systems, this IDOC can be used.&#160;</li>\r\n<li><strong>CREMAS</strong>:&#160;If you want to integrate supplier (without BP) data between two systems, this IDOC can be used.</li>\r\n</ol></ol>\r\n<p><strong style=\"font-size: 14px;\"><strong>&#160; &#160; &#160; &#160; b. SOA Services</strong></strong></p>\r\n<ol><ol>\r\n<li>If the system has data of Business Partners, use SOA services for integration. Business Partner SOAP services enable you to replicate data between two systems. There are both inbound and outbound services available. Refer to SAP Note&#160;<strong>2472030</strong>&#160;for more information.</li>\r\n</ol></ol>\r\n<p>However, the IDocs DEBMAS and CREMAS are not recommended for data integration between S/4HANA systems. In S/4HANA, Business Partner is the leading object. Therefore, you can use Business Partner web services (SOAP) for SAP S/4HANA integration.</p>\r\n<p>Note: IDOCs integration&#160;in S/4 for creation of Customer/Supplier with same number as BP (<em><strong>Customizing configured as BP Internal numbering and Customer/Supplier External numbering with same number</strong></em>) will not be supported.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I022787"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I056103)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002417298/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002417298/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002417298/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2848660", "RefComponent": "LO-MD-BP", "RefTitle": "BP Extension - Vendor / Customer", "RefUrl": "/notes/2848660 "}, {"RefNumber": "2848224", "RefComponent": "LO-MD-BP-MIG", "RefTitle": "Migration Cockpit: Collective KBA for Business Partner (Customer, Supplier)", "RefUrl": "/notes/2848224 "}, {"RefNumber": "2790563", "RefComponent": "LO-MD-BP", "RefTitle": "Message F2 056 \"Changes have been made\" is displayed when posting DEBMAS IDoc", "RefUrl": "/notes/2790563 "}, {"RefNumber": "2796032", "RefComponent": "LO-MD-BP", "RefTitle": "Error:  'No update is defined for BP role 000000' R11 300", "RefUrl": "/notes/2796032 "}, {"RefNumber": "2721220", "RefComponent": "LO-MD-BP", "RefTitle": "IDOC BUPA_INBOUND_MAIN_SAVE_M09", "RefUrl": "/notes/2721220 "}, {"RefNumber": "2650140", "RefComponent": "LO-MD-BP", "RefTitle": "RFC_CVI_EI_INBOUND_MAIN to become obsolete.", "RefUrl": "/notes/2650140 "}, {"RefNumber": "2546010", "RefComponent": "LO-MD-BP", "RefTitle": "RFC_CVI_EI_INBOUND_MAIN does not create/update the Business Partner master record as expected.", "RefUrl": "/notes/2546010 "}, {"RefNumber": "2515806", "RefComponent": "FS-BP", "RefTitle": "Message DC006 issues when you execute call transaction BP in background", "RefUrl": "/notes/2515806 "}, {"RefNumber": "2484299", "RefComponent": "AP-MD-BP", "RefTitle": "BAPI list for BP", "RefUrl": "/notes/2484299 "}, {"RefNumber": "2405714", "RefComponent": "FS-BP", "RefTitle": "RFC_CVI_EI_INBOUND_MAIN enhancements to support update scenario (Function module is not supported from the release S/4 Hana OP 1709 FPS2 and  in cloud edition 1805 onwards)", "RefUrl": "/notes/2405714 "}, {"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "105", "To": "105", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}