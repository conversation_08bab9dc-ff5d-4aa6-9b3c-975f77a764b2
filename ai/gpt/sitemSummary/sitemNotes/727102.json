{"Request": {"Number": "727102", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 357, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015646532017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=806F66B77A53288E191968108E811014"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "727102"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.06.2005"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-BR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Brazil"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Brazil", "value": "XX-CSC-BR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-BR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "727102 - New DDIC Objects for ISS, PIS, COFINS; CSSL"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>New laws in Brazil for taxes:</p> <UL><LI>ISS</LI></UL> <UL><LI>PIS</LI></UL> <UL><LI>COFINS</LI></UL> <UL><LI>CSSL</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Brazil; MP135; LC116, ISS; PIS; COFINS; CSSL; DDIC, Data Dictionary</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is only relevant for Brazil.<br /><br />Changes in Brazilian tax legeslation requires calculation and posting<br />of the taxes on transaction level. For the functionality new<br />dictionary objects are necessary.<br /><br />The detailed description how to implement all necessary changes are<br />described in note 727425.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>************************************************************************<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A T T E N T I O N&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;***<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;M A K E&#x00A0;&#x00A0; S U R E&#x00A0;&#x00A0; Y O U&#x00A0;&#x00A0; H A V E<br />***&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R E A D&#x00A0;&#x00A0; N O T E&#x00A0;&#x00A0;852302&#x00A0;&#x00A0;F I R S T !!!<br />***<br />***********************************************************************<br />***<br />***&#x00A0;&#x00A0;This is step 2 of the implementation. Please make sure&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;***<br />***&#x00A0;&#x00A0;that step 1 described in note 727475 is already done.<br />***<br />************************************************************************<br />Import the attached file P9CK401046.zip with new DDIC objects. <B>Important: This transport is NOT to be applied to Release 4.5B even though the validity of this note is extended also to this releases.</B><br /><br />The transport must be imported first before the changes of other objects could be done. Existing DDIC Objects needs to be adapted with objects delivered with this transport.<br /><br />For Introductions about imports please read note 13719.<br /></p> <b>Delivered objects</b><br /> <b>Domains</b><br /> <p>J_1BCALCCONTROL, J_1BSERVTYPE_IN, J_1BSERVTYPE_OUT, J_1BSHIPFROM, J_1BTAXINCL, J_1BTAXLW3, J_1BTAXRELLOC, J_1BTXJCD, J_1BTXPRICE, WITHCD2<br /></p> <b>Data elements</b><br /> <p>J_1BACTIVCO, J_1BACTIVPI, J_1BACTIVWT, J_1BGENSERVTYPE, J_1BISSMINPRICE, J_1BISSWT, J_1BKSCHL_PRICE, J_1BPSTCDFROM, J_1BPSTCDTO, J_1BSERVTYPE_IN, J_1BSERVTYPE_OUT, J_1BTAXINCL, J_1BTAXINNET, J_1BTAXLW3, J_1BTAXOFFSET, J_1BTAXRELLOC, J_1BTXCPA, J_1BTXCPF, J_1BTXCPU, J_1BTXJCD, J_1BTXJCD_PR, J_1BTXJCD_SE, J_1BTXJCD_SR, J_1BTXPPA, J_1BTXPPF, J_1BTXPPU, J_1BTXSDCOFINS, J_1BTXSDPIS, J_1BTXSDWHT, J_1BVCALCACTIV, J_1BVCALCBESTD, J_1BVCALCBESTU, J_1BVCALCPRDIF, J_1BVCALCUMDIF, J_1BVCALCVERBR, J_1BVCALCVWERE, J_1BWHTCOLL, J_1BWHTCOLL_CSLL, J_1BWHTCOLL_GEN, J_1BWHTCOLL_IR, J_1BWHTCOLL_PIS, J_1BWHTRATE_COFINS, J_1BWHTRATE_CSLL, J_1BWHTRATE_GEN, J_1BWHTRATE_IR, J_1BWHTRATE_PIS, WITHCD2, WITHCD2_TXT<br /></p> <b>Tables</b><br /> <p>J_1BATL3, J_1BATL3T, J_1BKON1, J_1BSERVASSIGN1, J_1BSERVASSIGN2, J_1BSERVTYPES1, J_1BSERVTYPES1T, J_1BSERVTYPES2, J_1BSERVTYPES2T, J_1BTAXGRPCD, J_1BTREG_CITY, J_1BTXCOF, J_1BTXISS, J_1BTXJUR, J_1BTXJURT, J_1BTXPIS, J_1BTXWITH, J_1BWHT, T059Z_WITHCD2, T059Z_WITHCD2T,<br /></p> <b>Structures</b><br /> <p>KOMP_BRAZIL<br /></p> <b>Views</b><br /> <p>J_1BCONDCALC<br /></p> <b>Searchhelps</b><br /> <p>H_J_1B_PRICECOND, H_J_1B_T683S<br /></p> <b>Table Maintanance Functions (Funktiongruops)</b><br /> <p>J1BISSCUST, J1BTAXCUST<br /></p> <b>IMG entries</b><br /> <p>SIMGV_T059Z_CO, SIMGV_T059Z_WITHCD2<br /></p> <b>Messages use in Programs</b><br /> <p>8B567, 8B688, 8B689; ICC_DATE_CHECK100<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AP (Accounts Payable)"}, {"Key": "Responsible                                                                                         ", "Value": "D025903"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (D034883)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "P9CK401046.zip", "FileSize": "178", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000134302004&iv_version=0003&iv_guid=C2C20FEB50568B4587771EE04B447E08"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "852302", "RefComponent": "XX-CSC-BR", "RefTitle": "Implementation Overview ISS 2004 - Legal Change MP135/LC116", "RefUrl": "/notes/852302"}, {"RefNumber": "840909", "RefComponent": "XX-CSC-BR", "RefTitle": "Brazilian fields are not filled in pricing after note 727102", "RefUrl": "/notes/840909"}, {"RefNumber": "727475", "RefComponent": "XX-CSC-BR", "RefTitle": "Overview about changes for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/727475"}, {"RefNumber": "713078", "RefComponent": "XX-CSC-BR", "RefTitle": "Legal Change in Taxes: ISS, PIS, COFINS, ...", "RefUrl": "/notes/713078"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "840909", "RefComponent": "XX-CSC-BR", "RefTitle": "Brazilian fields are not filled in pricing after note 727102", "RefUrl": "/notes/840909 "}, {"RefNumber": "852302", "RefComponent": "XX-CSC-BR", "RefTitle": "Implementation Overview ISS 2004 - Legal Change MP135/LC116", "RefUrl": "/notes/852302 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "727475", "RefComponent": "XX-CSC-BR", "RefTitle": "Overview about changes for ISS, PIS, COFINS; CSSL", "RefUrl": "/notes/727475 "}, {"RefNumber": "713078", "RefComponent": "XX-CSC-BR", "RefTitle": "Legal Change in Taxes: ISS, PIS, COFINS, ...", "RefUrl": "/notes/713078 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B62", "URL": "/supportpackage/SAPKH45B62"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C48", "URL": "/supportpackage/SAPKH46C48"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C49", "URL": "/supportpackage/SAPKH46C49"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47021", "URL": "/supportpackage/SAPKH47021"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "840909", "RefTitle": "Brazilian fields are not filled in pricing after note 727102", "RefUrl": "/notes/0000840909"}]}}}}}