{"Request": {"Number": "96732", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 497, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000362572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000096732?language=E&token=24491ED81F9555976128FB9493B82842"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000096732", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000096732/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "96732"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 65}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.10.2001"}, "SAPComponentKey": {"_label": "Component", "value": "BC-CTS-CCO"}, "SAPComponentKeyText": {"_label": "Component", "value": "Client Copy (For ByD issues select BC-TLM-CP)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Change and Transport System", "value": "BC-CTS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Client Copy (For ByD issues select BC-TLM-CP)", "value": "BC-CTS-CCO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-CTS-CCO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "96732 - CC-INFO: DB2/390 - client copy is very slow"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><b>Note the following:</b><br /> <p>As of Release 4.0, this Note contains functions which are not contained in the standard. Therefore you must read this Note prior to the import of the transport request.<br /><br />This Note is an optimized version of the client copy which has been especially designed for DB2/390. The functionality is not tested on other databases, however, no problems are known to date.<br /><br />If problems occur, please always check first whether this note contains new information and whether a newer version of the tool is available on your sapservX in directory &#x00A0;&#x00A0; '/general/R3server/abap/note.0096732'.<br />This directory also contains files <B>40BHistory.txt</B> and <B>45BHistory.txt</B> where you can find up-to-date information about problem solutions and improvements in Releases 4.0B and 4.5B.<br /><br />If you need to create a problem message for one of the the client copy tools (Component BC-CTS-CCO), always refer to this Note and to the imported transport request.<br /><br />SAP <B>cannot provide any support</B> if you do not indicate the Note number <B>96732</B> and the <B>transport request number.</B> You can find the transport request number for example in the version management of report RSCCPROT (Transaction SE38 -&gt; Utilities -&gt; Version management).<br /></p> <b>The symptom</b><br /> <p>A client copy in the database DB2/390 is very slow.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>MVS, OS390, OS/390, ABAP, SCC9, SCC0, SCCL, KPP1CONT1, DB error -310<br />ANALYZE_UNVERT_SELTAB LSCCRU06 RSCLXCOP SYNTAX_ERROR KEY_ALLOWED<br />MSCC1O01 SAPMSCC1 SAPLSCC1 SAPLSCCA SAPLSCCV SAPLSCCR SAPLSCCB LSRTTU02<br />SAVED_PROFILE SAVED_PROFIL I32760S LSCC1U04 CCSELTAB MSCC1002 MSCC1I01<br />CHECK_MODE SELTAB_N LSCCRF02 SOURCE_SYSID RSCLXCOP SELTAB_G LSCCRF01<br />CALL_FUNCTION_NOT_FOUND COPY_TAB_ALL_CLIENT</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The database interface of the DB2/390 can be very slow when accessing mass data.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>By installing the feature \"Pre-open dataset\" and as of Release 4.x additionally by using a parallel copier (part of the standard system as of Release 4.6) you can significantly improve the total runtime compared with the previous version.<br /><br />The transport requests with the new functions are ready on sapservX. Download the transport that is appropriate for your system from directory: '/general/R3server/abap/note.0096732'.<br /> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KD2xxx.CAR (Release 3.1)<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;D6Txxx.CAR (Release 4.0)<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;KDIxxx.CAR (Release 4.5)<br />Note 13719 describes how to download the fix from sapservX and import it into the R/3 System. (SAP is to deliver the compression tool CAR to all customers. This will run on all platforms supported by SAP. Under UNIX you can find CAR under /usr/local/bin/CAR. Under Windows NT you can find the program on all SAP R/3 software CDs. You can obtain CAR'S syntax by starting the program without parameters.)<br /><br /><B>Please note:</B></p><UL><LI>This functionality is not delivered via a Hot Package. To avoid <B>syntax errors</B> or <B>data loss</B>, you have to re-import the transport request after importing a Hot Package.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the new functionality is only required for a single project, save the current status of the client copy tool - with a 'Transport of copies' with the object list of the note's transport request - <B>before importing the transport request</B> and restore it after making the copy.</p> <UL><LI>After you import the transport request, you can not always revert to the standard functionality of the client copying tool. However, the transport requests are regularly updated and contain all the required Support Package corrections. Corrections from other Notes for the client copying tool are not generally suitable for this special preliminary version. In addition, corrections that are added manually are overwritten when the transport request is imported. Therefore, please advise us of any problems that occur, so that we can make a new transport request available, which contains the corrections.</LI></UL> <UL><LI>The sole possibility of returning to the standard is to carry out a 'transport of copies' with the object list from the transport request. To do this, the source system should have exactly the same Hot Package level as the system currently has with Note 96732. There will not be any problems, however, if the Hot Package level of the source system is higher. SAP cannot and will not make available such transport requests.</LI></UL> <UL><LI>In order to clean up Transaction <B>SPAU</B> after an <B>upgrade</B>, select the objects by the imported transport request or - if this is not possible - by developer name. You can then mark all objects and return to the standard by selecting the 'Reset to original' push button. You can ignore warnings indicating that your modifications get lost.</LI></UL> <UL><LI>The languages German and English are always supported but other languages may not be.</LI></UL> <UL><LI>The transport requests already contain the improved text copying function of Note 180541.</LI></UL> <UL><LI>In older versions prior to KD2K000171, KDFK000248 or KDIK000089, the <B>SAPscript texts</B> may get lost during a RESTART. In this case, you can copy the texts as described in Note 339721.</LI></UL> <UL><LI>In a <B>remote copy</B>, the transport request must be imported in the source system as well. This is not necessarily required, if Note 180541 has been imported in the source system.</LI></UL> <UL><LI>During the import of Hot Package SAPKH45B23, the following error message is issued:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TW165: Function SCCR_CHECK_TABLESPACE_ADABAS (SCCR 10) does not fit into the existing function group ((SCCA 02))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you import the Hot Package, please create report RSDELTFDIR (which is part of the delivery as of KDI000202) that is attached to this Note and execute it once. After the import of the Hot Package, reimport the corresponding current KDI transport from your local sapservX.</p> <UL><LI>During the import of Hot Package SAPKH40B63 or SAPKH45B42, the following error messages are issued:</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TW165: Function SCCR_CHECK_TABLESPACE_INFORMIX (SCCR 09) does not fit into the existing function group ((SCCA 05))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TW165: Function SCCR_CHECK_TABLESPACE_ORACLE (SCCR 08) does not fit into the existing function group ((SCCA 06))<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Before you import the Hot Package, please create report RSDELTFDIR2 that is attached to this note and execute it once. After the import of the Hot Package, reimport the corresponding current transport from your local sapservX.</p> <UL><LI>With individual tables, <B>long runtimes</B>&#x00A0;&#x00A0;may occur to read the data on certain databases, if the tables are strongly fragmented. Under DB2/390, for example, all tables found by the appended sample program RSREORG should be <B>reorganized</B> (for remote copies in both systems) before the copy is started.<br /></LI></UL> <p>Using parallel processes for <B>Release 4.0 and 4.5</B>:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The transport request contains the copy function with parallel processesfrom Release 4.6. The parallel processes of the client copy program are parallel RFC processes. These are managed via RFC/server groups. When doing so, the maximum number of processes used corresponds to the number of processes set in the copier (system and work load dependent). The chosen server group settings are standard. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Setting up server groups:<br /> Start Transaction SM59 and define the server groups (RFC -&gt; RFC groups). To do so, also refer to the documentation of the RFC server groups. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Setting up the parallelity:<br /> Start the client copy program (Transaction SCCL). Call up a dialog box via 'Goto -&gt; Parallel processes' and enter the maximum number of processes to be used and the server group. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Aborting a copy<br /> A copy with parallel processes can be aborted with the SCC3 by using the command 'ABRT' in the command field. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For additional information, refer to Note 212727. <p></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-SCR (SAPscript)"}, {"Key": "Other Components", "Value": "BC-DB-DB2 (DB2 for z/OS)"}, {"Key": "Database System", "Value": "DB2/390"}, {"Key": "Database System", "Value": "DB2/390 5.1"}, {"Key": "Database System", "Value": "DB2/390 6.1"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON> (I073741)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D024946)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000096732/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000096732/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000096732/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "40BHistory.txt", "FileSize": "7", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008332632001&iv_version=0065&iv_guid=E99950B27F591D4A8AFB064E60A45D03"}, {"FileName": "Kdi00338.car", "FileSize": "508", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008332632001&iv_version=0065&iv_guid=A7A4C69E2A67F44DA997C0C917090AAB"}, {"FileName": "KD200171.CAR", "FileSize": "128", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008332632001&iv_version=0065&iv_guid=0DC18230E5FB2F4E81ECB47DC1C67A70"}, {"FileName": "45BHistory.txt", "FileSize": "7", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008332632001&iv_version=0065&iv_guid=9A2BCA9ED98E2845B364FDC75819C6C9"}, {"FileName": "D6T00158.CAR", "FileSize": "439", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=011000358700008332632001&iv_version=0065&iv_guid=7A7B07E42A0B7F4E8FC6D0A940FC8AF2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "92589", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: TCP/IP read/write buffer > 1 MB", "RefUrl": "/notes/92589"}, {"RefNumber": "88271", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/88271"}, {"RefNumber": "77131", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/77131"}, {"RefNumber": "77130", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/77130"}, {"RefNumber": "67205", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large and productive clients", "RefUrl": "/notes/67205"}, {"RefNumber": "539376", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Tables are not deleted on DB2/390", "RefUrl": "/notes/539376"}, {"RefNumber": "438997", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC INFO: Syntax error after Support Package - note 96732", "RefUrl": "/notes/438997"}, {"RefNumber": "339721", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC INFO: Subsequent copy of long texts", "RefUrl": "/notes/339721"}, {"RefNumber": "31496", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client deleted by mistake", "RefUrl": "/notes/31496"}, {"RefNumber": "212727", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: FAQs for parallel processes up to Release 4.6D", "RefUrl": "/notes/212727"}, {"RefNumber": "180541", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Performance problem when copying text", "RefUrl": "/notes/180541"}, {"RefNumber": "151394", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/151394"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "103707", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/103707"}, {"RefNumber": "101303", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/101303"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "539376", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ERROR: Tables are not deleted on DB2/390", "RefUrl": "/notes/539376 "}, {"RefNumber": "438997", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC INFO: Syntax error after Support Package - note 96732", "RefUrl": "/notes/438997 "}, {"RefNumber": "339721", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC INFO: Subsequent copy of long texts", "RefUrl": "/notes/339721 "}, {"RefNumber": "67205", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large and productive clients", "RefUrl": "/notes/67205 "}, {"RefNumber": "212727", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: FAQs for parallel processes up to Release 4.6D", "RefUrl": "/notes/212727 "}, {"RefNumber": "31496", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Client deleted by mistake", "RefUrl": "/notes/31496 "}, {"RefNumber": "180541", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Performance problem when copying text", "RefUrl": "/notes/180541 "}, {"RefNumber": "92589", "RefComponent": "BC-OP-AS4", "RefTitle": "IBM i: TCP/IP read/write buffer > 1 MB", "RefUrl": "/notes/92589 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31G", "To": "31I", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": "X"}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 3, "URL": "/corrins/0000096732/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}