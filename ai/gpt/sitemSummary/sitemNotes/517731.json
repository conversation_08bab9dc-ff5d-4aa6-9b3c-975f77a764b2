{"Request": {"Number": "517731", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 488, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015210962017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000517731?language=E&token=ECC9F2BADB69183D5BA949345C4AC50A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000517731", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000517731/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "517731"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 65}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2007"}, "SAPComponentKey": {"_label": "Component", "value": "BC-UPG-RDM"}, "SAPComponentKeyText": {"_label": "Component", "value": "README: Upgrade Supplements"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Upgrade - general", "value": "BC-UPG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "README: Upgrade Supplements", "value": "BC-UPG-RDM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-UPG-RDM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "517731 - Corrections for the upgrade to systems with Basis 620"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This note describes new features and repairs in R3up 6.20.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Upgrade, 620,<br />System Switch Upgrade, shadow upgrade</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>You are upgrading to a system with Basis Release 620.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>To avoid the error scenarios described below, we recommend that you<br />swap out the first<br />archive from the R3up before the PREPARE.<br />To do this, proceed as follows:</p> <UL><LI>Exit R3up after the \"Parameter input\" module.</LI></UL> <UL><LI>Rename R3up (to R3up_org): To do this, go to the following directory:<br />&lt;DIR_PUT&gt;/bin on UNIX, or &lt;DIR_PUT&gt;\\exe on NT.<br />For iSeries, rename R3up in the R3up library.</LI></UL> <UL><LI>Replace R3up with the latest version for your product on the SAP Service Marketplace.<br /><B>Caution: The R3up is no longer at \"SAP Kernel\" under service.sap.com, but is at \"Upgrade Tools\".</B><br />Switch to the alias /patches, select \"Entry by Application Group\", then \"Additional Components\" -&gt; \"Upgrade Tools\". Then select \"R3up\" or \"R3up for Unicode\" if you have a Unicode system. Select \"R3up 620\". The R3up is in the relevant operating system in the archive R3up620&lt;nr&gt;.SAR.<br />There are different versions (the &lt;nr&gt; in the archive name) of the R3up for the different products. You can check which R3up you need in the list below.</LI></UL> <UL><LI>Unpack R3up into the following directory:<br />&#x00A0;&#x00A0;&lt;DIR_PUT&gt;/bin on UNIX, or &lt;DIR_PUT&gt;\\exe on NT.<br />&#x00A0;&#x00A0; iSeries: Import the patch for the R3up library.</LI></UL><UL><LI>Restart PREPARE or R3up.</LI></UL> <p><br /><STRONG>Replace the R3up before you start the actual upgrade.<br />You should ONLY replace the R3up during the upgrade in consultation with the SAP Support team.<br />.</STRONG><br /><br /><STRONG>- How do I determine the R3up Version currently used?</STRONG><br />UNIX: Go to &lt;DIR_PUT&gt;/bin and call './R3up -V'.<br />NT: Go to &lt;DIR_PUT&gt;\\exe and call '.\\R3up.exe -V'.<br />iSeries: ADDLIBLE &lt;R3uplib&gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;R3up '-V'<br />R3up displays the following message:<br />This is R3up version 6.20/&lt;nr&gt;, patch level &lt;major&gt;.&lt;minor&gt;<br />For example:<br />This is R3up version 6.20/2, patch level 11.099<br /><br /><STRONG>Which versions of R3up are compatible?</STRONG><br />6.20/2 is compatible with 6.20/1, so you should always use 6.20/2 if 6.20/1 is on the CD.<br />6.20/6 is compatible with 6.20/3, 6.20/4 and 6.20/5. <B> However, you need a higher version of the tp in &lt;DIR_PUT&gt;/exe: Release 6.20, 320.56.62, if you replace R3up 6.20/3 by 6.20/6.</B><br />Releases 6.20/3, 6.20/4, 6.20/5 and 6.20/6 are <B>not compatible </B> with 6.20/2 and 6.20/1. The system also checks this when you start.<br /><br /><STRONG>Which R3up is required?</STRONG><br /><br />You<br />R/3 Enterprise 470 Ext 1.10&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;6.20/2<br />R/3 Enterprise 470 Ext 1.10/SR1:&#x00A0;&#x00A0;6.20/6<br />R/3 Enterprise 470 Ext 2.00&#x00A0;&#x00A0;&#x00A0;&#x00A0;:&#x00A0;&#x00A0;6.20/6<br />R/3 Enterprise 470 Ext 2.00/SR1:&#x00A0;&#x00A0;6.20/6<br />WebAS 620: 6.20/2<br />CRM 310&#x00A0;&#x00A0;&#x00A0;&#x00A0;: 6.20/2<br />BBP 350/SR1: 6.20/6 (note the tp version)<br />BBP 350: 6.20/2<br />BW 30B: 6.20/2<br />BW 310: 6.20/6 (note the tp version)<br />CRM 400&#x00A0;&#x00A0;&#x00A0;&#x00A0;: 6.20/6<br />SCM 400: 6.20/6<br />CRM 310/SR1: 6.20/6<br />The R3up in Version 6.20/nr is contained in the R3up620nr.SAR archive. For example:<br />For example: R3up6205.SAR contains version 6.20/5., and so on.<br />6.20/2 is also referred to as 620-2, and so on.<br /><B>Note the tp version:</B><br />If you use R3up 6.20/4 or higher, you need a higher version of the tp in &lt;DIR_PUT&gt;/exe: Rel 6.20, 320.56.62.<br /><br />Note that if you are using iSeries with WebAS 620, BBP 350 or BW 30B, you still need to import additional patches. Note <B>500297</B> contains more information.<br /><br /><STRONG>Repairs and R3up versions:<br /></STRONG><br />620-6: 15.131<br />DB2/390-specific corrections:<br />- Correction of an error in the EXIT phase (REORG check for DB2 v7.1)<br /><br />620-6: 15.130<br />DB2/390-specific corrections:<br />- Correction of an error in phase DBPREP_CHK<br /><br />620-6: 15.126<br />DB2/390-specific corrections:<br />(only for R3E47x2 and CRM40SR1 - Refer to Note 905420 for details)<br />- Modified handling of REBUILD INDEX<br />- Fix of an error in SPACECHK_INI<br /><br /><br />620-6: 15.110<br />DB2/390-specific corrections:<br />- Additional consistency check for RAW/LRAW/VARC fields<br />- Handling of upgrade with 6.40 downward compatible kernel<br /><STRONG></STRONG><br />620-2: 11.165<br />DB2/390 correction: Cleanup of table DDSTORAGE to avoid problems<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;in phase PARCONV_UPG<br /><br />620-6: 15.077<br />DB2/390 specific corrections, e.g.:<br />- DROP TABLE instead of RENAME TABLE in PSCRGEN_DROP_SHD_TABLE<br />- Phase PSCRGEN_DROP_SHD_TABLE moved before STARTR3_FOR_TESTS<br />- KEYCARD option modified in RUNSTATS<br />- z/OS Problem with DEFAULT.TPP resolved (NEWTAB_CRE, LIST_LOAD)<br />- DSNACCOR usage in DBPREP_CHK switched off<br />- Check of TEMP SPACE switched off<br /><br />620-2: 11.163<br />DB2/390 correction: Check of TEMP SPACE switched off<br /><br />620-6: 15.070<br />Eliminates errors in the import sequence for entries of the TCPUCATTR<br />table in SAPKX and Support Package requests.<br /><br /><br />620-2: 11.161:<br />620-6: 15.067:<br />DB2/390 enhancement in RUNSTATS handling (+Option KEYCARD)<br /><br /><br />620-6: 15.060:<br />DB2/390 fix for errors in the EU_IMPORT5 (SQLCODE -104 with INDEX.SQL) and EXIT phases.<br /><br /><br />620-6: 15.054. Release of the R3up tested for compatibility with<br />products delivered with 620/3, 620/4 or 620/5.<br /><br />620-6: 15:045<br />Parallel execution of the \"move nametabs\" step in the PARMVNT_XCNV<br />phase for upgrades to R/3 Enterprise.<br /><br />620-5: 14.060<br />Eliminates errors in the import sequence for TCP table entries of SAPKX requests.<br /><br />620-5: 14.054<br />Activation for SAPKCCDrel and SAPKCDCrel documentation requests.<br /><br />620-5: 14.050<br />ORACLE: Fix for SYS_IOT indexes.<br /><br />620-2: 11.155<br />620-4: 13.042<br />620-5: 14.043<br />DB2/390-specific correction of an error in the DBPREP_CHK phase<br />(see Note 634251).<br /><br />620-4: 13.039<br />Correction for tp putsteps with the Addtobuffer of the SAPKMCM620 request.<br /><br />620-3: 12.046<br />With lower versions, errors may occur in the IS_SELECT phase when you include add-on upgrades with SAINT packages (UPG SAINT).<br /><br />620-3: 12.044<br />In the case of lower versions of SAPDB databases, the R3up terminates<br />in the STARTR3_* and STOPR3_* phases.<br /><br />620-3: 12.034<br />Depending on the import conditions, a termination may occur in the ADDON_QCALC phase if you use add-on updates in versions lower than 12.034.<br /><br />620-3: 12.031<br />With a lower version, errors may occur in the BIND_PATCH phase<br />(for example, \"Internal error: PI_BASIS Level 0005 NOT_FOUND\").<br /><br />620-2: 11.154<br />Correction for tp putsteps with the Addtobuffer of the SAPKMCM620 request.<br /><br />620-2: 11.149<br />DB2/390 specific corrections, e.g.:<br />- additional GRANT USE STOGROUP for SAPR3S<br />- Fix in the EXIT phase (determination of the REORG candidates)<br /><br />620-2: 11.145<br />In the case of lower versions of SAPDB databases, the R3up terminates<br />in the STARTR3_* and STOPR3_* phases.<br /><br />620-2: 11.144<br />The inclusion of the modification adjustment transport does not work in versions lower than 11.144 (see Note 583835)<br /><br />620-2: 11.141<br />With a lower version, errors may occur in the BIND_PATCH phase<br />(for example, \"Internal error: PI_BASIS Level 0005 NOT_FOUND\").<br /><br />620-2: 11.139<br />Depending on the import conditions, a termination may occur in the ADDON_QCALC phase if you use add-on updates in versions lower than 11.139.<br /><br />620-2: 11.138<br />Version 11.138 contains some DB2/390-specific corrections and enhancements (see Notes 423726, 545281 and 569821).<br /><br />620-2: 11.133<br />With versions lower than 11.133, the system may not recognize startup terminations of the tp in the TP_ACTION- phases.<br />A correction is also provided for directory names for &lt;DIR_PUT&gt; with more than 20 characters.<br /><br />620-2: 11.131<br />Adjustments for Oracle 9.2.<br />Oracle note 491598 indicates whether the upgrade to a product for Oracle 9.2 is released. The general note on the upgrade to 620 is: 484876.<br /><br />620-2: 11.121<br />Support Packages may not be included correctly with R3up versions lower than 11.121 if an add-on update is included with a supplement CD at the same time (for example, if you upgrade to BW 30B with the FINBASIS and SEM-BW add-ons).<br /><br />All errors of the R3up 620-1 described below are also corrected in the downward-compatible 620-2 status. Therefore, you must only use this R3up as described above.<br /><br />620-1: 10.104<br />If you are using a lower R3up version with a large number of languages or clients in the system, requirements that are far too large may occur in the case of the system freespace calculation because the system treats cross-client tables as client-specific tables.<br /><br />620-1: 10.102<br />A problem may occur with the triggers created in the upgrade. This only occurs if you are using the \"downtime-minimized\" strategy. If you are using an R3up version lower than 10.102, select the \"resource-minimized\" strategy.<br /><br />620-1: 10.100<br />On NT, the system may terminate when determining the instance number via the entry for the startup procedure in the registry if the entry contains forward slashes \"/\" instead of backslashes \"\\\" as path separators. This behavior is corrected with the R3up version mentioned above.<br /><br />620-1: 10.099<br />Automatically stopping the SAP system on True 64 Unix during the upgrade may cause the upgrade to shut down because the system cannot be stopped.<br /><br />620-1: 10.095<br />Deadlocks may occur in the SHADOW_NTACT_CP phase when upgrading with DB6. We have add a SCRGEN_DB6_VOLATILE9 phase to eliminate the problem.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D038245)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D046257)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000517731/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000517731/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "905420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/905420"}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "684990", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/684990"}, {"RefNumber": "673631", "RefComponent": "BC-UPG-PRP", "RefTitle": "Extension PREPARE module: Concepts and troubleshooting", "RefUrl": "/notes/673631"}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258"}, {"RefNumber": "658301", "RefComponent": "BC-DWB-DIC", "RefTitle": "Activation errors in ACT_620 with transport SAPKCDC620", "RefUrl": "/notes/658301"}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "634251", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Size check of user catalog fails", "RefUrl": "/notes/634251"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278"}, {"RefNumber": "601886", "RefComponent": "FS-CD", "RefTitle": "Addtl info for R/3 4.7x110 SR1 upgrade with Insurance 4.71", "RefUrl": "/notes/601886"}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941"}, {"RefNumber": "569821", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCI upgrade", "RefUrl": "/notes/569821"}, {"RefNumber": "557437", "RefComponent": "BC-OP-AS4", "RefTitle": "New config file for the 620 upgrade with R3up/2 and higher", "RefUrl": "/notes/557437"}, {"RefNumber": "545281", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Performance of DD activation", "RefUrl": "/notes/545281"}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569"}, {"RefNumber": "522711", "RefComponent": "BC-UPG", "RefTitle": "Corrections for upgrade to Basis 620", "RefUrl": "/notes/522711"}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278"}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267"}, {"RefNumber": "513536", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/513536"}, {"RefNumber": "506339", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.0B", "RefUrl": "/notes/506339"}, {"RefNumber": "500297", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.20: iSeries", "RefUrl": "/notes/500297"}, {"RefNumber": "493599", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP 3.5", "RefUrl": "/notes/493599"}, {"RefNumber": "490065", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to Web AS 6.20", "RefUrl": "/notes/490065"}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876"}, {"RefNumber": "423726", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/423726"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "601886", "RefComponent": "FS-CD", "RefTitle": "Addtl info for R/3 4.7x110 SR1 upgrade with Insurance 4.71", "RefUrl": "/notes/601886 "}, {"RefNumber": "500297", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional Information: Upgrade to SAP Web AS 6.20: iSeries", "RefUrl": "/notes/500297 "}, {"RefNumber": "545281", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Performance of DD activation", "RefUrl": "/notes/545281 "}, {"RefNumber": "569821", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: MCI upgrade", "RefUrl": "/notes/569821 "}, {"RefNumber": "484876", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP Web AS 6.20", "RefUrl": "/notes/484876 "}, {"RefNumber": "610417", "RefComponent": "XX-PROJ-JP-PHM", "RefTitle": "R/3 Enterprise 47x110 SR1 upgrade with KJCPH 2.0A", "RefUrl": "/notes/610417 "}, {"RefNumber": "634219", "RefComponent": "XX-PROJ-JP-EIM", "RefTitle": "Add. info on R/3 Enterprise 4.70 upgrade with KEPS-MM", "RefUrl": "/notes/634219 "}, {"RefNumber": "663258", "RefComponent": "BC-UPG-RDM", "RefTitle": "Corrections for R3up version 640", "RefUrl": "/notes/663258 "}, {"RefNumber": "737800", "RefComponent": "BC-UPG-RDM", "RefTitle": "Enhancements to upgrade on SAP CRM 4.0 SR1", "RefUrl": "/notes/737800 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}, {"RefNumber": "544569", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.10", "RefUrl": "/notes/544569 "}, {"RefNumber": "517278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 3.1", "RefUrl": "/notes/517278 "}, {"RefNumber": "585941", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7 SR1", "RefUrl": "/notes/585941 "}, {"RefNumber": "517267", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 Enterprise 4.7", "RefUrl": "/notes/517267 "}, {"RefNumber": "600824", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to CRM 4.0 / SRM Server 4.0", "RefUrl": "/notes/600824 "}, {"RefNumber": "506339", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to BW 3.0B", "RefUrl": "/notes/506339 "}, {"RefNumber": "603278", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to SCM 4.0", "RefUrl": "/notes/603278 "}, {"RefNumber": "493599", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to EBP 3.5", "RefUrl": "/notes/493599 "}, {"RefNumber": "640516", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to R/3 4.7 Ext.Set 2.00", "RefUrl": "/notes/640516 "}, {"RefNumber": "490065", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2/390: Additions upgrade to Web AS 6.20", "RefUrl": "/notes/490065 "}, {"RefNumber": "522711", "RefComponent": "BC-UPG", "RefTitle": "Corrections for upgrade to Basis 620", "RefUrl": "/notes/522711 "}, {"RefNumber": "673631", "RefComponent": "BC-UPG-PRP", "RefTitle": "Extension PREPARE module: Concepts and troubleshooting", "RefUrl": "/notes/673631 "}, {"RefNumber": "658301", "RefComponent": "BC-DWB-DIC", "RefTitle": "Activation errors in ACT_620 with transport SAPKCDC620", "RefUrl": "/notes/658301 "}, {"RefNumber": "634251", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2/390: Size check of user catalog fails", "RefUrl": "/notes/634251 "}, {"RefNumber": "557437", "RefComponent": "BC-OP-AS4", "RefTitle": "New config file for the 620 upgrade with R3up/2 and higher", "RefUrl": "/notes/557437 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "30B", "To": "30B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "BBPCRM", "From": "350", "To": "350", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}