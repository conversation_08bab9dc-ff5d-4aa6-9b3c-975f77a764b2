{"Request": {"Number": "1849497", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 415, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000010932802017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001849497?language=E&token=E2AFEC91D96C6976EE7A8469BCAD6DB3"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001849497", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001849497/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1849497"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "24.02.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DBA-DSO"}, "SAPComponentKeyText": {"_label": "Component", "value": "DataStore Object (classic)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Basis", "value": "BW-WHM-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DataStore Object (classic)", "value": "BW-WHM-DBA-DSO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DBA-DSO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1849497 - SAP HANA: Optimization of standard DataStore objects"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use the database management system (DBMS) SAP HANA and load data in DataStore objects (DSOs).<br /><br />Up to now, you had to model SAP HANA-optimized DataStore objects or convert existing standard DataStore objects into SAP HANA-optimized DataStores using transaction RSMIGRHANADB in order to benefit from the outstanding activation performance. During this process, the table layout was changed in such a way that the data of the change log was calculated with a calculation view.<br /><br />After you implement the advance correction or import the Support Packages specified below, the conversion or explicit modeling of SAP HANA-optimized DataStores is no longer required.<br /><br />All standard DataStore objects now use an SAP HANA-optimized activation process or rollback process that has a comparable performance. During this process, the data of the change log is saved in a transparent table so that no conversion of the table layout is required. By supporting the concept for non-active data (see SAP Note 1767880), the memory consumption remains at the level of the SAP HANA-optimized DataStores.<br /><br />Creating SAP HANA-optimized DataStores is no longer possible when this SAP Note is available. Existing SAP HANA-optimized DataStores are still supported. However, for standardization reasons, we recommend that you convert SAP HANA-optimized DataStore objects back into classic DataStore objects. For more information on this, see SAP Note 1849498.<br /><br />Further information is available at http://scn.sap.com/docs/DOC-41327.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RSODSO RSODSO_PROCESSING RSODSO_ROLLBACK DSO ODS HDB HANADB change log</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You are using the DBMS SAP HANA SP5 with a revision &gt;= 57.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.30<br /><br />Import Support Package 10 for SAP NetWeaver BW 7.30 (SAPKW73010) into your BW system. The Support Package is available once <strong>SAP Note 1810084</strong> (\"SAPBWNews NW BW 7.30 ABAP SP10\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.31 (SAP NW BW 7.3 Enhancement Package 1)<br /><br />Import Support Package 09 for SAP NetWeaver BW 7.31 (SAPKW73109) into your BW system. The Support Package is available once <strong>SAP Note 1847231</strong> (\"SAPBWNews NW BW 7.31/7.03 ABAP SP9\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<ul>\r\n<li>SAP NetWeaver BW 7.40<br /><br />Import Support Package 4 for SAP NetWeaver BW 7.40 (SAPKW74004) into your BW system. The Support Package is available once <strong>SAP Note 1853730</strong> (\"SAPBWNews NW BW 7.40 ABAP SP04\"), which describes this SP in more detail, has been released for customers.</li>\r\n</ul>\r\n<p><br />In urgent cases, you can implement the correction instructions as an advance correction.<br /><br /><strong>You must first read SAP Note 1668882, which provides information about transaction SNOTE.</strong><br /><br />To provide information in advance, the SAP Notes mentioned above may already be available before the Support Package is released. In this case, the short text of the SAP Note still contains the words \"Preliminary version\".</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA (Data Basis)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D027368)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I044837)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001849497/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001849497/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001849497/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1888106", "RefComponent": "HAN-DB", "RefTitle": "Table RSODSACTUPDTYPE is not found when activating DSO", "RefUrl": "/notes/1888106"}, {"RefNumber": "1895207", "RefComponent": "HAN-DB", "RefTitle": "Delta merge of history tables fails with error 2454", "RefUrl": "/notes/1895207"}, {"RefNumber": "1879656", "RefComponent": "HAN-DB", "RefTitle": "Numeric overflow in commit ids: History table gets unusable", "RefUrl": "/notes/1879656"}, {"RefNumber": "1849498", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "SAP HANA: Reconversion of SAP HANA-optimized DataStores", "RefUrl": "/notes/1849498"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2000002", "RefComponent": "HAN-DB-PERF", "RefTitle": "FAQ: SAP HANA SQL Optimization", "RefUrl": "/notes/2000002 "}, {"RefNumber": "2593710", "RefComponent": "HAN-DB-ENG-BW", "RefTitle": "Getting HANA Optimized DSO Error in EWA Report", "RefUrl": "/notes/2593710 "}, {"RefNumber": "1943498", "RefComponent": "CA-DDF-COR-BWD", "RefTitle": "Reconversion of DSiM 1.0 HANA-optimized DataStores with Support Package 04", "RefUrl": "/notes/1943498 "}, {"RefNumber": "1879656", "RefComponent": "HAN-DB", "RefTitle": "Numeric overflow in commit ids: History table gets unusable", "RefUrl": "/notes/1879656 "}, {"RefNumber": "1895207", "RefComponent": "HAN-DB", "RefTitle": "Delta merge of history tables fails with error 2454", "RefUrl": "/notes/1895207 "}, {"RefNumber": "1849498", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "SAP HANA: Reconversion of SAP HANA-optimized DataStores", "RefUrl": "/notes/1849498 "}, {"RefNumber": "1873687", "RefComponent": "BW-BCT-GEN", "RefTitle": "Improve activation performance for SAP HANA-opt. BI Content", "RefUrl": "/notes/1873687 "}, {"RefNumber": "1873686", "RefComponent": "BW-BCT-GEN", "RefTitle": "BI Content 737/747 SP4: Use of in-memory optimized DSOs", "RefUrl": "/notes/1873686 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW 730", "SupportPackage": "SAPKW73010", "URL": "/supportpackage/SAPKW73010"}, {"SoftwareComponentVersion": "SAP_BW 731", "SupportPackage": "SAPKW73109", "URL": "/supportpackage/SAPKW73109"}, {"SoftwareComponentVersion": "SAP_BW 740", "SupportPackage": "SAPKW74004", "URL": "/supportpackage/SAPKW74004"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 5, "URL": "/corrins/0001849497/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "730", "Number": "1799168 ", "URL": "/notes/1799168 ", "Title": "Release DSO Plannig", "Component": "BW-PLA-IP"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "731", "Number": "1749848 ", "URL": "/notes/1749848 ", "Title": "DSO: SAP HANA-optimized DSOs for quantity conversion", "Component": "BW-WHM-DBA-DSO"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "731", "Number": "1762935 ", "URL": "/notes/1762935 ", "Title": "HANADB: Using the compressed change log (delta)", "Component": "BW-WHM-DBA-DSO"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "731", "Number": "1849497 ", "URL": "/notes/1849497 ", "Title": "SAP HANA: Optimization of standard DataStore objects", "Component": "BW-WHM-DBA-DSO"}, {"SoftwareComponent": "SAP_BW", "ValidFrom": "730", "ValidTo": "740", "Number": "1827823 ", "URL": "/notes/1827823 ", "Title": "HybridProvider DSO UI: Property \"Optimized for SAP HANA\"", "Component": "BW-WHM-DBA-DSO"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}