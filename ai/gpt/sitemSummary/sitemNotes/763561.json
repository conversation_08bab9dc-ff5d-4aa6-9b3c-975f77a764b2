{"Request": {"Number": "763561", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 583, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015739892017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000763561?language=E&token=DEEDC7AFB47A50C3C40C701818B56166"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000763561", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000763561/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "763561"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 46}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.05.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SMG-SDD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Service Data Download"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Solution Manager", "value": "SV-SMG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Service Data Download", "value": "SV-SMG-SDD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SMG-SDD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "763561 - Service Data Control Center (SDCCN)  - FAQ"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>For the planned execution of a service session or for using the EarlyWatch Alert you want to provide service data (aka download) on the use of the ABAP based SAP system. For this purpose, you want to use the Service Data Control Center (transaction SDCCN).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Service Data, RFC Download, EarlyWatch, GoingLive Check,&#160;&#160;EarlyWatch Alert, SDCC, SDCCN, Service Data Control Center, Data Collection, Data Transfer, SAP Solution Manager, Remote Service</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>While using the Service Data Control Center you have encountered problems, or you are looking for basic information.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Below you will find some answers to questions which are often raised in connection with transaction SDCCN (Service Data Control Center).</p>\r\n<p>The functionalities in this tool are described in the documentation at&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/index\">https://help.sap.com/viewer/index</a> -&gt; use the search functionality to search for &#8220;<em>service data control center</em>&#8221;.</p>\r\n<p><strong>Remark:</strong>&#160;Due to changes with ST-PI 740 SP18 this SAP Note is no longer mentioning the \"master flag\" in terms of SDCCN RFC destinations or Service Definition Refresh. Due to a renaming it is now called \"Source\" or \"source flag\".</p>\r\n<p>1. Q: What authorizations are needed for the dialog user to work with SDCCN?</p>\r\n<p>A: The following authorizations exist:</p>\r\n<p>Roles (as of Basis Release 6.10)</p>\r\n<ul>\r\n<li>SAP_SDCCN_DIS Read authorization</li>\r\n<li>SAP_SDCCN_EXE Collect and send data</li>\r\n<li>SAP_SDCCN_ALL Admin authorization; role for service data collection in SDCCN (i.e. the job /BDL/TASK_PROCESSOR)</li>\r\n</ul>\r\n<p>The roles are delivered with ST-PI Support Packages and documented in the Secure Configuration Guide for Solution Manager (search for&#160;<em>Secure Configuration Guide</em>&#160;using the search engine on the&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/product/SAP_Solution_Manager\">SAP Help portal</a>&#160;).</p>\r\n<p>When assigning the roles for the first time please ensure that they contain the corresponding profile.</p>\r\n<p>&#160;</p>\r\n<p>2. Q: What authorizations are needed to work with SDCCN in a Focused Run (FRUN) system?</p>\r\n<p>A: On a FRUN system, specific authorizations are required for the user running the step of job /BDL/TASK_PROCESSOR. Make sure, that this is user FRN_BTC_EWA, the user which is supplied with the FRUN specific authorizations. Please see the FRUN guide -&gt;&#160;<a target=\"_blank\" href=\"https://support.sap.com/en/alm/sap-focused-run/expert-portal.html\">https://support.sap.com/en/alm/sap-focused-run/expert-portal.html</a>&#160;-&gt; SAP Focused Run - Guides</p>\r\n<p>&#160;</p>\r\n<p>3. Q: I am currently using transaction SDCC. How can I get SDCCN?</p>\r\n<p>A: If you have used the old transaction SDCC this must be deactivated prior to the activation of SDCCN. To do this please delete any future background jobs connected with SDCC:</p>\r\n<p>AUTO_SESSION_MANAGER, SESS*, SASM*</p>\r\n<p>Then locally activate SDCCN via 'Utilities -&gt; Activate'.</p>\r\n<p>SDCCN in a satellite system can also be activated from a connected SAP Solution Manager system.</p>\r\n<p>&#160;</p>\r\n<p>4.Q: What is the minimum set up needed for SDCCN?</p>\r\n<p>A: To ensure that SDCCN can continuously monitor your systems for sessions created in connected SAP Solution Manager systems as well as sessions ordered from SAP a number of prerequisites must be met.</p>\r\n<p>SDCCN must be activated as described in the documentation.</p>\r\n<p>After a successful activation the customizing in Goto -&gt; Settings -&gt; Task specific settings is filled with the SAP proposed default values.</p>\r\n<p>In Goto -&gt; Settings -&gt; Task Processor you should see that the Task Processor job is 'active' and scheduled hourly.</p>\r\n<p>Two tasks should have been created:</p>\r\n<p><strong>Service Preparation Check</strong></p>\r\n<p>This is an interactive Task which you can start by clicking on it. This leads you to RTCCTOOL, which gives you an overview of tools and notes needed for the correct preparation of any service session. More information about RTCCTOOL is in SAP Note&#160;<a target=\"_blank\" href=\"/notes/69455\">69455</a>. SAP Note&#160;<a target=\"_blank\" href=\"/notes/91488\">91488</a>&#160;documents the various preparations for a service session. The Service Preparation Check is a periodic task, i.e. when the original task has been executed, the next task of the same type is scheduled as per the customizing in the 'Task specific settings'.</p>\r\n<p><strong>Maintenance package</strong></p>\r\n<p>The maintenance package is a periodic task, which should run every day. It consists of 5 'housekeeping' tasks, which each get executed when necessary.&#160;The frequency of the individual tasks is based on the customizing of the Maintenance settings in the 'Task specific settings'.</p>\r\n<ul>\r\n<li>Session refresh</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">The session refresh which is triggered by a maintenance task checks all destinations known in the list of RFC destinations (Goto -&gt; Settings -&gt; Task specific -&gt; RFC destinations -&gt; Settings). It updates the the list of sessions planned for the system like e.g. the EarlyWatch Alert (EWA).&#160; &#160; &#160; &#160;&#160;</span></p>\r\n<ul>\r\n<li>Service definition refresh</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">The service definition refresh is also based on the above list of destinations. It uses either the destination for which the 'Source' flag has been set, or if this flag has not been set, the destination which is marked to SAP and active. It updates the Service Definitions which define the data to be collected for the EarlyWatch Alert and other service sessions.</span></p>\r\n<ul>\r\n<li>Delete data</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">This task is executed as per the customizing in the task specific setting.</span></p>\r\n<ul>\r\n<li>License data</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">The maintenance certificate available in SAP Solution Manager is retrieved and installed in the managed system. See SAP Note&#160;</span><a target=\"_blank\" href=\"/notes/1280664\" style=\"font-size: 14px;\">1280664</a><span style=\"font-size: 14px;\">&#160;for further information.</span></p>\r\n<ul>\r\n<li>Certificate Renewal</li>\r\n</ul>\r\n<p><span style=\"font-size: 14px;\">Automated renewal of the client certificate of technical communication users. See SAP KBA&#160;</span><a target=\"_blank\" href=\"/notes/2911301\" style=\"font-size: 14px;\">2911301</a>&#160;<span style=\"font-size: 14px;\">for further information.</span></p>\r\n<p>When the Task Processor is active, and Service Preparation Task and Maintenance package Task have been created successfully, no further tasks are needed. For control purposes individual tasks can be created, but these should generally created with mode 'runs once'.</p>\r\n<p>&#160;</p>\r\n<p>5. Q: SDCCN is activated, but SDCC can still be called. How can SDCC be locked?</p>\r\n<p>A: Most likely you have activated SDCCN when the system was on basis release 4.x, and then upgraded to basis release 6.x This has to do with the change of the namespace for the old SDCC , from /BDL/* on basis release 4.x to BDL*. When SDCCN was activated on 4.x SDCC was locked via table /BDL/CUST. In 6.x the relevant table is called BDLCUST and does not yet have the lock entry.</p>\r\n<p>It must be created manually, with report BDLSETUP:</p>\r\n<ul>\r\n<li>KEY = LOCKED</li>\r\n<li>VALUE = X</li>\r\n<li>Delete = unchecked</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>6. Q: When trying to create a task in SDCCN an error message appears:</p>\r\n<p>'No action possible'</p>\r\n<p>A: SDCCN has not yet been initialized correctly, and so the customizing for the tasks has not been filled yet. SDCCN can be initialized locally by following these steps:</p>\r\n<ul>\r\n<li>Ensure that the task processor job is deactivated, via SDCCN -&gt; Goto -&gt; Settings -&gt; Task processor</li>\r\n<li>Then initialize the tool via SDCCN -&gt;Utilities -&gt; Activate.</li>\r\n</ul>\r\n<p>If the initialization was successful you should get the following messages:</p>\r\n<p><em>Local initialization of new Service Data Control Center</em></p>\r\n<p><em>Activation successful</em></p>\r\n<p><em>Destination to SAP created</em></p>\r\n<p><em>AUTO_SESSION_MANAGER descheduled</em></p>\r\n<p><em>Settings filled</em></p>\r\n<p><em>Service Data Control Manager tasks created</em></p>\r\n<p><em>Task processor scheduled</em></p>\r\n<p><em>Jobname /BDL/TASK_PROCESSOR</em></p>\r\n<p>&#160;</p>\r\n<p>7. Q: A task in SDCCN appears 'active' in the 'To do' tab, but the corresponding background job /BDL/TASK_PROCESSOR* has been cancelled. The apparently 'active' task cannot be cancelled.</p>\r\n<p>A: Highlight the task -&gt; right click -&gt; select 'Start now'.</p>\r\n<p>The icon should change to 'matchstick' while the task searches for a free background job. Now you must immediately delete the task (if you wait, the task will be re-started).</p>\r\n<p>&#160;</p>\r\n<p>8. Q: Which destinations are used by SDCCN?</p>\r\n<p>A:</p>\r\n<ul>\r\n<li>Destination to SAP Solution Manager:The connections between managed systems and SAP Solution Manager are created in then Managed System Setup (SOLMAN_SETUP) in the Solution Manager and are described in the configuration guide for SAP Solution Manager. The authorizations needed for that user are described in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2257213\">2257213</a></li>\r\n</ul>\r\n<p>An ABAP system can be connected to several different Solution Manager systems at the same time.</p>\r\n<ul>\r\n<li>Destination to SAP Support Backend:</li>\r\n</ul>\r\n<p>&#160; &#160; &#160; &#160; &#160;- ST-PI 740 SP09 and higher: Two destinations to the SAP Support Backend are required:</p>\r\n<p>&#160; &#160; &#160; &#160; &#160; &#160;SAP-SUPPORT_PORTAL and SAP-SUPPORT_PARCELBOX. Only the SAP-SUPPORT_PORTAL destination is listed in the destinations table of SDCCN. Please see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2664268\">2664268</a>&#160;for details.</p>\r\n<p>&#160; &#160; &#160; &#160; &#160;- ST-PI 2008_1_7xx: Please see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2664268\">2664268</a>&#160;for information about SP dependencies related to the SAP Support Backbone update January 2020. You also have the option to use https connections to SAP Support Backbone by applying SAP Note&#160;<a target=\"_blank\" href=\"/notes/2837310\">2837310</a>.</p>\r\n<p><strong>Remark about destinations SDCC_OSS and SAPOSS:</strong></p>\r\n<p>SAPOSS has been shut down so destinations SAPOSS and SDCC_OSS cannot longer be used so please consider deleting those destinations from SDCCN. Please see SAP Note&#160;<a target=\"_blank\" href=\"/notes/2923799\">2923799</a></p>\r\n<p>&#160;</p>\r\n<p>9. Q: What is the purpose of the 'Source' flag in SDCCN (Goto -&gt; Settings -&gt; Task specific -&gt; RFC destinations -&gt; Settings -&gt; Destinations)?</p>\r\n<p>A: If the 'Source' flag is set the satellite system refreshes the service definitions from the selected source system. Different to managed systems a source must not be set in a Solution Manager system.</p>\r\n<p>&#160;</p>\r\n<p>10. Q: Can I force the Task Processor background job to run on a particular instance?</p>\r\n<p>A: Yes, provided the below conditions apply.</p>\r\n<p>To execute a Task Processor job successfully an instance must fulfill two conditions</p>\r\n<ul>\r\n<li>a background process of class 'C' must be available</li>\r\n<li>the connection to the target system must be functional from this instance.</li>\r\n</ul>\r\n<p>To identify which instances can be used to successfully run the Task Processor background job you can follow this path:</p>\r\n<p>SDCCN -&gt; Goto -&gt; Settings -&gt; Task Processor -&gt; Job settings -&gt; Check hosts. Select the destination you wish to check against -&gt; Confirm</p>\r\n<p>The check will bring back a list of instances. For each you can see if a 'ping' was successful, and if background processes exist.</p>\r\n<p>At the bottom of the list instances which meet both prerequisites are listed.</p>\r\n<p>In SDCCN -&gt; Goto -&gt; Settings -&gt; Task Processor -&gt; Job settings -&gt; Target host you can use the F4 help to choose a host which fulfills the above prerequisites.</p>\r\n<p>Deactivate the Task Processor. Maintain the target host as described above. Reactivate the Task Processor.</p>\r\n<p>All future tasks will be executed on the maintained target host.</p>\r\n<p>&#160;</p>\r\n<p>11. Q: A task 'Data request' was performed. The log for this task shows warnings and/or error messages for some function modules. Where can I find information about these?</p>\r\n<p>A:</p>\r\n<ul>\r\n<li>In the log please double click on the exception for the function module. Many function modules provide a help text.</li>\r\n<li>For some function modules the responsible developer has documented the problem in a note, which can be found by a note search.</li>\r\n<li>For some function modules documentation has been provided in SAP Note&#160;<a target=\"_blank\" href=\"/notes/781680\">781680</a>.</li>\r\n</ul>\r\n<p>&#160;</p>\r\n<p>12. Q: A task was not processed at the time it was scheduled for. Why?</p>\r\n<p>A: How close to the scheduled time a task can be processed depends on the frequency of the 'Task processor'. The Task processor cannot process tasks planned in the future. If, for example, the Task processor is scheduled to run only once a day, at 21:14, all the tasks in the to do list , which are in the past, will be processed after 21:14. The sequence in which they are processed is determined by the time they are scheduled for; the task scheduled for the earliest time is processed first. As mentioned above, the Task processor job should run once an hour.</p>\r\n<p>&#160;</p>\r\n<p>13. Q: Job /BDL/TASK_PROCESSOR is cancelled. In SM37 the job log has entries similar to this:</p>\r\n<p>Job started Step 001 started (program /BDL/TASK_SCHEDULER, variant &amp;0000000000000, user ID &lt;user name&gt;)</p>\r\n<p>Variant &amp;0000000000000 does not exist Job cancelled</p>\r\n<p>How can this be resolved?</p>\r\n<p>A: Deactivate and then reactivate /BDL/TASK_PROCESSOR as follows:</p>\r\n<ul>\r\n<li>Logon to the productive client with a user containing SAP_SDCCN_ALL authorization.</li>\r\n<li>SDCCN-&gt;GoTo-&gt;Settings-&gt;Task Processor-&gt;Change settings-&gt;Deactivate. Enter Change Modus again -&gt; Set Defaults-&gt;Activate.</li>\r\n</ul>\r\n<p>Remark: the job will be run by your dialog user in that case. We recommend to change the step user to standard user SM_SDCCN.</p>\r\n<p>&#160;</p>\r\n<p>14. Q1: In SDCCN-&gt;Task Log, the following message can be seen:</p>\r\n<p>Another task processor in same category is running</p>\r\n<p>Enqueue for category failed</p>\r\n<p>A: While tasks are running lock entries have to be set to prevent the same type of task from running at the exact same time.</p>\r\n<p>If a task is cancelled it is possible that the lock entry doesn't get deleted, even though the corresponding task isn't running anymore. In such a case, the solution is to delete the lock entry manually.</p>\r\n<p>Check if there are any lock entries in sm12 with the following criteria:</p>\r\n<ul>\r\n<li>Table name: /BDL/*</li>\r\n<li>Client:*</li>\r\n<li>Username:*</li>\r\n</ul>\r\n<p>If you find a lock entry, delete it and check if tasks are correctly processed the next time the task processor runs</p>\r\n<p>&#160;</p>\r\n<p>15. Q: /BDL/TASK_PROCESSOR log shows a message like \"User has no authorization to start task processor\" but the job is still shown as being active/scheduled and runs without getting cancelled.</p>\r\n<p>A: Reschedule the task processor with a different user:</p>\r\n<ul>\r\n<li>Logon to the productive client with a user containing SAP_SDCCN_ALL authorization.</li>\r\n<li>SDCCN-&gt;GoTo-&gt;Settings-&gt;Task Processor-&gt;Change settings-&gt;Deactivate.</li>\r\n<li>Enter Change Mode again -&gt; Set Defaults-&gt; Activate.</li>\r\n</ul>\r\n<p>Remark: the job will be run by your dialog user in that case. We recommend to change the step user to standard user SM_SDCCN.</p>\r\n<p>&#160;</p>\r\n<p>16. Q: In the log of a SDCCN task I see the messages:</p>\r\n<p>\"Error transferring session data to SAP-SUPPORT_PORTAL\"</p>\r\n<p>\"&gt; Unauthorized, check authorization of technical communication user of destination SAP-SUPPORT_PARCELBOX\"</p>\r\n<p>How can I resolve this?</p>\r\n<p>A: Please make sure the user used for authentication in destinations SAP-SUPPORT_PORTAL and SAP-SUPPORT_PARCELBOX is a technical communication user. The technical communication user must belong to the same customer as the system (relations defined through customer groups also apply).</p>\r\n<p>&#160;</p>\r\n<p>17. Q: How can I test the destinations SAP-SUPPORT_PORTAL and SAP-SUPPORT_PARCELBOX?</p>\r\n<p>A: The destinations can be tested in SM59. The connection test for SAP-SUPPORT_PARCELBOX and SAP-SUPPORT_PORTAL must return code 200.</p>\r\n<p>To run a functional test use SDCCN -&gt; menu GoTo -&gt; Settings -&gt; Task specific -&gt; RFC Destinations -&gt; Settings: choose the table icon -&gt; Mark the row SAP-SUPPORT_PORTAL in the table Destinations -&gt; F8 or choose the caliper icon (Test RFC destination).</p>\r\n<p>&#160;</p>\r\n<p>18. Q: When testing the destination SAP-SUPPORT_PORTAL in SDCCN, the following message is displayed: 'The user in destination SAP-SUPPORT_PARCELBOX could not be checked. &gt;User comparison for SAP-SUPPORT destinations because API is missing'. How can I resolve this?</p>\r\n<p>A: This is an informational message only and occurs on older releases.</p>\r\n<p>If both destinations SAP-SUPPORT_PORTAL and SAP-SUPPORT_PARCELBOX are tested as functional, the test is passed successful.</p>\r\n<p>&#160;</p>\r\n<p>19. Q: When testing the destination SAP-SUPPORT_PORTAL in SDCCN, the following message is displayed: 'Destination SAP-SUPPORT_PORTAL is not functional. NO_AUTHORIZATION_TO_READ_SM59_DESTINATION.'. How can I resolve this?</p>\r\n<p>A: This is an issue with the authorization of the user user doing the test (the user does not have read authorization for SM59 destinations). Consequently, the test result 'nonfunctional' can be ignored. Either repeat the check with sufficient authorizations or do connection tests in SM59 for both destinations.</p>\r\n<p>&#160;</p>\r\n<p>20. Q: What user should be used as a step user for job /BDL/TASK_PROCESSOR?</p>\r\n<p>A: We recommend to use dedicated user \"SM_SDCCN\" which can be created during the Managed System Configuration. <br />The procedure applies to the Solution Manager itself as well as to the ABAP managed systems connected to the Solution Manager system.<br /><strong>Important:</strong> You must add authorization object AI_LMDB_OB with ACTVT 03 to the relevant user SM_SDCCN in the SAP Solution Manager system itself. This additional authorization is not needed for the user in the managed systems.</p>\r\n<p>Please refer to item '2' above if using a Focused Run system. If using neither Solution Manager nor Focused Run to manage the ABAP system but you want to configure SDCCN, please create a dedicated user of type 'system' and assign authorization role \"SAP_SDCCN_ALL\".</p>\r\n<p>21. Q: May user 'SM_SM2B' also be used as a step user for job /BDL/TASK_PROCESSOR in the Solution Manager System?</p>\r\n<p>A: No, SM_SM2B does not have the required authorizations and is not intended to be used for the /BDL/TASK_PROCESSOR</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D073660)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D073590)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000763561/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000763561/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2715504", "RefComponent": "SV-FRN-INF-SDA", "RefTitle": "Configure Early Watch <PERSON><PERSON>", "RefUrl": "/notes/2715504"}, {"RefNumber": "990856", "RefComponent": "XX-SER-LAS", "RefTitle": "FAQ: Transferring measurement results from LAW", "RefUrl": "/notes/990856"}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488"}, {"RefNumber": "861753", "RefComponent": "XX-SER-LAS", "RefTitle": "LAW: Replacing SDCC with SDCCN", "RefUrl": "/notes/861753"}, {"RefNumber": "792941", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792941"}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680"}, {"RefNumber": "762696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/762696"}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998"}, {"RefNumber": "712511", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/712511"}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455"}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604"}, {"RefNumber": "588128", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/588128"}, {"RefNumber": "2837310", "RefComponent": "SV-SMG-SDD", "RefTitle": "Connecting Legacy Systems Like Solution Manager 7.1 with https to SAP Support Backbone", "RefUrl": "/notes/2837310"}, {"RefNumber": "2664268", "RefComponent": "SV-SMG-SDD", "RefTitle": "New feature of SDCCN for ST-PI 740 SP09 and 2008_1_7xx SP19: use new communication channel to SAP Backbone", "RefUrl": "/notes/2664268"}, {"RefNumber": "2250709", "RefComponent": "SV-SMG-AUT", "RefTitle": "Solution Manager 7.2: End-User Roles and Authorizations Corrections as of SP09 and higher", "RefUrl": "/notes/2250709"}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223"}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032"}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951"}, {"RefNumber": "1470375", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1470375"}, {"RefNumber": "1172939", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1172939"}, {"RefNumber": "1142694", "RefComponent": "XX-SER-LAS", "RefTitle": "FAQ: Transferring measurement results from USMM", "RefUrl": "/notes/1142694"}, {"RefNumber": "1095227", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1095227"}, {"RefNumber": "1062557", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1062557"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3418477", "RefComponent": "SV-SMG-SDD", "RefTitle": "Error \"No customizing items available\" when accessing Task-specific Settings in SDCCN", "RefUrl": "/notes/3418477 "}, {"RefNumber": "3277512", "RefComponent": "SV-SMG-SDD", "RefTitle": "Job “/BDL/TASK_PROCESSOR” cannot be released in HEC environment", "RefUrl": "/notes/3277512 "}, {"RefNumber": "1684537", "RefComponent": "SV-SMG-OP", "RefTitle": "EarlyWatch Alert <PERSON> to SAP: Troubleshooting Guide", "RefUrl": "/notes/1684537 "}, {"RefNumber": "1769513", "RefComponent": "SV-SMG-SER", "RefTitle": "EWA not generated - session data is overdue (red flag status)", "RefUrl": "/notes/1769513 "}, {"RefNumber": "2729186", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "General Process of EWA Generation in SAP Solution Manager 7.2", "RefUrl": "/notes/2729186 "}, {"RefNumber": "1257308", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "FAQ: Using EarlyWatch Alert", "RefUrl": "/notes/1257308 "}, {"RefNumber": "1075827", "RefComponent": "SV-SMG-SER", "RefTitle": "Flag in SDCC: Source System for Service Definitions", "RefUrl": "/notes/1075827 "}, {"RefNumber": "2616023", "RefComponent": "SV-SMG-SDD", "RefTitle": "ST-PI 2008_1_7xx SP19, ST-PI 740 SP09: Enhancements in functionality", "RefUrl": "/notes/2616023 "}, {"RefNumber": "812386", "RefComponent": "XX-SER-NET-HTL", "RefTitle": "RFC connections to SAPNet R/3 front end", "RefUrl": "/notes/812386 "}, {"RefNumber": "69455", "RefComponent": "SV-SMG-SDD", "RefTitle": "Servicetools for Applications ST-A/PI (ST14, RTCCTOOL, ST12)", "RefUrl": "/notes/69455 "}, {"RefNumber": "1834759", "RefComponent": "SV-SMG-SER", "RefTitle": "EarlyWatch Alert: <PERSON><PERSON> <PERSON>s Missing After ST-PI SP08", "RefUrl": "/notes/1834759 "}, {"RefNumber": "781680", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC/ SDCCN - Problems with function modules", "RefUrl": "/notes/781680 "}, {"RefNumber": "1784032", "RefComponent": "SV-SMG-SDD", "RefTitle": "Extension of EWA data by EWM data", "RefUrl": "/notes/1784032 "}, {"RefNumber": "91488", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Support Services - Central preparatory note", "RefUrl": "/notes/91488 "}, {"RefNumber": "617604", "RefComponent": "SV-SMG-SER", "RefTitle": "SAP Services for SAP PI/XI", "RefUrl": "/notes/617604 "}, {"RefNumber": "207223", "RefComponent": "SV-SMG-SDD", "RefTitle": "SAP EarlyWatch Alert Processed at SAP", "RefUrl": "/notes/207223 "}, {"RefNumber": "727998", "RefComponent": "SV-SMG-SDD", "RefTitle": "Complete Replacement of Service Definitions of the Service Data Control Center", "RefUrl": "/notes/727998 "}, {"RefNumber": "1470375", "RefComponent": "SV-SMG-SER-EWA", "RefTitle": "Service Definitions -- When to Refresh them ?", "RefUrl": "/notes/1470375 "}, {"RefNumber": "160777", "RefComponent": "SV-PERF", "RefTitle": "SAP Remote Services for SAP BI/BW", "RefUrl": "/notes/160777 "}, {"RefNumber": "1601951", "RefComponent": "SV-SMG-SER", "RefTitle": "Self Service 'SQL Statement Tuning' - Prerequisites and FAQ", "RefUrl": "/notes/1601951 "}, {"RefNumber": "1142694", "RefComponent": "XX-SER-LAS", "RefTitle": "FAQ: Transferring measurement results from USMM", "RefUrl": "/notes/1142694 "}, {"RefNumber": "848146", "RefComponent": "SV-SMG-SDD", "RefTitle": "SDCC: Collective correction note", "RefUrl": "/notes/848146 "}, {"RefNumber": "990856", "RefComponent": "XX-SER-LAS", "RefTitle": "FAQ: Transferring measurement results from LAW", "RefUrl": "/notes/990856 "}, {"RefNumber": "861753", "RefComponent": "XX-SER-LAS", "RefTitle": "LAW: Replacing SDCC with SDCCN", "RefUrl": "/notes/861753 "}, {"RefNumber": "1062557", "RefComponent": "SV-SMG-SDD", "RefTitle": "Service Data Download without RFC connection", "RefUrl": "/notes/1062557 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}