{"Request": {"Number": "991094", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 977, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016174182017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000991094?language=E&token=03E2C7202EAAB4D230EEF15E0DE49E53"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000991094", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000991094/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "991094"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 14}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "991094 - SAPBWNews BW 7.00 ABAP SP 14"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Support Package 14 for BI Release 7.0</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.0, BW 7.0, BW 7.00, BW Patches, BI, BI 7.00, SAPBINews, NW2004s, 2004s</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBINews for Support Package 14 for SAP NetWeaver 7.0 BI ABAP. Here you can find a list of all notes that describe BI-relevant corrections or enhancements in Support Package 14.  This note will be updated when other notes are added. <br /><br />Note that Support Package 14 is an additional Support Package, which is delivered between Support Package Stack 12 and Support Package Stack 13.  Therefore, the SAP Help Portal documentation (help.sap.com) for this additional Support Package is delivered with the following SAP NetWeaver Support Package Stack 13.<br /><br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected in this Support Package. Enhancements delivered with this Support Package.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><br /></p>\r\n<ul>\r\n<ul>\r\n<li>We recommend that you implement SAP Note 1157796, so that a consistency check is performed for the characteristic 0REQUEST. For more information, see SAP Note 1157796. The source code correction is contained in the standard system as of SP18.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before you import a Support Package, use transaction SNOTE to implement Note 1106385. This prevents a potential source code loss when you use transaction SPAU to adjust obsolete notes. </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Before you import the Support Package, check if there are inactive objects in your system: Call transaction SE80, select \"Inactive objects\" and enter * in the user field.  If objects that are assigned to the SAP namespace are displayed in the section \"Transportable Objects\", you must activate these before you import the Support Package.  If you find objects, proceed as described SAP consulting Note 1131831.  You must never delete inactive objects from the transport request as this would result in extensive follow-up problems after you import the Support Package.  For more detailed information, refer to Note 822379.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Note that the SAP Help Portal documentation (help.sap.com) for the additional NetWeaver 7.0 BI Support Package 14 is updated with the following SAP NetWeaver Support Package Stack. (SAP NetWeaver Support Package Stack 13 inclusive)</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li><strong><strong><span style=\"text-decoration: underline;\">Please find out about the latest SAP Notes that you also have to read and perhaps implement after you import SP16. We recommend that you do not simply indiscriminately implement all SAP Notes that could be implemented after the corresponding Support Package. However, always implement \"Hot News\" and priority 1 notes. In the search function in the notes database or on SAP Service Marketplace, use search term: \"SAPKW70017\", and under \"Search Criteria\", choose \"Extended Search Criteria\"; select \"HotNews\" and \"Correction with high priority\" as the \"Priority\". You can also restrict the search to a specific component by entering the relevant specifications in the \"Applic. Area\" field, for example BW-WHM*. </span></strong></strong>If new Support Packages were released, you should add the name of the new Support Package to the search term (for example, SAPKW70015).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Following the import of a Support Package, <strong>post-implementation steps</strong> are normally <strong>required</strong> in transaction SNOTE. SAP Notes that have already been implemented can become inconsistent and can cause <strong>functions that work incorrectly or syntax errors</strong>. Call transaction <strong>SNOTE</strong> and <strong>reimplement</strong> the notes that have become inconsistent. When you have done this, your BW system is operative and consistent again.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Import the SNOTE corrections first and read the composite SAP Note 875986, which contains important notes about Note Assistant.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You should implement Notes 932065 and 948389 before you use transaction SNOTE to implement advance corrections. Otherwise, problems may occur when you try to deimplement notes again.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If documents have already been migrated to the portal, you may have to repeat the migration. For more information, see Note 950877.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>For information about the compatibility check for the revision of the BI Accelerator for the current Support Package, see Note 1050330.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Due to an enhancement in the Persistent Staging Area (PSA), requests with (PARTNO = 0) may be written. Therefore, implement Note 1064898 (Note 1052447 is then automatically implemented) to prevent this.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>You must implement Note 1085318 to prevent the rules of a transformation from disappearing during the after-import or the content activation.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<ul>\r\n<li>An error may occur after you implement Note 1033967 (contained in Support Package 13), whereby InfoPackages are no longer offered in the input help for process chain maintenance or process maintenance. For more information, see Note 1062704.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A program error occurs in program 'CL_RSR_RRKO_Hierarchy' in form 'APPLY_SLIER_NAV-02- (Brain 299)'  when you execute a query. For more information, see Note 1067594.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Authorization problems may occur when you transport transformations. For more information, see note 1113718.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Delivered transformations are deleted and can no longer be transferred from the content. The active and saved versions of the transformation are not affected.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Errors corrected in this Support Package: </strong></p>\r\n<ul>\r\n<li>In the <strong>end user technology</strong> area:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>No data in MDX with NON EMPTY and WHERE. For more information, see Note 1043819.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A query terminates with 'RTIME_FUELLEN_08-03-'. For more information, see SAP Note 1049491.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>In the <strong>OLAP technology</strong> area:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A query containing characteristics from an InfoSet terminates when the authorized attributes are determined. The query designer terminates when you use display attributes. For more information, see Note 1041760.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>No data in MDX with NON EMPTY and WHERE.  For more information, see Note 1043819.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>No data for include and exclude selection. For more information, see Note 1047688.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>An error occurs for currencies that have more than three decimal places. For more information, see Note 1056015.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>A text variable is not replaced in a query. For more information, see Note 1050669.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>In the <strong>Warehouse Management</strong> area:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Runtime error UNCAUGHT_EXCEPTION occurs in class CL_ RSDA_DAP_N when you create a data archiving process. For more information, see Note 1050037.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Dump RAISE_EXCEPTION occurs when you delete requests. For more information, see Note 1043373.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The error only occurs when you extract data from database platforms IBM DB2, IBM DB4 and MS-SQL-Server. For more information, see Note 1046507.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>DSO extraction does not obtain the entire delta. For more information, see Note 1054499.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000991094/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000991094/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "995937", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX error with combinations of WITH MEMBER and SAP VARIABLES", "RefUrl": "/notes/995937"}, {"RefNumber": "742716", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSDRI_INFOPROV_READ returns illegal_input_range", "RefUrl": "/notes/742716"}, {"RefNumber": "1113718", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Transport: No authorization for object", "RefUrl": "/notes/1113718"}, {"RefNumber": "1085318", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Rules disappear", "RefUrl": "/notes/1085318"}, {"RefNumber": "1079404", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Calling the transport system in BCT batch actvtn", "RefUrl": "/notes/1079404"}, {"RefNumber": "1078718", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Log checked instead of model", "RefUrl": "/notes/1078718"}, {"RefNumber": "1078717", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: No message after error during Unicode check", "RefUrl": "/notes/1078717"}, {"RefNumber": "1078195", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P16:P21:BAPI:BAPI_IPAK_START terminates w/TIMEOUT for event", "RefUrl": "/notes/1078195"}, {"RefNumber": "1078038", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16:P21:AWB:PSA tree: Too much memory consumption RSMONMESS", "RefUrl": "/notes/1078038"}, {"RefNumber": "1078001", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect icon in RSOR for process chains", "RefUrl": "/notes/1078001"}, {"RefNumber": "1076663", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1076663"}, {"RefNumber": "1076314", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Performance: Process always waits one second before start", "RefUrl": "/notes/1076314"}, {"RefNumber": "1075876", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: <PERSON><PERSON><PERSON> is not set to active", "RefUrl": "/notes/1075876"}, {"RefNumber": "1075403", "RefComponent": "BW-WHM", "RefTitle": "Unallowed characters and ALL_CAPITAL", "RefUrl": "/notes/1075403"}, {"RefNumber": "1075206", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Long runtime for query with non-cumul & constant selection", "RefUrl": "/notes/1075206"}, {"RefNumber": "1075159", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P21:BAPI:Bapi_ipak_start waits for events endlessly", "RefUrl": "/notes/1075159"}, {"RefNumber": "1072992", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination RTIME_APPEND-02- in program SAPLRRS2", "RefUrl": "/notes/1072992"}, {"RefNumber": "1072602", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "'Text and Text' displayed in filter dialog on the Web", "RefUrl": "/notes/1072602"}, {"RefNumber": "1071944", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Missing GET_ALL modules for RSPT and EVEN", "RefUrl": "/notes/1071944"}, {"RefNumber": "1071129", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1071129"}, {"RefNumber": "1071067", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Calculate Singlve Value as\" in list without drilldown", "RefUrl": "/notes/1071067"}, {"RefNumber": "1070456", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Create (01) authorization for S_RS_COMP1 is required", "RefUrl": "/notes/1070456"}, {"RefNumber": "1069619", "RefComponent": "BW-WHM-DST", "RefTitle": "BRAIN 299 if you have no authorization for InfoSource", "RefUrl": "/notes/1069619"}, {"RefNumber": "1059978", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selection for documents is incorrect", "RefUrl": "/notes/1059978"}, {"RefNumber": "1059942", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception aggregation incorr used in calculated key figure", "RefUrl": "/notes/1059942"}, {"RefNumber": "1059767", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you input filters in the BEx Web Analyzer", "RefUrl": "/notes/1059767"}, {"RefNumber": "1059759", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "You cannot edit formulas", "RefUrl": "/notes/1059759"}, {"RefNumber": "1059244", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Syntax error in program CL_RSWAD_MIME_REPOSITORY", "RefUrl": "/notes/1059244"}, {"RefNumber": "1059128", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incorrect default values for hierarchy variables", "RefUrl": "/notes/1059128"}, {"RefNumber": "1059024", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Unit or currency conversion type DEFAULTNOT is not found", "RefUrl": "/notes/1059024"}, {"RefNumber": "1058906", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "GET_DIME_VALUES-02 in CL_RSMD_RS_SPECIAL", "RefUrl": "/notes/1058906"}, {"RefNumber": "1058679", "RefComponent": "BW-PLA-IP", "RefTitle": "Several key selections in the select statement", "RefUrl": "/notes/1058679"}, {"RefNumber": "1058274", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Displaying recorded reference data terminates", "RefUrl": "/notes/1058274"}, {"RefNumber": "1057650", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error GETWA_NOT_ASSIGNED in form MEGA_SORT", "RefUrl": "/notes/1057650"}, {"RefNumber": "1057429", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSDRC_MULTIPROV; form SOLVE_CMP-03-", "RefUrl": "/notes/1057429"}, {"RefNumber": "1057286", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in the input-ready query class", "RefUrl": "/notes/1057286"}, {"RefNumber": "1057192", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1057192"}, {"RefNumber": "1056669", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P14:P21:Request red after timeout though load succeeds", "RefUrl": "/notes/1056669"}, {"RefNumber": "1056323", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Cache was used despite virtual characteristics/key figures", "RefUrl": "/notes/1056323"}, {"RefNumber": "1056294", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process can be created for non-cumul InfoCube", "RefUrl": "/notes/1056294"}, {"RefNumber": "1056293", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems when archiving with constant characteristics", "RefUrl": "/notes/1056293"}, {"RefNumber": "1056184", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization log: Termination after saving and back navig", "RefUrl": "/notes/1056184"}, {"RefNumber": "1056068", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with the initial value display", "RefUrl": "/notes/1056068"}, {"RefNumber": "1056060", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Reactivation in the production system", "RefUrl": "/notes/1056060"}, {"RefNumber": "1056011", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you change the view of the Content transformation", "RefUrl": "/notes/1056011"}, {"RefNumber": "1055989", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SAPSQL_INVALID_FIELDNAME when you load data from PSA", "RefUrl": "/notes/1055989"}, {"RefNumber": "1055725", "RefComponent": "BW-BEX-ET", "RefTitle": "Problems for queries with temporal hierarchy join", "RefUrl": "/notes/1055725"}, {"RefNumber": "1055691", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Application log is overwritten/missing", "RefUrl": "/notes/1055691"}, {"RefNumber": "1055583", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination in RSEC_GET_AUTH_FOR_USER", "RefUrl": "/notes/1055583"}, {"RefNumber": "1055493", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Not enough values with hierarchy and master data access mode", "RefUrl": "/notes/1055493"}, {"RefNumber": "1055483", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Renaming system in RDA tables (BDLS)", "RefUrl": "/notes/1055483"}, {"RefNumber": "1055449", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text of variable in variable screen not displayed", "RefUrl": "/notes/1055449"}, {"RefNumber": "1055437", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X299 Brain in CL_RSR program; GET_CHANM-03- form", "RefUrl": "/notes/1055437"}, {"RefNumber": "1055350", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Node positioning changes after data entered in nodes", "RefUrl": "/notes/1055350"}, {"RefNumber": "1055322", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Cannot diagnose near-line connection", "RefUrl": "/notes/1055322"}, {"RefNumber": "1055314", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1055314"}, {"RefNumber": "1055274", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rules not deleted correctly", "RefUrl": "/notes/1055274"}, {"RefNumber": "1055253", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X_299 BRAIN in SAPLRRi2, form CSC_SETZEN-01-", "RefUrl": "/notes/1055253"}, {"RefNumber": "1055227", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "GUI scroll bar in Analyzer var scrn blocks other scroll bar", "RefUrl": "/notes/1055227"}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182"}, {"RefNumber": "1055071", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index for FLOAT key figure with rounding errors", "RefUrl": "/notes/1055071"}, {"RefNumber": "1055044", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Performance in SAPLRRK0, form s_data_fuellen", "RefUrl": "/notes/1055044"}, {"RefNumber": "1055033", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "\"??????????\" cannot be interpreted as a number", "RefUrl": "/notes/1055033"}, {"RefNumber": "1055003", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance probs for hierarchies in Java Runtime (1062537)", "RefUrl": "/notes/1055003"}, {"RefNumber": "1054963", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination for artificial crcy w/more than 5 decimal places", "RefUrl": "/notes/1054963"}, {"RefNumber": "1054812", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unclear errors and dumps for currency and unit rules", "RefUrl": "/notes/1054812"}, {"RefNumber": "1054612", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "DataSource: Deleting source system without dependent objects", "RefUrl": "/notes/1054612"}, {"RefNumber": "1054588", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299: termination in MEGA_SORT_14-01- with invalid filters", "RefUrl": "/notes/1054588"}, {"RefNumber": "1054587", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display hierarchy is changed when you update", "RefUrl": "/notes/1054587"}, {"RefNumber": "1054390", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Random sequence when you refresh queries", "RefUrl": "/notes/1054390"}, {"RefNumber": "1054263", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Cx_rsr_hier_member_not_found is not caught", "RefUrl": "/notes/1054263"}, {"RefNumber": "1054224", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Version and status information in the near-line interface", "RefUrl": "/notes/1054224"}, {"RefNumber": "1054110", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Exception: CX_SY_REF_IS_INITIAL", "RefUrl": "/notes/1054110"}, {"RefNumber": "1054109", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Termination in CL_RSMD_RS and _GET_READMODE_RESTRICT-01", "RefUrl": "/notes/1054109"}, {"RefNumber": "1054103", "RefComponent": "BW-BEX-OT", "RefTitle": "Query $$DEFAULT does not exist (BRAIN 605)", "RefUrl": "/notes/1054103"}, {"RefNumber": "1054065", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving run for write-optimized DataStore terminates", "RefUrl": "/notes/1054065"}, {"RefNumber": "1053962", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Hierarchy ## in not valid for InfoObject ##", "RefUrl": "/notes/1053962"}, {"RefNumber": "1053910", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Program error in FILL_CHARACTERISTIC: INVALID_IOBJNM", "RefUrl": "/notes/1053910"}, {"RefNumber": "1053844", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH: Message \"Source system & does not exist\"", "RefUrl": "/notes/1053844"}, {"RefNumber": "1053735", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Sorting function for SAP BI queries enhanced", "RefUrl": "/notes/1053735"}, {"RefNumber": "1053612", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correctn: Error handling prog contains incorrect source code", "RefUrl": "/notes/1053612"}, {"RefNumber": "1053605", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination 'GET_STATE_OF_AGGREGATES_CR-2-' in change run", "RefUrl": "/notes/1053605"}, {"RefNumber": "1053510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: DWWB takes a long time to set up", "RefUrl": "/notes/1053510"}, {"RefNumber": "1053504", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Descriptions of DataSource fields are missing", "RefUrl": "/notes/1053504"}, {"RefNumber": "1053436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with \"Temporal Hierarchy Join\" in JAVA Web", "RefUrl": "/notes/1053436"}, {"RefNumber": "1053308", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED in reload from archive/near-line", "RefUrl": "/notes/1053308"}, {"RefNumber": "1053229", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Recovering does not generate 3.x DataSource", "RefUrl": "/notes/1053229"}, {"RefNumber": "1053165", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DB connect: Direct access improvements", "RefUrl": "/notes/1053165"}, {"RefNumber": "1053143", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcasting: Endless jobs in parallel processing", "RefUrl": "/notes/1053143"}, {"RefNumber": "1053084", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASSIGN_TYPE_CONFLICT in CONVERT_FROM_PSA", "RefUrl": "/notes/1053084"}, {"RefNumber": "1053054", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Poor performance in hierarchy nodes in the filter", "RefUrl": "/notes/1053054"}, {"RefNumber": "1053052", "RefComponent": "BW-BEX-ET", "RefTitle": "Incorrect characters in long text of error messages", "RefUrl": "/notes/1053052"}, {"RefNumber": "1052941", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Creating shadow indexes in SAP NetWeaver BI Accelerator", "RefUrl": "/notes/1052941"}, {"RefNumber": "1052748", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 mode D for 0INFOPROV", "RefUrl": "/notes/1052748"}, {"RefNumber": "1052703", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After import processing of R3TR SHDS is slow", "RefUrl": "/notes/1052703"}, {"RefNumber": "1052660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error stack can be versioned", "RefUrl": "/notes/1052660"}, {"RefNumber": "1052645", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14:DSO:Further authorization check before request activated", "RefUrl": "/notes/1052645"}, {"RefNumber": "1052626", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1052626"}, {"RefNumber": "1052471", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Input help in Web Application Designer for structure member", "RefUrl": "/notes/1052471"}, {"RefNumber": "1052037", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Exception reporting: Exception visualization, scaling", "RefUrl": "/notes/1052037"}, {"RefNumber": "1052031", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request: Termination after verification phase", "RefUrl": "/notes/1052031"}, {"RefNumber": "1051994", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH:Selecting excluding DS/source syst combinations", "RefUrl": "/notes/1051994"}, {"RefNumber": "1051979", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Web service DataSource cannot be activated", "RefUrl": "/notes/1051979"}, {"RefNumber": "1051767", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Terminatn in RRBA_NUMBER_GET_BW \"commit connection DEFAULT\"", "RefUrl": "/notes/1051767"}, {"RefNumber": "1051664", "RefComponent": "BW-WHM", "RefTitle": "Check and repair program BW7.x for Note 849857", "RefUrl": "/notes/1051664"}, {"RefNumber": "1051652", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message: Target field is missing/dump w/ rule w/ units", "RefUrl": "/notes/1051652"}, {"RefNumber": "1051651", "RefComponent": "BW-BEX-ET", "RefTitle": "Error when you personalize variables", "RefUrl": "/notes/1051651"}, {"RefNumber": "1051623", "RefComponent": "BW-BEX-OT", "RefTitle": "Adjustments of transaction RSTT (usability)", "RefUrl": "/notes/1051623"}, {"RefNumber": "1051614", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump occurs in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1051614"}, {"RefNumber": "1051291", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DBMAN 305: Error when reading data of InfoProvider ...$N", "RefUrl": "/notes/1051291"}, {"RefNumber": "1051246", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Cursor position gets lost in routine", "RefUrl": "/notes/1051246"}, {"RefNumber": "1051170", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "InfoObject &1 is not available in version &2", "RefUrl": "/notes/1051170"}, {"RefNumber": "1051168", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: SUCC_PRED_GET-2 in prog CL_RSR_HIERARCHY_BINCL", "RefUrl": "/notes/1051168"}, {"RefNumber": "1051142", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Dump during activation of update<PERSON>les (content installation)", "RefUrl": "/notes/1051142"}, {"RefNumber": "1051127", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data in a very particular situation", "RefUrl": "/notes/1051127"}, {"RefNumber": "1051080", "RefComponent": "BW-BCT-TCT", "RefTitle": "RSDRI runtime statistics contain caller", "RefUrl": "/notes/1051080"}, {"RefNumber": "1051055", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "InfoSource remains in the status \"Not Executable\"", "RefUrl": "/notes/1051055"}, {"RefNumber": "1051036", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "You cannot restart change run", "RefUrl": "/notes/1051036"}, {"RefNumber": "1051005", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "After error, status of archiving request remains \"yellow\"(1)", "RefUrl": "/notes/1051005"}, {"RefNumber": "1050948", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not enough texts for RSPCVARIANTT", "RefUrl": "/notes/1050948"}, {"RefNumber": "1050889", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH-02-01", "RefUrl": "/notes/1050889"}, {"RefNumber": "1050868", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "ASSERTION_FAILED in MODIFY_SEGMENT when you activate a DAP", "RefUrl": "/notes/1050868"}, {"RefNumber": "1050783", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems in logging during removal in archive and nearline", "RefUrl": "/notes/1050783"}, {"RefNumber": "1050695", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_TABLES_NOT_COMPATIBLE", "RefUrl": "/notes/1050695"}, {"RefNumber": "1050669", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1050669"}, {"RefNumber": "1050379", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Not enough fields requested when loading w/o PSA", "RefUrl": "/notes/1050379"}, {"RefNumber": "1050330", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 14 BIA revision compatibility check extended", "RefUrl": "/notes/1050330"}, {"RefNumber": "1050327", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Non-cumulatives and formula variables from replacement path", "RefUrl": "/notes/1050327"}, {"RefNumber": "1050293", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "General pattern in InfoPackage selection is not supported", "RefUrl": "/notes/1050293"}, {"RefNumber": "1050208", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination BRAIN 299 _GET_AUTH_RESTRICT-03 in CL_RSMD_RS", "RefUrl": "/notes/1050208"}, {"RefNumber": "1050097", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Formula variable not replaced (hierarchy deactivated)", "RefUrl": "/notes/1050097"}, {"RefNumber": "1049998", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump loading data into open hub destn; incorrect error msg", "RefUrl": "/notes/1049998"}, {"RefNumber": "1049932", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error \"CX_SY_REF_IS_INITIAL\" when you go from ALV to BEx", "RefUrl": "/notes/1049932"}, {"RefNumber": "1049857", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Reason for BIA error in messages missing", "RefUrl": "/notes/1049857"}, {"RefNumber": "1049735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect status after deallocation", "RefUrl": "/notes/1049735"}, {"RefNumber": "1049700", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL: Dynamic hierarchy selection and new headers", "RefUrl": "/notes/1049700"}, {"RefNumber": "1049691", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in CL_RSR_RRK0_RQTS->_CHECK_DELTAPAIR_01", "RefUrl": "/notes/1049691"}, {"RefNumber": "1049672", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Application component not found during collection", "RefUrl": "/notes/1049672"}, {"RefNumber": "1049671", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSR_RRK0_PARTITION and IF_RSR_RRK0_PARTITION~BROWSE-01-", "RefUrl": "/notes/1049671"}, {"RefNumber": "1049623", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Scheduling if server does not exist", "RefUrl": "/notes/1049623"}, {"RefNumber": "1049564", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data with exception aggregation and two structures", "RefUrl": "/notes/1049564"}, {"RefNumber": "1049506", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Specifying the sequence during rebuilding for BIA", "RefUrl": "/notes/1049506"}, {"RefNumber": "1049469", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Switching from ABAP program", "RefUrl": "/notes/1049469"}, {"RefNumber": "1049456", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Index errors reported in DB02 after migration to DB6", "RefUrl": "/notes/1049456"}, {"RefNumber": "1049406", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Empty BIA index is set to active", "RefUrl": "/notes/1049406"}, {"RefNumber": "1049403", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message \"Incorrect call sequence\" when you delete", "RefUrl": "/notes/1049403"}, {"RefNumber": "1049261", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Short dump for RSRV check to compary keyfigures", "RefUrl": "/notes/1049261"}, {"RefNumber": "1049258", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Delta cache data displayed twice in certain circumstances", "RefUrl": "/notes/1049258"}, {"RefNumber": "1049209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 Komponente G_S_KENNZ-%F_$P$ unbekannt", "RefUrl": "/notes/1049209"}, {"RefNumber": "1049187", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction for CTC (creating source system in background)", "RefUrl": "/notes/1049187"}, {"RefNumber": "1049157", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect color for nodes/leaves in CCMS for BIA", "RefUrl": "/notes/1049157"}, {"RefNumber": "1049147", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Deadlock on hierarchy table", "RefUrl": "/notes/1049147"}, {"RefNumber": "1049141", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA type group on IF_RSDAI_NEARLINE_CONNECTION interface", "RefUrl": "/notes/1049141"}, {"RefNumber": "1048972", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "No authorization for a transformation that does not exist", "RefUrl": "/notes/1048972"}, {"RefNumber": "1048947", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Corr:Source syst names,pseudo D versions for transformations", "RefUrl": "/notes/1048947"}, {"RefNumber": "1048923", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in SAPLRRI2; form INPUTABLE_CHAFREE-02-", "RefUrl": "/notes/1048923"}, {"RefNumber": "1048864", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close empty requests", "RefUrl": "/notes/1048864"}, {"RefNumber": "1048857", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Activating the default Web templates", "RefUrl": "/notes/1048857"}, {"RefNumber": "1048701", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Sending to printer: No printout with scheduling", "RefUrl": "/notes/1048701"}, {"RefNumber": "1048690", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error messages and error logs not issued", "RefUrl": "/notes/1048690"}, {"RefNumber": "1048648", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI7.0(SP14) DataStore: Deletion of request terminates", "RefUrl": "/notes/1048648"}, {"RefNumber": "1048506", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSource vers converted during transformation", "RefUrl": "/notes/1048506"}, {"RefNumber": "1048505", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Execution authorization for generated queries with '!' signs", "RefUrl": "/notes/1048505"}, {"RefNumber": "1048504", "RefComponent": "BW-WHM-AWB", "RefTitle": "P14: Administering PSA to new DS-PSA displays incorrect icon", "RefUrl": "/notes/1048504"}, {"RefNumber": "1048502", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump 'connection closed' when window is closed", "RefUrl": "/notes/1048502"}, {"RefNumber": "1048477", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048477"}, {"RefNumber": "1048423", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "R7 105 during migration of update rules", "RefUrl": "/notes/1048423"}, {"RefNumber": "1048350", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Incorrect text in error step in error DTP", "RefUrl": "/notes/1048350"}, {"RefNumber": "1048349", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Connection errors with multiple events and collector", "RefUrl": "/notes/1048349"}, {"RefNumber": "1048345", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Check function in InfoCube administration", "RefUrl": "/notes/1048345"}, {"RefNumber": "1048344", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Checkman: Various checkman problems", "RefUrl": "/notes/1048344"}, {"RefNumber": "1048290", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1048290"}, {"RefNumber": "1048280", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Additional corrections for Note 1002682", "RefUrl": "/notes/1048280"}, {"RefNumber": "1048253", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Template migration", "RefUrl": "/notes/1048253"}, {"RefNumber": "1048227", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data or too much data in rare cases (after SP13)", "RefUrl": "/notes/1048227"}, {"RefNumber": "1048178", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Default member for virtual hierarchies incorrect", "RefUrl": "/notes/1048178"}, {"RefNumber": "1048161", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Set QM status for yellow requests using funcion module", "RefUrl": "/notes/1048161"}, {"RefNumber": "1048114", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjusting RSRV checks for the BI accelerator", "RefUrl": "/notes/1048114"}, {"RefNumber": "1048100", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A299 in SAPLRRI2 and CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1048100"}, {"RefNumber": "1048095", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Checking variables and I_STEP equal to three in exit", "RefUrl": "/notes/1048095"}, {"RefNumber": "1048091", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1048091"}, {"RefNumber": "1048078", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Target*___T unknown when you transport update rules", "RefUrl": "/notes/1048078"}, {"RefNumber": "1048015", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Rollup: Exception INCONSISTENT_N_O_REQS", "RefUrl": "/notes/1048015"}, {"RefNumber": "1047992", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: For internal use", "RefUrl": "/notes/1047992"}, {"RefNumber": "1047978", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy auth. and intervals: \"No authorization\", EYE 007", "RefUrl": "/notes/1047978"}, {"RefNumber": "1047852", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Selection in MEMBERS rowset for [measures] or structures", "RefUrl": "/notes/1047852"}, {"RefNumber": "1047840", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS + RSDS not deleted", "RefUrl": "/notes/1047840"}, {"RefNumber": "1047829", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:BATCH: Cancel work process in a 'safe' way", "RefUrl": "/notes/1047829"}, {"RefNumber": "1047776", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Too many process variants when deleting a DAP", "RefUrl": "/notes/1047776"}, {"RefNumber": "1047735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: You cannot transport interrupt", "RefUrl": "/notes/1047735"}, {"RefNumber": "1047688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query returns no data (including and excluding selection)", "RefUrl": "/notes/1047688"}, {"RefNumber": "1047598", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Manage:QM action on loaded request in DSO", "RefUrl": "/notes/1047598"}, {"RefNumber": "1047527", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "\"Flat\" BIA index: Indexing performance with line items", "RefUrl": "/notes/1047527"}, {"RefNumber": "1047497", "RefComponent": "BW", "RefTitle": "Date dialog disappears when choosing in Hebrew / RTL", "RefUrl": "/notes/1047497"}, {"RefNumber": "1047492", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1047492"}, {"RefNumber": "1047299", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays data of a deleted request", "RefUrl": "/notes/1047299"}, {"RefNumber": "1047176", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Undeclared exception in the error handling", "RefUrl": "/notes/1047176"}, {"RefNumber": "1047174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect scope when locking in process chains", "RefUrl": "/notes/1047174"}, {"RefNumber": "1047023", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Termination CL_RSMD_RS_SPECIAL and _CHECK_SELOPT-04-", "RefUrl": "/notes/1047023"}, {"RefNumber": "1046544", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems: Migrating w/source fields with fixed unit/currency", "RefUrl": "/notes/1046544"}, {"RefNumber": "1046507", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "SP 11: Error in DB connect for DB2, DB4 and MS-SQL-Server", "RefUrl": "/notes/1046507"}, {"RefNumber": "1046465", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface: Saving an InfoSource", "RefUrl": "/notes/1046465"}, {"RefNumber": "1046446", "RefComponent": "BW-BEX-OT", "RefTitle": "CX_SY_REF_IS_INITIAL in RSRT when you execute OK code RALL", "RefUrl": "/notes/1046446"}, {"RefNumber": "1046394", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Performance:RSSELDONE:HASH_CREATE:Index access PID", "RefUrl": "/notes/1046394"}, {"RefNumber": "1046312", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you assign key figure with fixed unit/currency", "RefUrl": "/notes/1046312"}, {"RefNumber": "1046270", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Additional fixed filter for the query runtime", "RefUrl": "/notes/1046270"}, {"RefNumber": "1046254", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Performance optimization of the verification phase", "RefUrl": "/notes/1046254"}, {"RefNumber": "1046233", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299(BRAIN) in program SAPLRRI2 and form FST_VAR_06-01-", "RefUrl": "/notes/1046233"}, {"RefNumber": "1046127", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you collect transformations", "RefUrl": "/notes/1046127"}, {"RefNumber": "1046066", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECAUTH:Cust exist variables for non-string characteristics", "RefUrl": "/notes/1046066"}, {"RefNumber": "1046003", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN X299 in SAPLRRK0; form MEGA_SORT_14-01-", "RefUrl": "/notes/1046003"}, {"RefNumber": "1045950", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RFC dump when calling FM RSS2_CALL_FCODE_EMPTY_GLOBALS", "RefUrl": "/notes/1045950"}, {"RefNumber": "1045923", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization for key figure (0TCAKYFNM) in InfoSet", "RefUrl": "/notes/1045923"}, {"RefNumber": "1045919", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No filter when accessing file DataSource directly", "RefUrl": "/notes/1045919"}, {"RefNumber": "1045861", "RefComponent": "BW-WHM-DST", "RefTitle": "Directly accessing time-dependent master data texts", "RefUrl": "/notes/1045861"}, {"RefNumber": "1045784", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14: DSO: DTP: Check on full REQU requests not required", "RefUrl": "/notes/1045784"}, {"RefNumber": "1045741", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA check for random queries", "RefUrl": "/notes/1045741"}, {"RefNumber": "1045735", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checking characteristic values", "RefUrl": "/notes/1045735"}, {"RefNumber": "1045734", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1045734"}, {"RefNumber": "1045711", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Attributes for characteristics with temporal hierarchy join", "RefUrl": "/notes/1045711"}, {"RefNumber": "1045624", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "'inlist of sids, but number-flag not set'", "RefUrl": "/notes/1045624"}, {"RefNumber": "1045560", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text symbol with value for hierarchy variable", "RefUrl": "/notes/1045560"}, {"RefNumber": "1045548", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Monitoring of RDA InfoPackage requests", "RefUrl": "/notes/1045548"}, {"RefNumber": "1045305", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect record mode rule is not reported", "RefUrl": "/notes/1045305"}, {"RefNumber": "1045300", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Display of non-postable values shows SIDs w/out master data", "RefUrl": "/notes/1045300"}, {"RefNumber": "1045296", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "RSRA_CLUSTER_TABLE_REORG dumps with an error.", "RefUrl": "/notes/1045296"}, {"RefNumber": "1045174", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation/routine missing when you collect content", "RefUrl": "/notes/1045174"}, {"RefNumber": "1045114", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and near-line storage", "RefUrl": "/notes/1045114"}, {"RefNumber": "1045063", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "No quantitiy conversion (target quantity unit from variable)", "RefUrl": "/notes/1045063"}, {"RefNumber": "1045054", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource replication: Shrt dump TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1045054"}, {"RefNumber": "1045008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RRK_LIST_OPEN: CX_SY_REF_IS_INITIAL UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1045008"}, {"RefNumber": "1044991", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "OBSOLETE", "RefUrl": "/notes/1044991"}, {"RefNumber": "1044860", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1044860"}, {"RefNumber": "1044808", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error msg in the after import/during replication", "RefUrl": "/notes/1044808"}, {"RefNumber": "1044708", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Performance of generic time derivation", "RefUrl": "/notes/1044708"}, {"RefNumber": "1044494", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by incorrect value", "RefUrl": "/notes/1044494"}, {"RefNumber": "1044398", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE 007 \" No auth.\" for several authorizations", "RefUrl": "/notes/1044398"}, {"RefNumber": "1044378", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Delete free processes in chain deletion", "RefUrl": "/notes/1044378"}, {"RefNumber": "1044377", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process saved in scheduling", "RefUrl": "/notes/1044377"}, {"RefNumber": "1044290", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIAMon SP 11 and 12 revision check - Revision is too low", "RefUrl": "/notes/1044290"}, {"RefNumber": "1044197", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule is created twice / Note 1036440 was implemented", "RefUrl": "/notes/1044197"}, {"RefNumber": "1044181", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Too many requests selected for DTP from DWWB", "RefUrl": "/notes/1044181"}, {"RefNumber": "1044110", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "INITRANS for InfoCube indexes in BW 7", "RefUrl": "/notes/1044110"}, {"RefNumber": "1044054", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Shadow DTP with DataSource cannot be displayed", "RefUrl": "/notes/1044054"}, {"RefNumber": "1044052", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DBC attribute can be changed in the DTP", "RefUrl": "/notes/1044052"}, {"RefNumber": "1044015", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Check whether all data has arrived in the BIA", "RefUrl": "/notes/1044015"}, {"RefNumber": "1044009", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Changing data class in reclustering not possible", "RefUrl": "/notes/1044009"}, {"RefNumber": "1043971", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination in RSDRI_CUBE_WRITE_PACKAGE for incorrect data", "RefUrl": "/notes/1043971"}, {"RefNumber": "1043949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checking compound values", "RefUrl": "/notes/1043949"}, {"RefNumber": "1043945", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Long runtime for F4 in 3.X variable screen", "RefUrl": "/notes/1043945"}, {"RefNumber": "1043919", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many authorizations w/ exclusive hierarchy authorization", "RefUrl": "/notes/1043919"}, {"RefNumber": "1043819", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No data for NON EMPTY and WHERE", "RefUrl": "/notes/1043819"}, {"RefNumber": "1043781", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "booked_values='A' does not work in 3.X front end", "RefUrl": "/notes/1043781"}, {"RefNumber": "1043724", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1043724"}, {"RefNumber": "1043456", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in functions BAPI_TD...", "RefUrl": "/notes/1043456"}, {"RefNumber": "1043343", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting the rule from the technical group deletes the group", "RefUrl": "/notes/1043343"}, {"RefNumber": "1043295", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Mannually interrupted run is continued", "RefUrl": "/notes/1043295"}, {"RefNumber": "1043231", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1043231"}, {"RefNumber": "1043192", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: English texts in report RSREQARCH_CHECK_HASH_ARCHIVE", "RefUrl": "/notes/1043192"}, {"RefNumber": "1043170", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Missing impact for currency translation and unit conversion", "RefUrl": "/notes/1043170"}, {"RefNumber": "1043153", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "'&' is not a valid characteristic for InfoProvider '&'", "RefUrl": "/notes/1043153"}, {"RefNumber": "1043132", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Toggle \"Use Temporal Hierarchy Join\" property", "RefUrl": "/notes/1043132"}, {"RefNumber": "1043103", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "A299: System error in CL_RSR_CHABIT and SET_BIT0-01-", "RefUrl": "/notes/1043103"}, {"RefNumber": "1043053", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Data type in open hub destination is incorrect", "RefUrl": "/notes/1043053"}, {"RefNumber": "1043039", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 14", "RefUrl": "/notes/1043039"}, {"RefNumber": "1043035", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Monitor does not display errors", "RefUrl": "/notes/1043035"}, {"RefNumber": "1043008", "RefComponent": "BW-WHM-DST", "RefTitle": "Key date selection ineffective for char with direct access", "RefUrl": "/notes/1043008"}, {"RefNumber": "1043000", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA: Change check whether request is updated", "RefUrl": "/notes/1043000"}, {"RefNumber": "1042924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program CL_RSR_OLAP and form FILL_DIM-01-", "RefUrl": "/notes/1042924"}, {"RefNumber": "1042920", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages in transformation", "RefUrl": "/notes/1042920"}, {"RefNumber": "1042896", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Release of memory after you close query", "RefUrl": "/notes/1042896"}, {"RefNumber": "1042895", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "RRS_VARIANT_CREATE: Incorrect variant values", "RefUrl": "/notes/1042895"}, {"RefNumber": "1042893", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Problems with large queries and quantity conversion", "RefUrl": "/notes/1042893"}, {"RefNumber": "1042883", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RSBATCH_WRITE_PROT_TO_APPLLOG and incorrect error msg", "RefUrl": "/notes/1042883"}, {"RefNumber": "1042881", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:No InfoPackage scheduler call without active DS", "RefUrl": "/notes/1042881"}, {"RefNumber": "1042827", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.0 (SP14): DataStore object - Batch Manager", "RefUrl": "/notes/1042827"}, {"RefNumber": "1042816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: No messages displayed in summary", "RefUrl": "/notes/1042816"}, {"RefNumber": "1042790", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P14: Real-time: QM action for real-time PSA request", "RefUrl": "/notes/1042790"}, {"RefNumber": "1042712", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: SQL error when extracting many requests", "RefUrl": "/notes/1042712"}, {"RefNumber": "1042703", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: IF_RSR_VAR_DEF~GET_VRNID_VALUE_RANGE_CMP-02-", "RefUrl": "/notes/1042703"}, {"RefNumber": "1042677", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication in application components is slow", "RefUrl": "/notes/1042677"}, {"RefNumber": "1042675", "RefComponent": "BW-WHM-AWB", "RefTitle": "Mapping is missing, search in workbench terminates, RSAR 203", "RefUrl": "/notes/1042675"}, {"RefNumber": "1042673", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Error: Assignment details in the report-report interface", "RefUrl": "/notes/1042673"}, {"RefNumber": "1042599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when creating a transformation between ISet and InfSrc", "RefUrl": "/notes/1042599"}, {"RefNumber": "1042590", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Application log (SLG1) for real-Ttme data acquisition (RDA)", "RefUrl": "/notes/1042590"}, {"RefNumber": "1042560", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Texts without language keys cannot be extracted", "RefUrl": "/notes/1042560"}, {"RefNumber": "1042502", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Not all leaves visible in display hierarchy", "RefUrl": "/notes/1042502"}, {"RefNumber": "1042463", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "ORA-00904: \"?\".\"DATE0\": invalid identifier", "RefUrl": "/notes/1042463"}, {"RefNumber": "1042461", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help for time characteristic in Q mode takes too long", "RefUrl": "/notes/1042461"}, {"RefNumber": "1042453", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: CheckMan: Domain exchange; package concept", "RefUrl": "/notes/1042453"}, {"RefNumber": "1042452", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: Generic InfoPackage deletion with searching for package", "RefUrl": "/notes/1042452"}, {"RefNumber": "1042451", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:APO:Planning: Dialog boxes when planning requ is closed", "RefUrl": "/notes/1042451"}, {"RefNumber": "1042391", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 Brain in SAPLRSDRC_SPLIT; form RANGE_EQUAL-01-", "RefUrl": "/notes/1042391"}, {"RefNumber": "1042390", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump after entering InfoObject when master data is read", "RefUrl": "/notes/1042390"}, {"RefNumber": "1042389", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting incorrect dialog for InfoSource", "RefUrl": "/notes/1042389"}, {"RefNumber": "1042388", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems when transporting deletions", "RefUrl": "/notes/1042388"}, {"RefNumber": "1042340", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA admin in DWB called only with maintain authorization", "RefUrl": "/notes/1042340"}, {"RefNumber": "1042339", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA:Deleting from PSA terminates with message RSM2 709", "RefUrl": "/notes/1042339"}, {"RefNumber": "1042338", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:UNIT fields cannot be selected in 7.x DataSource", "RefUrl": "/notes/1042338"}, {"RefNumber": "1042337", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: SDL: InfoPackage queries whether you want to save", "RefUrl": "/notes/1042337"}, {"RefNumber": "1042299", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport check also performed in display mode", "RefUrl": "/notes/1042299"}, {"RefNumber": "1042296", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: No information for InfoSet in monitor tree", "RefUrl": "/notes/1042296"}, {"RefNumber": "1041994", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect data type in routine and formula and constants", "RefUrl": "/notes/1041994"}, {"RefNumber": "1041827", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Activating the technical content: \"UNCAUGHT_EXCEPTION\"", "RefUrl": "/notes/1041827"}, {"RefNumber": "1041822", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Optimizing the \"Change run monitor\"", "RefUrl": "/notes/1041822"}, {"RefNumber": "1041760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination: Authorized attribs with InfoSet characteristics", "RefUrl": "/notes/1041760"}, {"RefNumber": "1041515", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating authorizations requires ODS values at least", "RefUrl": "/notes/1041515"}, {"RefNumber": "1041306", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: \"Record not in cross-rec.\" when buffer displayed", "RefUrl": "/notes/1041306"}, {"RefNumber": "1041284", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Incorrect sorting when you extract from the PSA", "RefUrl": "/notes/1041284"}, {"RefNumber": "1041000", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Program not valid for request creation", "RefUrl": "/notes/1041000"}, {"RefNumber": "1040649", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance when temporal hierarchy join is used", "RefUrl": "/notes/1040649"}, {"RefNumber": "1040293", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimizations for writable InfoProviders", "RefUrl": "/notes/1040293"}, {"RefNumber": "1040291", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Compound attribute value displayed incorrectly", "RefUrl": "/notes/1040291"}, {"RefNumber": "1040278", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Return code 12 after query transport", "RefUrl": "/notes/1040278"}, {"RefNumber": "1040071", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM Check Status: Msg R7 245 InfoObject not available", "RefUrl": "/notes/1040071"}, {"RefNumber": "1039071", "RefComponent": "BW-WHM-DST", "RefTitle": "P13:SDL:Content:InfoPackage activation terminates - RSM 142", "RefUrl": "/notes/1039071"}, {"RefNumber": "1038836", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P14:DSO:DTP:Deactivating consistency check for DTP requests", "RefUrl": "/notes/1038836"}, {"RefNumber": "1014312", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System messages are only displayed after you navigate", "RefUrl": "/notes/1014312"}, {"RefNumber": "1003456", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Excel download: x:str problems with Broadcaster", "RefUrl": "/notes/1003456"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1051664", "RefComponent": "BW-WHM", "RefTitle": "Check and repair program BW7.x for Note 849857", "RefUrl": "/notes/1051664 "}, {"RefNumber": "1053735", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Sorting function for SAP BI queries enhanced", "RefUrl": "/notes/1053735 "}, {"RefNumber": "1052941", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Creating shadow indexes in SAP NetWeaver BI Accelerator", "RefUrl": "/notes/1052941 "}, {"RefNumber": "1056060", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Reactivation in the production system", "RefUrl": "/notes/1056060 "}, {"RefNumber": "1075876", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: <PERSON><PERSON><PERSON> is not set to active", "RefUrl": "/notes/1075876 "}, {"RefNumber": "1055483", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Renaming system in RDA tables (BDLS)", "RefUrl": "/notes/1055483 "}, {"RefNumber": "1053165", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DB connect: Direct access improvements", "RefUrl": "/notes/1053165 "}, {"RefNumber": "1075403", "RefComponent": "BW-WHM", "RefTitle": "Unallowed characters and ALL_CAPITAL", "RefUrl": "/notes/1075403 "}, {"RefNumber": "1055583", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination in RSEC_GET_AUTH_FOR_USER", "RefUrl": "/notes/1055583 "}, {"RefNumber": "1051767", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Terminatn in RRBA_NUMBER_GET_BW \"commit connection DEFAULT\"", "RefUrl": "/notes/1051767 "}, {"RefNumber": "1113718", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP17): Transport: No authorization for object", "RefUrl": "/notes/1113718 "}, {"RefNumber": "1048477", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048477 "}, {"RefNumber": "1049672", "RefComponent": "BW-WHM-MTD-INST", "RefTitle": "Application component not found during collection", "RefUrl": "/notes/1049672 "}, {"RefNumber": "1048078", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Target*___T unknown when you transport update rules", "RefUrl": "/notes/1048078 "}, {"RefNumber": "1043781", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "booked_values='A' does not work in 3.X front end", "RefUrl": "/notes/1043781 "}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182 "}, {"RefNumber": "1051291", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DBMAN 305: Error when reading data of InfoProvider ...$N", "RefUrl": "/notes/1051291 "}, {"RefNumber": "1071067", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "\"Calculate Singlve Value as\" in list without drilldown", "RefUrl": "/notes/1071067 "}, {"RefNumber": "1041994", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect data type in routine and formula and constants", "RefUrl": "/notes/1041994 "}, {"RefNumber": "1049998", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump loading data into open hub destn; incorrect error msg", "RefUrl": "/notes/1049998 "}, {"RefNumber": "1085318", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI7.0(SP16) Rules disappear", "RefUrl": "/notes/1085318 "}, {"RefNumber": "1048502", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump 'connection closed' when window is closed", "RefUrl": "/notes/1048502 "}, {"RefNumber": "1058274", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Displaying recorded reference data terminates", "RefUrl": "/notes/1058274 "}, {"RefNumber": "1049157", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect color for nodes/leaves in CCMS for BIA", "RefUrl": "/notes/1049157 "}, {"RefNumber": "1051651", "RefComponent": "BW-BEX-ET", "RefTitle": "Error when you personalize variables", "RefUrl": "/notes/1051651 "}, {"RefNumber": "1041827", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Activating the technical content: \"UNCAUGHT_EXCEPTION\"", "RefUrl": "/notes/1041827 "}, {"RefNumber": "1042299", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport check also performed in display mode", "RefUrl": "/notes/1042299 "}, {"RefNumber": "1047688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query returns no data (including and excluding selection)", "RefUrl": "/notes/1047688 "}, {"RefNumber": "1053605", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination 'GET_STATE_OF_AGGREGATES_CR-2-' in change run", "RefUrl": "/notes/1053605 "}, {"RefNumber": "1042827", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.0 (SP14): DataStore object - Batch Manager", "RefUrl": "/notes/1042827 "}, {"RefNumber": "1054110", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Exception: CX_SY_REF_IS_INITIAL", "RefUrl": "/notes/1054110 "}, {"RefNumber": "1047299", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays data of a deleted request", "RefUrl": "/notes/1047299 "}, {"RefNumber": "1071944", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Missing GET_ALL modules for RSPT and EVEN", "RefUrl": "/notes/1071944 "}, {"RefNumber": "1059942", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Exception aggregation incorr used in calculated key figure", "RefUrl": "/notes/1059942 "}, {"RefNumber": "1070456", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Create (01) authorization for S_RS_COMP1 is required", "RefUrl": "/notes/1070456 "}, {"RefNumber": "1048091", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048091 "}, {"RefNumber": "1045054", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource replication: Shrt dump TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1045054 "}, {"RefNumber": "1048947", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Corr:Source syst names,pseudo D versions for transformations", "RefUrl": "/notes/1048947 "}, {"RefNumber": "1051005", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "After error, status of archiving request remains \"yellow\"(1)", "RefUrl": "/notes/1051005 "}, {"RefNumber": "1053143", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Broadcasting: Endless jobs in parallel processing", "RefUrl": "/notes/1053143 "}, {"RefNumber": "1051623", "RefComponent": "BW-BEX-OT", "RefTitle": "Adjustments of transaction RSTT (usability)", "RefUrl": "/notes/1051623 "}, {"RefNumber": "1042920", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages in transformation", "RefUrl": "/notes/1042920 "}, {"RefNumber": "1048227", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data or too much data in rare cases (after SP13)", "RefUrl": "/notes/1048227 "}, {"RefNumber": "1041284", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Incorrect sorting when you extract from the PSA", "RefUrl": "/notes/1041284 "}, {"RefNumber": "1044015", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Check whether all data has arrived in the BIA", "RefUrl": "/notes/1044015 "}, {"RefNumber": "1048505", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Execution authorization for generated queries with '!' signs", "RefUrl": "/notes/1048505 "}, {"RefNumber": "1079404", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Calling the transport system in BCT batch actvtn", "RefUrl": "/notes/1079404 "}, {"RefNumber": "1056294", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process can be created for non-cumul InfoCube", "RefUrl": "/notes/1056294 "}, {"RefNumber": "1049258", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Delta cache data displayed twice in certain circumstances", "RefUrl": "/notes/1049258 "}, {"RefNumber": "1059767", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Error when you input filters in the BEx Web Analyzer", "RefUrl": "/notes/1059767 "}, {"RefNumber": "1042703", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: IF_RSR_VAR_DEF~GET_VRNID_VALUE_RANGE_CMP-02-", "RefUrl": "/notes/1042703 "}, {"RefNumber": "1055989", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SAPSQL_INVALID_FIELDNAME when you load data from PSA", "RefUrl": "/notes/1055989 "}, {"RefNumber": "1042712", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: SQL error when extracting many requests", "RefUrl": "/notes/1042712 "}, {"RefNumber": "1052471", "RefComponent": "BW-BEX-ET-WJR-AD", "RefTitle": "Input help in Web Application Designer for structure member", "RefUrl": "/notes/1052471 "}, {"RefNumber": "1052703", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After import processing of R3TR SHDS is slow", "RefUrl": "/notes/1052703 "}, {"RefNumber": "1051979", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Web service DataSource cannot be activated", "RefUrl": "/notes/1051979 "}, {"RefNumber": "1048864", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close empty requests", "RefUrl": "/notes/1048864 "}, {"RefNumber": "1048344", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Checkman: Various checkman problems", "RefUrl": "/notes/1048344 "}, {"RefNumber": "1046270", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Additional fixed filter for the query runtime", "RefUrl": "/notes/1046270 "}, {"RefNumber": "1046465", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface: Saving an InfoSource", "RefUrl": "/notes/1046465 "}, {"RefNumber": "1054390", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Random sequence when you refresh queries", "RefUrl": "/notes/1054390 "}, {"RefNumber": "1042673", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Error: Assignment details in the report-report interface", "RefUrl": "/notes/1042673 "}, {"RefNumber": "1048972", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "No authorization for a transformation that does not exist", "RefUrl": "/notes/1048972 "}, {"RefNumber": "1078717", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction: No message after error during Unicode check", "RefUrl": "/notes/1078717 "}, {"RefNumber": "1078718", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Log checked instead of model", "RefUrl": "/notes/1078718 "}, {"RefNumber": "1050330", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 14 BIA revision compatibility check extended", "RefUrl": "/notes/1050330 "}, {"RefNumber": "1078038", "RefComponent": "BW-WHM-AWB", "RefTitle": "P16:P21:AWB:PSA tree: Too much memory consumption RSMONMESS", "RefUrl": "/notes/1078038 "}, {"RefNumber": "1078195", "RefComponent": "BW-WHM-DST-BAPI", "RefTitle": "P16:P21:BAPI:BAPI_IPAK_START terminates w/TIMEOUT for event", "RefUrl": "/notes/1078195 "}, {"RefNumber": "1078001", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect icon in RSOR for process chains", "RefUrl": "/notes/1078001 "}, {"RefNumber": "1076314", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Performance: Process always waits one second before start", "RefUrl": "/notes/1076314 "}, {"RefNumber": "1055725", "RefComponent": "BW-BEX-ET", "RefTitle": "Problems for queries with temporal hierarchy join", "RefUrl": "/notes/1055725 "}, {"RefNumber": "1047829", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:BATCH: Cancel work process in a 'safe' way", "RefUrl": "/notes/1047829 "}, {"RefNumber": "1075206", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Long runtime for query with non-cumul & constant selection", "RefUrl": "/notes/1075206 "}, {"RefNumber": "1049403", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message \"Incorrect call sequence\" when you delete", "RefUrl": "/notes/1049403 "}, {"RefNumber": "1051652", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message: Target field is missing/dump w/ rule w/ units", "RefUrl": "/notes/1051652 "}, {"RefNumber": "1054812", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unclear errors and dumps for currency and unit rules", "RefUrl": "/notes/1054812 "}, {"RefNumber": "1049735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect status after deallocation", "RefUrl": "/notes/1049735 "}, {"RefNumber": "1075159", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:P21:BAPI:Bapi_ipak_start waits for events endlessly", "RefUrl": "/notes/1075159 "}, {"RefNumber": "1072992", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination RTIME_APPEND-02- in program SAPLRRS2", "RefUrl": "/notes/1072992 "}, {"RefNumber": "1040293", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimizations for writable InfoProviders", "RefUrl": "/notes/1040293 "}, {"RefNumber": "1044377", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process saved in scheduling", "RefUrl": "/notes/1044377 "}, {"RefNumber": "1045305", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect record mode rule is not reported", "RefUrl": "/notes/1045305 "}, {"RefNumber": "1042390", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump after entering InfoObject when master data is read", "RefUrl": "/notes/1042390 "}, {"RefNumber": "1003456", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Excel download: x:str problems with Broadcaster", "RefUrl": "/notes/1003456 "}, {"RefNumber": "1043132", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Toggle \"Use Temporal Hierarchy Join\" property", "RefUrl": "/notes/1043132 "}, {"RefNumber": "1072602", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "'Text and Text' displayed in filter dialog on the Web", "RefUrl": "/notes/1072602 "}, {"RefNumber": "1053054", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Poor performance in hierarchy nodes in the filter", "RefUrl": "/notes/1053054 "}, {"RefNumber": "1059759", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "You cannot edit formulas", "RefUrl": "/notes/1059759 "}, {"RefNumber": "1048857", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Activating the default Web templates", "RefUrl": "/notes/1048857 "}, {"RefNumber": "1041760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination: Authorized attribs with InfoSet characteristics", "RefUrl": "/notes/1041760 "}, {"RefNumber": "1045735", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checking characteristic values", "RefUrl": "/notes/1045735 "}, {"RefNumber": "1056068", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Problems with the initial value display", "RefUrl": "/notes/1056068 "}, {"RefNumber": "1049506", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Specifying the sequence during rebuilding for BIA", "RefUrl": "/notes/1049506 "}, {"RefNumber": "1043039", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 14", "RefUrl": "/notes/1043039 "}, {"RefNumber": "1069619", "RefComponent": "BW-WHM-DST", "RefTitle": "BRAIN 299 if you have no authorization for InfoSource", "RefUrl": "/notes/1069619 "}, {"RefNumber": "1043819", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No data for NON EMPTY and WHERE", "RefUrl": "/notes/1043819 "}, {"RefNumber": "1051142", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Dump during activation of update<PERSON>les (content installation)", "RefUrl": "/notes/1051142 "}, {"RefNumber": "1040649", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance when temporal hierarchy join is used", "RefUrl": "/notes/1040649 "}, {"RefNumber": "1056669", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P14:P21:Request red after timeout though load succeeds", "RefUrl": "/notes/1056669 "}, {"RefNumber": "1043919", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many authorizations w/ exclusive hierarchy authorization", "RefUrl": "/notes/1043919 "}, {"RefNumber": "1055003", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Performance probs for hierarchies in Java Runtime (1062537)", "RefUrl": "/notes/1055003 "}, {"RefNumber": "1047023", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Termination CL_RSMD_RS_SPECIAL and _CHECK_SELOPT-04-", "RefUrl": "/notes/1047023 "}, {"RefNumber": "1048178", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Default member for virtual hierarchies incorrect", "RefUrl": "/notes/1048178 "}, {"RefNumber": "1042463", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "ORA-00904: \"?\".\"DATE0\": invalid identifier", "RefUrl": "/notes/1042463 "}, {"RefNumber": "1050097", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Formula variable not replaced (hierarchy deactivated)", "RefUrl": "/notes/1050097 "}, {"RefNumber": "1043153", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "'&' is not a valid characteristic for InfoProvider '&'", "RefUrl": "/notes/1043153 "}, {"RefNumber": "1052660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error stack can be versioned", "RefUrl": "/notes/1052660 "}, {"RefNumber": "1051994", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH:Selecting excluding DS/source syst combinations", "RefUrl": "/notes/1051994 "}, {"RefNumber": "1054224", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Version and status information in the near-line interface", "RefUrl": "/notes/1054224 "}, {"RefNumber": "1043000", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA: Change check whether request is updated", "RefUrl": "/notes/1043000 "}, {"RefNumber": "1055437", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X299 Brain in CL_RSR program; GET_CHANM-03- form", "RefUrl": "/notes/1055437 "}, {"RefNumber": "1045548", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Monitoring of RDA InfoPackage requests", "RefUrl": "/notes/1045548 "}, {"RefNumber": "1042590", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Application log (SLG1) for real-Ttme data acquisition (RDA)", "RefUrl": "/notes/1042590 "}, {"RefNumber": "1055493", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Not enough values with hierarchy and master data access mode", "RefUrl": "/notes/1055493 "}, {"RefNumber": "1042896", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Release of memory after you close query", "RefUrl": "/notes/1042896 "}, {"RefNumber": "1059024", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Unit or currency conversion type DEFAULTNOT is not found", "RefUrl": "/notes/1059024 "}, {"RefNumber": "1055044", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Performance in SAPLRRK0, form s_data_fuellen", "RefUrl": "/notes/1055044 "}, {"RefNumber": "1042391", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 Brain in SAPLRSDRC_SPLIT; form RANGE_EQUAL-01-", "RefUrl": "/notes/1042391 "}, {"RefNumber": "1055274", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rules not deleted correctly", "RefUrl": "/notes/1055274 "}, {"RefNumber": "1048290", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "DB connect: Direct access improvements", "RefUrl": "/notes/1048290 "}, {"RefNumber": "1056011", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you change the view of the Content transformation", "RefUrl": "/notes/1056011 "}, {"RefNumber": "1054612", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "DataSource: Deleting source system without dependent objects", "RefUrl": "/notes/1054612 "}, {"RefNumber": "1059128", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incorrect default values for hierarchy variables", "RefUrl": "/notes/1059128 "}, {"RefNumber": "1051127", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data in a very particular situation", "RefUrl": "/notes/1051127 "}, {"RefNumber": "995937", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX error with combinations of WITH MEMBER and SAP VARIABLES", "RefUrl": "/notes/995937 "}, {"RefNumber": "1059978", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Selection for documents is incorrect", "RefUrl": "/notes/1059978 "}, {"RefNumber": "1053910", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Program error in FILL_CHARACTERISTIC: INVALID_IOBJNM", "RefUrl": "/notes/1053910 "}, {"RefNumber": "1059244", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Syntax error in program CL_RSWAD_MIME_REPOSITORY", "RefUrl": "/notes/1059244 "}, {"RefNumber": "1042502", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Not all leaves visible in display hierarchy", "RefUrl": "/notes/1042502 "}, {"RefNumber": "1056323", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Cache was used despite virtual characteristics/key figures", "RefUrl": "/notes/1056323 "}, {"RefNumber": "1058679", "RefComponent": "BW-PLA-IP", "RefTitle": "Several key selections in the select statement", "RefUrl": "/notes/1058679 "}, {"RefNumber": "1042461", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help for time characteristic in Q mode takes too long", "RefUrl": "/notes/1042461 "}, {"RefNumber": "1057286", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in the input-ready query class", "RefUrl": "/notes/1057286 "}, {"RefNumber": "1045008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RRK_LIST_OPEN: CX_SY_REF_IS_INITIAL UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1045008 "}, {"RefNumber": "1057429", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSDRC_MULTIPROV; form SOLVE_CMP-03-", "RefUrl": "/notes/1057429 "}, {"RefNumber": "1057650", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error GETWA_NOT_ASSIGNED in form MEGA_SORT", "RefUrl": "/notes/1057650 "}, {"RefNumber": "1050783", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems in logging during removal in archive and nearline", "RefUrl": "/notes/1050783 "}, {"RefNumber": "1050695", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_TABLES_NOT_COMPATIBLE", "RefUrl": "/notes/1050695 "}, {"RefNumber": "1056184", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Authorization log: Termination after saving and back navig", "RefUrl": "/notes/1056184 "}, {"RefNumber": "1038836", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "P14:DSO:DTP:Deactivating consistency check for DTP requests", "RefUrl": "/notes/1038836 "}, {"RefNumber": "1047776", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Too many process variants when deleting a DAP", "RefUrl": "/notes/1047776 "}, {"RefNumber": "1057192", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Dump bei P<PERSON><PERSON> von <PERSON>", "RefUrl": "/notes/1057192 "}, {"RefNumber": "1056293", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems when archiving with constant characteristics", "RefUrl": "/notes/1056293 "}, {"RefNumber": "1048690", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error messages and error logs not issued", "RefUrl": "/notes/1048690 "}, {"RefNumber": "1050669", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1050669 "}, {"RefNumber": "1045114", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and near-line storage", "RefUrl": "/notes/1045114 "}, {"RefNumber": "1055322", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Cannot diagnose near-line connection", "RefUrl": "/notes/1055322 "}, {"RefNumber": "1055227", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "GUI scroll bar in Analyzer var scrn blocks other scroll bar", "RefUrl": "/notes/1055227 "}, {"RefNumber": "1054109", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Termination in CL_RSMD_RS and _GET_READMODE_RESTRICT-01", "RefUrl": "/notes/1054109 "}, {"RefNumber": "1055071", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA index for FLOAT key figure with rounding errors", "RefUrl": "/notes/1055071 "}, {"RefNumber": "1045741", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA check for random queries", "RefUrl": "/notes/1045741 "}, {"RefNumber": "1054065", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving run for write-optimized DataStore terminates", "RefUrl": "/notes/1054065 "}, {"RefNumber": "1055691", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Application log is overwritten/missing", "RefUrl": "/notes/1055691 "}, {"RefNumber": "1050327", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Non-cumulatives and formula variables from replacement path", "RefUrl": "/notes/1050327 "}, {"RefNumber": "1055350", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Node positioning changes after data entered in nodes", "RefUrl": "/notes/1055350 "}, {"RefNumber": "1055449", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text of variable in variable screen not displayed", "RefUrl": "/notes/1055449 "}, {"RefNumber": "1055314", "RefComponent": "BW-BEX-OT", "RefTitle": "Abbruch bei einem Korrupten Workbook", "RefUrl": "/notes/1055314 "}, {"RefNumber": "1055253", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X_299 BRAIN in SAPLRRi2, form CSC_SETZEN-01-", "RefUrl": "/notes/1055253 "}, {"RefNumber": "1054587", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display hierarchy is changed when you update", "RefUrl": "/notes/1054587 "}, {"RefNumber": "1055033", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "\"??????????\" cannot be interpreted as a number", "RefUrl": "/notes/1055033 "}, {"RefNumber": "1054963", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination for artificial crcy w/more than 5 decimal places", "RefUrl": "/notes/1054963 "}, {"RefNumber": "1053962", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Hierarchy ## in not valid for InfoObject ##", "RefUrl": "/notes/1053962 "}, {"RefNumber": "1052748", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 mode D for 0INFOPROV", "RefUrl": "/notes/1052748 "}, {"RefNumber": "1049456", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Index errors reported in DB02 after migration to DB6", "RefUrl": "/notes/1049456 "}, {"RefNumber": "1054588", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299: termination in MEGA_SORT_14-01- with invalid filters", "RefUrl": "/notes/1054588 "}, {"RefNumber": "1053308", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED in reload from archive/near-line", "RefUrl": "/notes/1053308 "}, {"RefNumber": "1050293", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "General pattern in InfoPackage selection is not supported", "RefUrl": "/notes/1050293 "}, {"RefNumber": "1054263", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Cx_rsr_hier_member_not_found is not caught", "RefUrl": "/notes/1054263 "}, {"RefNumber": "1043945", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Long runtime for F4 in 3.X variable screen", "RefUrl": "/notes/1043945 "}, {"RefNumber": "1053436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with \"Temporal Hierarchy Join\" in JAVA Web", "RefUrl": "/notes/1053436 "}, {"RefNumber": "1053229", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Recovering does not generate 3.x DataSource", "RefUrl": "/notes/1053229 "}, {"RefNumber": "1053504", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Descriptions of DataSource fields are missing", "RefUrl": "/notes/1053504 "}, {"RefNumber": "1053510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: DWWB takes a long time to set up", "RefUrl": "/notes/1053510 "}, {"RefNumber": "1053052", "RefComponent": "BW-BEX-ET", "RefTitle": "Incorrect characters in long text of error messages", "RefUrl": "/notes/1053052 "}, {"RefNumber": "1054103", "RefComponent": "BW-BEX-OT", "RefTitle": "Query $$DEFAULT does not exist (BRAIN 605)", "RefUrl": "/notes/1054103 "}, {"RefNumber": "1053612", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correctn: Error handling prog contains incorrect source code", "RefUrl": "/notes/1053612 "}, {"RefNumber": "1044009", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Changing data class in reclustering not possible", "RefUrl": "/notes/1044009 "}, {"RefNumber": "1053084", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASSIGN_TYPE_CONFLICT in CONVERT_FROM_PSA", "RefUrl": "/notes/1053084 "}, {"RefNumber": "1050379", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Not enough fields requested when loading w/o PSA", "RefUrl": "/notes/1050379 "}, {"RefNumber": "1048506", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSource vers converted during transformation", "RefUrl": "/notes/1048506 "}, {"RefNumber": "1050125", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Endless runtime during authorization check", "RefUrl": "/notes/1050125 "}, {"RefNumber": "1045711", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Attributes for characteristics with temporal hierarchy join", "RefUrl": "/notes/1045711 "}, {"RefNumber": "1044378", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Delete free processes in chain deletion", "RefUrl": "/notes/1044378 "}, {"RefNumber": "1053844", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH: Message \"Source system & does not exist\"", "RefUrl": "/notes/1053844 "}, {"RefNumber": "1052031", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request: Termination after verification phase", "RefUrl": "/notes/1052031 "}, {"RefNumber": "1052645", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14:DSO:Further authorization check before request activated", "RefUrl": "/notes/1052645 "}, {"RefNumber": "1052037", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Exception reporting: Exception visualization, scaling", "RefUrl": "/notes/1052037 "}, {"RefNumber": "1048114", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjusting RSRV checks for the BI accelerator", "RefUrl": "/notes/1048114 "}, {"RefNumber": "1042560", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Texts without language keys cannot be extracted", "RefUrl": "/notes/1042560 "}, {"RefNumber": "1048923", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in SAPLRRI2; form INPUTABLE_CHAFREE-02-", "RefUrl": "/notes/1048923 "}, {"RefNumber": "1042924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program CL_RSR_OLAP and form FILL_DIM-01-", "RefUrl": "/notes/1042924 "}, {"RefNumber": "1050889", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH-02-01", "RefUrl": "/notes/1050889 "}, {"RefNumber": "1051614", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump occurs in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1051614 "}, {"RefNumber": "1049700", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL: Dynamic hierarchy selection and new headers", "RefUrl": "/notes/1049700 "}, {"RefNumber": "1047978", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy auth. and intervals: \"No authorization\", EYE 007", "RefUrl": "/notes/1047978 "}, {"RefNumber": "1045919", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No filter when accessing file DataSource directly", "RefUrl": "/notes/1045919 "}, {"RefNumber": "1051170", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "InfoObject &1 is not available in version &2", "RefUrl": "/notes/1051170 "}, {"RefNumber": "1051080", "RefComponent": "BW-BCT-TCT", "RefTitle": "RSDRI runtime statistics contain caller", "RefUrl": "/notes/1051080 "}, {"RefNumber": "1049857", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Reason for BIA error in messages missing", "RefUrl": "/notes/1049857 "}, {"RefNumber": "1049671", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "CL_RSR_RRK0_PARTITION and IF_RSR_RRK0_PARTITION~BROWSE-01-", "RefUrl": "/notes/1049671 "}, {"RefNumber": "1043008", "RefComponent": "BW-WHM-DST", "RefTitle": "Key date selection ineffective for char with direct access", "RefUrl": "/notes/1043008 "}, {"RefNumber": "1046394", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Performance:RSSELDONE:HASH_CREATE:Index access PID", "RefUrl": "/notes/1046394 "}, {"RefNumber": "1047852", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Selection in MEMBERS rowset for [measures] or structures", "RefUrl": "/notes/1047852 "}, {"RefNumber": "1047840", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS + RSDS not deleted", "RefUrl": "/notes/1047840 "}, {"RefNumber": "1046254", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Performance optimization of the verification phase", "RefUrl": "/notes/1046254 "}, {"RefNumber": "1047992", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: For internal use", "RefUrl": "/notes/1047992 "}, {"RefNumber": "1048015", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Rollup: Exception INCONSISTENT_N_O_REQS", "RefUrl": "/notes/1048015 "}, {"RefNumber": "1048095", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Checking variables and I_STEP equal to three in exit", "RefUrl": "/notes/1048095 "}, {"RefNumber": "1048100", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A299 in SAPLRRI2 and CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1048100 "}, {"RefNumber": "1048161", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Set QM status for yellow requests using funcion module", "RefUrl": "/notes/1048161 "}, {"RefNumber": "1048280", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Additional corrections for Note 1002682", "RefUrl": "/notes/1048280 "}, {"RefNumber": "1040291", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Compound attribute value displayed incorrectly", "RefUrl": "/notes/1040291 "}, {"RefNumber": "1041000", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Program not valid for request creation", "RefUrl": "/notes/1041000 "}, {"RefNumber": "1041306", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: \"Record not in cross-rec.\" when buffer displayed", "RefUrl": "/notes/1041306 "}, {"RefNumber": "1041515", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating authorizations requires ODS values at least", "RefUrl": "/notes/1041515 "}, {"RefNumber": "1042296", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: No information for InfoSet in monitor tree", "RefUrl": "/notes/1042296 "}, {"RefNumber": "1042337", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: SDL: InfoPackage queries whether you want to save", "RefUrl": "/notes/1042337 "}, {"RefNumber": "1042338", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:UNIT fields cannot be selected in 7.x DataSource", "RefUrl": "/notes/1042338 "}, {"RefNumber": "1042388", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems when transporting deletions", "RefUrl": "/notes/1042388 "}, {"RefNumber": "1042389", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting incorrect dialog for InfoSource", "RefUrl": "/notes/1042389 "}, {"RefNumber": "1042451", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:APO:Planning: Dialog boxes when planning requ is closed", "RefUrl": "/notes/1042451 "}, {"RefNumber": "1042452", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: Generic InfoPackage deletion with searching for package", "RefUrl": "/notes/1042452 "}, {"RefNumber": "1042453", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: CheckMan: Domain exchange; package concept", "RefUrl": "/notes/1042453 "}, {"RefNumber": "1042599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when creating a transformation between ISet and InfSrc", "RefUrl": "/notes/1042599 "}, {"RefNumber": "1042675", "RefComponent": "BW-WHM-AWB", "RefTitle": "Mapping is missing, search in workbench terminates, RSAR 203", "RefUrl": "/notes/1042675 "}, {"RefNumber": "1042677", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication in application components is slow", "RefUrl": "/notes/1042677 "}, {"RefNumber": "1042790", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P14: Real-time: QM action for real-time PSA request", "RefUrl": "/notes/1042790 "}, {"RefNumber": "1042816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: No messages displayed in summary", "RefUrl": "/notes/1042816 "}, {"RefNumber": "1042881", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:No InfoPackage scheduler call without active DS", "RefUrl": "/notes/1042881 "}, {"RefNumber": "1042893", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Problems with large queries and quantity conversion", "RefUrl": "/notes/1042893 "}, {"RefNumber": "1042895", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "RRS_VARIANT_CREATE: Incorrect variant values", "RefUrl": "/notes/1042895 "}, {"RefNumber": "1043035", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Monitor does not display errors", "RefUrl": "/notes/1043035 "}, {"RefNumber": "1043053", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Data type in open hub destination is incorrect", "RefUrl": "/notes/1043053 "}, {"RefNumber": "1043103", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "A299: System error in CL_RSR_CHABIT and SET_BIT0-01-", "RefUrl": "/notes/1043103 "}, {"RefNumber": "1043170", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Missing impact for currency translation and unit conversion", "RefUrl": "/notes/1043170 "}, {"RefNumber": "1043192", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: English texts in report RSREQARCH_CHECK_HASH_ARCHIVE", "RefUrl": "/notes/1043192 "}, {"RefNumber": "1043295", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Mannually interrupted run is continued", "RefUrl": "/notes/1043295 "}, {"RefNumber": "1043343", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting the rule from the technical group deletes the group", "RefUrl": "/notes/1043343 "}, {"RefNumber": "1043724", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump nach Eintragung eines InfoObjektes bei Stammdaten nachl", "RefUrl": "/notes/1043724 "}, {"RefNumber": "1043971", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination in RSDRI_CUBE_WRITE_PACKAGE for incorrect data", "RefUrl": "/notes/1043971 "}, {"RefNumber": "1044052", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DBC attribute can be changed in the DTP", "RefUrl": "/notes/1044052 "}, {"RefNumber": "1044054", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Shadow DTP with DataSource cannot be displayed", "RefUrl": "/notes/1044054 "}, {"RefNumber": "1044181", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Too many requests selected for DTP from DWWB", "RefUrl": "/notes/1044181 "}, {"RefNumber": "1044197", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule is created twice / Note 1036440 was implemented", "RefUrl": "/notes/1044197 "}, {"RefNumber": "1044398", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE 007 \" No auth.\" for several authorizations", "RefUrl": "/notes/1044398 "}, {"RefNumber": "1044494", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by incorrect value", "RefUrl": "/notes/1044494 "}, {"RefNumber": "1044708", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Performance of generic time derivation", "RefUrl": "/notes/1044708 "}, {"RefNumber": "1044808", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error msg in the after import/during replication", "RefUrl": "/notes/1044808 "}, {"RefNumber": "1044860", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "ESH Open Hub", "RefUrl": "/notes/1044860 "}, {"RefNumber": "1045063", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "No quantitiy conversion (target quantity unit from variable)", "RefUrl": "/notes/1045063 "}, {"RefNumber": "1045174", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation/routine missing when you collect content", "RefUrl": "/notes/1045174 "}, {"RefNumber": "1045300", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Display of non-postable values shows SIDs w/out master data", "RefUrl": "/notes/1045300 "}, {"RefNumber": "1045560", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text symbol with value for hierarchy variable", "RefUrl": "/notes/1045560 "}, {"RefNumber": "1045784", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14: DSO: DTP: Check on full REQU requests not required", "RefUrl": "/notes/1045784 "}, {"RefNumber": "1045861", "RefComponent": "BW-WHM-DST", "RefTitle": "Directly accessing time-dependent master data texts", "RefUrl": "/notes/1045861 "}, {"RefNumber": "1045923", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization for key figure (0TCAKYFNM) in InfoSet", "RefUrl": "/notes/1045923 "}, {"RefNumber": "1045950", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RFC dump when calling FM RSS2_CALL_FCODE_EMPTY_GLOBALS", "RefUrl": "/notes/1045950 "}, {"RefNumber": "1046003", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN X299 in SAPLRRK0; form MEGA_SORT_14-01-", "RefUrl": "/notes/1046003 "}, {"RefNumber": "1046066", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECAUTH:Cust exist variables for non-string characteristics", "RefUrl": "/notes/1046066 "}, {"RefNumber": "1046127", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you collect transformations", "RefUrl": "/notes/1046127 "}, {"RefNumber": "1046233", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299(BRAIN) in program SAPLRRI2 and form FST_VAR_06-01-", "RefUrl": "/notes/1046233 "}, {"RefNumber": "1046312", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you assign key figure with fixed unit/currency", "RefUrl": "/notes/1046312 "}, {"RefNumber": "1046544", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems: Migrating w/source fields with fixed unit/currency", "RefUrl": "/notes/1046544 "}, {"RefNumber": "1047174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect scope when locking in process chains", "RefUrl": "/notes/1047174 "}, {"RefNumber": "1047176", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Undeclared exception in the error handling", "RefUrl": "/notes/1047176 "}, {"RefNumber": "1051055", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "InfoSource remains in the status \"Not Executable\"", "RefUrl": "/notes/1051055 "}, {"RefNumber": "1047735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: You cannot transport interrupt", "RefUrl": "/notes/1047735 "}, {"RefNumber": "1051246", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Cursor position gets lost in routine", "RefUrl": "/notes/1051246 "}, {"RefNumber": "1050948", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not enough texts for RSPCVARIANTT", "RefUrl": "/notes/1050948 "}, {"RefNumber": "1049406", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Empty BIA index is set to active", "RefUrl": "/notes/1049406 "}, {"RefNumber": "1051168", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: SUCC_PRED_GET-2 in prog CL_RSR_HIERARCHY_BINCL", "RefUrl": "/notes/1051168 "}, {"RefNumber": "1050868", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "ASSERTION_FAILED in MODIFY_SEGMENT when you activate a DAP", "RefUrl": "/notes/1050868 "}, {"RefNumber": "1049932", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error \"CX_SY_REF_IS_INITIAL\" when you go from ALV to BEx", "RefUrl": "/notes/1049932 "}, {"RefNumber": "1051036", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "You cannot restart change run", "RefUrl": "/notes/1051036 "}, {"RefNumber": "1050208", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination BRAIN 299 _GET_AUTH_RESTRICT-03 in CL_RSMD_RS", "RefUrl": "/notes/1050208 "}, {"RefNumber": "1049209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 Komponente G_S_KENNZ-%F_$P$ unbekannt", "RefUrl": "/notes/1049209 "}, {"RefNumber": "1049261", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Short dump for RSRV check to compary keyfigures", "RefUrl": "/notes/1049261 "}, {"RefNumber": "1044290", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIAMon SP 11 and 12 revision check - Revision is too low", "RefUrl": "/notes/1044290 "}, {"RefNumber": "1014312", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "System messages are only displayed after you navigate", "RefUrl": "/notes/1014312 "}, {"RefNumber": "1049564", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data with exception aggregation and two structures", "RefUrl": "/notes/1049564 "}, {"RefNumber": "1049141", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA type group on IF_RSDAI_NEARLINE_CONNECTION interface", "RefUrl": "/notes/1049141 "}, {"RefNumber": "1049691", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in CL_RSR_RRK0_RQTS->_CHECK_DELTAPAIR_01", "RefUrl": "/notes/1049691 "}, {"RefNumber": "1049469", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Switching from ABAP program", "RefUrl": "/notes/1049469 "}, {"RefNumber": "1049623", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Scheduling if server does not exist", "RefUrl": "/notes/1049623 "}, {"RefNumber": "1049147", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Deadlock on hierarchy table", "RefUrl": "/notes/1049147 "}, {"RefNumber": "1049187", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction for CTC (creating source system in background)", "RefUrl": "/notes/1049187 "}, {"RefNumber": "1048648", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI7.0(SP14) DataStore: Deletion of request terminates", "RefUrl": "/notes/1048648 "}, {"RefNumber": "1048345", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Check function in InfoCube administration", "RefUrl": "/notes/1048345 "}, {"RefNumber": "1048504", "RefComponent": "BW-WHM-AWB", "RefTitle": "P14: Administering PSA to new DS-PSA displays incorrect icon", "RefUrl": "/notes/1048504 "}, {"RefNumber": "1044991", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "OBSOLETE", "RefUrl": "/notes/1044991 "}, {"RefNumber": "1045624", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "'inlist of sids, but number-flag not set'", "RefUrl": "/notes/1045624 "}, {"RefNumber": "1047598", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Manage:QM action on loaded request in DSO", "RefUrl": "/notes/1047598 "}, {"RefNumber": "1047527", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "\"Flat\" BIA index: Indexing performance with line items", "RefUrl": "/notes/1047527 "}, {"RefNumber": "1048701", "RefComponent": "BW-BEX-ET-BC", "RefTitle": "Sending to printer: No printout with scheduling", "RefUrl": "/notes/1048701 "}, {"RefNumber": "1048350", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Incorrect text in error step in error DTP", "RefUrl": "/notes/1048350 "}, {"RefNumber": "1045296", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "RSRA_CLUSTER_TABLE_REORG dumps with an error.", "RefUrl": "/notes/1045296 "}, {"RefNumber": "1048349", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Connection errors with multiple events and collector", "RefUrl": "/notes/1048349 "}, {"RefNumber": "1048423", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "R7 105 during migration of update rules", "RefUrl": "/notes/1048423 "}, {"RefNumber": "1046507", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "SP 11: Error in DB connect for DB2, DB4 and MS-SQL-Server", "RefUrl": "/notes/1046507 "}, {"RefNumber": "1048253", "RefComponent": "BW-BEX-ET-WEB-AD", "RefTitle": "Template migration", "RefUrl": "/notes/1048253 "}, {"RefNumber": "1047497", "RefComponent": "BW", "RefTitle": "Date dialog disappears when choosing in Hebrew / RTL", "RefUrl": "/notes/1047497 "}, {"RefNumber": "742716", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSDRI_INFOPROV_READ returns illegal_input_range", "RefUrl": "/notes/742716 "}, {"RefNumber": "1047492", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "Verbesserung der Statistik Granularität im BExAnalyzer", "RefUrl": "/notes/1047492 "}, {"RefNumber": "1042883", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RSBATCH_WRITE_PROT_TO_APPLLOG and incorrect error msg", "RefUrl": "/notes/1042883 "}, {"RefNumber": "1042340", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA admin in DWB called only with maintain authorization", "RefUrl": "/notes/1042340 "}, {"RefNumber": "1042339", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA:Deleting from PSA terminates with message RSM2 709", "RefUrl": "/notes/1042339 "}, {"RefNumber": "1039071", "RefComponent": "BW-WHM-DST", "RefTitle": "P13:SDL:Content:InfoPackage activation terminates - RSM 142", "RefUrl": "/notes/1039071 "}, {"RefNumber": "1046446", "RefComponent": "BW-BEX-OT", "RefTitle": "CX_SY_REF_IS_INITIAL in RSRT when you execute OK code RALL", "RefUrl": "/notes/1046446 "}, {"RefNumber": "1043456", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in functions BAPI_TD...", "RefUrl": "/notes/1043456 "}, {"RefNumber": "1043231", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Änderungen an Hierarchieknotenwertehilfe fürs Trace Tool", "RefUrl": "/notes/1043231 "}, {"RefNumber": "1044110", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "INITRANS for InfoCube indexes in BW 7", "RefUrl": "/notes/1044110 "}, {"RefNumber": "1043949", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Checking compound values", "RefUrl": "/notes/1043949 "}, {"RefNumber": "1040071", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSSM Check Status: Msg R7 245 InfoObject not available", "RefUrl": "/notes/1040071 "}, {"RefNumber": "1040278", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Return code 12 after query transport", "RefUrl": "/notes/1040278 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}