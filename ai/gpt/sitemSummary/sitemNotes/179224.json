{"Request": {"Number": "179224", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 483, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000903642017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000179224?language=E&token=A8DDBD068A6D8D5CED01E8B1746B33F7"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000179224", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000179224/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "179224"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 27}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Performance"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.05.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-NUM"}, "SAPComponentKeyText": {"_label": "Component", "value": "Number Range Management"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Number Range Management", "value": "BC-SRV-NUM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-NUM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "179224 - Doc.no.assignment for unbuffered number ranges"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The allocation of document numbers is serialized by locks in the<br />database (excluding lockwaits) on the tables NRIV or NRIV_LOKAL.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB01; Buffering in local file, deadlock</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p><br />For legal reasons, the numbers of certain documents (for example, FI documents) must be assigned continuously so as to be traceable. The respective number range objects (for example, RF_BELEG) cannot be buffered in the main memory since, for example, restarting the application servers would cause gaps in number range allocation.<br /><br />As of Release 3.0B, an enhanced numbering method is available, which meets the legal requirements of most countries. Instead of managing all document numbers centrally in one table/line (NRIV), number ranges are taken for each application server (instance) and managed in an additional table (NRIV_LOKAL). Since the number range buffer is located in Pool&#x00A0;&#x00A0; 40,<br />you may have to increase its size by 260000 bytes.<br />- the parameter change is valid only after the server has been restarted. Part of the primary key used in this case is the instance name. Consequently, locks in the database (excluding lockwaits) can only occur within an instance and not systemwide.<br /><br />However, a disadvantage of the enhanced buffering method \"NRIV_LOKAL\" is that processes that are running in parallel continue to lock each other at instance level. Consequently, the locking problems can only be resolved effectively if you set up as many instances as possible. In an existing R/3 client/server architecture, however, you cannot set up as many instances as required since this also means a division of limited resources (in particular, main storage buffers, for example, PXA buffer).<br /><br />Due to the deficits described, the numbering method \"NRIV_LOKAL\" has been improved again. In addition to distribution via the application server, the logically unique number of the R/3 work process became part of the key of table NRIV_LOKAL.<br /><br />Instead of managing all document numbers centrally in one table/line (NRIV), number ranges are taken from table NRIV for each instance and the logically unique number of the R/3 work process and are managed in the additional table NRIV_LOKAL.<br /><br />Consequently, database locks will occur less frequently, but cannot be avoided completely. For this, refer to Notes 599157 and 840901. This method can also be used successfully for central instances. The number of instances is no longer important for the performance of document number allocation.<br /><br />Note:</p> <UL><LI>If continuous document number assignment is required by law, activate the enhanced buffering for large installations with a very large document amount (with internal number assignment) to optimize performance. If possible and legally permitted, prefer a pure main storage buffering.</LI></UL> <UL><LI>Numbers are no longer assigned in ascending chronological order. A document with a higher number might have been created earlier than a document with a lower number. This also applies to numbers assigned on the same application server or the same instance.</LI></UL> <UL><LI>Possibly not all numbers of a range are assigned (for example, end of fiscal year, renaming of an instance). Unassigned numbers can be documented with report RSSNR0A1. The numbers that are unassigned can also be evaluated for fiscal years further in the past. If the conventional NRIV_LOKAL buffering was used before, then certain ranges already assigned can no longer be used completely. In this case, report RSSNR0A1 delivers the statement of unassigned numbers as well.</LI></UL> <UL><LI>If required, individual ranges of the number range are read from central table NRIV and transferred to table NRIV_LOKAL. This reading causes temporary database locks on table NRIV. This effect occurs in particular when using the new method for the first time (first massively parallel load). Important: When using the enhanced method NRIV_LOKAL for the first time, initially process smaller documents or IDOCs. Since the frequency of using the work processes of an R/3 System differs, this effect is qualified in the course of the runtime. For initialization, note that the subobject of the number range (for example, company code) is also part of the key NRIV_LOKAL.</LI></UL> <UL><LI>Note that if you use the method described here, the main storage buffering should no longer be avoided for certain number range intervals. See Note 0023835 Buffering RV_BELEG / Number assignment in SD.</LI></UL> <UL><LI>A prerequisite for the use of the new method is that you previously set buffering method NRIV_LOKAL for the number range object in question and any instance.</LI></UL> <UL><LI>The name of the instance must have a maximum length of 17 characters (see transaction SM51). The system terminates if there are more than 17 characters.</LI></UL> <UL><LI>From a technical point of view, changing the buffering type is a modification. But this modification is only allowed for number range objects that are explicitly specified in SAP Notes for this purpose. Among other things, the use is limited by the fact that the application may draw only one number each. If local buffering or extended local buffering is set for number ranges for which the application draws several numbers at a time, error NR030 occurs.</LI></UL> <UL><LI>For technical reasons (field length of logical work process is two), not more than 100 work processes should be defined per instance and be used in parallel for document number assignment. Normally however, you will use much less work processes.</LI></UL> <p><br />To set up the new NRIV_LOKAL buffering method, the following steps are required:</p> <OL>1. Configure buffering in \"local file\" for the number range object in question.</OL> <OL><OL>a) Execute transaction SNRO, and enter the corresponding number range object (for example RV_BELEG). Change to the change mode.</OL></OL> <OL><OL>b) Choose Edit &gt; Set-up buffering &gt; Local file\" (as of Release 4.0B (and the corresponding Support Package) local file + workprocess ID).<br /></OL></OL> <p>There should not be less than 100 numbers so that the subsequent reading process does not have to be carried out too often from the central resource NRIV. See the recommendations above for initialization of the new method. On the other hand, you should not select too many numbers in the buffer so as not to exhaust the number range intervals used too early.<br /><br /><br />As of Release 4.0B., buffering has thus been activated.<br /><br />In addition, make the following changes to the dictionary:<br />STRUKTUR NRIV_LOK:<br />BUFFER_L NRBUFFER_L CHAR 1 0 ind. whthr intrvl to be saved in locl file<br />IVBUFF_L NRIVBUFF_L NUMC 8 0 No. of numbers available in local file<br />BUFFER_P NRBUFFER_L CHAR 1 0 ind., ...&lt;---&#x00A0;&#x00A0;insert<br />IVBUFF_P NRIVBUFF_L NUMC 8 0 No. .&#x00A0;&#x00A0;..&lt;----&#x00A0;&#x00A0;insert<br /><br />...thus you have to include BUFFER_P und IVBUFF_P analog to buffers BUFFER_L and IVBUFF_L that already exist and then activate the structure .<br /><br />In addition, if you have NOT imported the changes by Hot Package, but implemented them with the correction instructions, note the following:<br />Function group SNR2</p> <UL><UL><LI>GUI status UPD: In the 'Menu bar -&gt; Edit -&gt; Set-up buffering' enter a new function (PUF3 'Loc. file and pro...').</LI></UL></UL> <p><br />In screen SAPLSNR2 0100, make the following enhancement in addition:<br />Depending on the maintenance level, the menu item is named Layout or Full screen:<br />Between the line \"Buffering in local file\" and \"Number ranges not buffered\", insert the line \"Extended buffering...\" as shown below:<br /><br />Buffering_in_local_file_____&#x00A0;&#x00A0;&#x00A0;&#x00A0; No_of_numbers_in_buffer_ _________<br />Extended_buffering____________&#x00A0;&#x00A0;&#x00A0;&#x00A0; No_of_numbers_in_buffer_ ________<br />Number_ranges_not_buffered<br />The field name of checkbox after \"Extended_buffering\" -&gt; D100_PUFFER3<br />The field name of field after \"No_of_numbers_in_buffer\" -&gt; &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;D100_NOIVBUFFER_P<br />All texts and fields should contain P03 in the fourth column in attribute field Groups. In addition, D100_NOIVBUFFER_P should contain 001 in the second column.<br /><br /><br /><br />For releases lower than 4.0B (or releases without the corresponding Support Package):<br />For activation, auxiliary report \"Z_NRIV_LOCAL_WITH_WP_ID\" is required. The report name can also be abbreviated to \"Z_NRIVWP\" in releases lower than 4.0B since the long names have not been supported here yet.<br /><br />Afterwards, activate the improved NRIV_LOKAL buffering as follows:</p> <OL><OL>a) Start the auxiliary report \"Z_NRIV_LOCAL_WITH_WP_ID\".</OL></OL> <OL><OL>b) Select \"Activation/Deactivation\" in the section \"Control\".</OL></OL> <OL><OL>c) Choose \"Activate\" under \"Improved NRIV_LOKAL buffering\", and enter the corresponding number range object in the input field \"Number range object\".</OL></OL> <OL><OL>d) Execute the report. The new buffering method is set. The deactivation of the buffering method can be made the same way. Note that the \"Local buffering\" indicator is displayed as not set for the corresponding object in transaction SNUM although the new method has been activated. This is a display problem and has nothing to do with incorrect settings.</OL></OL> <OL><OL>e) *End of specifications for releases lower than 4.0B</OL></OL> <OL>1. To display the current settings of the number range objects, carry out the report in the analysis mode.</OL> <OL>2. When you double-click one of the output lines, the report mentioned above (RSSNR0A1) is executed in order to account for the numbers not yet used.</OL> <p><br />We strongly recommend you also implement Note 315202.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Enhanced NRIV_LOKAL method for document number allocation</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "HIER"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "0000"}, {"Key": "Transaction codes", "Value": "0100"}, {"Key": "Transaction codes", "Value": "SNRO"}, {"Key": "Transaction codes", "Value": "SM51"}, {"Key": "Transaction codes", "Value": "SNUM"}, {"Key": "Transaction codes", "Value": "DB01"}, {"Key": "Responsible                                                                                         ", "Value": "D019962"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D036683)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000179224/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000179224/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000179224/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "9942", "RefComponent": "BC-CST-LL", "RefTitle": "Maximum number of work processes", "RefUrl": "/notes/9942"}, {"RefNumber": "859240", "RefComponent": "SD-BIL-GF", "RefTitle": "Procedure for document number gaps in the billing document", "RefUrl": "/notes/859240"}, {"RefNumber": "810757", "RefComponent": "FI-GL-GL-X", "RefTitle": "FAQ FI-GL-GL-X Data Consistency Check Composite Note", "RefUrl": "/notes/810757"}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501"}, {"RefNumber": "677488", "RefComponent": "SD-BIL-GF-OC", "RefTitle": "EU Directive 2001/115/EG", "RefUrl": "/notes/677488"}, {"RefNumber": "653235", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/653235"}, {"RefNumber": "612211", "RefComponent": "IS-A-SBO", "RefTitle": "FI document number ranges bufferng for ERS parallel procssng", "RefUrl": "/notes/612211"}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875"}, {"RefNumber": "454528", "RefComponent": "FS-CML", "RefTitle": "Contr.crcy chngovr:termntn during mass changeover (deadlock)", "RefUrl": "/notes/454528"}, {"RefNumber": "399207", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges; error STOPPED NUM: Bypassing of buffer", "RefUrl": "/notes/399207"}, {"RefNumber": "388566", "RefComponent": "FS-CS", "RefTitle": "CS: Measures for performance improvement", "RefUrl": "/notes/388566"}, {"RefNumber": "37844", "RefComponent": "FI", "RefTitle": "Performance: document number assignment RF_BELEG", "RefUrl": "/notes/37844"}, {"RefNumber": "366825", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO: Gaps in MM document numbers and external FI numbers", "RefUrl": "/notes/366825"}, {"RefNumber": "333649", "RefComponent": "RE", "RefTitle": "Collective note, performance in Real Estate", "RefUrl": "/notes/333649"}, {"RefNumber": "333356", "RefComponent": "CO-OM-OPA-A", "RefTitle": "Internal order: order nmbrs nt assignd continuously", "RefUrl": "/notes/333356"}, {"RefNumber": "318104", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil-internal: Ref.to performance issues ....", "RefUrl": "/notes/318104"}, {"RefNumber": "311004", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Lock wait for patient accounting", "RefUrl": "/notes/311004"}, {"RefNumber": "23835", "RefComponent": "SD-BIL-IV", "RefTitle": "Buffering RV_BELEG/number assignment in SD", "RefUrl": "/notes/23835"}, {"RefNumber": "213546", "RefComponent": "IS-R-IFC-IN", "RefTitle": "HPR collective note: POS inbound", "RefUrl": "/notes/213546"}, {"RefNumber": "199246", "RefComponent": "BC-SRV-NUM", "RefTitle": "Function keys for scrolling without function", "RefUrl": "/notes/199246"}, {"RefNumber": "175047", "RefComponent": "FI-GL-GL-X", "RefTitle": "Causes for FI document number gaps (RF_BELEG)", "RefUrl": "/notes/175047"}, {"RefNumber": "154091", "RefComponent": "LE-SHP-GF", "RefTitle": "Performance in delivery process: Collective note", "RefUrl": "/notes/154091"}, {"RefNumber": "141497", "RefComponent": "IS-H", "RefTitle": "Consulting: Buffering of number ranges", "RefUrl": "/notes/141497"}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444"}, {"RefNumber": "1091025", "RefComponent": "BC-SRV-NUM", "RefTitle": "DATA_OFFSET_TOO_LARGE in SAPLSNR3", "RefUrl": "/notes/1091025"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2599056", "RefComponent": "BC-SRV-NUM", "RefTitle": "NR: Duplicate key error for local buffering", "RefUrl": "/notes/2599056 "}, {"RefNumber": "2474833", "RefComponent": "XX-PROJ-FI-CA", "RefTitle": "FI-CA: Enabling number of jobs in mass activities > 999", "RefUrl": "/notes/2474833 "}, {"RefNumber": "2042606", "RefComponent": "XX-PART-IPS", "RefTitle": "IP and DMP numbers not assigned continuously", "RefUrl": "/notes/2042606 "}, {"RefNumber": "859240", "RefComponent": "SD-BIL-GF", "RefTitle": "Procedure for document number gaps in the billing document", "RefUrl": "/notes/859240 "}, {"RefNumber": "862597", "RefComponent": "SD-BIL-IV", "RefTitle": "Display of document number gaps for SD billing documents", "RefUrl": "/notes/862597 "}, {"RefNumber": "504875", "RefComponent": "BC-SRV-NUM", "RefTitle": "Buffering of number ranges", "RefUrl": "/notes/504875 "}, {"RefNumber": "9942", "RefComponent": "BC-CST-LL", "RefTitle": "Maximum number of work processes", "RefUrl": "/notes/9942 "}, {"RefNumber": "399207", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges; error STOPPED NUM: Bypassing of buffer", "RefUrl": "/notes/399207 "}, {"RefNumber": "1679514", "RefComponent": "XX-PART-DTM", "RefTitle": "Material, customer, vendor numbers not assigned continuously", "RefUrl": "/notes/1679514 "}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444 "}, {"RefNumber": "678501", "RefComponent": "BC-SRV-NUM", "RefTitle": "System standstill, locks on NRIV", "RefUrl": "/notes/678501 "}, {"RefNumber": "1356382", "RefComponent": "IS-B-BCA", "RefTitle": "Number range object BKK_ITEM", "RefUrl": "/notes/1356382 "}, {"RefNumber": "454528", "RefComponent": "FS-CML", "RefTitle": "Contr.crcy chngovr:termntn during mass changeover (deadlock)", "RefUrl": "/notes/454528 "}, {"RefNumber": "199246", "RefComponent": "BC-SRV-NUM", "RefTitle": "Function keys for scrolling without function", "RefUrl": "/notes/199246 "}, {"RefNumber": "213546", "RefComponent": "IS-R-IFC-IN", "RefTitle": "HPR collective note: POS inbound", "RefUrl": "/notes/213546 "}, {"RefNumber": "1091025", "RefComponent": "BC-SRV-NUM", "RefTitle": "DATA_OFFSET_TOO_LARGE in SAPLSNR3", "RefUrl": "/notes/1091025 "}, {"RefNumber": "810757", "RefComponent": "FI-GL-GL-X", "RefTitle": "FAQ FI-GL-GL-X Data Consistency Check Composite Note", "RefUrl": "/notes/810757 "}, {"RefNumber": "175047", "RefComponent": "FI-GL-GL-X", "RefTitle": "Causes for FI document number gaps (RF_BELEG)", "RefUrl": "/notes/175047 "}, {"RefNumber": "677488", "RefComponent": "SD-BIL-GF-OC", "RefTitle": "EU Directive 2001/115/EG", "RefUrl": "/notes/677488 "}, {"RefNumber": "154091", "RefComponent": "LE-SHP-GF", "RefTitle": "Performance in delivery process: Collective note", "RefUrl": "/notes/154091 "}, {"RefNumber": "23835", "RefComponent": "SD-BIL-IV", "RefTitle": "Buffering RV_BELEG/number assignment in SD", "RefUrl": "/notes/23835 "}, {"RefNumber": "333356", "RefComponent": "CO-OM-OPA-A", "RefTitle": "Internal order: order nmbrs nt assignd continuously", "RefUrl": "/notes/333356 "}, {"RefNumber": "612211", "RefComponent": "IS-A-SBO", "RefTitle": "FI document number ranges bufferng for ERS parallel procssng", "RefUrl": "/notes/612211 "}, {"RefNumber": "366825", "RefComponent": "MM-IV-LIV", "RefTitle": "MIRO: Gaps in MM document numbers and external FI numbers", "RefUrl": "/notes/366825 "}, {"RefNumber": "388566", "RefComponent": "FS-CS", "RefTitle": "CS: Measures for performance improvement", "RefUrl": "/notes/388566 "}, {"RefNumber": "318104", "RefComponent": "IS-OIL-BC", "RefTitle": "IS-Oil-internal: Ref.to performance issues ....", "RefUrl": "/notes/318104 "}, {"RefNumber": "333649", "RefComponent": "RE", "RefTitle": "Collective note, performance in Real Estate", "RefUrl": "/notes/333649 "}, {"RefNumber": "141497", "RefComponent": "IS-H", "RefTitle": "Consulting: Buffering of number ranges", "RefUrl": "/notes/141497 "}, {"RefNumber": "37844", "RefComponent": "FI", "RefTitle": "Performance: document number assignment RF_BELEG", "RefUrl": "/notes/37844 "}, {"RefNumber": "311004", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Lock wait for patient accounting", "RefUrl": "/notes/311004 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30D", "To": "30F", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "31G", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "40B", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "46B", "To": "46D", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 40B", "SupportPackage": "SAPKH40B51", "URL": "/supportpackage/SAPKH40B51"}, {"SoftwareComponentVersion": "SAP_HR 40B", "SupportPackage": "SAPKE40B50", "URL": "/supportpackage/SAPKE40B50"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B22", "URL": "/supportpackage/SAPKH45B22"}, {"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B28", "URL": "/supportpackage/SAPKH45B28"}, {"SoftwareComponentVersion": "SAP_BASIS 46B", "SupportPackage": "SAPKB46B06", "URL": "/supportpackage/SAPKB46B06"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C01", "URL": "/supportpackage/SAPKB46C01"}, {"SoftwareComponentVersion": "SAP_BASIS 46C", "SupportPackage": "SAPKB46C07", "URL": "/supportpackage/SAPKB46C07"}, {"SoftwareComponentVersion": "SAP_BASIS 46D", "SupportPackage": "SAPKB46D02", "URL": "/supportpackage/SAPKB46D02"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0000179224/41"}, {"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 4, "URL": "/corrins/0000179224/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 5, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 2, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_APPL", "ValidFrom": "300", "ValidTo": "45A", "Number": "173343 ", "URL": "/notes/173343 ", "Title": "Error in the NUMBER_GET_NEXT", "Component": "BC-SRV-NUM"}, {"SoftwareComponent": "SAP_APPL", "ValidFrom": "40B", "ValidTo": "46B", "Number": "142237 ", "URL": "/notes/142237 ", "Title": "Process waits infinitely for number range buffers", "Component": "BC-SRV-NUM"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}