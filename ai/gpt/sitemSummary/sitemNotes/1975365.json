{"Request": {"Number": "1975365", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 260, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017805552017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001975365?language=E&token=2BB9CED6CC8C69BB4BF767B31973F155"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001975365", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1975365"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 1}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.03.2014"}, "SAPComponentKey": {"_label": "Component", "value": "IS-U-DM-DI"}, "SAPComponentKeyText": {"_label": "Component", "value": "Device Installation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Utilities", "value": "IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Device Management", "value": "IS-U-DM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-DM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Device Installation", "value": "IS-U-DM-DI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-U-DM-DI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1975365 - IS-U: Timestamp for device installation, removal and replacement"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note introduces a timestamp (time and time zone) for the installation, removal and replacement of devices.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The prerequisite for this SAP Note is <a target=\"_blank\" href=\"/notes/1973413\">SAP Note 1973413</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<div class=\"longtext MonoSpace\" id=\"MonoSpace\">\r\n<p><strong>Improvement</strong></p>\r\n<p>As of SAP Enhancement Package 7, Support Package 4 for SAP ERP 6.0, Industry Extension Utilities, Waste &amp; Recycling (ISU_UTIL_WASTE), the independent and reversible business function ISU_AMI_4C with the switch ISU_AMI_4_DTI is available. When this business function and the corresponding switch is activated, you can log the exact time (day/time) and time zone of a device-related physical activity (device installation/removal/replacement). The timestamp is used only as information in SAP for Utilities and is not available to other IS-U processes.<br />The processes in the solution SAP for Utilities are not affected by this field.  This applies particularly to reading and billing processes that work only on a daily basis.<br />In IS-U, device installation, removal and replacement is entered on a daily basis.<br />Device installation and removal is an action with an absolute beginning and end. Device installation is dated on the operation date and 00:00, whereas device removal is terminated on the operation date 1 and 23:59.<br />The master data and transaction date of other actions in IS-U, for example the reading (meter reading on installation, removal reading) are organized in relation to the operation date and time. <br />Due to the different data storage method in the IS-U and the MDUS system, this information can lead to errors in interpreting measurement data (discreet reading and interval measurement) on the MDUS side during validation, estimation and replacement value creation, as well as the assignment of measurement values. To avoid such errors, it is necessary to communicate the actual installation and removal time for the MDUS systems. This is particularly relevant for the process of device replacement of interval counters for different data models in IS-U and MDUS systems. The actual installation and removal time refers to the time at which the device was technically installed or removed on-site by the service technician. In addition to the operation time and operation date, the time zone is also taken into account.<br />In the IS-U back-end system, the operation time is used as pure information and is not supported in any IS-U process (for example, reading and billing).</p>\r\n<p><strong>Comment</strong></p>\r\n<p>This new function cannot only be used for AMI devices, but also for classic meters without an MDUS connection. It is also available for IS-U devices that have no interval register.<br />Additionally, this new function is supported only for real devices and not for device information records.</p>\r\n<p><strong>Delivery</strong></p>\r\n<p>You can obtain this improvement via the relevant Support Package. Activate the business function&#x00A0;ISU_AMI_4C and thus the switch ISU_AMI_4_DTI.</p>\r\n</div>\r\n</div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Improvement Note", "Value": "Yes"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D025920)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> Bez (D035807)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001975365/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001975365/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1974561", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Interface note - BF ISU_AMI_4C", "RefUrl": "/notes/1974561"}, {"RefNumber": "1973413", "RefComponent": "IS-U-DM-DI", "RefTitle": "IS-U: Interface note - timestamp", "RefUrl": "/notes/1973413"}, {"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2179030", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Empty time zone saved in time stamp", "RefUrl": "/notes/2179030 "}, {"RefNumber": "2044300", "RefComponent": "IS-OIL", "RefTitle": "Incl. BF to Combined BFS Oil & Gas and Mining with Utilities", "RefUrl": "/notes/2044300 "}, {"RefNumber": "1754249", "RefComponent": "IS-U", "RefTitle": "Enterprise Services for AMI Integration with MDUS systems", "RefUrl": "/notes/1754249 "}, {"RefNumber": "1991236", "RefComponent": "CRM-IU-IC", "RefTitle": "Enhanced invoice correction - missing fields in structure V_EGER", "RefUrl": "/notes/1991236 "}, {"RefNumber": "1974561", "RefComponent": "IS-U-DM", "RefTitle": "IS-U: Interface note - BF ISU_AMI_4C", "RefUrl": "/notes/1974561 "}, {"RefNumber": "1973413", "RefComponent": "IS-U-DM-DI", "RefTitle": "IS-U: Interface note - timestamp", "RefUrl": "/notes/1973413 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-UT", "From": "617", "To": "617", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-UT 617", "SupportPackage": "SAPK-61704INISUT", "URL": "/supportpackage/SAPK-61704INISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}