{"Request": {"Number": "1931427", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 521, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017746202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001931427?language=E&token=1D92D03D430BC4513696376F4EA4DAA4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001931427", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001931427/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1931427"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "06.07.2020"}, "SAPComponentKey": {"_label": "Component", "value": "BC-BW-ODP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Operational Data Provisioning (ODP) and Delta Queue (ODQ)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW Service API", "value": "BC-BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Operational Data Provisioning (ODP) and Delta Queue (ODQ)", "value": "BC-BW-ODP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-BW-ODP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1931427 - ODP Data Replication API 2.0"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The \"ODP Data Replication API 2.0\" is used for the internal connection of SAP BW/4HANA or SAP BW &gt;= 7.3x, SAP BusinessObjects Data Services 4.2 and SAP HANA Smart Data Integration (ABAP Adapter) to different data provider types such as DataSources/Extractors (ODP-SAPI Context), ABAP CDS Views (ODP-CDS Context) or&#160; InfoProviders of a SAP BW/4HANA or SAP BW &gt;= 7.4. (ODP-BW Context).</p>\r\n<p>The \"ODP Data Replication API 2.0\" is an functional enhancement to the first version of this interface released with SAP Note 1521883. But it does not deactivate or invalidate the first version of the interface which can still be used to connect SAP BusinessObjects Data Services 4.0.</p>\r\n<p>For more information on ODP 1.0 and 2.0 compatibility and availability see <a target=\"_blank\" href=\"/notes/2481315\">SAP Note 2481315</a> and the <a target=\"_blank\" href=\"https://blogs.sap.com/2017/07/20/operational-data-provisioning-odp-faq/\">Operational Data Provisioning FAQ</a>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BW, ETL, extraction, transformation and loading, ETL interface, ODP, operational data provider, RSAR032, RSAR 032</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The complete \"ODP Data Replication API 2.0\" is available with the following Support Packages (SP) for the applicable component (PI_BASIS, SAP_BW, or DW4CORE). We strongly recommend to import these Support Packages (SP) that contain all relevant enhancements:</p>\r\n<ul>\r\n<li>PI_BASIS 730 SP 14 (part of SAP NetWeaver 7.30 SP 14)</li>\r\n<li>PI_BASIS 731 SP 16 (part of SAP NetWeaver 7.03 SP 16 and 7.31 SP 16)</li>\r\n<li>PI_BASIS 740 SP 11 (part of SAP NetWeaver 7.40 SP 11)</li>\r\n<li>SAP_BW 750 SP 0 (incl. former PI_BASIS packages)</li>\r\n<li>DW4CORE 100 SP 0 (incl. former PI_BASIS packages)</li>\r\n<li>DW4CORE 200 SP 0 (incl. former PI_BASIS packages)</li>\r\n</ul>\r\n<p>This is the only recommended way to update your system to ODP API 2.0 and to a consistent state.</p>\r\n<p>On an exceptional project basis (this is <span style=\"text-decoration: underline;\">not</span> the recommended way), the implementation of ODP API 2.0 is also possible via advance corrections with at least the following minimum Support Packages (SP) for the applicable component PI_BASIS:</p>\r\n<ul>\r\n<li>PI_BASIS 730 SP 10 (part of SAP NetWeaver 7.30 SP 10)</li>\r\n<li>PI_BASIS 731 SP 8 (part of SAP NetWeaver 7.03 SP 8 and 7.31 SP 8)</li>\r\n<li>PI_BASIS 740 SP 4 (part of SAP NetWeaver 7.40 SP 4)</li>\r\n</ul>\r\n<p>For this, the attached list of SAP Notes with advance corrections has to be analyzed on custom project basis and all relevant SAP Notes (for the specific release and SP level) must get implemented.</p>\r\n<p>In case you need and want to implement the complete list, you can use the attached ABAP program with the attached SAP Note list in the following way:</p>\r\n<ol>1. Ensure that you have implemented SAP Note 1734555.</ol><ol>2. Download the latest version of this SAP Note (1931427) from SAP Service Marketplace.</ol><ol>3. Unpack the attached file \"ZNOTE_ANALYZER_1931427.zip\" and copy the contained files \"ZNOTE_ANALYZER_1931427.txt\" and \"ODP Data Replication API 2.0 Notes.csv\" to your computer.</ol><ol>4. Use transaction SE38 \"ABAP Editor\" to create the program ZNOTE_ANALYZER_1931427.</ol><ol>5. Enter a description in the dialog box and select \"Executable program\".</ol><ol>6. Save the program as a local, temporary object.</ol><ol>7. In the menu, choose \"Utilities -&gt; Upload/Download -&gt; Upload\".</ol><ol>8. Select the path for the file \"ZNOTE_ANALYZER_1931427.txt\" on your computer.</ol><ol>9. Save and activate the program.</ol><ol>10. Execute the program.</ol><ol>11. Select the path for the file \"ODP Data Replication API 2.0 Notes.csv\" on your computer.</ol><ol>12. Select the option \"Parallel Download\".</ol><ol>13. Choose \"Execute (F8)\". The program starts several background jobs to load the latest versions of the listed SAP Notes from SAP Service Marketplace.</ol><ol>14. Execute the program again when these background jobs finish.</ol><ol>15. Select the path for the file \"ODP Data Replication API 2.0 Notes.csv\" on your computer.</ol><ol>16. Do not select the option \"Parallel Download\".</ol><ol>17. Choose \"Execute (F8)\" and confirm the dialog box. The program lists the SAP Notes concerning known errors that you have not yet implemented in your system or the latest versions of these SAP Notes.</ol><ol>18. Scroll through the list from top to bottom and double-click the SAP Note number to navigate to transaction SNOTE in order to implement the SAP Notes.</ol>\r\n<p>If an error occurs when you use the interface, repeat the above procedure to ensure that the latest corrections are implemented in your system.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-BW-SRV (BW Generation Tool and Set Object)"}, {"Key": "Other Components", "Value": "EIM-DS-ODP (SAP ERP Extractors)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D046539)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027464)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001931427/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001931427/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "ZNOTE_ANALYZER_1931427.zip", "FileSize": "7", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900001436982013&iv_version=0011&iv_guid=6CAE8B3EA1831ED6B78609B2929C60D0"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2767245", "RefComponent": "EIM-DS-SAP", "RefTitle": "Unable to import ODP object by name for data extraction from ECC - Data Services", "RefUrl": "/notes/2767245 "}, {"RefNumber": "2440562", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP: Hierarchy extraction", "RefUrl": "/notes/2440562 "}, {"RefNumber": "2436790", "RefComponent": "EIM-DS-SAP", "RefTitle": "Hierarchy extractor import error message Hierarchie DataSource is not suitable for ODP", "RefUrl": "/notes/2436790 "}, {"RefNumber": "3255746", "RefComponent": "BC-BW-ODP", "RefTitle": "Unpermitted usage of ODP Data Replication APIs", "RefUrl": "/notes/3255746 "}, {"RefNumber": "3094747", "RefComponent": "BW-BCT-ISM", "RefTitle": "DataSource 0ME_MATERIAL_ATTR correction: IS-M fields cannot be transferred", "RefUrl": "/notes/3094747 "}, {"RefNumber": "2855052", "RefComponent": "BC-BW-ODP", "RefTitle": "Authorizations required for ODP Data Replication API 2.0", "RefUrl": "/notes/2855052 "}, {"RefNumber": "2480284", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL - Hierarchy DataSources", "RefUrl": "/notes/2480284 "}, {"RefNumber": "2481315", "RefComponent": "BC-BW-ODP", "RefTitle": "Operational Data Provisioning (ODP): Extracting from SAP Systems to SAP BW or SAP BW/4HANA – Availability and Limitations", "RefUrl": "/notes/2481315 "}, {"RefNumber": "2037476", "RefComponent": "CA-LTR-BW", "RefTitle": "Operational Data Provisioning with SAP LT Replication Server", "RefUrl": "/notes/2037476 "}, {"RefNumber": "1521883", "RefComponent": "BC-BW-ODP", "RefTitle": "ODP Data Replication API 1.0", "RefUrl": "/notes/1521883 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "PI_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "PI_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}