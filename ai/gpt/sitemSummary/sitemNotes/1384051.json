{"Request": {"Number": "1384051", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 341, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016868122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001384051?language=E&token=6CBE3EE5873258018DD0E4986A2F157F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001384051", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001384051/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1384051"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 15}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Special development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "14.07.2020"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PROJ-IMS-UPGR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Application Specific Upgrade Tools"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Project-based solutions", "value": "XX-PROJ", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installed Base Development Projects", "value": "XX-PROJ-IMS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Application Specific Upgrade Tools", "value": "XX-PROJ-IMS-UPGR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PROJ-IMS-UPGR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1384051 - ASU variant restorer: Consistency check after upgrade"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>***********************************</p>\r\n<p><strong>Attention:</strong> The transports of this SAP Note are obsolete. With the releases/patches specified in SAP Note 2783555, the program RASUVAR_DETECT is available in the standard delivery. In this case, do not import any of the transports! For older releases/patches, you can use the transport from SAP Note 2915357.</p>\r\n<p>***********************************</p>\r\n<p><strong>Achtung: </strong>Die Transporte dieses SAP-Hinweises sind veraltet. Mit den im SAP-Hinweis 2783555 genannten Releases/Patches steht das Programm RASUVAR_DETECT im Standard zur Verf&#x00FC;gung - spielen <PERSON>e in diesem Fall keinen der Transporte ein! F&#x00FC;r &#x00E4;ltere Releases/Patches kann der Transport aus SAP-Hinweis 2915357 genutzt werden.</p>\r\n<p>***********************************</p>\r\n<p>You executed the ASU variant restorer either during the technical upgrade (JOB_RASUVAR1/2) or via the programs RASUVAR_START / RASUVAR_FINISH (from SAP Note 1160685). After you do this, you want to check the consistency of the variants that were converted.<br />Since the variant restorer performs a pure conversion of the selection variants, data may be missing from the variants after this. Only the user can check this by performing a manual comparison. However, it is not clear which variants are to be checked.<br />Therefore, this SAP Note provides the report RASUVAR_DETECT in the <a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&amp;iv_guid=6CAE8B28C71B1ED6BDF06B3621F880CA&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F49333537368BF734378A3730B472490FA834F0CFAA2CCDF2702CB7A8CA332828CDCE760E2D30738ACF298C28284DAB0C08F50A0B2AD1750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">transport</a>&#x00A0;(or <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&amp;iv_guid=5DC239E6C7D07D47917DEB2C6CF7D6B8&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F49333537368BF734378A3730B472490FA834F0CFAA2CCDF2702CB7A8CA332828CDCE760E2D30738ACF298C28284DAB0C08F50A0B2AD1750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">Y53K901845</a></span> for SAP_BASIS 620). This report performs a check of the variants or the selection screens of the reports. As a result, you can restrict the number of variants that are to be checked.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>ASU, variant restorer, selection variants, conversion, technical conversion of selection screens, variants, selection variants, RASUVAR_DETECT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Consulting</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>*********************************************************************<br />* Attention: The transport can be imported only in systems with Release *<br />* SAP_BASIS&#x00A0;700 or higher. *<br />* -----------------------------------------------------------------*<br />* This functionality is delivered in SAP Standard with release *<br />* SAP_BASIS 7.50 SP15. After this, the transport should not *<br />* be imported anymore! *<br />*********************************************************************<br />* Attention: The transport should be imported in systems with *<br />* release SAP_BASIS&#x00A0;700 or higher only. *<br />* -----------------------------------------------------------------*<br />* This functionality is delivered in SAP Standard with release *<br />* SAP_BASIS 7.50 SP15. After this, the transport should not *<br />* be imported anymore! *<br />*********************************************************************<br /><br />Import the <a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&amp;iv_guid=6CAE8B28C71B1ED6BDF06B3621F880CA&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F49333537368BF734378A3730B472490FA834F0CFAA2CCDF2702CB7A8CA332828CDCE760E2D30738ACF298C28284DAB0C08F50A0B2AD1750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">transport file</a> (for SAP_BASIS 620, the transport file <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&amp;iv_guid=5DC239E6C7D07D47917DEB2C6CF7D6B8&amp;alt=2BCE4CB10DF674B172F4F3F7B32A284F49333537368BF734378A3730B472490FA834F0CFAA2CCDF2702CB7A8CA332828CDCE760E2D30738ACF298C28284DAB0C08F50A0B2AD1750C09514BCECFCFCE4C8DCF4BCC4DB5F575F4F4F3F57771F571F6F70B01B25D83D4120B0A722092A599504EB16D715E3E00\">Y53K901845.zip</a></span>) from the attachment of this SAP Note. A return code 4 may occur during the import, but you can ignore this. After this step, the program RASUVAR_DETECT is available (English only, no translation supplied).</p>\r\n<ul>\r\n<li>After the variant restorer performs the conversion (RASUVAR2 or RASUVAR_FINISH, that is, after the upgrade), use transactions SE38 or SA38 to execute the program RASUVAR_DETECT manually. It then checks the reports and the variants, and compares the results with the information contained in the table TASUVAR1. It is therefore necessary for this information (that was created before the upgrade) to remain available.</li>\r\n</ul>\r\n<ul>\r\n<li>The report RASUVAR_DETECT has the following options on the selection screen:</li>\r\n</ul>\r\n<p>Option to restrict to individual reports<br /> Selection of whether only reports or variants that are contained in (background) jobs (table TBTCO or TBTCP) are to be checked or whether all variants that exist in the system are to be checked. By default, the system checks only the reports or variants from the jobs because the user generally checks the other variants (in online mode) before they are used anyway.<br /> Restricting the output to the reports or variants in which the check contained errors (default)</p>\r\n<ul>\r\n<li>The output of the report RASUVAR_DETECT can identify the following situations and issue relevant messages:</li>\r\n</ul>\r\n<p>\"Error during access to report\" - status red: the system issues this message if it could not perform the check in RASUVAR_DETECT for a report. In this situation, you must check the specified report or variant.<br /> \"Report has syntax error\" - status yellow - in this case, the syntax check for the specified report resulted in an error. You cannot execute the report without correcting the syntax error and you cannot check the variants until after you have corrected the report.<br /> \"Report not exist\" - status green: the check identified that the report no longer exists (for example, because it was deleted by the upgrade and is therefore obsolete). The variants and jobs can no longer be executed and can be deleted as part of a cleanup action.<br /> \"Standard variant - might be overwriten by SAP\" - status yellow: variants that begin with SAP&amp; are variants that are delivered by SAP. During an upgrade, these variants are imported again and overwrite the existing definition. Therefore, you must check the contents before you use these in a background job.<br /> \"Variant damaged - access not possible\" - status yellow: using the variant restorer to convert the variant was not successful. Accessing the data of the variant is no longer possible. Before you use the variant, you must delete it and recreate it if it is still required.<br /> \"No data stored in TASUVAR1 - check not possible\" - status red: in this case, RASUVAR_DETECT cannot perform any further checks. No information exists about setting up the selection screen of the report before the upgrade.<br /> \"Number of parameters on selection screen changed\" - status yellow: RASUVAR_DETECT has determined that the number of parameters on the selection screen has changed. You must check whether the variants contain all of the required information or whether additional data has to be stored in the variant.<br /> \"Parameters on selection screen changed\" - status yellow: technical changes have been made to the selection screen due to the upgrade. This may result in information from the variant no longer being able to be assigned. You must check the variant.<br /> \"Report/variant unchanged\" - status green: no errors or differences were found on the selection screen. You can use the variant without performing additional checks.</p>\r\n<ul>\r\n<li>If the report and the variant are used in a background job, the output of RASUVAR_DETECT also lists the name of the background job in addition to the status information and the data for the client, report name, and variant name. An indicator also specifies whether the background job is a background job that is scheduled periodically.</li>\r\n</ul>\r\n<ul>\r\n<li>RASUVAR_DETECT also checks temporary variants (&amp;000...) that exist only within a background job. If these variants are to be changed or checked, you can do this only via the corresponding step in the relevant job.</li>\r\n</ul>\r\n<ul>\r\n<li>When you execute the report RASUVAR_DETECT, the system attempts to read in the variant information. When the system does this, terminations may occur in certain cases that RASUVAR_DETECT cannot catch. Therefore, dumps may occur in transaction ST22. If this should terminate RASUVAR_DETECT, you should exclude the relevant reports from the selection of RASUVAR_DETECT and restart the check of the remaining reports. You must manually check the variants of the report that caused the dump. The following reports are normally affected by this problem:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HIDCALC0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HIECEDT0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HMXCLJN0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HNZCALC0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HNZCEDT0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HTHCEDT0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>HTWCALC0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORB00</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD00</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD10</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD11</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD20</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD30</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD40</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD50</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD60</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORD80</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDC1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDJ1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDJ2</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDJ3</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDJ4</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORDR1</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RFKORK00</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCA0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCB0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCC0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCD0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCE0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCF0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCG0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCK0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCL0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCM0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCN0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCQ0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCR0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCU0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCALCX0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTD0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTF0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTK0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTM0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTR0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTU0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCEDTW0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCLEGG0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCLJNL0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPCLJNU0</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>RPTEB200</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>SAPMV50S</li>\r\n</ul>\r\n</ul></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023370)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D023370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001384051/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001384051/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "E4AK300973.zip", "FileSize": "87", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&iv_version=0015&iv_guid=6CAE8B28C71B1ED6BDF06B3621F880CA"}, {"FileName": "Y53K901845.zip", "FileSize": "59", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000395092009&iv_version=0015&iv_guid=5DC239E6C7D07D47917DEB2C6CF7D6B8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2915357", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "UNKNOWN_COMPONENT_OBJECT for objects in package ASU1", "RefUrl": "/notes/2915357"}, {"RefNumber": "2783555", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Initial delivery ASU variants tool in SAP_ABA", "RefUrl": "/notes/2783555"}, {"RefNumber": "1696821", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Technical documentation: ASU variant restorer functionality", "RefUrl": "/notes/1696821"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2185009", "RefComponent": "PS-IS-LOG", "RefTitle": "Variants of the project info system after upgrade to ECC 617 or later", "RefUrl": "/notes/2185009 "}, {"RefNumber": "1696821", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Technical documentation: ASU variant restorer functionality", "RefUrl": "/notes/1696821 "}, {"RefNumber": "1416105", "RefComponent": "XX-PROJ-IMS-UPGR", "RefTitle": "Technische Doku: Funktionsweise ASU Variantenretter (intern)", "RefUrl": "/notes/1416105 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}