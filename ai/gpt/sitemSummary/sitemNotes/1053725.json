{"Request": {"Number": "1053725", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1037, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016282922017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001053725?language=E&token=85F61E5F782A6B5D1DAEEE3BBF1515A9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001053725", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001053725/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1053725"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1053725 - SAPBWNews BW 7.10 ABAP SP 03"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note concerns Support Package 03 for BI Release 7.10, which is part of NetWeaver 7.10.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.10, BW 7.10, BI 7.10, BW Patches, BI, BI 7.10, SAPBINEWS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBINews for Support Package 03 for SAP NetWeaver 7.10. It provides a list of all notes describing corrections or enhancements in Support Package 03.<br />This note will be updated when other notes are added. <br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected in this Support Package </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements delivered with this Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<p><br />- To date, no errors are known.</p>\r\n<p><strong>Errors corrected in this Support Package: </strong><br /><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS (Basis System and Installation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001053725/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001053725/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1079310", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 15", "RefUrl": "/notes/1079310"}, {"RefNumber": "1074356", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "EXCEPTION cx_rs_input_invalid in SET_RETURNFLS", "RefUrl": "/notes/1074356"}, {"RefNumber": "1074104", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Error in ABAP Dictionary when you activate a DTP", "RefUrl": "/notes/1074104"}, {"RefNumber": "1073912", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Lower-level nodes are not written back to the cache", "RefUrl": "/notes/1073912"}, {"RefNumber": "1073859", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Termination in CL_RSR_HIERARCHY_DIM and SELECT_SIDS_3", "RefUrl": "/notes/1073859"}, {"RefNumber": "1072982", "RefComponent": "BW-BEX-OT", "RefTitle": "Metadata buffer for MultiProvider is not deleted", "RefUrl": "/notes/1072982"}, {"RefNumber": "1072970", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems durnig data archiving using a process chain", "RefUrl": "/notes/1072970"}, {"RefNumber": "1072923", "RefComponent": "BW-BEX-ET-KPI", "RefTitle": "KPI/DAS: Output of results - no decimal places", "RefUrl": "/notes/1072923"}, {"RefNumber": "1072911", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon waits too long", "RefUrl": "/notes/1072911"}, {"RefNumber": "1072790", "RefComponent": "BW-BEX-OT", "RefTitle": "BRAIN 299 in class CL_RSR; form GET_SID_FROM_CHAVL-01-", "RefUrl": "/notes/1072790"}, {"RefNumber": "1072764", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Remote master data read with incorrect key date", "RefUrl": "/notes/1072764"}, {"RefNumber": "1072555", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "RSAODS 006: Invalid parameters for L_R_STRUCTDESCR_SID", "RefUrl": "/notes/1072555"}, {"RefNumber": "1072549", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump when you copy a DataSource", "RefUrl": "/notes/1072549"}, {"RefNumber": "1072548", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Maintenance of data type, size category of PSA", "RefUrl": "/notes/1072548"}, {"RefNumber": "1072483", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Incorrect error message when you activate a hierarchy", "RefUrl": "/notes/1072483"}, {"RefNumber": "1072425", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Runtime error TEXTENV_CODEPAGE_NOT_ALLOWED", "RefUrl": "/notes/1072425"}, {"RefNumber": "1072333", "RefComponent": "BW-BEX-OT", "RefTitle": "Introduction of problem class for messages", "RefUrl": "/notes/1072333"}, {"RefNumber": "1072332", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Link node selection visible multiple times in variable dialo", "RefUrl": "/notes/1072332"}, {"RefNumber": "1072240", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data in aggregate (time-dependent hierarchy)", "RefUrl": "/notes/1072240"}, {"RefNumber": "1072145", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Incorrect texts in RSDS_ACCESS", "RefUrl": "/notes/1072145"}, {"RefNumber": "1072115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unexpected aggregations in formulas with replacement path", "RefUrl": "/notes/1072115"}, {"RefNumber": "1071986", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Deleting entries that are no longer needed from the K table", "RefUrl": "/notes/1071986"}, {"RefNumber": "1071790", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Status of transformation incorrect", "RefUrl": "/notes/1071790"}, {"RefNumber": "1071661", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Filtering new records that have the same key does not work", "RefUrl": "/notes/1071661"}, {"RefNumber": "1071640", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not execute inactive data transfer process", "RefUrl": "/notes/1071640"}, {"RefNumber": "1071547", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "No PSA attributes for error stack", "RefUrl": "/notes/1071547"}, {"RefNumber": "1071347", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Unauthorized data displayed for chargeable nodes/leaves", "RefUrl": "/notes/1071347"}, {"RefNumber": "1071255", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Short dump during migration after transformation", "RefUrl": "/notes/1071255"}, {"RefNumber": "1071035", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA SP test: Information about current SP is ambiguous", "RefUrl": "/notes/1071035"}, {"RefNumber": "1070732", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DTP deactivated during transformation activation", "RefUrl": "/notes/1070732"}, {"RefNumber": "1070542", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Aggregation behavior is incorrect", "RefUrl": "/notes/1070542"}, {"RefNumber": "1070447", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Ready for input status for elements with quantity conversion", "RefUrl": "/notes/1070447"}, {"RefNumber": "1070378", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Hard-coded source system in DataSource program", "RefUrl": "/notes/1070378"}, {"RefNumber": "1070085", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA Monitor e-mail notification using RSDDTREXADMIN", "RefUrl": "/notes/1070085"}, {"RefNumber": "1069957", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Restarting delete phase with partially deleted data", "RefUrl": "/notes/1069957"}, {"RefNumber": "1069800", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Error when transporting open hub destinations", "RefUrl": "/notes/1069800"}, {"RefNumber": "1069675", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Further performance improvement", "RefUrl": "/notes/1069675"}, {"RefNumber": "1069401", "RefComponent": "BW-BEX-ET-KPI", "RefTitle": "Problems when you activate KPI Content objects", "RefUrl": "/notes/1069401"}, {"RefNumber": "1069325", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems occur when you create a constant rule", "RefUrl": "/notes/1069325"}, {"RefNumber": "1069175", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 for 0weekday1: 0 instead of # and 'unassigned'", "RefUrl": "/notes/1069175"}, {"RefNumber": "1069152", "RefComponent": "BW-BCT-CSC-ES", "RefTitle": "LOPD: Corrections for legal requirements", "RefUrl": "/notes/1069152"}, {"RefNumber": "1068827", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: RSPC 57 \"Process did not report an instance\"", "RefUrl": "/notes/1068827"}, {"RefNumber": "1068740", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:P21:Timeout time ignored during the load through PC", "RefUrl": "/notes/1068740"}, {"RefNumber": "1068721", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Syntax error occurs in the generated transfer program", "RefUrl": "/notes/1068721"}, {"RefNumber": "1068478", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Message: Rule is invalid and is being deleted", "RefUrl": "/notes/1068478"}, {"RefNumber": "1068332", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No restriction on 0INFOPROV (F4 mode M instead of D)", "RefUrl": "/notes/1068332"}, {"RefNumber": "1068254", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise search: Search for workbooks and query views", "RefUrl": "/notes/1068254"}, {"RefNumber": "1068187", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termination GETWA_NOT_ASSIGNED with non-cumulative query", "RefUrl": "/notes/1068187"}, {"RefNumber": "1068162", "RefComponent": "BW-WHM-DST", "RefTitle": "To be defined", "RefUrl": "/notes/1068162"}, {"RefNumber": "1068122", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: GETWA_NOT_ASSIGNED_RANGE in program SAPLRRK0", "RefUrl": "/notes/1068122"}, {"RefNumber": "1068054", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "You cannot delete hierarchy node authorization", "RefUrl": "/notes/1068054"}, {"RefNumber": "1067984", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in CL_RSDRC_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1067984"}, {"RefNumber": "1067943", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: TRIGGER: Invalid call sequence for interfaces", "RefUrl": "/notes/1067943"}, {"RefNumber": "1067750", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: \"l_chavl already declared\" when activating DS", "RefUrl": "/notes/1067750"}, {"RefNumber": "1067724", "RefComponent": "BW-WHM-DST", "RefTitle": "P15:DTP:Manage: Empty date/time field in request list", "RefUrl": "/notes/1067724"}, {"RefNumber": "1067612", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: TIMEINTERVAL_GET in program SAPLRRHI", "RefUrl": "/notes/1067612"}, {"RefNumber": "1067605", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: PSA: Rebuilding: <PERSON><PERSON><PERSON> set too late or not at all", "RefUrl": "/notes/1067605"}, {"RefNumber": "1067594", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSR_RRK0_Hierarchy;  APPLY_SLICER_NAV-02-", "RefUrl": "/notes/1067594"}, {"RefNumber": "1067550", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Optimizing SFK when selecting structure elements (KIDSEL)", "RefUrl": "/notes/1067550"}, {"RefNumber": "1067546", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"No authorization\" for document check", "RefUrl": "/notes/1067546"}, {"RefNumber": "1067519", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Proposal processes despite expert mode for DTP", "RefUrl": "/notes/1067519"}, {"RefNumber": "1067377", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRI2; form CELL_FUELLEN_FEMZ-02-", "RefUrl": "/notes/1067377"}, {"RefNumber": "1067225", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.0 (SP15) DataStore default settings are not applied", "RefUrl": "/notes/1067225"}, {"RefNumber": "1067067", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Deletion of PC terminates if there is active run", "RefUrl": "/notes/1067067"}, {"RefNumber": "1067042", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archived data areas are not protected in InfoCube", "RefUrl": "/notes/1067042"}, {"RefNumber": "1067027", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Initial entries from master data and texts are not extracted", "RefUrl": "/notes/1067027"}, {"RefNumber": "1066979", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC:BATCH:Parallelism settings and defaults", "RefUrl": "/notes/1066979"}, {"RefNumber": "1066720", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "No execution authorization for queries with COMPID '!!*'", "RefUrl": "/notes/1066720"}, {"RefNumber": "1066575", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A125  GVAR has no component called \"LKnnnn\"", "RefUrl": "/notes/1066575"}, {"RefNumber": "1066525", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Update rules: Tecnhnical fields displayed", "RefUrl": "/notes/1066525"}, {"RefNumber": "1066471", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data is lost when you execute reload requests in parallel", "RefUrl": "/notes/1066471"}, {"RefNumber": "1066426", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: Error messages from data manager missing from log", "RefUrl": "/notes/1066426"}, {"RefNumber": "1066395", "RefComponent": "BW-WHM-DST", "RefTitle": "Support Package 15: No server dialog box for certain users", "RefUrl": "/notes/1066395"}, {"RefNumber": "1066244", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Version handling in the transformation", "RefUrl": "/notes/1066244"}, {"RefNumber": "1066180", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P15:SDL: Dump CALL_FUNCTION_REMOTE_ERROR in LRSSMU17", "RefUrl": "/notes/1066180"}, {"RefNumber": "1066071", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Refresh function for queries", "RefUrl": "/notes/1066071"}, {"RefNumber": "1066053", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Buffered data during transport", "RefUrl": "/notes/1066053"}, {"RefNumber": "1066026", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Reload req after termintn in del process cannot be resumed", "RefUrl": "/notes/1066026"}, {"RefNumber": "1065534", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Dump OBJECTS_OBJREF_NOT_ASSIGNED when you display log", "RefUrl": "/notes/1065534"}, {"RefNumber": "1065494", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "More precise error message for BRAIN X299 MEGA_SORT_05-02-", "RefUrl": "/notes/1065494"}, {"RefNumber": "1065440", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when displaying Content version", "RefUrl": "/notes/1065440"}, {"RefNumber": "1065435", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages when collecting content (A version missing)", "RefUrl": "/notes/1065435"}, {"RefNumber": "1065360", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 15 BIA revision compatibility check enhanced", "RefUrl": "/notes/1065360"}, {"RefNumber": "1065358", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process not completely activated", "RefUrl": "/notes/1065358"}, {"RefNumber": "1065352", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "GETWA_NOT_ASSIGNED in prog CL_RSDA_DAP_DYNPRO_CONTROLLER=CP", "RefUrl": "/notes/1065352"}, {"RefNumber": "1065308", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "\"No authorization\" when you use temporal hierarchy join", "RefUrl": "/notes/1065308"}, {"RefNumber": "1065307", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "No currency conversion during calculation before aggregation", "RefUrl": "/notes/1065307"}, {"RefNumber": "1065088", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Recording BIA Monitor details for BIA Performance Trace", "RefUrl": "/notes/1065088"}, {"RefNumber": "1065081", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chain merged although it is incorrect", "RefUrl": "/notes/1065081"}, {"RefNumber": "1065072", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Message BRAIN 087 during query generation", "RefUrl": "/notes/1065072"}, {"RefNumber": "1065052", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Error RSDA 213 when resuming an archiving request", "RefUrl": "/notes/1065052"}, {"RefNumber": "1065051", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No existence check for archived data using DME class", "RefUrl": "/notes/1065051"}, {"RefNumber": "1064960", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: Deleting subtress incorrect for intervals", "RefUrl": "/notes/1064960"}, {"RefNumber": "1064879", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT wizard: Selection of data objects is initialized", "RefUrl": "/notes/1064879"}, {"RefNumber": "1064798", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "You cannot delete activated requests after archiving", "RefUrl": "/notes/1064798"}, {"RefNumber": "1064328", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Resetting interrupts", "RefUrl": "/notes/1064328"}, {"RefNumber": "1064322", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "IP: BRAIN X299 _GET_R_MOVE_TYPE-06-/ACTIVATE_B_BUFFER-01-", "RefUrl": "/notes/1064322"}, {"RefNumber": "1064273", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problems when you process the variable screen again", "RefUrl": "/notes/1064273"}, {"RefNumber": "1064081", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Error message missing for RDA InfoPackage request", "RefUrl": "/notes/1064081"}, {"RefNumber": "1063991", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: obsolete D versions (also transformation, DTP)", "RefUrl": "/notes/1063991"}, {"RefNumber": "1063930", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Dump FILTER_SFC_TIMOBJS-1- for MultiProv w/ non-cum. key fig", "RefUrl": "/notes/1063930"}, {"RefNumber": "1063926", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Objektstatus wird nicht zurückgegeben bzw. ist falsch", "RefUrl": "/notes/1063926"}, {"RefNumber": "1063768", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Performance of plan queries with high no. of key figures", "RefUrl": "/notes/1063768"}, {"RefNumber": "1063767", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Various minor MDX errors", "RefUrl": "/notes/1063767"}, {"RefNumber": "1063661", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Branching from Web to Web does not work", "RefUrl": "/notes/1063661"}, {"RefNumber": "1063541", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during time split with fiscal year variant", "RefUrl": "/notes/1063541"}, {"RefNumber": "1063539", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termination: _SEL_TO_SELDR_14-02- in CL_RSR_RRK0_ATTR_C", "RefUrl": "/notes/1063539"}, {"RefNumber": "1063483", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Content activation in A1S mode", "RefUrl": "/notes/1063483"}, {"RefNumber": "1063466", "RefComponent": "BW-WHM-DST", "RefTitle": "Transactional request is not set to qualok", "RefUrl": "/notes/1063466"}, {"RefNumber": "1063465", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Input help (F4) in InfoSource maintenance", "RefUrl": "/notes/1063465"}, {"RefNumber": "1063281", "RefComponent": "BW-BEX-OT", "RefTitle": "The method GET_DATA_TIMESTMP had to be adjusted", "RefUrl": "/notes/1063281"}, {"RefNumber": "1063184", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect DTP/InfoP. if pseudo D does not exist", "RefUrl": "/notes/1063184"}, {"RefNumber": "1063024", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: NUMC field extension incompatible with database", "RefUrl": "/notes/1063024"}, {"RefNumber": "1063022", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Search appliance: No dialog box when deleting source system", "RefUrl": "/notes/1063022"}, {"RefNumber": "1062928", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "HybridProvider without database persistence", "RefUrl": "/notes/1062928"}, {"RefNumber": "1062863", "RefComponent": "BW-BEX-ET-RA-BC", "RefTitle": "Precalculation fails when exclude sign is used", "RefUrl": "/notes/1062863"}, {"RefNumber": "1062704", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC: InfoPackage input help does not display any values", "RefUrl": "/notes/1062704"}, {"RefNumber": "1062577", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems with time characteristic rule / constant rule", "RefUrl": "/notes/1062577"}, {"RefNumber": "1062574", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Time dependency when reading master data", "RefUrl": "/notes/1062574"}, {"RefNumber": "1062379", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Quantities are displayed incorrectly in PSA", "RefUrl": "/notes/1062379"}, {"RefNumber": "1062166", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Settings that are not allowed in transformations not caught", "RefUrl": "/notes/1062166"}, {"RefNumber": "1062128", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "SQL0104N An unexpected token \")\" was found following \"D \"K\"", "RefUrl": "/notes/1062128"}, {"RefNumber": "1062123", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data if query is filtered according to non-assigned nodes", "RefUrl": "/notes/1062123"}, {"RefNumber": "1062064", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Missing information in API RSB_API_OHS_SPOKE_GETLIST", "RefUrl": "/notes/1062064"}, {"RefNumber": "1062039", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Two DataSources have the same structure", "RefUrl": "/notes/1062039"}, {"RefNumber": "1061859", "RefComponent": "BW-PLA-IP", "RefTitle": "Selection condition w/ several hierarchy nodes is incorrect", "RefUrl": "/notes/1061859"}, {"RefNumber": "1061832", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Chains are not activated in the background", "RefUrl": "/notes/1061832"}, {"RefNumber": "1061796", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Lock NOT set for: Condensing the Data in an InfoProvider", "RefUrl": "/notes/1061796"}, {"RefNumber": "1061668", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Empty error stack accessed for reading", "RefUrl": "/notes/1061668"}, {"RefNumber": "1061525", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: RRS_VREP_NODE_FROM_VAR_FILL-01- in SAPLRRS2", "RefUrl": "/notes/1061525"}, {"RefNumber": "1061378", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 087 when generating query: different hierarchies used", "RefUrl": "/notes/1061378"}, {"RefNumber": "1061324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in Program SAPLRRI2, form CUDIM_FUELLEN_GEN-03-", "RefUrl": "/notes/1061324"}, {"RefNumber": "1061322", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error when you activate transformation for quantity DS", "RefUrl": "/notes/1061322"}, {"RefNumber": "1061165", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Leaf selection: Unautorized data is displayed", "RefUrl": "/notes/1061165"}, {"RefNumber": "1061119", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Field update when you load master data using end routine", "RefUrl": "/notes/1061119"}, {"RefNumber": "1061115", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Field sequence of open hub destination", "RefUrl": "/notes/1061115"}, {"RefNumber": "1060735", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of the variables during input", "RefUrl": "/notes/1060735"}, {"RefNumber": "1060406", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BRAIN 605: \"The requested query... does not exist...\"", "RefUrl": "/notes/1060406"}, {"RefNumber": "1060405", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Open Hub Destination maintenance: Unit fields incorrect", "RefUrl": "/notes/1060405"}, {"RefNumber": "1060387", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analysis with incorrect results in BIA queries", "RefUrl": "/notes/1060387"}, {"RefNumber": "1060197", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination \"VIEW_BUILD-2-\" in change run/monitor", "RefUrl": "/notes/1060197"}, {"RefNumber": "1060195", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Vague error messages after activation in content system", "RefUrl": "/notes/1060195"}, {"RefNumber": "1060170", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance improvement during analysis authorizations", "RefUrl": "/notes/1060170"}, {"RefNumber": "1060094", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Errors with fixed source unit; problems with conversion", "RefUrl": "/notes/1060094"}, {"RefNumber": "1060093", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when working with content - collect/activate or create", "RefUrl": "/notes/1060093"}, {"RefNumber": "1059882", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Import of Web service DataSource terminates", "RefUrl": "/notes/1059882"}, {"RefNumber": "1059837", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Obsolete formulas & routines in import postprocessing method", "RefUrl": "/notes/1059837"}, {"RefNumber": "1059833", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Currency conversion in the transformation", "RefUrl": "/notes/1059833"}, {"RefNumber": "1059565", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA request status yello, number of records inconsistent", "RefUrl": "/notes/1059565"}, {"RefNumber": "1059529", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem with formula variable of type 'Date' or 'Time'", "RefUrl": "/notes/1059529"}, {"RefNumber": "1059528", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: Necessary reorg. always not yet possible", "RefUrl": "/notes/1059528"}, {"RefNumber": "1059479", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: dump during synchronous call", "RefUrl": "/notes/1059479"}, {"RefNumber": "1059381", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "InfoProvider restriction in input help using MultiProviders", "RefUrl": "/notes/1059381"}, {"RefNumber": "1059289", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termintn IF_RSMD_RS_ACCESS~GET_VALUES in CL_RRHI_MDACCESS_TD", "RefUrl": "/notes/1059289"}, {"RefNumber": "1059287", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Incorrect error handling when checking archive locks", "RefUrl": "/notes/1059287"}, {"RefNumber": "1059248", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Search variant in variable screen in BW 3.x BEx Analyzer", "RefUrl": "/notes/1059248"}, {"RefNumber": "1059231", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: Service modules for A1S, and so on", "RefUrl": "/notes/1059231"}, {"RefNumber": "1059183", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination \"SELAGGR_SIMPLE_GET\" when you fill an aggregate", "RefUrl": "/notes/1059183"}, {"RefNumber": "1059180", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Status unknown if errors occur in dialog processing", "RefUrl": "/notes/1059180"}, {"RefNumber": "1059176", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: Various dumps in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1059176"}, {"RefNumber": "1059175", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: DSO: Write optimized DSO and reconstruction dump", "RefUrl": "/notes/1059175"}, {"RefNumber": "1059174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dump ENTRY_EXCP_CLEANUP_LEFT", "RefUrl": "/notes/1059174"}, {"RefNumber": "1059076", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "SAP exit variables and name spaces", "RefUrl": "/notes/1059076"}, {"RefNumber": "1059049", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Status temporarily remains red after a restart", "RefUrl": "/notes/1059049"}, {"RefNumber": "1058857", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data displayed in a very special situation", "RefUrl": "/notes/1058857"}, {"RefNumber": "1058785", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "The InfoSource is not defined in the source system (R3 005)", "RefUrl": "/notes/1058785"}, {"RefNumber": "1058680", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 BRAIN in CL_RSDRC_MULTIPROV; form  _COLLECT_SFC_RETFL-0", "RefUrl": "/notes/1058680"}, {"RefNumber": "1058679", "RefComponent": "BW-PLA-IP", "RefTitle": "Several key selections in the select statement", "RefUrl": "/notes/1058679"}, {"RefNumber": "1058433", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Planning in foreign currency and empty cells", "RefUrl": "/notes/1058433"}, {"RefNumber": "1058274", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Displaying recorded reference data terminates", "RefUrl": "/notes/1058274"}, {"RefNumber": "1058237", "RefComponent": "BW-WHM-DST-INP", "RefTitle": "Enhanced syntax check: Error correction", "RefUrl": "/notes/1058237"}, {"RefNumber": "1058181", "RefComponent": "BW-BEX-OT", "RefTitle": "Optimized performance during determination of master data ID", "RefUrl": "/notes/1058181"}, {"RefNumber": "1057685", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Missing content time stamp (only replicating)", "RefUrl": "/notes/1057685"}, {"RefNumber": "1057650", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error GETWA_NOT_ASSIGNED in form MEGA_SORT", "RefUrl": "/notes/1057650"}, {"RefNumber": "1057429", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSDRC_MULTIPROV; form SOLVE_CMP-03-", "RefUrl": "/notes/1057429"}, {"RefNumber": "1057286", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in the input-ready query class", "RefUrl": "/notes/1057286"}, {"RefNumber": "1057192", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Dump bei P<PERSON><PERSON> von <PERSON>", "RefUrl": "/notes/1057192"}, {"RefNumber": "1056775", "RefComponent": "BW-WHM-DST", "RefTitle": "Process type for loading several hierarchies", "RefUrl": "/notes/1056775"}, {"RefNumber": "1056323", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Cache was used despite virtual characteristics/key figures", "RefUrl": "/notes/1056323"}, {"RefNumber": "1056294", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process can be created for non-cumul InfoCube", "RefUrl": "/notes/1056294"}, {"RefNumber": "1056293", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems when archiving with constant characteristics", "RefUrl": "/notes/1056293"}, {"RefNumber": "1056060", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Reactivation in the production system", "RefUrl": "/notes/1056060"}, {"RefNumber": "1056011", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you change the view of the Content transformation", "RefUrl": "/notes/1056011"}, {"RefNumber": "1055989", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SAPSQL_INVALID_FIELDNAME when you load data from PSA", "RefUrl": "/notes/1055989"}, {"RefNumber": "1055827", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC: Restarting loading processes in background and dump", "RefUrl": "/notes/1055827"}, {"RefNumber": "1055823", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Entwicklung: Erweiterung in Transformation", "RefUrl": "/notes/1055823"}, {"RefNumber": "1055691", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Application log is overwritten/missing", "RefUrl": "/notes/1055691"}, {"RefNumber": "1055583", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination in RSEC_GET_AUTH_FOR_USER", "RefUrl": "/notes/1055583"}, {"RefNumber": "1055493", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Not enough values with hierarchy and master data access mode", "RefUrl": "/notes/1055493"}, {"RefNumber": "1055483", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Renaming system in RDA tables (BDLS)", "RefUrl": "/notes/1055483"}, {"RefNumber": "1055449", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text of variable in variable screen not displayed", "RefUrl": "/notes/1055449"}, {"RefNumber": "1055437", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X299 Brain in CL_RSR program; GET_CHANM-03- form", "RefUrl": "/notes/1055437"}, {"RefNumber": "1055417", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Hierarchy not extracted for single request", "RefUrl": "/notes/1055417"}, {"RefNumber": "1055350", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Node positioning changes after data entered in nodes", "RefUrl": "/notes/1055350"}, {"RefNumber": "1055342", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Corr.: Incorrect error handling default for DTP in hierarchy", "RefUrl": "/notes/1055342"}, {"RefNumber": "1055322", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Cannot diagnose near-line connection", "RefUrl": "/notes/1055322"}, {"RefNumber": "1055274", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rules not deleted correctly", "RefUrl": "/notes/1055274"}, {"RefNumber": "1055253", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X_299 BRAIN in SAPLRRi2, form CSC_SETZEN-01-", "RefUrl": "/notes/1055253"}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182"}, {"RefNumber": "1055044", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Performance in SAPLRRK0, form s_data_fuellen", "RefUrl": "/notes/1055044"}, {"RefNumber": "1055033", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "\"??????????\" cannot be interpreted as a number", "RefUrl": "/notes/1055033"}, {"RefNumber": "1054963", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination for artificial crcy w/more than 5 decimal places", "RefUrl": "/notes/1054963"}, {"RefNumber": "1054812", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unclear errors and dumps for currency and unit rules", "RefUrl": "/notes/1054812"}, {"RefNumber": "1054612", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "DataSource: Deleting source system without dependent objects", "RefUrl": "/notes/1054612"}, {"RefNumber": "1054588", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299: termination in MEGA_SORT_14-01- with invalid filters", "RefUrl": "/notes/1054588"}, {"RefNumber": "1054587", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display hierarchy is changed when you update", "RefUrl": "/notes/1054587"}, {"RefNumber": "1054224", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Version and status information in the near-line interface", "RefUrl": "/notes/1054224"}, {"RefNumber": "1054110", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Exception: CX_SY_REF_IS_INITIAL", "RefUrl": "/notes/1054110"}, {"RefNumber": "1054104", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "SAP NetWeaver BI Accelerator for Support Package Stack 3", "RefUrl": "/notes/1054104"}, {"RefNumber": "1054065", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving run for write-optimized DataStore terminates", "RefUrl": "/notes/1054065"}, {"RefNumber": "1053844", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH: Message \"Source system & does not exist\"", "RefUrl": "/notes/1053844"}, {"RefNumber": "1053779", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: BI 7.1 SPS03", "RefUrl": "/notes/1053779"}, {"RefNumber": "1053735", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Sorting function for SAP BI queries enhanced", "RefUrl": "/notes/1053735"}, {"RefNumber": "1053510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: DWWB takes a long time to set up", "RefUrl": "/notes/1053510"}, {"RefNumber": "1053504", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Descriptions of DataSource fields are missing", "RefUrl": "/notes/1053504"}, {"RefNumber": "1053229", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Recovering does not generate 3.x DataSource", "RefUrl": "/notes/1053229"}, {"RefNumber": "1052703", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After import processing of R3TR SHDS is slow", "RefUrl": "/notes/1052703"}, {"RefNumber": "1050125", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Endless runtime during authorization check", "RefUrl": "/notes/1050125"}, {"RefNumber": "1030988", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Consistency check: RH 207: Non-included nodes", "RefUrl": "/notes/1030988"}, {"RefNumber": "1026749", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: Consistency check for duplicate nodes", "RefUrl": "/notes/1026749"}, {"RefNumber": "1025501", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: You can save inconsistent hierarchies", "RefUrl": "/notes/1025501"}, {"RefNumber": "1023464", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies:Consistency checks for interval nodes", "RefUrl": "/notes/1023464"}, {"RefNumber": "1021972", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Function group RSNDI_SHIE: Updating interval nodes", "RefUrl": "/notes/1021972"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1053735", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "Sorting function for SAP BI queries enhanced", "RefUrl": "/notes/1053735 "}, {"RefNumber": "1056060", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Reactivation in the production system", "RefUrl": "/notes/1056060 "}, {"RefNumber": "1055483", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Renaming system in RDA tables (BDLS)", "RefUrl": "/notes/1055483 "}, {"RefNumber": "1063466", "RefComponent": "BW-WHM-DST", "RefTitle": "Transactional request is not set to qualok", "RefUrl": "/notes/1063466 "}, {"RefNumber": "1063991", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: obsolete D versions (also transformation, DTP)", "RefUrl": "/notes/1063991 "}, {"RefNumber": "1064273", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problems when you process the variable screen again", "RefUrl": "/notes/1064273 "}, {"RefNumber": "1061165", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Leaf selection: Unautorized data is displayed", "RefUrl": "/notes/1061165 "}, {"RefNumber": "1061119", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Field update when you load master data using end routine", "RefUrl": "/notes/1061119 "}, {"RefNumber": "1055583", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination in RSEC_GET_AUTH_FOR_USER", "RefUrl": "/notes/1055583 "}, {"RefNumber": "1062704", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC: InfoPackage input help does not display any values", "RefUrl": "/notes/1062704 "}, {"RefNumber": "1069675", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Further performance improvement", "RefUrl": "/notes/1069675 "}, {"RefNumber": "1065494", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "More precise error message for BRAIN X299 MEGA_SORT_05-02-", "RefUrl": "/notes/1065494 "}, {"RefNumber": "1069957", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Restarting delete phase with partially deleted data", "RefUrl": "/notes/1069957 "}, {"RefNumber": "1067550", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Optimizing SFK when selecting structure elements (KIDSEL)", "RefUrl": "/notes/1067550 "}, {"RefNumber": "1067027", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "Initial entries from master data and texts are not extracted", "RefUrl": "/notes/1067027 "}, {"RefNumber": "1063541", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error during time split with fiscal year variant", "RefUrl": "/notes/1063541 "}, {"RefNumber": "1055827", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC: Restarting loading processes in background and dump", "RefUrl": "/notes/1055827 "}, {"RefNumber": "1068254", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise search: Search for workbooks and query views", "RefUrl": "/notes/1068254 "}, {"RefNumber": "1055182", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: Reclustering and Compression", "RefUrl": "/notes/1055182 "}, {"RefNumber": "1064328", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Resetting interrupts", "RefUrl": "/notes/1064328 "}, {"RefNumber": "1066244", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Version handling in the transformation", "RefUrl": "/notes/1066244 "}, {"RefNumber": "1072923", "RefComponent": "BW-BEX-ET-KPI", "RefTitle": "KPI/DAS: Output of results - no decimal places", "RefUrl": "/notes/1072923 "}, {"RefNumber": "1065360", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 15 BIA revision compatibility check enhanced", "RefUrl": "/notes/1065360 "}, {"RefNumber": "1062039", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Two DataSources have the same structure", "RefUrl": "/notes/1062039 "}, {"RefNumber": "1058274", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RSTT: Displaying recorded reference data terminates", "RefUrl": "/notes/1058274 "}, {"RefNumber": "1059175", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: DSO: Write optimized DSO and reconstruction dump", "RefUrl": "/notes/1059175 "}, {"RefNumber": "1074104", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Error in ABAP Dictionary when you activate a DTP", "RefUrl": "/notes/1074104 "}, {"RefNumber": "1061796", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Lock NOT set for: Condensing the Data in an InfoProvider", "RefUrl": "/notes/1061796 "}, {"RefNumber": "1072425", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Runtime error TEXTENV_CODEPAGE_NOT_ALLOWED", "RefUrl": "/notes/1072425 "}, {"RefNumber": "1072115", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Unexpected aggregations in formulas with replacement path", "RefUrl": "/notes/1072115 "}, {"RefNumber": "1065435", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages when collecting content (A version missing)", "RefUrl": "/notes/1065435 "}, {"RefNumber": "1054110", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Exception: CX_SY_REF_IS_INITIAL", "RefUrl": "/notes/1054110 "}, {"RefNumber": "1064879", "RefComponent": "BW-BEX-OT", "RefTitle": "RSTT wizard: Selection of data objects is initialized", "RefUrl": "/notes/1064879 "}, {"RefNumber": "1067377", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 299 in SAPLRRI2; form CELL_FUELLEN_FEMZ-02-", "RefUrl": "/notes/1067377 "}, {"RefNumber": "1067225", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI 7.0 (SP15) DataStore default settings are not applied", "RefUrl": "/notes/1067225 "}, {"RefNumber": "1064322", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "IP: BRAIN X299 _GET_R_MOVE_TYPE-06-/ACTIVATE_B_BUFFER-01-", "RefUrl": "/notes/1064322 "}, {"RefNumber": "1071347", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Unauthorized data displayed for chargeable nodes/leaves", "RefUrl": "/notes/1071347 "}, {"RefNumber": "1058181", "RefComponent": "BW-BEX-OT", "RefTitle": "Optimized performance during determination of master data ID", "RefUrl": "/notes/1058181 "}, {"RefNumber": "1058857", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "No data displayed in a very special situation", "RefUrl": "/notes/1058857 "}, {"RefNumber": "1062928", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "HybridProvider without database persistence", "RefUrl": "/notes/1062928 "}, {"RefNumber": "1072982", "RefComponent": "BW-BEX-OT", "RefTitle": "Metadata buffer for MultiProvider is not deleted", "RefUrl": "/notes/1072982 "}, {"RefNumber": "1063768", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Performance of plan queries with high no. of key figures", "RefUrl": "/notes/1063768 "}, {"RefNumber": "1071547", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "No PSA attributes for error stack", "RefUrl": "/notes/1071547 "}, {"RefNumber": "1055342", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Corr.: Incorrect error handling default for DTP in hierarchy", "RefUrl": "/notes/1055342 "}, {"RefNumber": "1063184", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect DTP/InfoP. if pseudo D does not exist", "RefUrl": "/notes/1063184 "}, {"RefNumber": "1070085", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA Monitor e-mail notification using RSDDTREXADMIN", "RefUrl": "/notes/1070085 "}, {"RefNumber": "1068478", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Message: Rule is invalid and is being deleted", "RefUrl": "/notes/1068478 "}, {"RefNumber": "1072555", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "RSAODS 006: Invalid parameters for L_R_STRUCTDESCR_SID", "RefUrl": "/notes/1072555 "}, {"RefNumber": "1072240", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Incorrect data in aggregate (time-dependent hierarchy)", "RefUrl": "/notes/1072240 "}, {"RefNumber": "1065534", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Dump OBJECTS_OBJREF_NOT_ASSIGNED when you display log", "RefUrl": "/notes/1065534 "}, {"RefNumber": "1068162", "RefComponent": "BW-WHM-DST", "RefTitle": "To be defined", "RefUrl": "/notes/1068162 "}, {"RefNumber": "1069800", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Error when transporting open hub destinations", "RefUrl": "/notes/1069800 "}, {"RefNumber": "1065358", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process not completely activated", "RefUrl": "/notes/1065358 "}, {"RefNumber": "1066979", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:PC:BATCH:Parallelism settings and defaults", "RefUrl": "/notes/1066979 "}, {"RefNumber": "1072970", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems durnig data archiving using a process chain", "RefUrl": "/notes/1072970 "}, {"RefNumber": "1056294", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data archiving process can be created for non-cumul InfoCube", "RefUrl": "/notes/1056294 "}, {"RefNumber": "1067984", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 Brain in CL_RSDRC_MULTIPROV; form GET_PART_IOBJNM-01-", "RefUrl": "/notes/1067984 "}, {"RefNumber": "1072911", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon waits too long", "RefUrl": "/notes/1072911 "}, {"RefNumber": "1053779", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: BI 7.1 SPS03", "RefUrl": "/notes/1053779 "}, {"RefNumber": "1055989", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "SAPSQL_INVALID_FIELDNAME when you load data from PSA", "RefUrl": "/notes/1055989 "}, {"RefNumber": "1079310", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 15", "RefUrl": "/notes/1079310 "}, {"RefNumber": "1064081", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Error message missing for RDA InfoPackage request", "RefUrl": "/notes/1064081 "}, {"RefNumber": "1059565", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA request status yello, number of records inconsistent", "RefUrl": "/notes/1059565 "}, {"RefNumber": "1070732", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DTP deactivated during transformation activation", "RefUrl": "/notes/1070732 "}, {"RefNumber": "1061859", "RefComponent": "BW-PLA-IP", "RefTitle": "Selection condition w/ several hierarchy nodes is incorrect", "RefUrl": "/notes/1061859 "}, {"RefNumber": "1062577", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems with time characteristic rule / constant rule", "RefUrl": "/notes/1062577 "}, {"RefNumber": "1069325", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems occur when you create a constant rule", "RefUrl": "/notes/1069325 "}, {"RefNumber": "1054104", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "SAP NetWeaver BI Accelerator for Support Package Stack 3", "RefUrl": "/notes/1054104 "}, {"RefNumber": "1052703", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After import processing of R3TR SHDS is slow", "RefUrl": "/notes/1052703 "}, {"RefNumber": "1063281", "RefComponent": "BW-BEX-OT", "RefTitle": "The method GET_DATA_TIMESTMP had to be adjusted", "RefUrl": "/notes/1063281 "}, {"RefNumber": "1060406", "RefComponent": "BW-BEX-ET-WB", "RefTitle": "BRAIN 605: \"The requested query... does not exist...\"", "RefUrl": "/notes/1060406 "}, {"RefNumber": "1073912", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Lower-level nodes are not written back to the cache", "RefUrl": "/notes/1073912 "}, {"RefNumber": "1065307", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "No currency conversion during calculation before aggregation", "RefUrl": "/notes/1065307 "}, {"RefNumber": "1059049", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Status temporarily remains red after a restart", "RefUrl": "/notes/1059049 "}, {"RefNumber": "1071640", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not execute inactive data transfer process", "RefUrl": "/notes/1071640 "}, {"RefNumber": "1071255", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Short dump during migration after transformation", "RefUrl": "/notes/1071255 "}, {"RefNumber": "1067750", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: \"l_chavl already declared\" when activating DS", "RefUrl": "/notes/1067750 "}, {"RefNumber": "1074356", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "EXCEPTION cx_rs_input_invalid in SET_RETURNFLS", "RefUrl": "/notes/1074356 "}, {"RefNumber": "1060387", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Analysis with incorrect results in BIA queries", "RefUrl": "/notes/1060387 "}, {"RefNumber": "1066071", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Refresh function for queries", "RefUrl": "/notes/1066071 "}, {"RefNumber": "1071986", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Deleting entries that are no longer needed from the K table", "RefUrl": "/notes/1071986 "}, {"RefNumber": "1070542", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Aggregation behavior is incorrect", "RefUrl": "/notes/1070542 "}, {"RefNumber": "1054812", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unclear errors and dumps for currency and unit rules", "RefUrl": "/notes/1054812 "}, {"RefNumber": "1060094", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Errors with fixed source unit; problems with conversion", "RefUrl": "/notes/1060094 "}, {"RefNumber": "1062166", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Settings that are not allowed in transformations not caught", "RefUrl": "/notes/1062166 "}, {"RefNumber": "1062574", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Time dependency when reading master data", "RefUrl": "/notes/1062574 "}, {"RefNumber": "1072764", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Remote master data read with incorrect key date", "RefUrl": "/notes/1072764 "}, {"RefNumber": "1071035", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA SP test: Information about current SP is ambiguous", "RefUrl": "/notes/1071035 "}, {"RefNumber": "1072483", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Incorrect error message when you activate a hierarchy", "RefUrl": "/notes/1072483 "}, {"RefNumber": "1071661", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Filtering new records that have the same key does not work", "RefUrl": "/notes/1071661 "}, {"RefNumber": "1071790", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Status of transformation incorrect", "RefUrl": "/notes/1071790 "}, {"RefNumber": "1072333", "RefComponent": "BW-BEX-OT", "RefTitle": "Introduction of problem class for messages", "RefUrl": "/notes/1072333 "}, {"RefNumber": "1063930", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Dump FILTER_SFC_TIMOBJS-1- for MultiProv w/ non-cum. key fig", "RefUrl": "/notes/1063930 "}, {"RefNumber": "1070447", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Ready for input status for elements with quantity conversion", "RefUrl": "/notes/1070447 "}, {"RefNumber": "1073859", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Termination in CL_RSR_HIERARCHY_DIM and SELECT_SIDS_3", "RefUrl": "/notes/1073859 "}, {"RefNumber": "1072549", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Short dump when you copy a DataSource", "RefUrl": "/notes/1072549 "}, {"RefNumber": "1072548", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Maintenance of data type, size category of PSA", "RefUrl": "/notes/1072548 "}, {"RefNumber": "1059176", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: Various dumps in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1059176 "}, {"RefNumber": "1072790", "RefComponent": "BW-BEX-OT", "RefTitle": "BRAIN 299 in class CL_RSR; form GET_SID_FROM_CHAVL-01-", "RefUrl": "/notes/1072790 "}, {"RefNumber": "1072145", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Incorrect texts in RSDS_ACCESS", "RefUrl": "/notes/1072145 "}, {"RefNumber": "1063483", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Content activation in A1S mode", "RefUrl": "/notes/1063483 "}, {"RefNumber": "1065051", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No existence check for archived data using DME class", "RefUrl": "/notes/1065051 "}, {"RefNumber": "1068332", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "No restriction on 0INFOPROV (F4 mode M instead of D)", "RefUrl": "/notes/1068332 "}, {"RefNumber": "1072332", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Link node selection visible multiple times in variable dialo", "RefUrl": "/notes/1072332 "}, {"RefNumber": "1068827", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: RSPC 57 \"Process did not report an instance\"", "RefUrl": "/notes/1068827 "}, {"RefNumber": "1069401", "RefComponent": "BW-BEX-ET-KPI", "RefTitle": "Problems when you activate KPI Content objects", "RefUrl": "/notes/1069401 "}, {"RefNumber": "1066720", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "No execution authorization for queries with COMPID '!!*'", "RefUrl": "/notes/1066720 "}, {"RefNumber": "1065072", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Message BRAIN 087 during query generation", "RefUrl": "/notes/1065072 "}, {"RefNumber": "1069152", "RefComponent": "BW-BCT-CSC-ES", "RefTitle": "LOPD: Corrections for legal requirements", "RefUrl": "/notes/1069152 "}, {"RefNumber": "1070378", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Hard-coded source system in DataSource program", "RefUrl": "/notes/1070378 "}, {"RefNumber": "1060405", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Open Hub Destination maintenance: Unit fields incorrect", "RefUrl": "/notes/1060405 "}, {"RefNumber": "1063539", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termination: _SEL_TO_SELDR_14-02- in CL_RSR_RRK0_ATTR_C", "RefUrl": "/notes/1063539 "}, {"RefNumber": "1067042", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archived data areas are not protected in InfoCube", "RefUrl": "/notes/1067042 "}, {"RefNumber": "1066575", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A125  GVAR has no component called \"LKnnnn\"", "RefUrl": "/notes/1066575 "}, {"RefNumber": "1068054", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "You cannot delete hierarchy node authorization", "RefUrl": "/notes/1068054 "}, {"RefNumber": "1059289", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termintn IF_RSMD_RS_ACCESS~GET_VALUES in CL_RRHI_MDACCESS_TD", "RefUrl": "/notes/1059289 "}, {"RefNumber": "1068740", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "P15:P21:Timeout time ignored during the load through PC", "RefUrl": "/notes/1068740 "}, {"RefNumber": "1068721", "RefComponent": "BW-WHM-DST-TRS", "RefTitle": "Syntax error occurs in the generated transfer program", "RefUrl": "/notes/1068721 "}, {"RefNumber": "1066426", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving: Error messages from data manager missing from log", "RefUrl": "/notes/1066426 "}, {"RefNumber": "1066471", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Data is lost when you execute reload requests in parallel", "RefUrl": "/notes/1066471 "}, {"RefNumber": "1063767", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Various minor MDX errors", "RefUrl": "/notes/1063767 "}, {"RefNumber": "1059528", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: Necessary reorg. always not yet possible", "RefUrl": "/notes/1059528 "}, {"RefNumber": "1069175", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "F4 for 0weekday1: 0 instead of # and 'unassigned'", "RefUrl": "/notes/1069175 "}, {"RefNumber": "1066026", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Reload req after termintn in del process cannot be resumed", "RefUrl": "/notes/1066026 "}, {"RefNumber": "1068187", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "Termination GETWA_NOT_ASSIGNED with non-cumulative query", "RefUrl": "/notes/1068187 "}, {"RefNumber": "1067605", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: PSA: Rebuilding: <PERSON><PERSON><PERSON> set too late or not at all", "RefUrl": "/notes/1067605 "}, {"RefNumber": "1067724", "RefComponent": "BW-WHM-DST", "RefTitle": "P15:DTP:Manage: Empty date/time field in request list", "RefUrl": "/notes/1067724 "}, {"RefNumber": "1055823", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Entwicklung: Erweiterung in Transformation", "RefUrl": "/notes/1055823 "}, {"RefNumber": "1068122", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: GETWA_NOT_ASSIGNED_RANGE in program SAPLRRK0", "RefUrl": "/notes/1068122 "}, {"RefNumber": "1065308", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "\"No authorization\" when you use temporal hierarchy join", "RefUrl": "/notes/1065308 "}, {"RefNumber": "1058433", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Planning in foreign currency and empty cells", "RefUrl": "/notes/1058433 "}, {"RefNumber": "1067943", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: TRIGGER: Invalid call sequence for interfaces", "RefUrl": "/notes/1067943 "}, {"RefNumber": "1067546", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "\"No authorization\" for document check", "RefUrl": "/notes/1067546 "}, {"RefNumber": "1061378", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN 087 when generating query: different hierarchies used", "RefUrl": "/notes/1061378 "}, {"RefNumber": "1067612", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: TIMEINTERVAL_GET in program SAPLRRHI", "RefUrl": "/notes/1067612 "}, {"RefNumber": "1067594", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSR_RRK0_Hierarchy;  APPLY_SLICER_NAV-02-", "RefUrl": "/notes/1067594 "}, {"RefNumber": "1063465", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Input help (F4) in InfoSource maintenance", "RefUrl": "/notes/1063465 "}, {"RefNumber": "1067519", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Proposal processes despite expert mode for DTP", "RefUrl": "/notes/1067519 "}, {"RefNumber": "1061832", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Chains are not activated in the background", "RefUrl": "/notes/1061832 "}, {"RefNumber": "1063022", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Search appliance: No dialog box when deleting source system", "RefUrl": "/notes/1063022 "}, {"RefNumber": "1056775", "RefComponent": "BW-WHM-DST", "RefTitle": "Process type for loading several hierarchies", "RefUrl": "/notes/1056775 "}, {"RefNumber": "1059287", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Incorrect error handling when checking archive locks", "RefUrl": "/notes/1059287 "}, {"RefNumber": "1067067", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Deletion of PC terminates if there is active run", "RefUrl": "/notes/1067067 "}, {"RefNumber": "1065052", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Error RSDA 213 when resuming an archiving request", "RefUrl": "/notes/1065052 "}, {"RefNumber": "1054224", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Version and status information in the near-line interface", "RefUrl": "/notes/1054224 "}, {"RefNumber": "1064798", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "You cannot delete activated requests after archiving", "RefUrl": "/notes/1064798 "}, {"RefNumber": "1065352", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "GETWA_NOT_ASSIGNED in prog CL_RSDA_DAP_DYNPRO_CONTROLLER=CP", "RefUrl": "/notes/1065352 "}, {"RefNumber": "1065088", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Recording BIA Monitor details for BIA Performance Trace", "RefUrl": "/notes/1065088 "}, {"RefNumber": "1066395", "RefComponent": "BW-WHM-DST", "RefTitle": "Support Package 15: No server dialog box for certain users", "RefUrl": "/notes/1066395 "}, {"RefNumber": "1066180", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P15:SDL: Dump CALL_FUNCTION_REMOTE_ERROR in LRSSMU17", "RefUrl": "/notes/1066180 "}, {"RefNumber": "1059231", "RefComponent": "BW-WHM-DST", "RefTitle": "P15: Service modules for A1S, and so on", "RefUrl": "/notes/1059231 "}, {"RefNumber": "1066525", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Update rules: Tecnhnical fields displayed", "RefUrl": "/notes/1066525 "}, {"RefNumber": "1065081", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Process chain merged although it is incorrect", "RefUrl": "/notes/1065081 "}, {"RefNumber": "1064960", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: Deleting subtress incorrect for intervals", "RefUrl": "/notes/1064960 "}, {"RefNumber": "1066053", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Buffered data during transport", "RefUrl": "/notes/1066053 "}, {"RefNumber": "1059381", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "InfoProvider restriction in input help using MultiProviders", "RefUrl": "/notes/1059381 "}, {"RefNumber": "1060170", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Performance improvement during analysis authorizations", "RefUrl": "/notes/1060170 "}, {"RefNumber": "1055437", "RefComponent": "BW-BEX-OT-NC", "RefTitle": "X299 Brain in CL_RSR program; GET_CHANM-03- form", "RefUrl": "/notes/1055437 "}, {"RefNumber": "1065440", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when displaying Content version", "RefUrl": "/notes/1065440 "}, {"RefNumber": "1061115", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Field sequence of open hub destination", "RefUrl": "/notes/1061115 "}, {"RefNumber": "1055493", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Not enough values with hierarchy and master data access mode", "RefUrl": "/notes/1055493 "}, {"RefNumber": "1060735", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of the variables during input", "RefUrl": "/notes/1060735 "}, {"RefNumber": "1063926", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Objektstatus wird nicht zurückgegeben bzw. ist falsch", "RefUrl": "/notes/1063926 "}, {"RefNumber": "1063661", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "RRI: Branching from Web to Web does not work", "RefUrl": "/notes/1063661 "}, {"RefNumber": "1063024", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: NUMC field extension incompatible with database", "RefUrl": "/notes/1063024 "}, {"RefNumber": "1062379", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Quantities are displayed incorrectly in PSA", "RefUrl": "/notes/1062379 "}, {"RefNumber": "1061525", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: RRS_VREP_NODE_FROM_VAR_FILL-01- in SAPLRRS2", "RefUrl": "/notes/1061525 "}, {"RefNumber": "1055044", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Performance in SAPLRRK0, form s_data_fuellen", "RefUrl": "/notes/1055044 "}, {"RefNumber": "1062863", "RefComponent": "BW-BEX-ET-RA-BC", "RefTitle": "Precalculation fails when exclude sign is used", "RefUrl": "/notes/1062863 "}, {"RefNumber": "1062128", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "SQL0104N An unexpected token \")\" was found following \"D \"K\"", "RefUrl": "/notes/1062128 "}, {"RefNumber": "1061322", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error when you activate transformation for quantity DS", "RefUrl": "/notes/1061322 "}, {"RefNumber": "1055274", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rules not deleted correctly", "RefUrl": "/notes/1055274 "}, {"RefNumber": "1061668", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Empty error stack accessed for reading", "RefUrl": "/notes/1061668 "}, {"RefNumber": "1056011", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you change the view of the Content transformation", "RefUrl": "/notes/1056011 "}, {"RefNumber": "1054612", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "DataSource: Deleting source system without dependent objects", "RefUrl": "/notes/1054612 "}, {"RefNumber": "1057685", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Missing content time stamp (only replicating)", "RefUrl": "/notes/1057685 "}, {"RefNumber": "1060195", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Vague error messages after activation in content system", "RefUrl": "/notes/1060195 "}, {"RefNumber": "1059180", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Status unknown if errors occur in dialog processing", "RefUrl": "/notes/1059180 "}, {"RefNumber": "1062123", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "No data if query is filtered according to non-assigned nodes", "RefUrl": "/notes/1062123 "}, {"RefNumber": "1059837", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Obsolete formulas & routines in import postprocessing method", "RefUrl": "/notes/1059837 "}, {"RefNumber": "1058785", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "The InfoSource is not defined in the source system (R3 005)", "RefUrl": "/notes/1058785 "}, {"RefNumber": "1062064", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Missing information in API RSB_API_OHS_SPOKE_GETLIST", "RefUrl": "/notes/1062064 "}, {"RefNumber": "1061324", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in Program SAPLRRI2, form CUDIM_FUELLEN_GEN-03-", "RefUrl": "/notes/1061324 "}, {"RefNumber": "1059833", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Currency conversion in the transformation", "RefUrl": "/notes/1059833 "}, {"RefNumber": "1060197", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination \"VIEW_BUILD-2-\" in change run/monitor", "RefUrl": "/notes/1060197 "}, {"RefNumber": "1059882", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Import of Web service DataSource terminates", "RefUrl": "/notes/1059882 "}, {"RefNumber": "1059529", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Problem with formula variable of type 'Date' or 'Time'", "RefUrl": "/notes/1059529 "}, {"RefNumber": "1060093", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when working with content - collect/activate or create", "RefUrl": "/notes/1060093 "}, {"RefNumber": "1059479", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: dump during synchronous call", "RefUrl": "/notes/1059479 "}, {"RefNumber": "1059183", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "Termination \"SELAGGR_SIMPLE_GET\" when you fill an aggregate", "RefUrl": "/notes/1059183 "}, {"RefNumber": "1059248", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Search variant in variable screen in BW 3.x BEx Analyzer", "RefUrl": "/notes/1059248 "}, {"RefNumber": "1059174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Dump ENTRY_EXCP_CLEANUP_LEFT", "RefUrl": "/notes/1059174 "}, {"RefNumber": "1059076", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "SAP exit variables and name spaces", "RefUrl": "/notes/1059076 "}, {"RefNumber": "1056323", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Cache was used despite virtual characteristics/key figures", "RefUrl": "/notes/1056323 "}, {"RefNumber": "1058679", "RefComponent": "BW-PLA-IP", "RefTitle": "Several key selections in the select statement", "RefUrl": "/notes/1058679 "}, {"RefNumber": "1058680", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 BRAIN in CL_RSDRC_MULTIPROV; form  _COLLECT_SFC_RETFL-0", "RefUrl": "/notes/1058680 "}, {"RefNumber": "1058237", "RefComponent": "BW-WHM-DST-INP", "RefTitle": "Enhanced syntax check: Error correction", "RefUrl": "/notes/1058237 "}, {"RefNumber": "1055417", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Hierarchy not extracted for single request", "RefUrl": "/notes/1055417 "}, {"RefNumber": "1057286", "RefComponent": "BW-PLA-IP", "RefTitle": "X299 Brain in the input-ready query class", "RefUrl": "/notes/1057286 "}, {"RefNumber": "1057429", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in CL_RSDRC_MULTIPROV; form SOLVE_CMP-03-", "RefUrl": "/notes/1057429 "}, {"RefNumber": "1057650", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Runtime error GETWA_NOT_ASSIGNED in form MEGA_SORT", "RefUrl": "/notes/1057650 "}, {"RefNumber": "1057192", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Dump bei P<PERSON><PERSON> von <PERSON>", "RefUrl": "/notes/1057192 "}, {"RefNumber": "1056293", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems when archiving with constant characteristics", "RefUrl": "/notes/1056293 "}, {"RefNumber": "1055322", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Cannot diagnose near-line connection", "RefUrl": "/notes/1055322 "}, {"RefNumber": "1054065", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving run for write-optimized DataStore terminates", "RefUrl": "/notes/1054065 "}, {"RefNumber": "1055691", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Application log is overwritten/missing", "RefUrl": "/notes/1055691 "}, {"RefNumber": "1055350", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Node positioning changes after data entered in nodes", "RefUrl": "/notes/1055350 "}, {"RefNumber": "1055449", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text of variable in variable screen not displayed", "RefUrl": "/notes/1055449 "}, {"RefNumber": "1055253", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X_299 BRAIN in SAPLRRi2, form CSC_SETZEN-01-", "RefUrl": "/notes/1055253 "}, {"RefNumber": "1054587", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Display hierarchy is changed when you update", "RefUrl": "/notes/1054587 "}, {"RefNumber": "1055033", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "\"??????????\" cannot be interpreted as a number", "RefUrl": "/notes/1055033 "}, {"RefNumber": "1054963", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Termination for artificial crcy w/more than 5 decimal places", "RefUrl": "/notes/1054963 "}, {"RefNumber": "1030988", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Consistency check: RH 207: Non-included nodes", "RefUrl": "/notes/1030988 "}, {"RefNumber": "1026749", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: Consistency check for duplicate nodes", "RefUrl": "/notes/1026749 "}, {"RefNumber": "1025501", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies: You can save inconsistent hierarchies", "RefUrl": "/notes/1025501 "}, {"RefNumber": "1023464", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchies:Consistency checks for interval nodes", "RefUrl": "/notes/1023464 "}, {"RefNumber": "1021972", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Function group RSNDI_SHIE: Updating interval nodes", "RefUrl": "/notes/1021972 "}, {"RefNumber": "1054588", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299: termination in MEGA_SORT_14-01- with invalid filters", "RefUrl": "/notes/1054588 "}, {"RefNumber": "1053229", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Recovering does not generate 3.x DataSource", "RefUrl": "/notes/1053229 "}, {"RefNumber": "1053504", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Descriptions of DataSource fields are missing", "RefUrl": "/notes/1053504 "}, {"RefNumber": "1053510", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: DWWB takes a long time to set up", "RefUrl": "/notes/1053510 "}, {"RefNumber": "1050125", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Endless runtime during authorization check", "RefUrl": "/notes/1050125 "}, {"RefNumber": "1053844", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH: Message \"Source system & does not exist\"", "RefUrl": "/notes/1053844 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}