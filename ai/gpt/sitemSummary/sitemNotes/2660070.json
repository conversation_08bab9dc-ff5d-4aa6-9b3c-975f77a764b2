{"Request": {"Number": "2660070", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 364, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001233042018"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=107B53DEE74CD7D2331824EF999A4878"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2660070"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.12.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-GL-GL-G"}, "SAPComponentKeyText": {"_label": "Component", "value": "Closing Operations / Period-End"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Ledger Accounting", "value": "FI-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-GL-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Closing Operations / Period-End", "value": "FI-GL-GL-G", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-GL-GL-G*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2660070 - Closing processes in RE-FX leasing"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You require an overview of the valuation processes for leasing liabilities and leasing receivables from RE-FX.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Leasing IFRS 16 closing processes IAS21 IAS1</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>The leasing solution in RE-FX posts items in the general ledger (leasing liabilities or leasing receivable) that are subjected to certain activities in the period-end closing.</p>\r\n<ul>\r\n<li>Foreign currency valuation<br />\r\n<div class=\"O1\">In the case of contracts in foreign currency, leasing liabilities/leasing receivables must be valuated accordingly on the closing key date.&#x00A0;</div>\r\n</li>\r\n<li>\r\n<p>Reclassification (current/non-current distinction)<br />IAS 1 (marginal 60 ff.) requires a differentiation by short-term portion (&lt; 12 months) and long-term portion.</p>\r\n</li>\r\n</ul>\r\n<p>These processes must be interlinked: The reclassification must take into account, for example, the effects of the foreign currency valuation.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This SAP Note provides a nonbinding outlook on the intended solution design and information about the project planning.</p>\r\n<ol>\r\n<li>SAP Note <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2657208\">&#xFEFF;2657208&#xFEFF;</a></span> adds new grouping fields for valuation objects to the FI document structures.<br />RE-FX is one of the applications that use these fields:<br /><br />&#x00A0; VALOBJTYPE (field content for RE-FX leasing: Fixed values 'RECL' for leasing liabilities and 'RECR' for leasing receivables).<br />&#x00A0; VALOBJ_ID (field content for RE-FX leasing: Technical contract number)<br />&#x00A0; VALSOBJ_ID (field content for RE-FX leasing: Number of valuation rule)<br /><br />The ACR* fields that were intended for this purpose are no longer used in this context. <br />The previous implementation of SAP Note 2563329 is still required to ensure the technical consistency of the structures.<br /><br /></li>\r\n<li>The RE-FX posting interface is enhanced to fill the new fields (SAP Notes <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2584579\">&#xFEFF;2584579&#xFEFF; </a></span> and <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2657208\">&#xFEFF;2661164&#xFEFF;</a></span>).<br />Remember to carry out the generation (report SAPFACCG).</li>\r\n<li>\r\n<div class=\"longtext\">\r\n<p>If you have already used RECEEP to post documents in a productive environment, read the \"Initial implementation of the new function\" section in SAP Note <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2733390\">&#xFEFF;2733390&#xFEFF;</a></span>.</p>\r\n</div>\r\n</li>\r\n<li>The valuation should be carried out for each valuation object. For this reason, a balance for each valuation object (VALOBJTYPE/VALOBJ_ID/VALSOBJ_ID) must be determined at the period end for the accounts for leasing liabilities and receivables for each currency.<br /><br />For performance reasons, an aggregation layer is required for this in SAP ERP in order to receive values per VALOBJTYPE/VALOBJ_ID/VALSOBJ_ID. <br />To enable this, a Special Purpose Ledger (FI-SL) must be configured in SAP ERP. SAP Note <span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2624449\">&#xFEFF;2624449&#xFEFF;</a></span> describes the relevant configuration.<br />Use <strong>Version 2.2</strong> of the configuration instructions. <br /><br /><br />After the implementation of steps 1 to 4, we recommend a <em><strong>regression test</strong></em> due to the regeneration of the posting logic that happens as part of the FI-SL activation.<br /><br /></li>\r\n<li>The new transaction OBJVAL for object-based foreign currency valuation is delivered with SAP Note 2727558.<br />&#x00A0;<br />This transaction reads the currency balances for each valuation object (VALOBJTYPE/VALOBJ_ID/VALSOBJ_ID) from FI-SL and calculates and then posts the foreign currency valuation with this granularity. The valuation is saved in an object-related manner in the new table OBJECT_VAL.<br /><br />Account determination and posting logic: <br />The foreign currency valuation should be posted directly to the account for leasing liabilities and leasing receivables. This means that only the definition of a profit and loss account is required.<br />The postings take the values posted in the previous valuations into account and take place using the reversal logic (valuation at period end, reversal at start of subsequent period).&#x00A0;<br /><br /></li>\r\n<li>The new transaction OBJREG for automated reclassification is provided with SAP Note 2727558.<br /><br />For this, data is read from FI-SI for each valuation object (VALOBJTYPE/VALOBJ_ID/VALSOBJ_ID) and the valuations from the table OBJECT_VAL are added.<br />RE-FX is then called in the background in order to receive a reclassification ratio per time grid, since SAP FI does not have information as to how leasing liabilities and receivables are broken down into short-term and long-term parts (&lt;/&gt; 12 months).<br />This means that a callback to the application RE-FX is required.<br />It is also possible to configure time grids that are more detailed (such as &lt; 1 year, 1-5 years, &gt; 5 years), but this is not recommended.<br /><br />Posting logic: <br />The reclassification posting is carried out in each case on the last day of the period with simultaneous creation of a cancelation posting on the 1st day of the subsequent period (reversal logic).</li>\r\n</ol>\r\n<p><span style=\"font-size: 14px;\">A detailed <strong>guide for the configuration of OBJVAL and OBJREG</strong> and a <strong><strong>guide for the use </strong><strong>of OBJVAL and OBJREG</strong>&#x00A0;(both available only in English)</strong> are attached to this SAP Note.&#x00A0;<br />Update: May 20, 2019: Enhanced Version 1.31 of the guides available. As requested by customers, configuration and usage have been split into two documents as of Version 1.3.&#x00A0;<br />Update from January 13, 2020:&#x00A0;Enhanced Version 1.4 of the guides available (in particular delta valuation with SAP Note </span><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><a target=\"_blank\" href=\"/notes/2871569\">&#xFEFF;2871569</a></span>).<br />Update 2023/12/05: Minor revised version 1.42 of the guides.</p>\r\n<p><span style=\"font-size: 14px;\"> <br /></span></p>\r\n<p>If you have any questions or problems, refer to the detailed information in the guide and SAP Note 2733390.</p>\r\n<p><span style=\"font-size: 14px;\"><br />It can be assumed that the solution design in SAP S/4HANA will continue to follow the ERP approach.<br /></span></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "RE-FX-LA (Lease Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033090)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033090)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "Object_valuation__User_guide__V1-42.pdf", "FileSize": "1692", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001659072018&iv_version=0016&iv_guid=00109B36BC761EDEA4F7C2BC38E08C1F"}, {"FileName": "Object_valuation__Config_guide__V1-42.pdf", "FileSize": "843", "MimeType": "application/pdf", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800001659072018&iv_version=0016&iv_guid=00109B36D58A1EDEA4F7C4F539CFD504"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3392782", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Runtime Error GETWA_NOT_ASSIGNED when displaying saved Log Data", "RefUrl": "/notes/3392782"}, {"RefNumber": "3380523", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Performance Improvement in the display of the posting list", "RefUrl": "/notes/3380523"}, {"RefNumber": "3334882", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Wrong roung of amounts after note 3309671", "RefUrl": "/notes/3334882"}, {"RefNumber": "3309671", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Solving of Rounding Differences", "RefUrl": "/notes/3309671"}, {"RefNumber": "3224629", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG - Post amount in Transaction Currency", "RefUrl": "/notes/3224629"}, {"RefNumber": "3217698", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Posting with transaction types / Consideration of field status", "RefUrl": "/notes/3217698"}, {"RefNumber": "3191717", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Error message RW002", "RefUrl": "/notes/3191717"}, {"RefNumber": "3168572", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Memory overflow through balance check", "RefUrl": "/notes/3168572"}, {"RefNumber": "3159681", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Determination of Valuated LC Amount for Revived Contract in Delta Logic scenario", "RefUrl": "/notes/3159681"}, {"RefNumber": "3156797", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Select on T044L not specific enough", "RefUrl": "/notes/3156797"}, {"RefNumber": "3155421", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJRECON: Error TYPE_NOT_FOUND in function module VAL_OBJ_RECONCILE / Consideration of field DUMMY", "RefUrl": "/notes/3155421"}, {"RefNumber": "3132591", "RefComponent": "FI-GL-GL-G", "RefTitle": "Enhancement OBJRECON", "RefUrl": "/notes/3132591"}, {"RefNumber": "3132452", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: WBS Element and partner profit center in OBJREG postings", "RefUrl": "/notes/3132452"}, {"RefNumber": "3111433", "RefComponent": "FI-GL-GL-G", "RefTitle": "Transaction OBJRECON: adjustment document not posted for closed contracts", "RefUrl": "/notes/3111433"}, {"RefNumber": "3082794", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Posting overflow", "RefUrl": "/notes/3082794"}, {"RefNumber": "3044302", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJRECON / OBJVAL / OBJREG: Check assignment level of contract balances / Consideration of closed contracts in reconciliation", "RefUrl": "/notes/3044302"}, {"RefNumber": "3034316", "RefComponent": "FI-GL-GL-G", "RefTitle": "Post closing operations to valuation objects with initial profit center", "RefUrl": "/notes/3034316"}, {"RefNumber": "3033475", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL / OBJREG: Additional closing entries (status 'Z') by OBJVAL after reconciliation / Selection of zero balances in OBJREG and OBJVAL", "RefUrl": "/notes/3033475"}, {"RefNumber": "3020073", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL/OBJREG/OBJRECON: Contract number in assignment field ZUONR", "RefUrl": "/notes/3020073"}, {"RefNumber": "3013698", "RefComponent": "RE-FX-LA-IS", "RefTitle": "OBJREG: Runtime Error BCD_ZERODIVIDE", "RefUrl": "/notes/3013698"}, {"RefNumber": "3013257", "RefComponent": "RE-FX-LA-RA", "RefTitle": "Service Method: Field Assignment in OBJRECON", "RefUrl": "/notes/3013257"}, {"RefNumber": "3013226", "RefComponent": "FI-GL-GL-G", "RefTitle": "Prerequisites reconciliation report for Valuation Objects", "RefUrl": "/notes/3013226"}, {"RefNumber": "3008264", "RefComponent": "RE-FX-LA", "RefTitle": "SAP Contract and Lease Management (SAP RE-FX) - Change of Organizational Assignment", "RefUrl": "/notes/3008264"}, {"RefNumber": "3005664", "RefComponent": "FI-GL-GL-G", "RefTitle": "SAPFOBJ_REGROUP: Clearing of Posting Period on Selection Screen", "RefUrl": "/notes/3005664"}, {"RefNumber": "3005476", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Runtime error RAISE_EXCEPTION", "RefUrl": "/notes/3005476"}, {"RefNumber": "3003556", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Valuation difference for terminated contracts", "RefUrl": "/notes/3003556"}, {"RefNumber": "3003431", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Valuation differences amounts not considered as expected by reclassification", "RefUrl": "/notes/3003431"}, {"RefNumber": "2994971", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Missing parameters in log display.", "RefUrl": "/notes/2994971"}, {"RefNumber": "2988178", "RefComponent": "FI-GL-GL-X", "RefTitle": "OBJREG: GC_REPID is not defined - Error in FCC Task", "RefUrl": "/notes/2988178"}, {"RefNumber": "2987510", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RE-FX: OBJREG Incorrect regrouping if posting for following period exists", "RefUrl": "/notes/2987510"}, {"RefNumber": "2976536", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL/OBJREG - reset run does not post to special periods", "RefUrl": "/notes/2976536"}, {"RefNumber": "2975626", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG/OBJVAL: Selection of reconciliation postings created by OBJRECON", "RefUrl": "/notes/2975626"}, {"RefNumber": "2968714", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Transfer CO account assignment for balance sheet account", "RefUrl": "/notes/2968714"}, {"RefNumber": "2963704", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2963704"}, {"RefNumber": "2945677", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Terminated contract with multiple Closing Entries in table OBJVAL (Status 'Z')", "RefUrl": "/notes/2945677"}, {"RefNumber": "2945290", "RefComponent": "RE-FX-LA-RA", "RefTitle": "Service method RE for FI transaction OBJRECON", "RefUrl": "/notes/2945290"}, {"RefNumber": "2933404", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Terminated contract / Closing Amounts of add. Currency Types", "RefUrl": "/notes/2933404"}, {"RefNumber": "2927516", "RefComponent": "FI-GL-GL-G", "RefTitle": "Valuation Object Reconciliation", "RefUrl": "/notes/2927516"}, {"RefNumber": "2921076", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Pre-check for CO relevant assignments in valuation difference document", "RefUrl": "/notes/2921076"}, {"RefNumber": "2908018", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Balance of terminated contracts is selected again", "RefUrl": "/notes/2908018"}, {"RefNumber": "2905358", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Enhancement of Error Handling / Common Structures", "RefUrl": "/notes/2905358"}, {"RefNumber": "2900709", "RefComponent": "RE-FX-LA-IS", "RefTitle": "OBJREG-OBJREG report is showing wrong result due to error in Valuation Cash flow", "RefUrl": "/notes/2900709"}, {"RefNumber": "2896770", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG/OBJVAL: Valuation of special period postings.", "RefUrl": "/notes/2896770"}, {"RefNumber": "2896742", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: KTOSL not found when the text is not maintained", "RefUrl": "/notes/2896742"}, {"RefNumber": "2894294", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: RE-FX Liabilities / Performance Issues", "RefUrl": "/notes/2894294"}, {"RefNumber": "2894229", "RefComponent": "RE-FX-LA-IS", "RefTitle": "OBJREG: Program terminates due to overflow / Error CX_SY_ARITHMETIC_OVERFLOW", "RefUrl": "/notes/2894229"}, {"RefNumber": "2883743", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Target account of valuation posting requires tax code", "RefUrl": "/notes/2883743"}, {"RefNumber": "2882748", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Delta valuation difference is not taken into account", "RefUrl": "/notes/2882748"}, {"RefNumber": "2879981", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Valuation of Second/Third Local Currency - Posting Amount populated in wrong Amount Field", "RefUrl": "/notes/2879981"}, {"RefNumber": "2879180", "RefComponent": "RE-FX-LA-IS", "RefTitle": "Dump - Overflow during an arithmetic operation while executing OBJREG report", "RefUrl": "/notes/2879180"}, {"RefNumber": "2877760", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Remeasurement  Translation amount in Second/Third Local Currency missing", "RefUrl": "/notes/2877760"}, {"RefNumber": "2872194", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Note 2871569 - Prerequisite Objects in Data Dictionary", "RefUrl": "/notes/2872194"}, {"RefNumber": "2872151", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL – foreign currency valuation does not end/ABAP Dictionary changes for SAP Note 2839162", "RefUrl": "/notes/2872151"}, {"RefNumber": "2871569", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Delta logic in Foreign Currency Valuation (SAPFOBJ_VALUATION)", "RefUrl": "/notes/2871569"}, {"RefNumber": "2865876", "RefComponent": "RE-FX-LA-CN", "RefTitle": "RECN: Valuation rule with postings can be deleted", "RefUrl": "/notes/2865876"}, {"RefNumber": "2864044", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Wrong determination of posting key for LC2 and LC3", "RefUrl": "/notes/2864044"}, {"RefNumber": "2857123", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2857123"}, {"RefNumber": "2853848", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Updating a history", "RefUrl": "/notes/2853848"}, {"RefNumber": "2853771", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Selection of contract balances with reference type AWTYP = 'REACI'", "RefUrl": "/notes/2853771"}, {"RefNumber": "2839162", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Valuation posting for terminated contracts", "RefUrl": "/notes/2839162"}, {"RefNumber": "2835866", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Error FAGL_LEDGER_CUST023 when using classic GL", "RefUrl": "/notes/2835866"}, {"RefNumber": "2835838", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Error FAGL_LEDGER_CUST023 when using Classic GL", "RefUrl": "/notes/2835838"}, {"RefNumber": "2835308", "RefComponent": "FI-GL-GL-G", "RefTitle": "SAPF100: Check of OBJECT_VAL history", "RefUrl": "/notes/2835308"}, {"RefNumber": "2822466", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL / OBJREG: Batch Input error for currencies with other than 2 decimals", "RefUrl": "/notes/2822466"}, {"RefNumber": "2821411", "RefComponent": "RE-FX-LA-IS", "RefTitle": "Improvements in Overview Valuation Cashflow", "RefUrl": "/notes/2821411"}, {"RefNumber": "2819778", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Reversal document number not shown in log", "RefUrl": "/notes/2819778"}, {"RefNumber": "2815501", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Target account of account determination is not saved in batch input session", "RefUrl": "/notes/2815501"}, {"RefNumber": "2812310", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEPRV: Check of incorrect document type with VBUND determination", "RefUrl": "/notes/2812310"}, {"RefNumber": "2806520", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RE-FX: Enhancement of internal function", "RefUrl": "/notes/2806520"}, {"RefNumber": "2802424", "RefComponent": "RE-FX-LA-IS", "RefTitle": "OBJREG: COMPUTE_BCD_OVERFLOW program error.", "RefUrl": "/notes/2802424"}, {"RefNumber": "2799813", "RefComponent": "RE-FX-LA-IS", "RefTitle": "Negative Short Term (Reclassify) and Introduction of new computation method - Refined Repayment", "RefUrl": "/notes/2799813"}, {"RefNumber": "2799503", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RECEISRECLASSIFY : incorrect result for contract using non local currency for payment", "RefUrl": "/notes/2799503"}, {"RefNumber": "2799282", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL/OBJREG: Aggregation of partner account assignments", "RefUrl": "/notes/2799282"}, {"RefNumber": "2796876", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEP: Partner profit center set incorrectly/unnecessarily (RECE)", "RefUrl": "/notes/2796876"}, {"RefNumber": "2796842", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEP: Partner profit center and elimination profit center set incorrectly/unnecessarily (standard)", "RefUrl": "/notes/2796842"}, {"RefNumber": "2786235", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Wrong ledger group determination for valuation resets", "RefUrl": "/notes/2786235"}, {"RefNumber": "2781030", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Object-based register group with transaction type", "RefUrl": "/notes/2781030"}, {"RefNumber": "2775867", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Reversal using FB08 instead of BAPI", "RefUrl": "/notes/2775867"}, {"RefNumber": "2775862", "RefComponent": "FI-GL-GL-G", "RefTitle": "SAPFOBJ_REGROUP: Incompability with classic GL - Error FAGL_LEDGER_CUST023.", "RefUrl": "/notes/2775862"}, {"RefNumber": "2769614", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RE-FX: Leasing reclassification - excluding key date", "RefUrl": "/notes/2769614"}, {"RefNumber": "2764562", "RefComponent": "FI-GL-GL-G", "RefTitle": "FAGL_FC_VAL: Execution of  OBJVAL triggers internal submit of FAGL_FCV by program FAGL_FC_VALUATION", "RefUrl": "/notes/2764562"}, {"RefNumber": "2764422", "RefComponent": "FI-GL-GL-G", "RefTitle": "Object based Regrouping: Valuation is not considered", "RefUrl": "/notes/2764422"}, {"RefNumber": "2764373", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RE-FX: Leasing – RECEISRECLASSIFY and several posted valuation steps", "RefUrl": "/notes/2764373"}, {"RefNumber": "2763829", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2763829"}, {"RefNumber": "2763267", "RefComponent": "RE-FX-LA-IS", "RefTitle": "RE-FX: Leasing reclassification - opening stock for end of liability and grid without liabilities", "RefUrl": "/notes/2763267"}, {"RefNumber": "2752078", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Remeasurement Translation in Third Local Currency", "RefUrl": "/notes/2752078"}, {"RefNumber": "2750473", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Incorrect data taken from OBJVAL with split characteristics", "RefUrl": "/notes/2750473"}, {"RefNumber": "2750228", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL: Transaction type not passed", "RefUrl": "/notes/2750228"}, {"RefNumber": "2748678", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEPRV: VBUND and inverse posting", "RefUrl": "/notes/2748678"}, {"RefNumber": "2747174", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL/OBJREG: Consideration of assignments in classic G/L", "RefUrl": "/notes/2747174"}, {"RefNumber": "2745573", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Additional Currencies defined in the valuation area are not considered.", "RefUrl": "/notes/2745573"}, {"RefNumber": "2744451", "RefComponent": "RE-FX-LA-RA", "RefTitle": "RECEEPRV: VALOBJ* Fields are missing for an inverse posting", "RefUrl": "/notes/2744451"}, {"RefNumber": "2741218", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL / OBJREG: Runtime Error CALL_FUNCTION_PARM_UNKNOWN", "RefUrl": "/notes/2741218"}, {"RefNumber": "2739753", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: requires a account detmerination although no posting happend / conversion routine for selection is missing", "RefUrl": "/notes/2739753"}, {"RefNumber": "2737590", "RefComponent": "FI-GL-GL-G", "RefTitle": "SAPFOBJ_VALUATION: Call of Function module FAGL_FC_MSG_GET is obsolete / Reset run in classic G/L context", "RefUrl": "/notes/2737590"}, {"RefNumber": "2734263", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: too many documents posted - Reversal with With wrong account", "RefUrl": "/notes/2734263"}, {"RefNumber": "2733709", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJREG: Message F5 816 is raised", "RefUrl": "/notes/2733709"}, {"RefNumber": "2733390", "RefComponent": "RE-FX-LA-RA", "RefTitle": "OBJVAL and OBJREG – questions and answers", "RefUrl": "/notes/2733390"}, {"RefNumber": "2731603", "RefComponent": "FI-GL-GL-G", "RefTitle": "OBJVAL, OBJREG Exception SELECTION_FAILED is raised", "RefUrl": "/notes/2731603"}, {"RefNumber": "2730951", "RefComponent": "RE-FX-LA-CN", "RefTitle": "RECN/RECECN: Profit center (business area) check in the context of validation", "RefUrl": "/notes/2730951"}, {"RefNumber": "2729894", "RefComponent": "RE-FX-LA-CN", "RefTitle": "RECEEP: Check VBUND/PRCTR/GSBER in the contract and the valuation object", "RefUrl": "/notes/2729894"}, {"RefNumber": "2728831", "RefComponent": "FI-GL-GL-G", "RefTitle": "SAPFOBJ_VALUATION/SAPFOBJ_REGROUP: ALV Default Layout Variant", "RefUrl": "/notes/2728831"}, {"RefNumber": "2727653", "RefComponent": "FI-GL", "RefTitle": "Note 2727558: Prerequisites", "RefUrl": "/notes/2727653"}, {"RefNumber": "2727558", "RefComponent": "FI-GL-GL-G", "RefTitle": "Object-based valuation/sorted list for lease payables and receivables", "RefUrl": "/notes/2727558"}, {"RefNumber": "2707117", "RefComponent": "RE-FX-LA", "RefTitle": "RE-FX: Leasing - callback reclassification", "RefUrl": "/notes/2707117"}, {"RefNumber": "2706219", "RefComponent": "RE-FX-LA", "RefTitle": "Leasing: Correction report to transfer valuation context to FI documents (3)", "RefUrl": "/notes/2706219"}, {"RefNumber": "2694650", "RefComponent": "RE-FX-LA", "RefTitle": "RECEEP: Transfer valuation context FI postings (3) Receivables / Payables", "RefUrl": "/notes/2694650"}, {"RefNumber": "2661164", "RefComponent": "RE-FX-LA", "RefTitle": "RECEEP: Transfer valuation context to FI postings (2)", "RefUrl": "/notes/2661164"}, {"RefNumber": "2657208", "RefComponent": "AC-INT", "RefTitle": "new fields for new generic valuation functionality in FI (2)", "RefUrl": "/notes/2657208"}, {"RefNumber": "2624449", "RefComponent": "FI-GL-GL-G", "RefTitle": "Configuration of Special Purpose Ledger as preliminary work for closing processes from RE-FX leasing (IFRS 16)", "RefUrl": "/notes/2624449"}, {"RefNumber": "2584579", "RefComponent": "RE-FX-LA", "RefTitle": "RECEEP: Transfer valuation context to FI postings (1)", "RefUrl": "/notes/2584579"}, {"RefNumber": "2563329", "RefComponent": "AC-INT", "RefTitle": "new fields for new generic valuation functionality in FI (1)", "RefUrl": "/notes/2563329"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2692119", "RefComponent": "RE-FX-LA", "RefTitle": "SAP Contract and Lease Management (SAP RE-FX) - Consulting notes", "RefUrl": "/notes/2692119 "}, {"RefNumber": "3289350", "RefComponent": "FI-GL-GL-G", "RefTitle": "Restrictions for Classic Valuation Runs in SAP S/4 HANA", "RefUrl": "/notes/3289350 "}, {"RefNumber": "3008264", "RefComponent": "RE-FX-LA", "RefTitle": "SAP Contract and Lease Management (SAP RE-FX) - Change of Organizational Assignment", "RefUrl": "/notes/3008264 "}, {"RefNumber": "2963704", "RefComponent": "FI-GL-GL-G", "RefTitle": "RE-FX-Leasing: Relevante Schritte im FI-SL bei Wechsel von Ko<PERSON>n- zu Ledgerlösung", "RefUrl": "/notes/2963704 "}, {"RefNumber": "2662137", "RefComponent": "RE-FX-LA", "RefTitle": "SAP Contract and Lease Management (SAP RE-FX) - FAQ note", "RefUrl": "/notes/2662137 "}, {"RefNumber": "2733390", "RefComponent": "RE-FX-LA-RA", "RefTitle": "OBJVAL and OBJREG – questions and answers", "RefUrl": "/notes/2733390 "}, {"RefNumber": "2255555", "RefComponent": "RE-FX-LA", "RefTitle": "Valuation of leasing contracts (SAP Contract and Lease Management based on SAP RE-FX)", "RefUrl": "/notes/2255555 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}