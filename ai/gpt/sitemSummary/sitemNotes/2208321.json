{"Request": {"Number": "2208321", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 519, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018152122017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002208321?language=E&token=91F7B7A7533106CC1BC61E6742BE42A4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002208321", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002208321/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2208321"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 29}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basic Functions"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2208321 - FAQ for legacy data transfer in SAP_FIN 720 and subsequent releases"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This SAP Note provides answers to some of the most frequently asked questions in relation to legacy data transfers. It is constantly updated with the latest information.</p>\r\n<p>[1] How does the conversion of Asset Accounting to the \"standard journal entry\" affect legacy data transfers?</p>\r\n<p>[2] Why is the \"Takeover values\" button no longer available in transaction AS91?</p>\r\n<p>[3] Have adjustments also been made to the legacy data transfer BAPI and transaction AS100? Can I continue to use both?</p>\r\n<p>[4] Up to Release S4CORE 102: Why are parallel currency amounts in transaction ABLDT not automatically converted from the base depreciation areas while BAPI_FIXEDASSET_OVRTAKE_CREATE converts the values even if they have already been entered?<br />As of Release S4CORE 103: How are the parallel currency amounts determined during manual entry (for example, transaction ABLDT) and during mass entry (using a BAPI)?</p>\r\n<p>[5] How can I make retroactive changes to the takeover values of a fixed asset?</p>\r\n<p>[6] Does the new transaction ABLDT support batch input?</p>\r\n<p>[7] Is it possible to perform an LSMW recording in transaction ABLDT?</p>\r\n<p>[8] Which alternative option is available in relation to uploading mass data?</p>\r\n<p>[9] What do I need to bear in mind when transferring old subnumbers?</p>\r\n<p>[10] What do I need to bear in mind when transferring assets under construction with line item management?</p>\r\n<p>[11] How are assets under construction for investment measures transferred?</p>\r\n<p>[12] Why can I still see duplicate values in the grid after the legacy data transfer?</p>\r\n<p>[13] What must be noted for the transfer of depreciations/revaluations/devaluations that have already been posted in the feeder system if these were entered manually and not calculated by the system?</p>\r\n<p>[14] Why, in rare cases, are the legacy assets not created despite the issue of a success message when you use the BAPI/IDoc?</p>\r\n<p>[15] How<span style=\"font-family: Arial;\"> can I reset a legacy data transfer that was carried out as part of a test?</span></p>\r\n<p><span style=\"font-family: Arial;\">[16]</span> How can the periodic transaction figures (debit/credit) of the asset values for the periods be transferred before the legacy data transfer date?</p>\r\n<p><span style=\"font-family: Arial;\">[17]</span> Which profit and loss accounts must not be transferred to the general ledger via the balance transfer in the case of a mid-year legacy data transfer because they are transferred when entering the line items (via AB01) as part of the legacy data transfer?</p>\r\n<p>[18] How can I transfer transactions with partner information (VBUND)?</p>\r\n<p>[19] How can I transfer write-ups?</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AS91, AS92, AS94, ABLDT, LSMW, BAPI_FIXEDASSET_OVRTAKE_CREATE, legacy data, LDT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>FAQ, additional information</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong><span style=\"text-decoration: underline;\">[1] Question:</span></strong> How does the conversion of Asset Accounting to the \"standard journal entry\" affect legacy data transfers?</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Answer:</span></strong></p>\r\n<p>Due to the conversion of Asset Accounting to the standard journal entry, subledger-specific Asset Accounting tables have been abolished. From now on, the contents are set up from standard document persistence at runtime. Consequently, it is no longer possible to handle the general ledger and the asset subledger separately during the legacy data transfer. While the takeover values for each fixed asset were, in the past, written directly to the subledger-specific tables without any integration with the general ledger, you must now post the takeover values against a transfer G/L account as a transfer document.</p>\r\n<p><span style=\"text-decoration: underline;\">Consequently, there are also changes to the following process:</span></p>\r\n<ul>\r\n<li>Up to now, you could maintain master data and takeover values in one transaction (AS91). The G/L account balances in the transfer G/L account were then manually written in summarized form in a separate step.</li>\r\n<li>In the new solution, the master data continues to be created in AS91 and/or changed in AS92. However, the takeover values are posted separately in the new posting transaction ABLDT. When you do this, a posting is made to the transfer clearing account immediately. In other words, the manual step for the summary write-off is no longer required.</li>\r\n</ul>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[2] Question:</span></strong> Why is the \"Takeover values\" button no longer available in transaction AS91?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>In the past, the asset values were written directly to a subledger-specific totals and balance table (ANLC). Due to the conversion to the standard journal entry, this table is obsolete and is no longer updated. Instead, the values in the former table ANLC are set up at runtime by means of aggregation from standard journal entry persistence. Consequently, the design is such that reconciliation problems between totals and line items in Asset Accounting are avoided.</p>\r\n<p>Instead of writing the takeover values directly to the subledger-specific table ANLC (as was previously the case), the values are, with immediate effect, posted as separate transfer documents. For this purpose, you must create and save the fixed asset in dialog mode (transaction AS91) first. This is related to the program design for legacy asset master record maintenance and the resulting technical restrictions. If the legacy asset has been created and saved, you can use the \"Takeover values\" button on the initial screen in transaction AS92 to navigate directly to transaction ABLDT in order to enter takeover values. However, it is important that the asset must exist first (that is, before you can make a posting to this asset). From transaction AS93, use the 'Takeover values' button to navigate to the asset document display (AB03) where the posted transfer documents are displayed.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[3] Question:</span></strong> Have adjustments also been made to the legacy data transfer BAPI and transaction AS100? Can I continue to use both?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>BAPI_FIXEDASSET_OVRTAKE_CREATE and transaction AS100 (which calls the BAPI internally) have been fully adjusted to the new logic. In contrast to dialog mode, the BAPI also supports the creation and transfer of legacy asset values in a single step.&#x00A0;If you want to transfer mid-year transactions, see the explanations in SAP Notes <strong>2182475</strong> and <strong>2516711</strong>. Furthermore, you must implement SAP Note <strong>2231678</strong> if you also want to transfer other transactions (apart from acquisitions).</p>\r\n<p>Note that the BAPI supports only new assets for legacy assets and the optional transfer of values. The transfer of values to an existing legacy asset or the correction of values transferred previously (see also Question [5]) are not supported.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[4] Question:</span> </strong>Up to Release S4CORE102: Why are parallel currency amounts in transaction ABLDT not automatically converted from the base depreciation areas while BAPI_FIXEDASSET_OVRTAKE_CREATE converts the values even if they have already been entered?<br />As of Release S4CORE 102: How are the parallel currency amounts determined during manual entry (for example, transaction ABLDT) and during mass entry (using a BAPI)?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>Parallel currencies contain historical values. In general, the conversion of the <span style=\"text-decoration: underline;\">entire history</span> on <strong>a</strong> key date with <strong>one</strong> exchange rate will, at best, return a useful approximated value. However, these values must actually be specified from the feeder system. Alternatively, you determine these values externally, for example. Overall, it must still be remembered that this system conversion is very questionable.</p>\r\n<p>In order to make the conversion less complex, this questionable mechanism is no longer implemented in transaction ABLDT. The following approach is adopted instead: During entry, users determine and acquire detailed values in a useful manner.</p>\r\n<p>In the past, the BAPI, in turn, was based on the same ANLC logic as transaction AS91. The conversion was implemented here. Since BAPIs are to ensure a special type of stability and compatibility, the logic has been retained for reasons of \"feature parity\". Whether the values are converted or taken over from the transferred interface values unaltered is determined as follows:</p>\r\n<ul>\r\n<li>Up to S4CORE Release 102, the conversion is determined by the setting of transaction OAYD.</li>\r\n<ul>\r\n<li>If the \"Enter values in foreign currency area\" setting is active, no further currency translations are performed.</li>\r\n<li>If the \"Enter values in foreign currency area\" setting is <strong>not</strong> active, values in parallel currencies are always calculated, regardless of any existing transfer data in the interface for the relevant areas.</li>\r\n</ul>\r\n<li>As of Release S4CORE103, the conversion is determined only via the transfer of values in the interface.</li>\r\n<ul>\r\n<li>Transaction OAYD is obsolete&#x2014;any existing settings are no longer taken into account.</li>\r\n<li>If values for depreciation areas of parallel currencies are transferred in the interface of the BAPI, the legacy data transfer is posted using these parallel currency values.</li>\r\n<li>If no values for depreciation areas of parallel currencies are transferred in the interface of the BAPI, currency translation takes place</li>\r\n</ul>\r\n</ul>\r\n<p>If an automatic translation takes place, the exchange rate is determined as follows:</p>\r\n<ul>\r\n<li>For the cumulative year start values, on the basis of the activation date of the asset</li>\r\n<li>For mid-year transactions, on the basis of the date of the legacy data transfer</li>\r\n</ul>\r\n<p>Note that for the transfer of open items on an AuC with line item management (transaction type 900 or 910), the exchange rate on the asset value date (that is, the first day of the fiscal year) is used for the currency translation. In general, particularly in this case, we recommend that you enter the parallel currency amounts directly because they were originally posted with an earlier date.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[5] Question:</span></strong> How can I make retroactive changes to the takeover values of a fixed asset?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>In the past, it was possible to change subledger-specific information directly in the table ANLC and therefore manipulate, as required, the takeover values already entered. Unfortunately, this option was also frequently misused outside the legacy data transfer, which resulted in reconciliation errors and a considerable support effort because these manipulations could be reproduced only to a limited extent.</p>\r\n<p>For greater transparency, and due to the fact that transfer<span style=\"text-decoration: underline;\">documents</span> are posted in the new solution, the only way to change takeover values is to reverse the posted transfer document and then repost it. In order to be able to reverse a transfer document, a fixed asset must not have any subsequent transactions because the legacy data transfer provides the value foundation for this fixed asset. If follow-on transactions have already been posted to the fixed asset, you must first reverse all transactions (preferably in reverse sequence), then reverse the transfer transaction, and finally post the new takeover values. You can then create the follow-on transactions again individually.</p>\r\n<p>See also SAP Note <strong>2217275</strong>, which corrected various errors associated with reversing transfer transactions.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[6] Question:</strong></span> Does the new transaction ABLDT support batch input?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>Transaction ABLDT uses an input-enabled ALV grid control. Therefore, according to SAP Note <strong>311440</strong>, it does <strong>not</strong> support batch input.</p>\r\n<p>As an alternative, you can use BAPI_FIXEDASSET_OVRTAKE_CREATE.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[7] Question:</strong></span> Is it possible to perform an LSMW recording in transaction ABLDT?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>The LSMW recording is based on the batch input recorder and therefore has the same restrictions as described in SAP Note 311440. Therefore, it is <strong>not</strong> possible to create an LSMW recording for transaction ABLDT. For more information, see SAP Note <strong>554635</strong>.</p>\r\n<p>As an alternative, you can use BAPI_FIXEDASSET_OVRTAKE_CREATE.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[8] Question:</strong></span> Which alternative option is available in relation to uploading mass data?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>In the past, batch input was frequently used to transfer legacy assets in large quantities. This option is <strong>no</strong> longer available (see point [6]).&#x00A0;Furthermore, direct input is <strong>no</strong> longer available. See SAP Note <strong>2014219</strong>.</p>\r\n<p>To transfer mass data, use either transaction <strong>AS100</strong> or the BAPI BAPI_FIXEDASSET_OVRTAKE_CREATE for the legacy data transfer (or the method CreateInclValues for the business object BUS1022) in combination with <strong>LSMW</strong>. Transaction AS100 can process a maximum of 5,000 assets in one file upload. However, you can split the file, if required.</p>\r\n<p>When <strong>LSMW</strong> is used in combination with <strong>BAPI_FIXEDASSET_OVRTAKE_CREATE</strong>, it is also possible to process, for example, an Excel file with more than 5,000 fixed assets whereby one fixed asset is always represented by a (very wide) line in the Excel file. To do this, you must maintain the field mapping accordingly and define internally which depreciation areas or depreciation-area-specific information are stored in which columns (fixed assignment; for example: column BK - BZ contains, by convention, the ANLB information for area 20). In particular, this procedure is very useful if the feeder system from which the data is to be transferred supports an Excel download.</p>\r\n<p>We recommend that you use the <span style=\"text-decoration: underline;\">default parallel processing mechanisms in LSMW</span>. For more detailed information, see the LSMW documentation and SAP Note <strong>752194</strong>.<strong> </strong>Parallel processing of IDocs in the background (report <strong>RBDAPP01</strong>) with package size 50 has proven successful. Ensure that your package contents are as homogenous as possible (for example, fixed assets from the same asset class). To make full use of the options associated with parallel processing, activate buffering for the number range objects ANLAGENNR and FIAA-BELNR. Otherwise, lock problems may occur during number assignment (see also <strong>point [9]</strong>). As of SAP S/4HANA on-premise Release 1809 or SAP S/4HANA Cloud Release 1805, both number range objects are delivered with the \"Parallel Buffering\" and \"Buffer Size = 1\" settings by default. For more information about the parallel buffering of number ranges, see SAP Notes 599157 and 1398444.</p>\r\n<p>If you require any support when setting up LSMW mapping or the IDoc parameters, contact your SAP consultant. Setting up field mapping is not treated as part of standard SAP support. Instead, it is a customer-specific consulting service. The same applies to setting up number range buffering, choosing optimized IDoc parameters, and IDOC serialization.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[9] Question:</strong></span> What do I need to bear in mind when transferring old subnumbers?</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Answer:</span></strong></p>\r\n<p>For the transfer of subnumbers, you must implement SAP Notes <strong>2245187</strong> and <strong>2243637</strong>. Otherwise, depending on the data constellation, you might find that the transferred documents are posted but the corresponding asset subnumbers are missing.</p>\r\n<p>For a mass upload via LSMW with subnumbers, it is important to know that:</p>\r\n<ul>\r\n<li>A subnumber can only be created if the main asset number already exists</li>\r\n<li>Each number must also lock the main asset number when it is created</li>\r\n</ul>\r\n<p>You should therefore split the upload as follows:</p>\r\n<ol>\r\n<li>Upload of all main asset numbers (completely without subnumbers) You can use the <span style=\"text-decoration: underline;\">standard LSMW parallelization mechanisms</span> for this. For more detailed information about this, see the LSMW documentation and SAP Note <strong>752194</strong>.</li>\r\n<li>Upload of subnumbers following successful completion of step 1 To do this, you should split the asset subnumbers into multiple files so that the corresponding main asset numbers can <em>never</em> occur in two files at the same time. You then start each of these files <span style=\"text-decoration: underline;\">without</span> parallel processing. However, you can upload the files at the same time, thus achieving a kind of manual parallelization.</li>\r\n</ol>\r\n<p>This procedure avoids lock problems as long as the files are disjoint with regard to the concerned main asset numbers.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[10] Question:</span> </strong>What do I need to bear in mind when transferring assets under construction with line item management?</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Answer:</span></strong></p>\r\n<p>In the case of assets under construction (AuCs), you cannot transfer cumulative APC or accumulated depreciations. As a result of a program error, this was possible in some cases, which resulted in a scenario where you could no longer settle the values to the AuC with line item management. This problem was solved for mid-year legacy data transfers with <strong>SAP Note 2252940</strong> and also for end-of-year transfers with <strong>SAP Note 2294012</strong>. The transfer must take place by means of the transfer of transactions, so:</p>\r\n<ul>\r\n<li>In the manual case, <strong>not</strong> via transaction <strong>ABLDT</strong> but via <strong>AB01</strong> or, as of SAP Note 2294012, via the new transaction <strong>ABLDT_OI</strong>.</li>\r\n<li>In the case of a transfer using BAPI_FIXEDASSET_OVRTAKE_CREATE, do <strong>not</strong> enter the values into the table CUMULATEDVALUES; enter them into the table TRANSACTIONS, instead.</li>\r\n</ul>\r\n<p>There are special transaction types (900/910) for transferring line items from previous years. Do <strong>not</strong> use the transaction types 970/980. If you have the special scenario of proportional depreciations for the AuC, enter these are proportional depreciations for previous years using the transaction.</p>\r\n<p>During a legacy data transfer at the <span style=\"text-decoration: underline;\">end of the year</span>, it is not possible to transfer the assets under construction on the last day of the closed year (that is, the transfer date). Instead, the system automatically uses the first day of the first open year as the transfer date. This is correct and unavoidable, since all subsequent settlement processes are based on this logic. For more information about reconciliation, see <strong>SAP Note 2304473</strong>.</p>\r\n<p>As part of a <span style=\"text-decoration: underline;\">mid-year</span> legacy data transfer, you enter the line items from previous years with transaction type 900/910 in transaction ABLDT_OI and the line items from the current fiscal year with the normal transaction types (for example, 100) using AB01L.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[11] Question:</span></strong> How are assets under construction for investment measures transferred?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>At present, transfers for AuCs for investment measures are not supported. You can only use a transfer to an independent AuC as described in <strong>SAP Note 29475</strong>. During the final settlement, the values transferred to the independent AuC must be transfer posted separately with the AuC settlement to the recipients of the final settlement.</p>\r\n<p>Alternatively, as described in SAP Note 29475, you can post the prior-year acquisitions with a posting date at the end of the last closed fiscal year to the order or WBS element, and settle them to the AuC in the same fiscal year. To do this, you have to perform the legacy data transfer in two steps. In the first step, you transfer the assets under construction, then close the fiscal year, and only then create the transfer segment for the normal assets. Note that the closed fiscal year cannot be reopened afterwards.&#x00A0;<br />If you have already incorrectly transferred assets with a legacy data transfer on 12/31/PY, you can only transfer the postings to the order/WBS element in the previous year. The settlement can then only be made to the corresponding asset under construction in the following year. For more information, see SAP Notes 2725359 for summary investment measures and 425601 for investment measures with line item management.</p>\r\n<p>The other options described in SAP Note 29475 (summary investment measures) are not yet fully available at present.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[12] Question:</span></strong> Why can I still see duplicate values in the grid after the legacy data transfer?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></p>\r\n<p>As a result of a program error, the year start values of the legacy data transfer are also added up as transactions of the current year. This error is corrected with SAP Note <strong>2212511</strong>.</p>\r\n<p>&#x00A0;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">[13]</span> <span style=\"text-decoration: underline;\">Question: </span></strong>What must be noted for the transfer of depreciations/revaluations/devaluations that have already been posted in the feeder system if these were entered manually and not calculated by the system?</p>\r\n<p><strong><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span></strong> Following the legacy data transfer, the transferred values must also be entered using transaction <strong>AB01</strong> and the corresponding transaction type. The legacy data transfer data must be entered as the posting date. You use the asset value date to control the point from when the transaction should be taken into account in the automatic calculation of depreciations/replacement values and so on in accordance with the period control. First, you must implement SAP Note <strong>2297667</strong>.</p>\r\n<p><strong>Make sure</strong> that you enter the relevant values for areas with parallel currencies since these are determined by the system during the entry of the transaction by means of currency conversion on the basis of the posting date.</p>\r\n<p>If, in the configuration, in \"Date Specifications -&gt; Specify Last Period Posted in Prv.System (Transf.During FY)\", you have specified a period before the posting period from the transfer date, the system adjusts the posting date you entered to the last day of the period.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[14] Question:</strong></span>&#x00A0;Why, in rare cases, are the legacy assets not created despite the issue of an error message?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>Answer:</strong></span> As a rule, this symptom occurs if you have not implemented all of the program corrections referenced here and also pass input data with errors. This problem can be expressed in the following ways:</p>\r\n<ul>\r\n<li>When the LSMW is used, the number of \"successfully\" processed IDocs (status 53) does not match the number of assets.</li>\r\n<li>You navigate from a transfer document to the corresponding legacy asset and receive error AA-001, which tells you that the asset does not exist.</li>\r\n</ul>\r\n<p>With SAP Note <strong>2302201</strong>, the consistency checks of the input data were enhanced to avoid such errors.</p>\r\n<p>Regardless of this, if you use your own report to call BAPI_FIXEDASSET_OVRTAKE_CREATE, you must note that once the BAPI has run the assets are initially only flagged for update. The actual update - in the database - takes place only with the subsequent COMMIT (via BAPI_TRANSACTION_COMMIT), which must be explicitly triggered by your program.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[15] Question:</strong></span> How<span style=\"font-family: Arial;\"> can I reset a legacy data transfer that was carried out as part of a test?</span></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Arial;\">Answer:</span></strong></span><span style=\"font-family: Arial;\">&#x00A0;</span>During a legacy data transfer, master data is created for legacy assets and the transferred values are posted as transfer documents as part of the journal entry. If you want to completely reset a legacy data transfer, the master data has to be reset in Asset Accounting <span style=\"text-decoration: underline;\">and</span> the transfer documents have to be reset in Financial Accounting. This is only possible for the complete company code, however, which means if you subsequently transfer an existing company code, it is not possible to only delete the data from the transfer. You can reset the Asset Accounting data in the Implementation Guide for Asset Accounting (new) with the activity <em>Asset Accounting (New)-&gt; Preparations for Going Live -&gt; Tools -&gt; Reset Company Code</em>. You can reset the general ledger data in the Implementation Guide for General Ledger with the activity <em>General Ledger Accounting (new) -&gt; Preparation for Production Start -&gt; New Installation -&gt; Delete Test Data -&gt; Delete Transaction Data -&gt; Delete Company Code Data</em>.</p>\r\n<p>&#x00A0;If you do not want to reset the complete company code, or cannot, you have the following options:</p>\r\n<ul>\r\n<li>You can use the BAPI BAPI_ASSET_REVERSAL_POST to reverse the transfer documents automatically. In this process, only the documents are reversed. Assets that were created during the legacy data transfer are retained. If required, you can then deactivate the legacy assets using the mass change function.</li>\r\n<li>When you post the transfer values again, you can enter and post these corrections manually in the Implementation Guide with <em>Asset Accounting (new) -&gt; Asset Data Transfer -&gt; Manual Online Transfer -&gt; Legacy Asset -&gt; Post Transfer Values</em> (transaction ABLDT), as long as you have not deactivated the legacy assets. <br />It is not possible to post only the corrections of the transfer values automatically, for example, using a BAPI. Note that the BAPI BAPI_FIXEDASSET_OVRTAKE_CREATE <em>always</em> creates new legacy assets and posts the new transfer documents to them.</li>\r\n</ul>\r\n<p>We recommend that you use a variety of tests to ensure that the transfer of production legacy data can be carried out and reconciled without errors, without requiring subsequent corrections to the transferred values.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong>[16] Question: </strong></span>How can the periodic transaction figures (debit/credit) of the asset values for the periods be transferred before the legacy data transfer date?</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Arial;\">Answer:</span></strong></span><span style=\"font-family: Arial;\">&#x00A0;</span>In the past, general ledger and asset subledger were handled separately during the legacy data transfer. The asset values were entered manually in Asset Accounting and in the G/L accounts. The G/L account was defined as the reconciliation account only afterwards; as a result of which the integration with Asset Accounting was switched on.<br />This made it possible to enter the debit transaction figures and credit transaction figures for the periods before the transfer date in the general ledger on the fixed asset accounts.</p>\r\n<p>Due to the changeover of Asset Accounting to the standard journal entry, the transfer values are now posted as transfer documents against a  transfer G/L account. The FI posting date is the same as the legacy data transfer date of FI-AA. As a result, the entire transfer amount (cumulative values and current transactions for mid-year legacy data transfer) is displayed in the general ledger in the transfer period. It is no longer possible and allowed to post directly on the reconciliation accounts of Asset Accounting.</p>\r\n<p><em>To prevent future data inconsistencies, these reconciliation accounts must never be posted to manually.</em></p>\r\n<p>To map the periodic asset balances in the general ledger, you can proceed as follows:</p>\r\n<ul>\r\n<li>Create a new G/L account for each reconciliation account of Asset Accounting (acquisition cost accounts and accumulated depreciation accounts). Define this as a G/L account (not as a reconciliation account).</li>\r\n<li>Assign these G/L accounts to the balance sheet item to which you have also assigned the relevant reconciliation accounts.</li>\r\n<li>As part of the legacy data transfer to the new G/L accounts, post the periodic debit balances and credit balances - if relevant, with the appropriate CO account assignments - for the periods up to the transfer date. Post against a balance sheet clearing account in each case. To do this, you can also use the offsetting account for the legacy data transfer in FI-AA.</li>\r\n<li>Post the cumulative balance of the G/L account in the period of the transfer against the balance sheet clearing account (transfer date for FI-AA). The balance of the G/L account is now zero.</li>\r\n<li>Lock these G/L accounts after the successful (reconciled) legacy data transfer. They are required only for reporting purposes.</li>\r\n</ul>\r\n<p>If you want to transfer the periodic transaction figures for several years, you must carry out a fiscal year change in the general ledger.</p>\r\n<p>Use transaction AS91 to create the master data for the legacy data transfer. Use transaction ABLDT to transfer the transfer values, and - in the context of a mid-year legacy data transfer - use transaction AB01 to transfer the mid-year asset transactions.<br />Alternatively, you can use the BAPI as well as transaction AS100 to transfer the data automatically.</p>\r\n<p>&#x00A0;</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Arial;\">[17] Question: </span></strong></span>Which profit and loss accounts must not be transferred to the general ledger via the balance transfer in the case of a mid-year legacy data transfer because they are transferred when entering the line items (via AB01) as part of the legacy data transfer?</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Answer:</span></strong> In the case of a mid-year legacy data transfer, transactions (for example, acquisitions and retirements), which were posted in the legacy system in the year of the transfer (before the transfer date), must be transferred (and posted) in the SAP system as line items as part of the legacy data transfer. This is carried out using transaction AB01L (<em>Customizing: Asset Accounting &#x2013;&gt; Asset Data Transfer &#x2013;&gt; Manual Data Transfer &#x2013;&gt; Legacy Fixed Asset &#x2013;&gt; Post Transfer Values</em>).</p>\r\n<p>However, in some business transactions, the offsetting account is not replaced with the <em>Offsetting Account for Legacy Data Transfer</em>. In these cases, the relevant G/L account must not be posted to the general ledger during the balance transfer.</p>\r\n<p>When you enter acquisitions, the contra account (asset balance sheet account) <em>Acquisition value posting (T095-KTANSG)</em> is automatically replaced with the <em>offsetting account for legacy data transfer</em>(<em>customizing: Asset Accounting -&gt; Asset Data Transfer -&gt; Parameters for Data Transfer -&gt; Specify Offsetting Account for Legacy Data Transfer</em>).<br />On the other hand, retirements are posted against the profit and loss account <em>Clearing Acct. Revenue from Asset Sale (T095-KTERLW)</em><em></em>. This clearing account is not replaced during the legacy data transfer when entering retirements. This different procedure is required so that the gains/losses can be displayed in Asset Explorer.</p>\r\n<p>The following profit and loss accounts are not replaced with the <em>Offsetting Account for Legacy Data Transfer</em>. These accounts are posted via the entry of the line items during the legacy data transfer and must therefore <span style=\"text-decoration: underline;\">not</span> be transferred to the general ledger:</p>\r\n<ul>\r\n<li>Clearing Acct. Revenue from Asset Sale (T095-KTERLW)</li>\r\n<li>Gain from Asset Sale (T095-KTMEHR)</li>\r\n<li>Loss from Asset Sale (T095-KTMIND)</li>\r\n</ul>\r\n<p>Use the legacy system to reconcile these accounts. Slight differences may occur because the SAP system determines the gains/losses in a different way than your legacy system has calculated them. In this case, you must carry out a standardizing entry in the general ledger during your legacy data transfer.</p>\r\n<p>Note, however, that the BAPI for the legacy data transfer always posts against the <em>Offsetting Account for Legacy Data Transfer</em>, even if you enter a mid-year retirement. The SAP line item schema is always used for the legacy data transfer. A custom line item schema is not permitted.</p>\r\n<p><strong>[18] Question:</strong>&#x00A0;How can I transfer transactions with partner information (VBUND) for a transfer during the fiscal year?</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Answer:</span></strong>&#x00A0;You cannot specify any partner information for the transactions. However, you can post to the corresponding reconciliation accounts with balances including partner information BEFORE the legacy data transfer of the assets. The balances are then transferred to the transfer account WITHOUT partner information. You then transfer the data for the assets that, in turn, post without partner information. This ultimately gives you the correct balances including partner information.</p>\r\n<p><strong>[19] Question:</strong>&#x00A0;How can I transfer write-ups for a mid-year transfer?</p>\r\n<p><strong>Answer:</strong>&#x00A0;During the transfer of write-up transactions, the system issues error message AA207. Therefore, you cannot use write-up transaction types (such as 700) for the transfer. However, during the transfer with a BAPI, you can use a transaction type for gross acquisitions instead (for example, 147) and enter the corresponding proportional values there according to the +/- sign.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D033895)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D070352)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002208321/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002208321/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "599157", "RefComponent": "BC-SRV-NUM", "RefTitle": "Number ranges: New buffering method", "RefUrl": "/notes/599157"}, {"RefNumber": "554635", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW and recordings", "RefUrl": "/notes/554635"}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176"}, {"RefNumber": "311440", "RefComponent": "BC-ABA-SC", "RefTitle": "Batch input and controls", "RefUrl": "/notes/311440"}, {"RefNumber": "2356929", "RefComponent": "FI-AA-AA-B", "RefTitle": "Legacy data transfer: Period in which the depreciation was posted not taken into account/AA026 for reversal", "RefUrl": "/notes/2356929"}, {"RefNumber": "2352662", "RefComponent": "FI-AA", "RefTitle": "Short dump ASSERTION_FAILED during legacy data transfer with subnumbers", "RefUrl": "/notes/2352662"}, {"RefNumber": "2350250", "RefComponent": "FI-AA-AA-A", "RefTitle": "Short dump ASSERTION_FAILED in BAPI_FIXEDASSET_OVRTAKE_CREATE after SAP Note 2304157", "RefUrl": "/notes/2350250"}, {"RefNumber": "2304473", "RefComponent": "FI-AA", "RefTitle": "New Asset Accounting: Reconciliation of the legacy data transfer", "RefUrl": "/notes/2304473"}, {"RefNumber": "2265335", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transfer: Error AA-833 after you implement SAP Note 2231678", "RefUrl": "/notes/2265335"}, {"RefNumber": "2256721", "RefComponent": "FI-AA-AA-A", "RefTitle": "IDOC_INPUT_FIXEDASSET_CREATEIV has status 53 but legacy assets are missing", "RefUrl": "/notes/2256721"}, {"RefNumber": "2252940", "RefComponent": "FI-AA-AA-A", "RefTitle": "ABLDT/BAPI_FIXEDASSET_OVRTAKE_CREATE: Transfer of open items for assets under construction", "RefUrl": "/notes/2252940"}, {"RefNumber": "2231678", "RefComponent": "FI-AA-AA-C", "RefTitle": "Incorrect legacy data transfer via BAPI_FIXEDASSET_OVRTAKE_CREATE", "RefUrl": "/notes/2231678"}, {"RefNumber": "2228586", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error FAA_POST 212 for reversal of mid-year legacy data transfer with transactions", "RefUrl": "/notes/2228586"}, {"RefNumber": "2217275", "RefComponent": "FI-AA-AA-C", "RefTitle": "Reversal of asset transactions: Incorrect reversal/line items missing from display", "RefUrl": "/notes/2217275"}, {"RefNumber": "2182475", "RefComponent": "FI-AA-AA-A", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Dump when transferring more than one mid-year transaction", "RefUrl": "/notes/2182475"}, {"RefNumber": "2014219", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transfer with SAP_FIN 720", "RefUrl": "/notes/2014219"}, {"RefNumber": "1398444", "RefComponent": "FI-GL-GL-A", "RefTitle": "Buffering the document number assignment for RF_BELEG", "RefUrl": "/notes/1398444"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3318060", "RefComponent": "FI-AA-AA-C", "RefTitle": "Central KBA Legacy Data Transfer in Asset Accounting S/4HANA  / How-to scenarios, standard behavior and guides.", "RefUrl": "/notes/3318060 "}, {"RefNumber": "2504746", "RefComponent": "FI-AA-AA", "RefTitle": "AS91 /ABLDT: Manual update of Parallel Currency Area not possible (AC359)", "RefUrl": "/notes/2504746 "}, {"RefNumber": "2724330", "RefComponent": "FI-AA", "RefTitle": "Trading Partner not updated in Asset Master (ANLA-VBUND)", "RefUrl": "/notes/2724330 "}, {"RefNumber": "3168999", "RefComponent": "FI-AA-AA-A", "RefTitle": "Request to enable AuC mid-year migration with investment measure", "RefUrl": "/notes/3168999 "}, {"RefNumber": "3031138", "RefComponent": "FI-AA-AA-C", "RefTitle": "Line item of \"Offsetting Account for Legacy Data Transfer\" during legacy transfer posting", "RefUrl": "/notes/3031138 "}, {"RefNumber": "2846327", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AW001 happens on the AUC asset in transaction AIAB", "RefUrl": "/notes/2846327 "}, {"RefNumber": "2368280", "RefComponent": "FI-AA", "RefTitle": "FAQ in a S/4HANA implementation in Asset Accounting", "RefUrl": "/notes/2368280 "}, {"RefNumber": "2660709", "RefComponent": "FI-AA-AA", "RefTitle": "Error FAA_POST215 & AA322 & AA352 during Legacy Data Transfer (AS91 / ABLDT)", "RefUrl": "/notes/2660709 "}, {"RefNumber": "2331123", "RefComponent": "FI-AA-AA", "RefTitle": "Prerequisite customizing required for legacy asset takeover in Simple Finance (sFIN)", "RefUrl": "/notes/2331123 "}, {"RefNumber": "2632388", "RefComponent": "FI-AA-AA-A", "RefTitle": "BAPI1022052 in BAPI_FIXEDASSET_OVRTAKE_CREATE", "RefUrl": "/notes/2632388 "}, {"RefNumber": "2631104", "RefComponent": "CA-GTF-MIG", "RefTitle": "Performance of migration the fixed assets", "RefUrl": "/notes/2631104 "}, {"RefNumber": "2501554", "RefComponent": "FI-AA", "RefTitle": "Error message FAA_POST214 occurs when using T-code : ABLDT to post legacy data transfer", "RefUrl": "/notes/2501554 "}, {"RefNumber": "2235848", "RefComponent": "FI-AA-AA", "RefTitle": "General steps for mid-year legacy asset transfer in Simple Finance (sFIN)", "RefUrl": "/notes/2235848 "}, {"RefNumber": "2495788", "RefComponent": "FI-AA-AA-B", "RefTitle": "Current year posted unplanned depreciation / revaluation is reversed by the system during mid-year legacy asset transfer in new Asset Accounting(S4HANA)", "RefUrl": "/notes/2495788 "}, {"RefNumber": "2462734", "RefComponent": "FI-AA-AA", "RefTitle": "Message AA324 issued when using T-Code: ABLDT to takeover values for legacy asset", "RefUrl": "/notes/2462734 "}, {"RefNumber": "3068253", "RefComponent": "FI-AA-AA-C", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Dump with custom line item schema", "RefUrl": "/notes/3068253 "}, {"RefNumber": "2565284", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS92: Message SFIN_FI004 when jumping to takeover values", "RefUrl": "/notes/2565284 "}, {"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "2533642", "RefComponent": "FI-AA-AA", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Example of transfer with revaluation", "RefUrl": "/notes/2533642 "}, {"RefNumber": "2533046", "RefComponent": "FI-AA-AA", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Example of transfer with investment support", "RefUrl": "/notes/2533046 "}, {"RefNumber": "2530739", "RefComponent": "FI-AA", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Example of accumulated depreciations for transfer during the fiscal year", "RefUrl": "/notes/2530739 "}, {"RefNumber": "2499730", "RefComponent": "FI-AA-AA-A", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Short dump ITAB_DUPLICATE_KEY for INSERT_FAAT_YDDA", "RefUrl": "/notes/2499730 "}, {"RefNumber": "2503711", "RefComponent": "FI-AA-AA-C", "RefTitle": "Error AAPO161 with transfer posting before legacy data transfer date", "RefUrl": "/notes/2503711 "}, {"RefNumber": "2494176", "RefComponent": "FI-AA-AA-A", "RefTitle": "Vorabauslieferung IDOC_INPUT_FIXEDASSET_LDTPOST", "RefUrl": "/notes/2494176 "}, {"RefNumber": "2493909", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error AA036 for legacy asset without values", "RefUrl": "/notes/2493909 "}, {"RefNumber": "2483037", "RefComponent": "FI-AA", "RefTitle": "AU 133 'Account 'Special reserves balance' could not be found for area xy' during ABLDT", "RefUrl": "/notes/2483037 "}, {"RefNumber": "2482314", "RefComponent": "FI-AA-AA-C", "RefTitle": "BAPI_FIXEDASSET_OVRTAKE_CREATE: Duplicate cumulative values", "RefUrl": "/notes/2482314 "}, {"RefNumber": "2481235", "RefComponent": "CA-GTF-MIG", "RefTitle": "Restrictions and extensibility of pre-delivered migration objects - SAP S/4HANA Migration Cockpit (on premise)", "RefUrl": "/notes/2481235 "}, {"RefNumber": "2427888", "RefComponent": "FI-AA-AA-A", "RefTitle": "Balance carryforward is required during legacy data transfer to AA in the first fiscal year", "RefUrl": "/notes/2427888 "}, {"RefNumber": "2405785", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS100: Message BAPI1022 052: 'Table &1: Fiscal year &2 not permitted (expected: &3)", "RefUrl": "/notes/2405785 "}, {"RefNumber": "2405815", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error FAA_POST 080 for mid-year legacy data transfer with quantity", "RefUrl": "/notes/2405815 "}, {"RefNumber": "2404736", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error AA 207 during legacy data transfer with mid-year investment support", "RefUrl": "/notes/2404736 "}, {"RefNumber": "2400487", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error AAPO 104 during legacy data transfer with manual currency translation", "RefUrl": "/notes/2400487 "}, {"RefNumber": "2307299", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transfer: Redundant entries in FAAT_DOC_IT", "RefUrl": "/notes/2307299 "}, {"RefNumber": "2342038", "RefComponent": "FI-AA-AA", "RefTitle": "Incorrect debit/credit indicator in the case of posted interest", "RefUrl": "/notes/2342038 "}, {"RefNumber": "2224556", "RefComponent": "FI-AA-AA-C", "RefTitle": "Problems with reserve for special depreciation", "RefUrl": "/notes/2224556 "}, {"RefNumber": "2383596", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error message AA 660 for reversal of legacy data transfer with manual depreciation", "RefUrl": "/notes/2383596 "}, {"RefNumber": "2302201", "RefComponent": "FI-AA-AA-A", "RefTitle": "Error AA 001 after legacy data transfer", "RefUrl": "/notes/2302201 "}, {"RefNumber": "2304473", "RefComponent": "FI-AA", "RefTitle": "New Asset Accounting: Reconciliation of the legacy data transfer", "RefUrl": "/notes/2304473 "}, {"RefNumber": "2287723", "RefComponent": "BC-SRV-DX-LSM", "RefTitle": "LSMW in SAP S/4HANA on-premise (S4CORE)", "RefUrl": "/notes/2287723 "}, {"RefNumber": "2294012", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transfer of open items of an AuC", "RefUrl": "/notes/2294012 "}, {"RefNumber": "2268737", "RefComponent": "FI-AA-AA-C", "RefTitle": "AA 698 or FAA_POST 205 with legacy data transfer and investment support", "RefUrl": "/notes/2268737 "}, {"RefNumber": "2265335", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transfer: Error AA-833 after you implement SAP Note 2231678", "RefUrl": "/notes/2265335 "}, {"RefNumber": "2231678", "RefComponent": "FI-AA-AA-C", "RefTitle": "Incorrect legacy data transfer via BAPI_FIXEDASSET_OVRTAKE_CREATE", "RefUrl": "/notes/2231678 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": "X"}, {"SoftwareComponent": "SAPSCORE", "From": "101", "To": "101", "Subsequent": "X"}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2014219", "RefTitle": "Legacy data transfer with SAP_FIN 720", "RefUrl": "/notes/0002014219"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}