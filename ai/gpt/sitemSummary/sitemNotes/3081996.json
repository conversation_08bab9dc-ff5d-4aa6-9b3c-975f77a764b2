{"Request": {"Number": "3081996", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 483, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000001470792021"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003081996?language=E&token=A92D3D8FA4EE862809FC63195FE8CEB5"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003081996", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003081996/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3081996"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Documentation error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "FI-FIO-GL"}, "SAPComponentKeyText": {"_label": "Component", "value": "<PERSON>ori UI for General Ledger Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Fiori UI for Financial Accounting", "value": "FI-FIO", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-FIO*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "<PERSON>ori UI for General Ledger Accounting", "value": "FI-FIO-GL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-FIO-GL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "3081996 - Deprecation of SAP Design Studio Apps in SAP S/4HANA 2021"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Most SAP Design Studio apps were deprecated in SAP S/4HANA 2021 and will be deleted from the system as of SAP S/4HANA 2022. This means that the apps are in maintenance mode and will not be enhanced anymore. However, if you open an incident because there&#8217;s an issue in one of the SAP Design Studio apps, you will get support.</p>\r\n<p>These SAP Design Studio apps include all Finance apps based on the Design Studio technology. The apps are no longer available by default on the SAP Fiori launchpad. However, you can still locate them using the app finder until they&#8217;re deleted.&#160;In the system, you can easily identify the deprecated apps by the word&#160;Deprecated&#160;on the respective tiles.</p>\r\n<p>In addition, the preview in the following apps is now provided in Web Dynpro in the following apps: <em>View Browser</em>, <em>Query Browser</em>, <em>Custom Analytical Queries</em>, and \"Run Advanced Compliance Reports\".</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Design Studio apps refer to multidimensional reports based on the SAP Design Studio template.</p>\r\n<p>In contrast, the Web Dynpro data grid is the default reporting tool for all drill-down reporting. The Web Dynpro apps offer not only feature parity compared to their Design Studio counterparts but also many additional advantages:</p>\r\n<ul class=\"ul\" id=\"loio34c82d14ab244cc29a9542e48963bdb1__ul_ggw_mqv_wnb\">\r\n<li class=\"li\">\r\n<p class=\"p\">Available for desktop and tablet devices</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">Comply with SAP's Accessibility Standard</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">Offer a generic PDF download function</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">Offer exception reporting</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">The extremely flexible filters are shown in the header area of a data grid app (no separate popup as in Design Studio apps).</p>\r\n</li>\r\n<li class=\"li\">\r\n<p class=\"p\">The navigation panel can be hidden to leave more space on the screen.</p>\r\n<p class=\"p\">For more information, see the following SAP Community blog:&#160;<a target=\"_blank\" class=\"extlink\" href=\"http://help.sap.com/disclaimer?site=https://blogs.sap.com/2021/03/18/six-reasons-why-web-dynpro-is-better-than-design-studio-in-sap-s-4hana-cloud/\" title=\"https://blogs.sap.com/2021/03/18/six-reasons-why-web-dynpro-is-better-than-design-studio-in-sap-s-4hana-cloud/\">Six reasons why Web Dynpro is Better than Design Studio in SAP S/4HANA Cloud</a>.</p>\r\n</li>\r\n</ul>\r\n<p>This note refers to the following apps:</p>\r\n<p>F2921, F1615A, F1617A, F1422A, F1423A, F1440A, F3752, F0925A, F0926A, F0927A, F0928A, F0929A,F0930A, F0931A, F0932A, F0933A, F0934A, F0935A, F0936A, F0937A, F0938A, F0939A, F0940A, F0941A, F0942A, F0943A, F0944A, F0945A, F0948A, F0949A, F0965A, F1582B,&#160; F1583B, F1584A, F1585A, F1586A, F2741, F2838, F2839, F2840, F2842, F2843, F2847, F2848,&#160; F2849, F2850, F2851,F2852, F2853, F2854, F2855, F2856, F2857, F2858, F2859, F2860, F2861, F2862, F2863, F2864, F2995, F2996, F2997, F2998, F2999, F0956A, F3274, F3802, F0740A, F0741A, F3252, F4068, F4269, F4620, F4702, F4703, F4956, F2766, F2136A, F2920, F3164, F3165, F3166, F3167, F3168, F3180, F3181, F0996A, F2767, F3084, F3000, F3076, F3267, F3800, F3801, F3388, F4093, F3889, F4191, F4192, F4193, F3432, F3432, F4060, F4061, F4063, F4064, F4065, W0051, F1615, F1617, F1422, F1423, F1440, W0134, F0925, F0926, F0927, F0928, F0929, F0930, F0931, F0932, F0933, F0934, F0935, F0936, F0937,F0938, F0939, F0940, F0941, F0942, F0943, F0944, F0945, F0948, F0949, F0965, F1582, F1583, F1584, F1585, F1586, W0163, W0099, W0100, W0101, W0103, W0104, W0056, W0135, W0059, W0060, W0061, W0062, W0063, W0135, W0067, W0068, W0070, W0072, W0073, F0956, W0128, W0137, F0740, F0741, W0125, W0157, W0156, W0155, W0158, W0159, W0154, W0075, F2136, W0049, W0119, W0120, W0118, W0122, W0123, W0124, F0996, W0097, W0161, W0135, W0137, W0129, W0136, W0178, W0140, W0139, W0147, W0148, W0149, W0130, W0143, W0142, W0146, W0144, W0141</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The Web Dynpro apps are the successor apps and they are already the default tiles on the SAP Fiori launchpad. We recommend that you switch to the successor apps as soon as possible.</p>\r\n<p>Complete lists of the deprecated apps and their successor apps are attached to this note:</p>\r\n<ul>\r\n<li>Customers on an SAP S/4HANA 2021 release: Please refer to \"SAP_S4HANA_2021_FPS01_Deprecated_Design_Studio_Apps.xlsx\"</li>\r\n<li>Customers on an SAP S/4HANA 2022 release: Please refer to \"SAP_S4HANA_2022_Deprecated_Design_Studio_Apps.xlsx\"</li>\r\n</ul>\r\n<p class=\"p\"><strong>Customer Design Studio Apps</strong></p>\r\n<p class=\"p\">Due to different technologies, you need to recreate variants in SAP Web Dynpro apps that you might have created in SAP Design Studio apps using the View Browser.</p>\r\n<p class=\"p\">------------------------------------------------------------------------------</p>\r\n<p class=\"p\">Unfortunately, the custom design studio apps cannot be migrated automatically to Web Dynpro apps. You need to recreate them as Web Dynpro reports using the View Browser.<br />For more information, see&#160;https://help.sap.com/viewer/6b356c79dea443c4bbeeaf0865e04207/Latest/en-US/0bde695751505c08e10000000a441470.html?q=View%20Browser.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BI-RA-AD-EA ( Embedded Analytics for S/4)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028147)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D041515)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0003081996/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003081996/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_S4HANA_2021_FPS01_Deprecated_Design_Studio_Apps.xlsx", "FileSize": "24", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001765472021&iv_version=0013&iv_guid=0090FAAA5DF81EED8BD2DEF284282BE8"}, {"FileName": "SAP_S4HANA_2022_Deprecated_Design_Studio_Apps_updated_2023-11-21.xlsx", "FileSize": "24", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125900001765472021&iv_version=0013&iv_guid=00109B36D62A1EDEA2A09629A8A50FD2"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3366644", "RefComponent": "LO-AB", "RefTitle": "Analyze Detailed Statement Fiori apps are showing as Deprecated in the SAP Fiori Application Reference Library", "RefUrl": "/notes/3366644 "}, {"RefNumber": "3059651", "RefComponent": "BW-RUI-FPM", "RefTitle": "Deprecation of Design Studio Apps - more info", "RefUrl": "/notes/3059651 "}, {"RefNumber": "3170381", "RefComponent": "BW-RUI-FPM", "RefTitle": "Deletion of SAP Design Studio Apps in SAP S/4HANA 2022", "RefUrl": "/notes/3170381 "}, {"RefNumber": "3133221", "RefComponent": "CA-UX", "RefTitle": "S4TWL - Deprecation of Design Studio Apps", "RefUrl": "/notes/3133221 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "106", "To": "106", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}