{"Request": {"Number": "2864638", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 475, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002102702019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002864638?language=E&token=AD708A4502DCB05F865F008EB6C8342B"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002864638", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2864638"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "22.01.2020"}, "SAPComponentKey": {"_label": "Component", "value": "EHS-MGM-PRC-IMD"}, "SAPComponentKeyText": {"_label": "Component", "value": "IMDS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Environment, Health, and Safety / Product Compliance", "value": "EHS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP EHS Management", "value": "EHS-MGM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Product Compliance Management", "value": "EHS-MGM-PRC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "IMDS", "value": "EHS-MGM-PRC-IMD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'EHS-MGM-PRC-IMD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2864638 - IMDS Import Short Dump - DATAREF_NOT_ASSIGNED"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n\r\n<p>You are using the&#160;Product Compliance&#160;functionality.</p>\r\n<p>You encounter either of the following behavior:</p>\r\n<ul>\r\n<li>You import Material Datasheets (MDS) in the IMDS Supplier Center or IMDS Customer Center. The import fails fatally with a short dump of type<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em> &#65279;DATAREF_NOT_ASSIGNED</em> in ABAP program&#160;<em>CL_EHPRC_COD_DATA_HELPER======CP&#65279;</em></span>.</li>\r\n<li>You have scheduled a job to follow up on compliance data changes having been planned for the current day (i.e. review of a released compliance status, exemptions becoming invalid, ...). The job fails with short dump like: <br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>&#65279;Runtime Errors MESSAGE_TYPE_X</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>...</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>Application Information:</em></span><br /><span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\"><em>While executing validation \"REQ_REVISION_CHK_CONS\" of node \"REQ_REVISION\" of business object \"EHPRC_COMPLIANCE_DATA\" an exception has occured. ... Association \"TO_ROOT\" not found...&#65279;</em></span></li>\r\n</ul>\r\n<p>&#160;</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n\r\n<p>International Material Data System, EHPRC_CPM_IMPORT 148, AO 556, ACP, EHPRC_SUPPLIER_RESPONSE, EHPRC_COMPLIANCE_DATA, SUBSTANCE_ID,&#160;R_EHPRC_ACP_WORKLIST_FUTUR_CHG</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n\r\n<p>The symptom is caused by a data inconsistency.</p>\r\n<p>Prerequisites can be found in the relevant correction instructions.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n\r\n<p>Implement this SAP Note or import either the corresponding Support Packages for the component extension of SAP EHS Management or the Feature Packages Stacks for SAP S/4HANA.</p>\r\n\r\n<p>Execute program R_EHPRC_RC1PHDEL to identify and delete the inconsistent data from your system. The program searches for all compliance data objects referring to an invalid or no specification record. Each of these compliance data objects is deleted and following message is reported: <em>Specification &#65279;&#65279;<span class=\"SNO_DNT\" translate=\"no\" style=\"float: none;\">xyz</span>&#65279; was not found.&#160;</em>Additionally the program identifies and removes references to any deleted compliance data object.</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span>&#160;It is strongly recommended to schedule the program R_EHPRC_RC1PHDEL to run regularly. Not following this recommendation would increase the run time of this program significantly.</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D054665)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D054619)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002864638/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002864638/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2799894", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Inconsistency After Deletion of Compliance Data Objects", "RefUrl": "/notes/2799894"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3171177", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Deletion of Compliance Data Objects", "RefUrl": "/notes/3171177 "}, {"RefNumber": "2981749", "RefComponent": "EHS-MGM-PRC", "RefTitle": "Icons of full material declaration disappear", "RefUrl": "/notes/2981749 "}, {"RefNumber": "2147718", "RefComponent": "XX-SER-REL", "RefTitle": "Component Extension 6.0 for SAP EHS Management: RIN", "RefUrl": "/notes/2147718 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "103", "To": "103", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "400", "To": "400", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "EHSM", "From": "600", "To": "600", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10010INS4CORE", "URL": "/supportpackage/SAPK-10010INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10108INS4CORE", "URL": "/supportpackage/SAPK-10108INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 102", "SupportPackage": "SAPK-10206INS4CORE", "URL": "/supportpackage/SAPK-10206INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 103", "SupportPackage": "SAPK-10304INS4CORE", "URL": "/supportpackage/SAPK-10304INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 104", "SupportPackage": "SAPK-10402INS4CORE", "URL": "/supportpackage/SAPK-10402INS4CORE"}, {"SoftwareComponentVersion": "EHSM 400", "SupportPackage": "SAPK-40007INEHSM", "URL": "/supportpackage/SAPK-40007INEHSM"}, {"SoftwareComponentVersion": "EHSM 500", "SupportPackage": "SAPK-50006INEHSM", "URL": "/supportpackage/SAPK-50006INEHSM"}, {"SoftwareComponentVersion": "EHSM 600", "SupportPackage": "SAPK-60006INEHSM", "URL": "/supportpackage/SAPK-60006INEHSM"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 5, "URL": "/corrins/0002864638/19773"}, {"SoftwareComponent": "EHSM", "NumberOfCorrin": 3, "URL": "/corrins/0002864638/9587"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 8, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 8, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "1924096 ", "URL": "/notes/1924096 ", "Title": "Test data export does not export any data", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "1931706 ", "URL": "/notes/1931706 ", "Title": "Material is validated against transient field", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2135673 ", "URL": "/notes/2135673 ", "Title": "Report R_EHPRC_TEST_CDO_DELETE deletes all ACP entries", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2313401 ", "URL": "/notes/2313401 ", "Title": "ACP: Correction in Message Handling", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "400", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2699539 ", "URL": "/notes/2699539 ", "Title": "Deletion of Compliance Data Objects Does Not Care For Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "400", "ValidTo": "600", "Number": "2711846 ", "URL": "/notes/2711846 ", "Title": "Testmode Deletes Specifcations of Compliance Data Object", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2135673 ", "URL": "/notes/2135673 ", "Title": "Report R_EHPRC_TEST_CDO_DELETE deletes all ACP entries", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2313401 ", "URL": "/notes/2313401 ", "Title": "ACP: Correction in Message Handling", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "500", "ValidTo": "500", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2313401 ", "URL": "/notes/2313401 ", "Title": "ACP: Correction in Message Handling", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "EHSM", "ValidFrom": "600", "ValidTo": "600", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2313401 ", "URL": "/notes/2313401 ", "Title": "ACP: Correction in Message Handling", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "102", "Number": "2699539 ", "URL": "/notes/2699539 ", "Title": "Deletion of Compliance Data Objects Does Not Care For Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "103", "Number": "2711846 ", "URL": "/notes/2711846 ", "Title": "Testmode Deletes Specifcations of Compliance Data Object", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "101", "ValidTo": "101", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "102", "ValidTo": "102", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2684509 ", "URL": "/notes/2684509 ", "Title": "Deletion report does not consider IMDS data model tables", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2699539 ", "URL": "/notes/2699539 ", "Title": "Deletion of Compliance Data Objects Does Not Care For Material Data Sheets", "Component": "EHS-MGM-PRC-IMD"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "103", "ValidTo": "103", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "104", "ValidTo": "104", "Number": "2799894 ", "URL": "/notes/2799894 ", "Title": "Inconsistency After Deletion of Compliance Data Objects", "Component": "EHS-MGM-PRC"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2799894", "RefTitle": "Inconsistency After Deletion of Compliance Data Objects", "RefUrl": "/notes/0002799894"}]}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2888423", "RefTitle": "Dump: Association \"TO_ROOT\" not found", "RefUrl": "/notes/0002888423"}]}}}}}