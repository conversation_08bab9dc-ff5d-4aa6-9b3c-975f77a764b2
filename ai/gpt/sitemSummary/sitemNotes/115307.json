{"Request": {"Number": "115307", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 346, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000473632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=D36F13EC1753AD7BC0AC675A6E13F33D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "115307"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.01.1999"}, "SAPComponentKey": {"_label": "Component", "value": "MM-PUR-VM-SET"}, "SAPComponentKeyText": {"_label": "Component", "value": "Subsequent Settlement"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Materials Management", "value": "MM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Purchasing", "value": "MM-PUR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Vendor-Material Relationships and Conditions", "value": "MM-PUR-VM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Subsequent Settlement", "value": "MM-PUR-VM-SET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'MM-PUR-VM-SET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "115307 - Updating business volumes for settled condition records"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>After the settlement of a condition record of the subsequent settlement for an arrangement to be settled by period, updating of the business volume is performed (for invoice receipt) with document date in the past. The period condition record corresponding to the document date has already been settled.<br />The business volumes are assigned to the settled period condition record and are therefore lost for the partial settlements of the arrangement.Only the final settlement based on the total sales in the arrangement period settles this business volume data.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Subsequent settlement (volume-based rebate) in purchasing, updating of business volume, arrangement</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The price determination of the purchase order item determines by means of the pricing date whether the purchase order item is relevant for an arrangement of the subsequent settlement.During invoice receipt, update is performed based on the price determination of the purchase order item.<br />For arrangements to be settled by period(arrangements with settlement calendar), you have the problem that the period-specific condition (at the date of price determination) determined in the purchase order item might not be current anymore and, in particular, that it might have already been settled.<br />As a result, the period condition record for arrangements to be settled by period is determined again by means of the document date of the invoice verification document. That is, the business volumes are assigned to the period condition record at the document date of invoice receipt and not to the period condition record for the purchase order item (price determination date).<br />Example:Arrangement to be settled monthly, purchase order item is created in August, period-specific condition for month August is determined in price determination, invoice receipt in September with document date in September.Business volume data should be assigned to month September.<br />Our aim is a realtime settlement of business volume data with the next settlement run.<br />If the period-specific condition has already been settled at the document date of the invoice receipt, yet the business volumes are assigned to this condition record (this is programmed this way in Release 3.0).<br />In the above example:Invoice receipt in September with document date in August, period-specific condition August has already been settled, business volume data nevertheless is assigned to August.The assignment to month September would be more useful.<br />The business volumes are therefore only settled in the final settlement of the arrangement (providing that this is provided, indicator of arrangement type).<br />It would be more useful to assign the business volumes to the next period not settled (this is programmed this way as of Release 4.0A).</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Carry out the attached correction or import the Support Package appropriate for your release.<br />You can check your arrangements automatically on updates for settled condition records using report RINCOME, see Note 98586. The report checks whether the income corresponds to the scale and condition basis (that is, the business volume data). In the case of deviations, the corresponding condition records are set again to status active so that a new settlement is possible.<br />During this settlement the correct income is determined.Already settled incomes are offset automatically.The system treats the settlement that has already been performed as an interim settlement.<br />An interim settlement is a settlement in which the status of the condition records remains active so that another settlement is possible (advance settlement at the runtime of a condition record).<br />Note:<br />However, for arrangements to be settled periodically which not yet have the status \"final settlement effected\", the business volume difference is also taken into account automatically for the final settlement.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D023678)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "98586", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 3.0", "RefUrl": "/notes/98586"}, {"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147"}, {"RefNumber": "133601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error of NAA040, NAA041 and MN272 in invoice verification", "RefUrl": "/notes/133601"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "40147", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Collective note: Subsequent settlement (Purch.) Release 3.0", "RefUrl": "/notes/40147 "}, {"RefNumber": "133601", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Error of NAA040, NAA041 and MN272 in invoice verification", "RefUrl": "/notes/133601 "}, {"RefNumber": "98586", "RefComponent": "MM-PUR-VM-SET", "RefTitle": "Analysis reports, repair reports, Release 3.0", "RefUrl": "/notes/98586 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "30F", "To": "31I", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F56", "URL": "/supportpackage/SAPKH30F56"}, {"SoftwareComponentVersion": "SAP_APPL 30F", "SupportPackage": "SAPKH30F46", "URL": "/supportpackage/SAPKH30F46"}, {"SoftwareComponentVersion": "SAP_HR 30F", "SupportPackage": "SAPKE30F56", "URL": "/supportpackage/SAPKE30F56"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H29", "URL": "/supportpackage/SAPKH31H29"}, {"SoftwareComponentVersion": "SAP_APPL 31H", "SupportPackage": "SAPKH31H38", "URL": "/supportpackage/SAPKH31H38"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H29", "URL": "/supportpackage/SAPKE31H29"}, {"SoftwareComponentVersion": "SAP_HR 31H", "SupportPackage": "SAPKE31H38", "URL": "/supportpackage/SAPKE31H38"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I14", "URL": "/supportpackage/SAPKH31I14"}, {"SoftwareComponentVersion": "SAP_APPL 31I", "SupportPackage": "SAPKH31I07", "URL": "/supportpackage/SAPKH31I07"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I14", "URL": "/supportpackage/SAPKE31I14"}, {"SoftwareComponentVersion": "SAP_HR 31I", "SupportPackage": "SAPKE31I07", "URL": "/supportpackage/SAPKE31I07"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_APPL", "NumberOfCorrin": 1, "URL": "/corrins/**********/1"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}