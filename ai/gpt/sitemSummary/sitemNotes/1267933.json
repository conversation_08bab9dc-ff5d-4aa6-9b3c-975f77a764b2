{"Request": {"Number": "1267933", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 435, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016659512017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001267933?language=E&token=958375288B1B7EB22473B2A182204D07"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001267933", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001267933/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1267933"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 16}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "02.10.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-DB2"}, "SAPComponentKeyText": {"_label": "Component", "value": "DB2 for z/OS"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "DB2 for z/OS", "value": "BC-DB-DB2", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-DB2*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1267933 - DB2-z/OS: J2EE server - update database statistics"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The following symptoms have been observed:</p> <UL><LI>Bad performance of the J2EE server</LI></UL> <UL><LI>The startup of the J2EE server takes very long.</LI></UL> <UL><LI>Long deployment times during installation, update or upgrade. Due to table changes and new tables the startup of the J2EE engine or shadow engine takes very long (Phases START_SHD_J2EE_DM, DEPLOY_ONLINE_SHD_DM).</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>DB2, z/OS,<br />J2EE, table statistics, upgrade, installation, ehp installer<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Usually the above symptoms are caused by the fact that the table statistics within the database are incorrect or have not been gathered yet. This leads to inappropriate access paths to the tables and long-running SELECT statements.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Proceed as follows:</p> <OL>1. Download the attached archive UPDSTATS.SAR to the application server.</OL> <OL>2. Extract the package using the command SAPCAR -xvf UPDSTATS.SAR. It creates a directory update_statistics which contains the following files:</OL> <UL><LI>db2radm.properties = parameter file</LI></UL> <UL><LI>run_update.bat = windows script</LI></UL> <UL><LI>run_update.csh = UNIX script</LI></UL> <OL>3. Download db2radm.jar, which is attached to note 1297362 \"DB2 z/OS: db2radm.jar patches\" and put it also into directory update_statistics.</OL> <OL>4. Switch to the newly created directory update_statistics.</OL> <OL>5. Check whether the environment variable CLASSPATH is set and contains the following jar files:</OL> <UL><LI>db2jcc.jar or db2jcc4.jar</LI></UL> <UL><LI>db2jcc_license_cisuz.jar</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this is not the case remove the comment pattern '#' (UNIX) or 'REM' (Windows) from the script files run_update.csh (UNIX) or run_update.bat (Windows) <B>and adjust the parameter SAPSID</B>.<br />In most of the cases this sets the CLASSPATH correctly. If this is not the case you have to correct the CLASSPATH setting within the script file. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;db2jcc4.jar requires java 1.6. Make sure that correct java version is invoked by the script file. Add fully qualified java path if needed. <OL>6. Adjust/set the parameters in the parameter file db2radm.properties</OL> <UL><LI>URL = can be obtained by calling the configtool and accessing the secure store area (parameter jdbc/pool/&lt;SAPSID&gt;/Url)</LI></UL> <UL><LI>Administrator = database administrator user</LI></UL> <UL><LI>AdministratorPassword = password of the database administrator</LI></UL> <UL><LI>Schema = schema of the J2EE instance; usually set within the URL with parameter currentSQLID; if there is no currentSQLID setting the schema is identical to the parameter jdbc/pool/&lt;SAPSID&gt;/User within the secure store. If you are performing an upgrade and the performance issue occurs during the start of the shadow instance or deployment the schema is still the original schema since the shadow instance contains aliases only.</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Examples (don't forget to substitute MYDBAUSER/MYDBAPWD by your DBA user and password!): <OL><OL>a) Secure store setting (You could get your URL and schema form there):<br />jdbc/pool/BK1/Url=****************************:<br />&#x00A0;&#x00A0;keepDynamic=yes;currentPackageSet=SAPJBK1FT53;<br />&#x00A0;&#x00A0;currentSQLID=SAPJ2;<br />jdbc/pool/BK1/User=SAPJAVA<br /><br />==&gt; db2radm.properties entries:<br />OutputFile=DB2STATS.LOG<br />URL=****************************:currentPackageSet=SAPJBK1FT53; <br />Administrator=MYDBAUSER<br />AdministratorPassword=MYDBAPWD<br />Schema=SAPJ2</OL></OL> <OL><OL>b) Secure store settings:<br />jdbc/pool/BK1/Url=****************************:<br />&#x00A0;&#x00A0;keepDynamic=yes;currentPackageSet=SAPJBK1FT53;<br />jdbc/pool/BK1/User=SAPBK1DB<br /><br />==&gt; db2radm.properties entries:<br />OutputFile=DB2STATS.LOG<br />URL=****************************:currentPackageSet=SAPJBK1FT53; <br />Administrator=MYDBAUSER<br />AdministratorPassword=MYDBAPWD<br />Schema=SAPBK1DB</OL></OL> <OL>7. If you are performing an upgrade and the performance issue is to shadow schema statistics, add a breakpoint to update the statistics:</OL> <UL><LI>Create or edit the phase phaselist.brk in folder upg/java/param.</LI></UL> <UL><LI>Add one line with the name of the phase where the break should happen (for example START_SHD_J2EE_DM).</LI></UL> <UL><LI>Perform the statistics update by calling the script (see below); after that go on with your upgrade.</LI></UL> <OL>8. If you are performing an installation/deployment, perform the statistics update by calling the script; then retry starting your J2EE engine.</OL> <OL>9. Call the script run_update.csh (UNIX) or run_update.bat (Windows).</OL> <p><br />The tool automatically performs RUNSTATS on all tablespaces that belong to the schema specified within db2radm.properties. The output is <B>appended</B> to file DB2STATS.LOG located in the directory update_statistics (specified with db2radm.properties parameter OutputFile).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D022631)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D022624)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001267933/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "UPDSTATS.SAR", "FileSize": "1", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000712562008&iv_version=0016&iv_guid=1F4FC8E2E82DE645AA2BB07CAF133A59"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "943141", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW 7.1", "RefUrl": "/notes/943141"}, {"RefNumber": "815202", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW AS 7.0", "RefUrl": "/notes/815202"}, {"RefNumber": "1926798", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1926798"}, {"RefNumber": "1878395", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1878395"}, {"RefNumber": "1844903", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1844903"}, {"RefNumber": "1836420", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1836420"}, {"RefNumber": "1775281", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1775281"}, {"RefNumber": "1763990", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1763990"}, {"RefNumber": "1710332", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1710332"}, {"RefNumber": "1691951", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1691951"}, {"RefNumber": "1612958", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1612958"}, {"RefNumber": "1503070", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "Inst. Systems Based on NW 7.3 on IBM DB2 for z/OS", "RefUrl": "/notes/1503070"}, {"RefNumber": "1297362", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: db2radm.jar patches", "RefUrl": "/notes/1297362"}, {"RefNumber": "1175848", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1175848"}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185"}, {"RefNumber": "1127815", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions EhP installer / SUM", "RefUrl": "/notes/1127815"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3346502", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP38 - covers no longer supported Operating Systems", "RefUrl": "/notes/3346502 "}, {"RefNumber": "3220901", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 SP35 - covers no longer supported CPU and operation system versions", "RefUrl": "/notes/3220901 "}, {"RefNumber": "2595196", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *70JDS*.SAR of Software Provisioning Manager 1.0 - Covers No Longer Supported Java and Dual-Stack Options and Access to Guides for Software Provisioning Manager 1.0 Java and Dual Stack", "RefUrl": "/notes/2595196 "}, {"RefNumber": "2505142", "RefComponent": "BC-INS-RMP", "RefTitle": "Release Note for *RMOS*.SAR of Software Provisioning Manager 1.0 - covers no longer supported operation system versions", "RefUrl": "/notes/2505142 "}, {"RefNumber": "1680045", "RefComponent": "BC-INS-SWPM", "RefTitle": "Release Note for Software Provisioning Manager 1.0 (recommended: SWPM 1.0 SP40)", "RefUrl": "/notes/1680045 "}, {"RefNumber": "1127815", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions EhP installer / SUM", "RefUrl": "/notes/1127815 "}, {"RefNumber": "1297362", "RefComponent": "BC-DB-DB2", "RefTitle": "DB2 z/OS: db2radm.jar patches", "RefUrl": "/notes/1297362 "}, {"RefNumber": "1503070", "RefComponent": "BC-DB-DB2-INS", "RefTitle": "Inst. Systems Based on NW 7.3 on IBM DB2 for z/OS", "RefUrl": "/notes/1503070 "}, {"RefNumber": "1156185", "RefComponent": "BC-UPG-RDM", "RefTitle": "Central Note: Upgrade to Systems on SAP NetWeaver 7.1 EHP 1", "RefUrl": "/notes/1156185 "}, {"RefNumber": "815202", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW AS 7.0", "RefUrl": "/notes/815202 "}, {"RefNumber": "943141", "RefComponent": "BC-UPG-RDM", "RefTitle": "DB2-z/OS: Additions upgrade to SAP NW 7.1", "RefUrl": "/notes/943141 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}