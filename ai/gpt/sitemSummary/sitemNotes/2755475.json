{"Request": {"Number": "2755475", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 602, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000351612019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=89DC37AB5202779BC76B290045313588"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2755475"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.10.2019"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP S/4HANA Data Migration Cockpit Content", "value": "CA-GTF-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2755475 - SAP S/4HANA Migration Cockpit: Central correction Note for content issues for SAP S/4HANA 1709"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<div class=\"longtext\">\r\n<p>You have an issue with the SAP S/4HANA Data Migration content delivered for SAP S/4HANA 1709 that is related to an error with the content</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Bank Master, Fixed Asset, Cost center in Fixed Asset</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>You have installed SAP S/4HANA 1709 SPS3</p>\r\n<p>You are using SAP S/4HANA migration cockpit</p>\r\n<p>You are using the pre-delivered SAP S/4HANA Data migration content without any modifications</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The TCI&#160;included with that SAP Note fixes the following issues listed in the table below. For detailed description on the issues see the linked SAP Notes.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Technical Name</td>\r\n<td>Type</td>\r\n<td>Issue</td>\r\n<td>SAP Note</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_BANK_MASTER</td>\r\n<td>Migration Object</td>\r\n<td>Length of the field 'Bank branch' updated from 15 to 40 in Bank Master</td>\r\n<td><a target=\"_blank\" href=\"/notes/2756499\">2756499</a></td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration Object</td>\r\n<td>Low performance during migration simulation</td>\r\n<td><a target=\"_blank\" href=\"/notes/2701197\">2701197</a></td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration Object</td>\r\n<td>Fixed Asset migration - field Cost Center must be optional</td>\r\n<td><a target=\"_blank\" href=\"/notes/2753883\">2753883</a></td>\r\n</tr>\r\n<tr>\r\n<td>MAP_KUKLA&#160;</td>\r\n<td>Translation Object</td>\r\n<td>\r\n<p>Mapping of Customer classification is not filled with the values of migration template</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"/notes/2644750\">2644750</a></td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_CUST_EXTEND</p>\r\n</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>Wrong message thrown in message log</p>\r\n</td>\r\n<td><a target=\"_blank\" href=\"/notes/2699116\">2699116</a></td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that the TCI only corrects the related objects of SAP delivered content. As a result, the generated migration objects will be updated automatically.</p>\r\n<p><strong>Note:</strong> If you have modified/copied your object, the correction will not be done within your modified/copied object.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I307134)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I307134)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2756499", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Bank Master - Truncated value in field 'Bank branch'", "RefUrl": "/notes/2756499"}, {"RefNumber": "2755460", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2019 - Central correction Note for content issues for SAP S/4HANA 1809 (I)", "RefUrl": "/notes/2755460"}, {"RefNumber": "2753883", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Assets - Cost center is a mandatory field", "RefUrl": "/notes/2753883"}, {"RefNumber": "2701197", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Asset migration -  Low performance during migration simulation", "RefUrl": "/notes/2701197"}, {"RefNumber": "2699116", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Customer Extend - Throws wrong message", "RefUrl": "/notes/2699116"}, {"RefNumber": "2672778", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Customer, Supplier - Data Definition of Sender fields", "RefUrl": "/notes/2672778"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3051389", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Customer extend: You can only flag 1 payment card as a standard", "RefUrl": "/notes/3051389 "}, {"RefNumber": "2832778", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: FI - Accounts payable & Accounts receivable open item - Target value is not valid for domain BUPLA", "RefUrl": "/notes/2832778 "}, {"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "2644750", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit - Convert values \"Mapping of Customer classification\" MAP_KUKLA not filled for SIF_CUSTOMER", "RefUrl": "/notes/2644750 "}, {"RefNumber": "2756499", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Bank Master - Truncated value in field 'Bank branch'", "RefUrl": "/notes/2756499 "}, {"RefNumber": "2631870", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: PM/EAM objects - Truncated values in field 'Functional location'", "RefUrl": "/notes/2631870 "}, {"RefNumber": "2753883", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Fixed Assets - Cost center is a mandatory field", "RefUrl": "/notes/2753883 "}, {"RefNumber": "2755460", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: MAY 2019 - Central correction Note for content issues for SAP S/4HANA 1809 (I)", "RefUrl": "/notes/2755460 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "102", "To": "102", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/**********/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}