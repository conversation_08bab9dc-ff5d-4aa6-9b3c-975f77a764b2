{"Request": {"Number": "2356929", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 447, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000013915062017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002356929?language=E&token=81448E0BB762DD0435EB01C0BE5324E1"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002356929", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002356929/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2356929"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.07.2017"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-B"}, "SAPComponentKeyText": {"_label": "Component", "value": "Transaction Figures / Valuation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Transaction Figures / Valuation", "value": "FI-AA-AA-B", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-B*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2356929 - Legacy data transfer: Period in which the depreciation was posted not taken into account/AA026 for reversal"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>\r\n<li>You have created a legacy asset and posted to it, but you have not entered any depreciations already posted in the source system. You notice that the system has created plan values for periods &lt;= the \"Period in which the depreciation was posted\" that is defined in Customizing.</li>\r\n<li>When you try to reverse the transaction from the legacy data transfer at the end of the fiscal year, the system issues error message AA 026 (\"Asset has no values in fiscal year &amp;\").</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>AB08, AS91, ABLDT, ABZON, AW01N, Asset Explorer, reporting, asset history sheet</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Program error</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Implement the attached program correction.</p>\r\n<p>For fixed assets that terminate with error AA026 during a reversal, use the attached correction report RACORR_INS_YDDA_LDT_EFY (see the attachments).</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-AA-AA-C (Transactions)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D018576)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D021112)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002356929/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002356929/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "RACORR_INS_YDDA_LDT_EFY.txt", "FileSize": "9", "MimeType": "text/plain", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001247102016&iv_version=0006&iv_guid=6CAE8B3EA6731ED79CBD0705987C20CC"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2208321", "RefComponent": "FI-AA-AA", "RefTitle": "FAQ for legacy data transfer in SAP_FIN 720 and subsequent releases", "RefUrl": "/notes/2208321 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_FIN", "From": "720", "To": "720", "Subsequent": ""}, {"SoftwareComponent": "SAP_FIN", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAPSCORE", "From": "106", "To": "106", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "100", "To": "100", "Subsequent": ""}, {"SoftwareComponent": "S4CORE", "From": "101", "To": "101", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "S4CORE 100", "SupportPackage": "SAPK-10003INS4CORE", "URL": "/supportpackage/SAPK-10003INS4CORE"}, {"SoftwareComponentVersion": "S4CORE 101", "SupportPackage": "SAPK-10101INS4CORE", "URL": "/supportpackage/SAPK-10101INS4CORE"}, {"SoftwareComponentVersion": "SAP_FIN 720", "SupportPackage": "SAPK-72006INSAPFIN", "URL": "/supportpackage/SAPK-72006INSAPFIN"}, {"SoftwareComponentVersion": "SAP_FIN 730", "SupportPackage": "SAPK-73005INSAPFIN", "URL": "/supportpackage/SAPK-73005INSAPFIN"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_FIN", "NumberOfCorrin": 2, "URL": "/corrins/0002356929/15841"}, {"SoftwareComponent": "S4CORE", "NumberOfCorrin": 2, "URL": "/corrins/0002356929/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2297667 ", "URL": "/notes/2297667 ", "Title": "Legacy data transfer: Depreciation posted-to date is not processed correctly", "Component": "FI-AA"}, {"SoftwareComponent": "S4CORE", "ValidFrom": "100", "ValidTo": "100", "Number": "2346170 ", "URL": "/notes/2346170 ", "Title": "ABLDT: Runtime error GETWA_NOT_ASSIGNED in CL_FAA_MDO_ITEM", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "720", "ValidTo": "720", "Number": "2297667 ", "URL": "/notes/2297667 ", "Title": "Legacy data transfer: Depreciation posted-to date is not processed correctly", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2297667 ", "URL": "/notes/2297667 ", "Title": "Legacy data transfer: Depreciation posted-to date is not processed correctly", "Component": "FI-AA"}, {"SoftwareComponent": "SAP_FIN", "ValidFrom": "730", "ValidTo": "730", "Number": "2329452 ", "URL": "/notes/2329452 ", "Title": "Conversion of the infrastructure for test automation", "Component": "FI-AA-AA"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2362634", "RefTitle": "Legacy data transfer: Period of posted depreciation not taken into account everywhere", "RefUrl": "/notes/**********"}, {"RefNumber": "2402477", "RefTitle": "Balance carryforward leads to inconsistencies for fixed assets from legacy data transfer", "RefUrl": "/notes/**********"}]}}}}}