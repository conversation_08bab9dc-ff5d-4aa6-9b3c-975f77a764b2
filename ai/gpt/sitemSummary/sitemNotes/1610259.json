{"Request": {"Number": "1610259", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 481, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000017276772017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001610259?language=E&token=EAC3C5F591A8B1DAA4239CBE36246914"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001610259", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001610259/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1610259"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 8}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "21.09.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DST-UPD"}, "SAPComponentKeyText": {"_label": "Component", "value": "Update Rules"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Staging", "value": "BW-WHM-DST", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Update Rules", "value": "BW-WHM-DST-UPD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DST-UPD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1610259 - Control of return code behavior in update rules"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>As a result of the implementation of this SAP Note, the return code in update rules for releases &lt; BW 7.30 is interpreted in the same way as for the routines in transformations. This simplifies a migration of the update rules to a transformation.<br /><br />You can activate the BW 7.30 standard system behavior by implementing this note in BW Releases 7.00, 7.01, 7.02, 7.11. This also applies to releases above BW 7.30.<br /><br />For BW Release 7.30 and above, you can use this SAP Note to restore the old system behavior.<br /><br />With this SAP Note, the different handling of 'RETURNCODE &lt;&gt; 0' with and without an error message in update rules can be deactivated for BW &lt; 7.30 so that error handling in transfer rules then behaves like the transformation logic.<br /><br />In the case of a data status of this kind, a routine error is triggered and the message RSAU 727 'Routine &amp;, return code = &amp; without error message. See Note 1530791' is sent.</p>\r\n<p>If the 'SKIP Value' behavior is desired in&#x00A0;BW 7.30 or above, this can also be realized by the initialization of the return parameter 'RESULT' in the transformation rule routine.</p>\r\n<p>As of SAP Note 2293381, RSAU 727 default error handling as per BW 7.30 or above has been adopted for the migration of update rules to a transformation.&#x00A0;The settings of this consulting note are used for the migration so that the error handling of the corresponding transformation and transfer rule should normally behave in the same way.<br />The system response is frozen in accordance with the current settings when the transformation is generated. Unlike for transfer rules, a later change to the settings has no effect on error handling for transformations.&#x00A0;<br /><br />The documentation adjustment in the data element 'RSROUTTXTLG' (description of the routine parameters) is contained in the delivery of SAP Note 1610009.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RSAU727, RSAU499, RSAU 499, UPDATE_INFOCUBE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Prerequisite: You have implemented Note 1610009 or imported the relevant BW Support Package.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The described function is activated across the entire system as follows for BW 7.00, 7.01, 7.02, and 7.11:</p>\r\n<ul>\r\n<li>Execute the program 'SAP_RSADMIN_MAINTAIN'.</li>\r\n</ul>\r\n<ul>\r\n<li>In the 'OBJECT' field, enter the value RSAU_RETURNCODE_NEW, and in the 'VALUE' field, enter the value X.</li>\r\n</ul>\r\n<p><br />You can restore the old logic across the entire system as follows for BW 7.30 or above:</p>\r\n<ul>\r\n<li>Execute the program 'SAP_RSADMIN_MAINTAIN'.</li>\r\n</ul>\r\n<ul>\r\n<li>In the 'OBJECT' field, enter the value RSAU_RETURNCODE_OLD, and in the 'VALUE' field, enter the value X.</li>\r\n</ul>\r\n<p><br />Note that activation must take place in all of the relevant systems.</p>\r\n<ul>\r\n<li>The change of the return code behavior takes effect after the relevant update rule is regenerated.</li>\r\n</ul>\r\n<ul>\r\n<li>You can force a regeneration of all update rules as follows:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Call transaction RSSGPCLA.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Select the program class RSAUTMPLUR (Generated Update Programs).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Choose \"Set Status\" (Ctrl+F3).</li>\r\n</ul>\r\n</ul>\r\n<p>The change can be<br />transported via the object <strong>R3TR TABU RSADMIN</strong> and the <strong>object key RSAU_RETURNCODE_NEW or RSAU_RETURNCODE_OLD</strong>.<br /><br />Transporting this type of RSADMIN entry is at your own risk. Note particularly that completely transporting the contents of the table RSADMIN may damage the receiving systems irreparably.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D002702"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I829561)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001610259/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001610259/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1610009", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Return code in update rules: Error RSAU 727", "RefUrl": "/notes/1610009"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2470352", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Data Flows (3.x)", "RefUrl": "/notes/2470352 "}, {"RefNumber": "2293381", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Migration of transfer/update rules with routines: RETURNCODE of routines is interpreted incorrectly", "RefUrl": "/notes/2293381 "}, {"RefNumber": "1610009", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Return code in update rules: Error RSAU 727", "RefUrl": "/notes/1610009 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "711", "To": "711", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}