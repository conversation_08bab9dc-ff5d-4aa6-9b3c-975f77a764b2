{"Request": {"Number": "1034450", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 984, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016249632017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001034450?language=E&token=78ADB56C0E40C6DDF062EFF69C68E825"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001034450", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001034450/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1034450"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "27.10.2015"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1034450 - SAPBWNews BW 7.10 ABAP SP 02"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Support Package 02 for &#x00A0;SAP NetWeaver BI 7.10</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for 7.10, BW 7.10, BI 7.10, BW Patches, BI, BI 7.10, SAPBINEWS</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBINews for Support Package 02 for SAP NetWeaver 7.0. This note lists all notes that describe the corrections or enhancements contained in Support Package 02.<br />This note will be updated when other notes are added. <br /><br />The information is divided into the following areas:</p>\r\n<ul>\r\n<li><strong>Manual actions that may be necessary:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Factors you must take into account when you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors that may occur after you import the Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li><strong>General information:</strong></li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Errors corrected in this Support Package </li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Enhancements delivered with this Support Package</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>See the release and information notes.</li>\r\n</ul>\r\n</ul>\r\n<p><strong>Factors you must take into account when you import the Support Package:</strong><br /><strong>Errors that may occur after you import the Support Package:</strong></p>\r\n<p><br />- To date, no errors are known.</p>\r\n<p><strong>Errors corrected in this Support Package: </strong><br /><strong>Enhancements delivered with this Support Package:</strong></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-SYS (Basis System and Installation)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "I822646"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001034450/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001034450/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1075101", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1040293", "RefUrl": "/notes/1075101"}, {"RefNumber": "1066132", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA:Check whether request is updated has changed", "RefUrl": "/notes/1066132"}, {"RefNumber": "1053612", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correctn: Error handling prog contains incorrect source code", "RefUrl": "/notes/1053612"}, {"RefNumber": "1053436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with \"Temporal Hierarchy Join\" in JAVA Web", "RefUrl": "/notes/1053436"}, {"RefNumber": "1053308", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED in reload from archive/near-line", "RefUrl": "/notes/1053308"}, {"RefNumber": "1053084", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASSIGN_TYPE_CONFLICT in CONVERT_FROM_PSA", "RefUrl": "/notes/1053084"}, {"RefNumber": "1052660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error stack can be versioned", "RefUrl": "/notes/1052660"}, {"RefNumber": "1052645", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14:DSO:Further authorization check before request activated", "RefUrl": "/notes/1052645"}, {"RefNumber": "1052626", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1052626"}, {"RefNumber": "1052222", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchien: ABAP-Unit-Tests für DTP-Anschluß", "RefUrl": "/notes/1052222"}, {"RefNumber": "1052031", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request: Termination after verification phase", "RefUrl": "/notes/1052031"}, {"RefNumber": "1051994", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH:Selecting excluding DS/source syst combinations", "RefUrl": "/notes/1051994"}, {"RefNumber": "1051979", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Web service DataSource cannot be activated", "RefUrl": "/notes/1051979"}, {"RefNumber": "1051767", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Terminatn in RRBA_NUMBER_GET_BW \"commit connection DEFAULT\"", "RefUrl": "/notes/1051767"}, {"RefNumber": "1051664", "RefComponent": "BW-WHM", "RefTitle": "Check and repair program BW7.x for Note 849857", "RefUrl": "/notes/1051664"}, {"RefNumber": "1051652", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message: Target field is missing/dump w/ rule w/ units", "RefUrl": "/notes/1051652"}, {"RefNumber": "1051623", "RefComponent": "BW-BEX-OT", "RefTitle": "Adjustments of transaction RSTT (usability)", "RefUrl": "/notes/1051623"}, {"RefNumber": "1051614", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump occurs in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1051614"}, {"RefNumber": "1051291", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DBMAN 305: Error when reading data of InfoProvider ...$N", "RefUrl": "/notes/1051291"}, {"RefNumber": "1051246", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Cursor position gets lost in routine", "RefUrl": "/notes/1051246"}, {"RefNumber": "1051170", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "InfoObject &1 is not available in version &2", "RefUrl": "/notes/1051170"}, {"RefNumber": "1051168", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: SUCC_PRED_GET-2 in prog CL_RSR_HIERARCHY_BINCL", "RefUrl": "/notes/1051168"}, {"RefNumber": "1051127", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data in a very particular situation", "RefUrl": "/notes/1051127"}, {"RefNumber": "1051055", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "InfoSource remains in the status \"Not Executable\"", "RefUrl": "/notes/1051055"}, {"RefNumber": "1051036", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "You cannot restart change run", "RefUrl": "/notes/1051036"}, {"RefNumber": "1051005", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "After error, status of archiving request remains \"yellow\"(1)", "RefUrl": "/notes/1051005"}, {"RefNumber": "1050948", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not enough texts for RSPCVARIANTT", "RefUrl": "/notes/1050948"}, {"RefNumber": "1050889", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH-02-01", "RefUrl": "/notes/1050889"}, {"RefNumber": "1050868", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "ASSERTION_FAILED in MODIFY_SEGMENT when you activate a DAP", "RefUrl": "/notes/1050868"}, {"RefNumber": "1050783", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems in logging during removal in archive and nearline", "RefUrl": "/notes/1050783"}, {"RefNumber": "1050695", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_TABLES_NOT_COMPATIBLE", "RefUrl": "/notes/1050695"}, {"RefNumber": "1050669", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1050669"}, {"RefNumber": "1050379", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Not enough fields requested when loading w/o PSA", "RefUrl": "/notes/1050379"}, {"RefNumber": "1050330", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 14 BIA revision compatibility check extended", "RefUrl": "/notes/1050330"}, {"RefNumber": "1050327", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Non-cumulatives and formula variables from replacement path", "RefUrl": "/notes/1050327"}, {"RefNumber": "1050293", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "General pattern in InfoPackage selection is not supported", "RefUrl": "/notes/1050293"}, {"RefNumber": "1050208", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination BRAIN 299 _GET_AUTH_RESTRICT-03 in CL_RSMD_RS", "RefUrl": "/notes/1050208"}, {"RefNumber": "1050097", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Formula variable not replaced (hierarchy deactivated)", "RefUrl": "/notes/1050097"}, {"RefNumber": "1049998", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump loading data into open hub destn; incorrect error msg", "RefUrl": "/notes/1049998"}, {"RefNumber": "1049932", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error \"CX_SY_REF_IS_INITIAL\" when you go from ALV to BEx", "RefUrl": "/notes/1049932"}, {"RefNumber": "1049735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect status after deallocation", "RefUrl": "/notes/1049735"}, {"RefNumber": "1049700", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL: Dynamic hierarchy selection and new headers", "RefUrl": "/notes/1049700"}, {"RefNumber": "1049691", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in CL_RSR_RRK0_RQTS->_CHECK_DELTAPAIR_01", "RefUrl": "/notes/1049691"}, {"RefNumber": "1049623", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Scheduling if server does not exist", "RefUrl": "/notes/1049623"}, {"RefNumber": "1049564", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data with exception aggregation and two structures", "RefUrl": "/notes/1049564"}, {"RefNumber": "1049469", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Switching from ABAP program", "RefUrl": "/notes/1049469"}, {"RefNumber": "1049403", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message \"Incorrect call sequence\" when you delete", "RefUrl": "/notes/1049403"}, {"RefNumber": "1049261", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Short dump for RSRV check to compary keyfigures", "RefUrl": "/notes/1049261"}, {"RefNumber": "1049258", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Delta cache data displayed twice in certain circumstances", "RefUrl": "/notes/1049258"}, {"RefNumber": "1049209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 Komponente G_S_KENNZ-%F_$P$ unbekannt", "RefUrl": "/notes/1049209"}, {"RefNumber": "1049187", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction for CTC (creating source system in background)", "RefUrl": "/notes/1049187"}, {"RefNumber": "1049157", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect color for nodes/leaves in CCMS for BIA", "RefUrl": "/notes/1049157"}, {"RefNumber": "1049147", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Deadlock on hierarchy table", "RefUrl": "/notes/1049147"}, {"RefNumber": "1049141", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA type group on IF_RSDAI_NEARLINE_CONNECTION interface", "RefUrl": "/notes/1049141"}, {"RefNumber": "1048977", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Probleme bei Zeitstempelfortschreibung", "RefUrl": "/notes/1048977"}, {"RefNumber": "1048972", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "No authorization for a transformation that does not exist", "RefUrl": "/notes/1048972"}, {"RefNumber": "1048947", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Corr:Source syst names,pseudo D versions for transformations", "RefUrl": "/notes/1048947"}, {"RefNumber": "1048923", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in SAPLRRI2; form INPUTABLE_CHAFREE-02-", "RefUrl": "/notes/1048923"}, {"RefNumber": "1048864", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close empty requests", "RefUrl": "/notes/1048864"}, {"RefNumber": "1048690", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error messages and error logs not issued", "RefUrl": "/notes/1048690"}, {"RefNumber": "1048648", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI7.0(SP14) DataStore: Deletion of request terminates", "RefUrl": "/notes/1048648"}, {"RefNumber": "1048506", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSource vers converted during transformation", "RefUrl": "/notes/1048506"}, {"RefNumber": "1048505", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Execution authorization for generated queries with '!' signs", "RefUrl": "/notes/1048505"}, {"RefNumber": "1048504", "RefComponent": "BW-WHM-AWB", "RefTitle": "P14: Administering PSA to new DS-PSA displays incorrect icon", "RefUrl": "/notes/1048504"}, {"RefNumber": "1048502", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump 'connection closed' when window is closed", "RefUrl": "/notes/1048502"}, {"RefNumber": "1048477", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048477"}, {"RefNumber": "1048423", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "R7 105 during migration of update rules", "RefUrl": "/notes/1048423"}, {"RefNumber": "1048350", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Incorrect text in error step in error DTP", "RefUrl": "/notes/1048350"}, {"RefNumber": "1048349", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Connection errors with multiple events and collector", "RefUrl": "/notes/1048349"}, {"RefNumber": "1048345", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Check function in InfoCube administration", "RefUrl": "/notes/1048345"}, {"RefNumber": "1048344", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Checkman: Various checkman problems", "RefUrl": "/notes/1048344"}, {"RefNumber": "1048280", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Additional corrections for Note 1002682", "RefUrl": "/notes/1048280"}, {"RefNumber": "1048227", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data or too much data in rare cases (after SP13)", "RefUrl": "/notes/1048227"}, {"RefNumber": "1048178", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Default member for virtual hierarchies incorrect", "RefUrl": "/notes/1048178"}, {"RefNumber": "1048161", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Set QM status for yellow requests using funcion module", "RefUrl": "/notes/1048161"}, {"RefNumber": "1048114", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjusting RSRV checks for the BI accelerator", "RefUrl": "/notes/1048114"}, {"RefNumber": "1048100", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A299 in SAPLRRI2 and CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1048100"}, {"RefNumber": "1048095", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Checking variables and I_STEP equal to three in exit", "RefUrl": "/notes/1048095"}, {"RefNumber": "1048091", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048091"}, {"RefNumber": "1048078", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Target*___T unknown when you transport update rules", "RefUrl": "/notes/1048078"}, {"RefNumber": "1048015", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Rollup: Exception INCONSISTENT_N_O_REQS", "RefUrl": "/notes/1048015"}, {"RefNumber": "1047992", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: For internal use", "RefUrl": "/notes/1047992"}, {"RefNumber": "1047978", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy auth. and intervals: \"No authorization\", EYE 007", "RefUrl": "/notes/1047978"}, {"RefNumber": "1047953", "RefComponent": "BW-WHM-DST", "RefTitle": "Checkman reports errors", "RefUrl": "/notes/1047953"}, {"RefNumber": "1047852", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Selection in MEMBERS rowset for [measures] or structures", "RefUrl": "/notes/1047852"}, {"RefNumber": "1047840", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS + RSDS not deleted", "RefUrl": "/notes/1047840"}, {"RefNumber": "1047829", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:BATCH: Cancel work process in a 'safe' way", "RefUrl": "/notes/1047829"}, {"RefNumber": "1047776", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Too many process variants when deleting a DAP", "RefUrl": "/notes/1047776"}, {"RefNumber": "1047735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: You cannot transport interrupt", "RefUrl": "/notes/1047735"}, {"RefNumber": "1047688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query returns no data (including and excluding selection)", "RefUrl": "/notes/1047688"}, {"RefNumber": "1047598", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Manage:QM action on loaded request in DSO", "RefUrl": "/notes/1047598"}, {"RefNumber": "1047299", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays data of a deleted request", "RefUrl": "/notes/1047299"}, {"RefNumber": "1047176", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Undeclared exception in the error handling", "RefUrl": "/notes/1047176"}, {"RefNumber": "1047174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect scope when locking in process chains", "RefUrl": "/notes/1047174"}, {"RefNumber": "1047022", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect \"currentness of data\" for master data provider", "RefUrl": "/notes/1047022"}, {"RefNumber": "1046544", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems: Migrating w/source fields with fixed unit/currency", "RefUrl": "/notes/1046544"}, {"RefNumber": "1046507", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "SP 11: Error in DB connect for DB2, DB4 and MS-SQL-Server", "RefUrl": "/notes/1046507"}, {"RefNumber": "1046465", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface: Saving an InfoSource", "RefUrl": "/notes/1046465"}, {"RefNumber": "1046394", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Performance:RSSELDONE:HASH_CREATE:Index access PID", "RefUrl": "/notes/1046394"}, {"RefNumber": "1046312", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you assign key figure with fixed unit/currency", "RefUrl": "/notes/1046312"}, {"RefNumber": "1046270", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Additional fixed filter for the query runtime", "RefUrl": "/notes/1046270"}, {"RefNumber": "1046254", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Performance optimization of the verification phase", "RefUrl": "/notes/1046254"}, {"RefNumber": "1046233", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299(BRAIN) in program SAPLRRI2 and form FST_VAR_06-01-", "RefUrl": "/notes/1046233"}, {"RefNumber": "1046127", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you collect transformations", "RefUrl": "/notes/1046127"}, {"RefNumber": "1046066", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECAUTH:Cust exist variables for non-string characteristics", "RefUrl": "/notes/1046066"}, {"RefNumber": "1046003", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN X299 in SAPLRRK0; form MEGA_SORT_14-01-", "RefUrl": "/notes/1046003"}, {"RefNumber": "1045950", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RFC dump when calling FM RSS2_CALL_FCODE_EMPTY_GLOBALS", "RefUrl": "/notes/1045950"}, {"RefNumber": "1045923", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization for key figure (0TCAKYFNM) in InfoSet", "RefUrl": "/notes/1045923"}, {"RefNumber": "1045919", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No filter when accessing file DataSource directly", "RefUrl": "/notes/1045919"}, {"RefNumber": "1045861", "RefComponent": "BW-WHM-DST", "RefTitle": "Directly accessing time-dependent master data texts", "RefUrl": "/notes/1045861"}, {"RefNumber": "1045784", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14: DSO: DTP: Check on full REQU requests not required", "RefUrl": "/notes/1045784"}, {"RefNumber": "1045711", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Attributes for characteristics with temporal hierarchy join", "RefUrl": "/notes/1045711"}, {"RefNumber": "1045560", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text symbol with value for hierarchy variable", "RefUrl": "/notes/1045560"}, {"RefNumber": "1045548", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Monitoring of RDA InfoPackage requests", "RefUrl": "/notes/1045548"}, {"RefNumber": "1045305", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect record mode rule is not reported", "RefUrl": "/notes/1045305"}, {"RefNumber": "1045300", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Display of non-postable values shows SIDs w/out master data", "RefUrl": "/notes/1045300"}, {"RefNumber": "1045296", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "RSRA_CLUSTER_TABLE_REORG dumps with an error.", "RefUrl": "/notes/1045296"}, {"RefNumber": "1045174", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation/routine missing when you collect content", "RefUrl": "/notes/1045174"}, {"RefNumber": "1045114", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and near-line storage", "RefUrl": "/notes/1045114"}, {"RefNumber": "1045063", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "No quantitiy conversion (target quantity unit from variable)", "RefUrl": "/notes/1045063"}, {"RefNumber": "1045054", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource replication: Shrt dump TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1045054"}, {"RefNumber": "1045008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RRK_LIST_OPEN: CX_SY_REF_IS_INITIAL UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1045008"}, {"RefNumber": "1044991", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "OBSOLETE", "RefUrl": "/notes/1044991"}, {"RefNumber": "1044860", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "ESH Open Hub", "RefUrl": "/notes/1044860"}, {"RefNumber": "1044808", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error msg in the after import/during replication", "RefUrl": "/notes/1044808"}, {"RefNumber": "1044708", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Performance of generic time derivation", "RefUrl": "/notes/1044708"}, {"RefNumber": "1044494", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by incorrect value", "RefUrl": "/notes/1044494"}, {"RefNumber": "1044398", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE 007 \" No auth.\" for several authorizations", "RefUrl": "/notes/1044398"}, {"RefNumber": "1044378", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Delete free processes in chain deletion", "RefUrl": "/notes/1044378"}, {"RefNumber": "1044377", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process saved in scheduling", "RefUrl": "/notes/1044377"}, {"RefNumber": "1044197", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule is created twice / Note 1036440 was implemented", "RefUrl": "/notes/1044197"}, {"RefNumber": "1044181", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Too many requests selected for DTP from DWWB", "RefUrl": "/notes/1044181"}, {"RefNumber": "1044054", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Shadow DTP with DataSource cannot be displayed", "RefUrl": "/notes/1044054"}, {"RefNumber": "1044052", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DBC attribute can be changed in the DTP", "RefUrl": "/notes/1044052"}, {"RefNumber": "1043971", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination in RSDRI_CUBE_WRITE_PACKAGE for incorrect data", "RefUrl": "/notes/1043971"}, {"RefNumber": "1043919", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many authorizations w/ exclusive hierarchy authorization", "RefUrl": "/notes/1043919"}, {"RefNumber": "1043819", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No data for NON EMPTY and WHERE", "RefUrl": "/notes/1043819"}, {"RefNumber": "1043724", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump nach Eintragung eines InfoObjektes bei Stammdaten nachl", "RefUrl": "/notes/1043724"}, {"RefNumber": "1043456", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in functions BAPI_TD...", "RefUrl": "/notes/1043456"}, {"RefNumber": "1043455", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Converting the query generation", "RefUrl": "/notes/1043455"}, {"RefNumber": "1043343", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting the rule from the technical group deletes the group", "RefUrl": "/notes/1043343"}, {"RefNumber": "1043295", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Mannually interrupted run is continued", "RefUrl": "/notes/1043295"}, {"RefNumber": "1043192", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: English texts in report RSREQARCH_CHECK_HASH_ARCHIVE", "RefUrl": "/notes/1043192"}, {"RefNumber": "1043170", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Missing impact for currency translation and unit conversion", "RefUrl": "/notes/1043170"}, {"RefNumber": "1043132", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Toggle \"Use Temporal Hierarchy Join\" property", "RefUrl": "/notes/1043132"}, {"RefNumber": "1043103", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "A299: System error in CL_RSR_CHABIT and SET_BIT0-01-", "RefUrl": "/notes/1043103"}, {"RefNumber": "1043053", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Data type in open hub destination is incorrect", "RefUrl": "/notes/1043053"}, {"RefNumber": "1043039", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 14", "RefUrl": "/notes/1043039"}, {"RefNumber": "1043035", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Monitor does not display errors", "RefUrl": "/notes/1043035"}, {"RefNumber": "1043008", "RefComponent": "BW-WHM-DST", "RefTitle": "Key date selection ineffective for char with direct access", "RefUrl": "/notes/1043008"}, {"RefNumber": "1043000", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA: Change check whether request is updated", "RefUrl": "/notes/1043000"}, {"RefNumber": "1042924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program CL_RSR_OLAP and form FILL_DIM-01-", "RefUrl": "/notes/1042924"}, {"RefNumber": "1042920", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages in transformation", "RefUrl": "/notes/1042920"}, {"RefNumber": "1042896", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Release of memory after you close query", "RefUrl": "/notes/1042896"}, {"RefNumber": "1042895", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "RRS_VARIANT_CREATE: Incorrect variant values", "RefUrl": "/notes/1042895"}, {"RefNumber": "1042893", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Problems with large queries and quantity conversion", "RefUrl": "/notes/1042893"}, {"RefNumber": "1042883", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RSBATCH_WRITE_PROT_TO_APPLLOG and incorrect error msg", "RefUrl": "/notes/1042883"}, {"RefNumber": "1042881", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:No InfoPackage scheduler call without active DS", "RefUrl": "/notes/1042881"}, {"RefNumber": "1042816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: No messages displayed in summary", "RefUrl": "/notes/1042816"}, {"RefNumber": "1042790", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P14: Real-time: QM action for real-time PSA request", "RefUrl": "/notes/1042790"}, {"RefNumber": "1042712", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: SQL error when extracting many requests", "RefUrl": "/notes/1042712"}, {"RefNumber": "1042703", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: IF_RSR_VAR_DEF~GET_VRNID_VALUE_RANGE_CMP-02-", "RefUrl": "/notes/1042703"}, {"RefNumber": "1042677", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication in application components is slow", "RefUrl": "/notes/1042677"}, {"RefNumber": "1042675", "RefComponent": "BW-WHM-AWB", "RefTitle": "Mapping is missing, search in workbench terminates, RSAR 203", "RefUrl": "/notes/1042675"}, {"RefNumber": "1042673", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Error: Assignment details in the report-report interface", "RefUrl": "/notes/1042673"}, {"RefNumber": "1042599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when creating a transformation between ISet and InfSrc", "RefUrl": "/notes/1042599"}, {"RefNumber": "1042590", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Application log (SLG1) for real-Ttme data acquisition (RDA)", "RefUrl": "/notes/1042590"}, {"RefNumber": "1042560", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Texts without language keys cannot be extracted", "RefUrl": "/notes/1042560"}, {"RefNumber": "1042502", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Not all leaves visible in display hierarchy", "RefUrl": "/notes/1042502"}, {"RefNumber": "1042453", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: CheckMan: Domain exchange; package concept", "RefUrl": "/notes/1042453"}, {"RefNumber": "1042452", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: Generic InfoPackage deletion with searching for package", "RefUrl": "/notes/1042452"}, {"RefNumber": "1042451", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:APO:Planning: Dialog boxes when planning requ is closed", "RefUrl": "/notes/1042451"}, {"RefNumber": "1042391", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 Brain in SAPLRSDRC_SPLIT; form RANGE_EQUAL-01-", "RefUrl": "/notes/1042391"}, {"RefNumber": "1042390", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump after entering InfoObject when master data is read", "RefUrl": "/notes/1042390"}, {"RefNumber": "1042389", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting incorrect dialog for InfoSource", "RefUrl": "/notes/1042389"}, {"RefNumber": "1042388", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems when transporting deletions", "RefUrl": "/notes/1042388"}, {"RefNumber": "1042353", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Temporal hierarchy join", "RefUrl": "/notes/1042353"}, {"RefNumber": "1042340", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA admin in DWB called only with maintain authorization", "RefUrl": "/notes/1042340"}, {"RefNumber": "1042339", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA:Deleting from PSA terminates with message RSM2 709", "RefUrl": "/notes/1042339"}, {"RefNumber": "1042338", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:UNIT fields cannot be selected in 7.x DataSource", "RefUrl": "/notes/1042338"}, {"RefNumber": "1042337", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: SDL: InfoPackage queries whether you want to save", "RefUrl": "/notes/1042337"}, {"RefNumber": "1042299", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport check also performed in display mode", "RefUrl": "/notes/1042299"}, {"RefNumber": "1042296", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: No information for InfoSet in monitor tree", "RefUrl": "/notes/1042296"}, {"RefNumber": "1041994", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect data type in routine and formula and constants", "RefUrl": "/notes/1041994"}, {"RefNumber": "1041827", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Activating the technical content: \"UNCAUGHT_EXCEPTION\"", "RefUrl": "/notes/1041827"}, {"RefNumber": "1041760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination: Authorized attribs with InfoSet characteristics", "RefUrl": "/notes/1041760"}, {"RefNumber": "1041697", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "System displays chargeable portions/remote hierarchy", "RefUrl": "/notes/1041697"}, {"RefNumber": "1041515", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating authorizations requires ODS values at least", "RefUrl": "/notes/1041515"}, {"RefNumber": "1041306", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: \"Record not in cross-rec.\" when buffer displayed", "RefUrl": "/notes/1041306"}, {"RefNumber": "1041284", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Incorrect sorting when you extract from the PSA", "RefUrl": "/notes/1041284"}, {"RefNumber": "1041000", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Program not valid for request creation", "RefUrl": "/notes/1041000"}, {"RefNumber": "1040649", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance when temporal hierarchy join is used", "RefUrl": "/notes/1040649"}, {"RefNumber": "1040334", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Termination when deleted entry is searched for in the tree", "RefUrl": "/notes/1040334"}, {"RefNumber": "1040293", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimizations for writable InfoProviders", "RefUrl": "/notes/1040293"}, {"RefNumber": "1040291", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Compound attribute value displayed incorrectly", "RefUrl": "/notes/1040291"}, {"RefNumber": "1040278", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Return code 12 after query transport", "RefUrl": "/notes/1040278"}, {"RefNumber": "1039979", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization (EYE 018) for key date variable for date", "RefUrl": "/notes/1039979"}, {"RefNumber": "1039890", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "INTERN: DataSource (RSDS): Generierung", "RefUrl": "/notes/1039890"}, {"RefNumber": "1039792", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE019: Hierarchy node maintenance for 0TCAIPROV not allowed", "RefUrl": "/notes/1039792"}, {"RefNumber": "1039736", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "BI 7.1 SPS02: Stammdaten: Paketierung", "RefUrl": "/notes/1039736"}, {"RefNumber": "1039211", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DYN_CALL_METH_NOT_FOUND when you verify and delete in dialog", "RefUrl": "/notes/1039211"}, {"RefNumber": "1039049", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archive file name and offset not in near-line interface", "RefUrl": "/notes/1039049"}, {"RefNumber": "1038948", "RefComponent": "BW", "RefTitle": "RSRV: Foreign key relationship between facts and dimensions", "RefUrl": "/notes/1038948"}, {"RefNumber": "1038923", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: RSDS198 if request is in write-optimized DSO", "RefUrl": "/notes/1038923"}, {"RefNumber": "1038611", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Delivered target or source not refreshed", "RefUrl": "/notes/1038611"}, {"RefNumber": "1038478", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Message 'Incorrect FRANGE row in FORM/DIM/FAC...'", "RefUrl": "/notes/1038478"}, {"RefNumber": "1038455", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: DTP requests missing from monitor detail tree", "RefUrl": "/notes/1038455"}, {"RefNumber": "1038369", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No authorization object for data archiving process", "RefUrl": "/notes/1038369"}, {"RefNumber": "1038044", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: cancel button does not work", "RefUrl": "/notes/1038044"}, {"RefNumber": "1038008", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Attributes are restricted incorrectly", "RefUrl": "/notes/1038008"}, {"RefNumber": "1037972", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/1037972"}, {"RefNumber": "1037902", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of variables for multiple queries", "RefUrl": "/notes/1037902"}, {"RefNumber": "1037881", "RefComponent": "BW-WHM-DST", "RefTitle": "P13:BATCHDATA: Deadlock during PSA writing to table", "RefUrl": "/notes/1037881"}, {"RefNumber": "1037813", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Assignment from DTP to RDA daemon is lost", "RefUrl": "/notes/1037813"}, {"RefNumber": "1037789", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Problem with intial value and GJAHR conversion exit", "RefUrl": "/notes/1037789"}, {"RefNumber": "1037447", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource (hierrarchy): Data request, display", "RefUrl": "/notes/1037447"}, {"RefNumber": "1037433", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Number of records in an RDA InfoPackage request", "RefUrl": "/notes/1037433"}, {"RefNumber": "1037405", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:Hier InfoPackage and RSOSOHIE entry after import", "RefUrl": "/notes/1037405"}, {"RefNumber": "1037040", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No DAP impact if you change InfoObject/InfoProvider", "RefUrl": "/notes/1037040"}, {"RefNumber": "1036861", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Changes to IF_RSPLS_CR_CONTROLLER", "RefUrl": "/notes/1036861"}, {"RefNumber": "1036665", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unklare Meldung bei Verwendung falscher Quellen und Ziele", "RefUrl": "/notes/1036665"}, {"RefNumber": "1036643", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 404 when you refresh a query", "RefUrl": "/notes/1036643"}, {"RefNumber": "1036581", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_OBJREF_NOT_ASSIGNED_NO", "RefUrl": "/notes/1036581"}, {"RefNumber": "1036440", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI710: Fehler im Segmenthandling", "RefUrl": "/notes/1036440"}, {"RefNumber": "1036433", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Deleting formulas and routines in update maintenance", "RefUrl": "/notes/1036433"}, {"RefNumber": "1036257", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Improving runtime for query selected using InfoAreas", "RefUrl": "/notes/1036257"}, {"RefNumber": "1036242", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Filter after leaf and nodes: Leaf is displayed twice", "RefUrl": "/notes/1036242"}, {"RefNumber": "1036236", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "<PERSON><PERSON>", "RefUrl": "/notes/1036236"}, {"RefNumber": "1035916", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "How to change a characteristic constant", "RefUrl": "/notes/1035916"}, {"RefNumber": "1035792", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:No access method activated when InfoPackage activate", "RefUrl": "/notes/1035792"}, {"RefNumber": "1035727", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Message R7 682 during transport of transformations", "RefUrl": "/notes/1035727"}, {"RefNumber": "1035558", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise search: Connector for BI", "RefUrl": "/notes/1035558"}, {"RefNumber": "1035527", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:Time-dependent delta using incorrect time interval", "RefUrl": "/notes/1035527"}, {"RefNumber": "1035225", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Generation of WebService source syst missing in CTC template", "RefUrl": "/notes/1035225"}, {"RefNumber": "1034983", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: InfoCube activation fails", "RefUrl": "/notes/1034983"}, {"RefNumber": "1034866", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA for BI 710 Support Package Stack 02", "RefUrl": "/notes/1034866"}, {"RefNumber": "1034849", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable dialog displayed but all variables are personalized", "RefUrl": "/notes/1034849"}, {"RefNumber": "1034843", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "X299 in CL_RSR_MDX_OLAP_REQUERST and ADD_REQUEST_INST_3", "RefUrl": "/notes/1034843"}, {"RefNumber": "1034841", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "X299 in CL_RSR_OLAP and IF_RSR_OLAP~CHECK_INPUTABLE-01-", "RefUrl": "/notes/1034841"}, {"RefNumber": "1034775", "RefComponent": "BW-WHM-DST-INP", "RefTitle": "Corrections for IQM in BI, second part", "RefUrl": "/notes/1034775"}, {"RefNumber": "1034378", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "SQL error when you fill an aggregate", "RefUrl": "/notes/1034378"}, {"RefNumber": "1034207", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for .CURRENTMEMBER.LEVEL.ORDINAL", "RefUrl": "/notes/1034207"}, {"RefNumber": "1034206", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables in the default value of the query", "RefUrl": "/notes/1034206"}, {"RefNumber": "1033966", "RefComponent": "BW", "RefTitle": "Wrong calculation of interval in the Seasonal t-test", "RefUrl": "/notes/1033966"}, {"RefNumber": "1031996", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRK0 and MEGA_SORT_14-01- with EMPTY", "RefUrl": "/notes/1031996"}, {"RefNumber": "1031459", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Refreshing a changeable exit variable with entry", "RefUrl": "/notes/1031459"}, {"RefNumber": "1031086", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect parameter names when you call transaction RSRR_WEB", "RefUrl": "/notes/1031086"}, {"RefNumber": "1030980", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "InfoObject not unique when replacing from query result", "RefUrl": "/notes/1030980"}, {"RefNumber": "1030977", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Cannot navigate any further after missing authorization", "RefUrl": "/notes/1030977"}, {"RefNumber": "1030657", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in CL_RSR_RRK0_CURR and FILL_CUDIM_02-04-", "RefUrl": "/notes/1030657"}, {"RefNumber": "1026843", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation or replacement path loses unit", "RefUrl": "/notes/1026843"}, {"RefNumber": "1024419", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "You can only enter 17 characters for text variables", "RefUrl": "/notes/1024419"}, {"RefNumber": "1024055", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRI2 / CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1024055"}, {"RefNumber": "1016067", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Improving performance for MDX and DataStore object", "RefUrl": "/notes/1016067"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1034983", "RefComponent": "BW-SYS-DB-DB6", "RefTitle": "DB6: InfoCube activation fails", "RefUrl": "/notes/1034983 "}, {"RefNumber": "1052222", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Hierarchien: ABAP-Unit-Tests für DTP-Anschluß", "RefUrl": "/notes/1052222 "}, {"RefNumber": "1051664", "RefComponent": "BW-WHM", "RefTitle": "Check and repair program BW7.x for Note 849857", "RefUrl": "/notes/1051664 "}, {"RefNumber": "1047953", "RefComponent": "BW-WHM-DST", "RefTitle": "Checkman reports errors", "RefUrl": "/notes/1047953 "}, {"RefNumber": "1066132", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA:Check whether request is updated has changed", "RefUrl": "/notes/1066132 "}, {"RefNumber": "1040334", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Termination when deleted entry is searched for in the tree", "RefUrl": "/notes/1040334 "}, {"RefNumber": "1051767", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Terminatn in RRBA_NUMBER_GET_BW \"commit connection DEFAULT\"", "RefUrl": "/notes/1051767 "}, {"RefNumber": "1048477", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048477 "}, {"RefNumber": "1048078", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Target*___T unknown when you transport update rules", "RefUrl": "/notes/1048078 "}, {"RefNumber": "1035558", "RefComponent": "BW-WHM-MTD", "RefTitle": "Enterprise search: Connector for BI", "RefUrl": "/notes/1035558 "}, {"RefNumber": "1051291", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DBMAN 305: Error when reading data of InfoProvider ...$N", "RefUrl": "/notes/1051291 "}, {"RefNumber": "1036236", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "<PERSON><PERSON>", "RefUrl": "/notes/1036236 "}, {"RefNumber": "1041994", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect data type in routine and formula and constants", "RefUrl": "/notes/1041994 "}, {"RefNumber": "1049998", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump loading data into open hub destn; incorrect error msg", "RefUrl": "/notes/1049998 "}, {"RefNumber": "1031459", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Refreshing a changeable exit variable with entry", "RefUrl": "/notes/1031459 "}, {"RefNumber": "1048502", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump 'connection closed' when window is closed", "RefUrl": "/notes/1048502 "}, {"RefNumber": "1049157", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Incorrect color for nodes/leaves in CCMS for BIA", "RefUrl": "/notes/1049157 "}, {"RefNumber": "1035727", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Message R7 682 during transport of transformations", "RefUrl": "/notes/1035727 "}, {"RefNumber": "1041827", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Activating the technical content: \"UNCAUGHT_EXCEPTION\"", "RefUrl": "/notes/1041827 "}, {"RefNumber": "1042299", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Transport check also performed in display mode", "RefUrl": "/notes/1042299 "}, {"RefNumber": "1047688", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query returns no data (including and excluding selection)", "RefUrl": "/notes/1047688 "}, {"RefNumber": "1039736", "RefComponent": "BW-WHM-DBA-MD", "RefTitle": "BI 7.1 SPS02: Stammdaten: Paketierung", "RefUrl": "/notes/1039736 "}, {"RefNumber": "1047299", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Query displays data of a deleted request", "RefUrl": "/notes/1047299 "}, {"RefNumber": "1048091", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "This note serves only as a prerequisite for other notes", "RefUrl": "/notes/1048091 "}, {"RefNumber": "1045054", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource replication: Shrt dump TSV_TNEW_PAGE_ALLOC_FAILED", "RefUrl": "/notes/1045054 "}, {"RefNumber": "1048947", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Corr:Source syst names,pseudo D versions for transformations", "RefUrl": "/notes/1048947 "}, {"RefNumber": "1051005", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "After error, status of archiving request remains \"yellow\"(1)", "RefUrl": "/notes/1051005 "}, {"RefNumber": "1051623", "RefComponent": "BW-BEX-OT", "RefTitle": "Adjustments of transaction RSTT (usability)", "RefUrl": "/notes/1051623 "}, {"RefNumber": "1042920", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error messages in transformation", "RefUrl": "/notes/1042920 "}, {"RefNumber": "1048227", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data or too much data in rare cases (after SP13)", "RefUrl": "/notes/1048227 "}, {"RefNumber": "1041284", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "Incorrect sorting when you extract from the PSA", "RefUrl": "/notes/1041284 "}, {"RefNumber": "1048505", "RefComponent": "BW-BEX-ET-AUT", "RefTitle": "Execution authorization for generated queries with '!' signs", "RefUrl": "/notes/1048505 "}, {"RefNumber": "1049258", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Delta cache data displayed twice in certain circumstances", "RefUrl": "/notes/1049258 "}, {"RefNumber": "1042703", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Termination: IF_RSR_VAR_DEF~GET_VRNID_VALUE_RANGE_CMP-02-", "RefUrl": "/notes/1042703 "}, {"RefNumber": "1039890", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "INTERN: DataSource (RSDS): Generierung", "RefUrl": "/notes/1039890 "}, {"RefNumber": "1042712", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: SQL error when extracting many requests", "RefUrl": "/notes/1042712 "}, {"RefNumber": "1038455", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: DTP requests missing from monitor detail tree", "RefUrl": "/notes/1038455 "}, {"RefNumber": "1075101", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Correction for Note 1040293", "RefUrl": "/notes/1075101 "}, {"RefNumber": "1047022", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect \"currentness of data\" for master data provider", "RefUrl": "/notes/1047022 "}, {"RefNumber": "1051979", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Web service DataSource cannot be activated", "RefUrl": "/notes/1051979 "}, {"RefNumber": "1048864", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon does not close empty requests", "RefUrl": "/notes/1048864 "}, {"RefNumber": "1048344", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Checkman: Various checkman problems", "RefUrl": "/notes/1048344 "}, {"RefNumber": "1046270", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Additional fixed filter for the query runtime", "RefUrl": "/notes/1046270 "}, {"RefNumber": "1046465", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Report-report interface: Saving an InfoSource", "RefUrl": "/notes/1046465 "}, {"RefNumber": "1042673", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Error: Assignment details in the report-report interface", "RefUrl": "/notes/1042673 "}, {"RefNumber": "1048972", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "No authorization for a transformation that does not exist", "RefUrl": "/notes/1048972 "}, {"RefNumber": "1050330", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BI SP 14 BIA revision compatibility check extended", "RefUrl": "/notes/1050330 "}, {"RefNumber": "1047829", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:BATCH: Cancel work process in a 'safe' way", "RefUrl": "/notes/1047829 "}, {"RefNumber": "1049403", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message \"Incorrect call sequence\" when you delete", "RefUrl": "/notes/1049403 "}, {"RefNumber": "1051652", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Error message: Target field is missing/dump w/ rule w/ units", "RefUrl": "/notes/1051652 "}, {"RefNumber": "1049735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect status after deallocation", "RefUrl": "/notes/1049735 "}, {"RefNumber": "1040293", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Optimizations for writable InfoProviders", "RefUrl": "/notes/1040293 "}, {"RefNumber": "1044377", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: AND process saved in scheduling", "RefUrl": "/notes/1044377 "}, {"RefNumber": "1045305", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Incorrect record mode rule is not reported", "RefUrl": "/notes/1045305 "}, {"RefNumber": "1042390", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump after entering InfoObject when master data is read", "RefUrl": "/notes/1042390 "}, {"RefNumber": "1043132", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Toggle \"Use Temporal Hierarchy Join\" property", "RefUrl": "/notes/1043132 "}, {"RefNumber": "1041760", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination: Authorized attribs with InfoSet characteristics", "RefUrl": "/notes/1041760 "}, {"RefNumber": "1043039", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RSRDA: Usability improvement in Support Package 14", "RefUrl": "/notes/1043039 "}, {"RefNumber": "1043819", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "No data for NON EMPTY and WHERE", "RefUrl": "/notes/1043819 "}, {"RefNumber": "1040649", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Improving performance when temporal hierarchy join is used", "RefUrl": "/notes/1040649 "}, {"RefNumber": "1043919", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Too many authorizations w/ exclusive hierarchy authorization", "RefUrl": "/notes/1043919 "}, {"RefNumber": "1048178", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Default member for virtual hierarchies incorrect", "RefUrl": "/notes/1048178 "}, {"RefNumber": "1050097", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Formula variable not replaced (hierarchy deactivated)", "RefUrl": "/notes/1050097 "}, {"RefNumber": "1033966", "RefComponent": "BW", "RefTitle": "Wrong calculation of interval in the Seasonal t-test", "RefUrl": "/notes/1033966 "}, {"RefNumber": "1052660", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Error stack can be versioned", "RefUrl": "/notes/1052660 "}, {"RefNumber": "1051994", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:REQARCH:Selecting excluding DS/source syst combinations", "RefUrl": "/notes/1051994 "}, {"RefNumber": "1043000", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:DSO:DTP:DTA: Change check whether request is updated", "RefUrl": "/notes/1043000 "}, {"RefNumber": "1045548", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Monitoring of RDA InfoPackage requests", "RefUrl": "/notes/1045548 "}, {"RefNumber": "1042590", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Application log (SLG1) for real-Ttme data acquisition (RDA)", "RefUrl": "/notes/1042590 "}, {"RefNumber": "1042353", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Temporal hierarchy join", "RefUrl": "/notes/1042353 "}, {"RefNumber": "1042896", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Release of memory after you close query", "RefUrl": "/notes/1042896 "}, {"RefNumber": "1042391", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "X299 Brain in SAPLRSDRC_SPLIT; form RANGE_EQUAL-01-", "RefUrl": "/notes/1042391 "}, {"RefNumber": "1051127", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Wrong data in a very particular situation", "RefUrl": "/notes/1051127 "}, {"RefNumber": "1042502", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Not all leaves visible in display hierarchy", "RefUrl": "/notes/1042502 "}, {"RefNumber": "1045008", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "RRK_LIST_OPEN: CX_SY_REF_IS_INITIAL UNCAUGHT_EXCEPTION", "RefUrl": "/notes/1045008 "}, {"RefNumber": "1050783", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Problems in logging during removal in archive and nearline", "RefUrl": "/notes/1050783 "}, {"RefNumber": "1050695", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_TABLES_NOT_COMPATIBLE", "RefUrl": "/notes/1050695 "}, {"RefNumber": "1047776", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Too many process variants when deleting a DAP", "RefUrl": "/notes/1047776 "}, {"RefNumber": "1048690", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error messages and error logs not issued", "RefUrl": "/notes/1048690 "}, {"RefNumber": "1050669", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable is not replaced", "RefUrl": "/notes/1050669 "}, {"RefNumber": "1045114", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Key figure definition and near-line storage", "RefUrl": "/notes/1045114 "}, {"RefNumber": "1050327", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Non-cumulatives and formula variables from replacement path", "RefUrl": "/notes/1050327 "}, {"RefNumber": "1034775", "RefComponent": "BW-WHM-DST-INP", "RefTitle": "Corrections for IQM in BI, second part", "RefUrl": "/notes/1034775 "}, {"RefNumber": "1053308", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "TSV_TNEW_PAGE_ALLOC_FAILED in reload from archive/near-line", "RefUrl": "/notes/1053308 "}, {"RefNumber": "1050293", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "General pattern in InfoPackage selection is not supported", "RefUrl": "/notes/1050293 "}, {"RefNumber": "1053436", "RefComponent": "BW-BEX-ET-WJR", "RefTitle": "Problems with \"Temporal Hierarchy Join\" in JAVA Web", "RefUrl": "/notes/1053436 "}, {"RefNumber": "1034866", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA for BI 710 Support Package Stack 02", "RefUrl": "/notes/1034866 "}, {"RefNumber": "1053612", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correctn: Error handling prog contains incorrect source code", "RefUrl": "/notes/1053612 "}, {"RefNumber": "1053084", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: ASSIGN_TYPE_CONFLICT in CONVERT_FROM_PSA", "RefUrl": "/notes/1053084 "}, {"RefNumber": "1050379", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Not enough fields requested when loading w/o PSA", "RefUrl": "/notes/1050379 "}, {"RefNumber": "1048506", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: DataSource vers converted during transformation", "RefUrl": "/notes/1048506 "}, {"RefNumber": "1045711", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Attributes for characteristics with temporal hierarchy join", "RefUrl": "/notes/1045711 "}, {"RefNumber": "1044378", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Delete free processes in chain deletion", "RefUrl": "/notes/1044378 "}, {"RefNumber": "1052031", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving request: Termination after verification phase", "RefUrl": "/notes/1052031 "}, {"RefNumber": "1052645", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14:DSO:Further authorization check before request activated", "RefUrl": "/notes/1052645 "}, {"RefNumber": "1035225", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Generation of WebService source syst missing in CTC template", "RefUrl": "/notes/1035225 "}, {"RefNumber": "1048114", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Adjusting RSRV checks for the BI accelerator", "RefUrl": "/notes/1048114 "}, {"RefNumber": "1042560", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "Correction: Texts without language keys cannot be extracted", "RefUrl": "/notes/1042560 "}, {"RefNumber": "1048923", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN X299 in SAPLRRI2; form INPUTABLE_CHAFREE-02-", "RefUrl": "/notes/1048923 "}, {"RefNumber": "1042924", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in program CL_RSR_OLAP and form FILL_DIM-01-", "RefUrl": "/notes/1042924 "}, {"RefNumber": "1050889", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "X299 BRAIN in SAPLRRK0; form LRECH-02-01", "RefUrl": "/notes/1050889 "}, {"RefNumber": "1051614", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Dump occurs in report RSBATCH_DEL_MSG_PARM_DTPTEMP", "RefUrl": "/notes/1051614 "}, {"RefNumber": "1049700", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL: Dynamic hierarchy selection and new headers", "RefUrl": "/notes/1049700 "}, {"RefNumber": "1047978", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Hierarchy auth. and intervals: \"No authorization\", EYE 007", "RefUrl": "/notes/1047978 "}, {"RefNumber": "1045919", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "No filter when accessing file DataSource directly", "RefUrl": "/notes/1045919 "}, {"RefNumber": "1051170", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "InfoObject &1 is not available in version &2", "RefUrl": "/notes/1051170 "}, {"RefNumber": "1043008", "RefComponent": "BW-WHM-DST", "RefTitle": "Key date selection ineffective for char with direct access", "RefUrl": "/notes/1043008 "}, {"RefNumber": "1046394", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Performance:RSSELDONE:HASH_CREATE:Index access PID", "RefUrl": "/notes/1046394 "}, {"RefNumber": "1047852", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Selection in MEMBERS rowset for [measures] or structures", "RefUrl": "/notes/1047852 "}, {"RefNumber": "1047840", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: After-import of ISFS + RSDS not deleted", "RefUrl": "/notes/1047840 "}, {"RefNumber": "1046254", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Performance optimization of the verification phase", "RefUrl": "/notes/1046254 "}, {"RefNumber": "1047992", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: For internal use", "RefUrl": "/notes/1047992 "}, {"RefNumber": "1048015", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Rollup: Exception INCONSISTENT_N_O_REQS", "RefUrl": "/notes/1048015 "}, {"RefNumber": "1048095", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Checking variables and I_STEP equal to three in exit", "RefUrl": "/notes/1048095 "}, {"RefNumber": "1048100", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "BRAIN A299 in SAPLRRI2 and CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1048100 "}, {"RefNumber": "1048161", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Set QM status for yellow requests using funcion module", "RefUrl": "/notes/1048161 "}, {"RefNumber": "1048280", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Additional corrections for Note 1002682", "RefUrl": "/notes/1048280 "}, {"RefNumber": "1040291", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Compound attribute value displayed incorrectly", "RefUrl": "/notes/1040291 "}, {"RefNumber": "1041000", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Program not valid for request creation", "RefUrl": "/notes/1041000 "}, {"RefNumber": "1041306", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: \"Record not in cross-rec.\" when buffer displayed", "RefUrl": "/notes/1041306 "}, {"RefNumber": "1041515", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Generating authorizations requires ODS values at least", "RefUrl": "/notes/1041515 "}, {"RefNumber": "1042296", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: No information for InfoSet in monitor tree", "RefUrl": "/notes/1042296 "}, {"RefNumber": "1042337", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: SDL: InfoPackage queries whether you want to save", "RefUrl": "/notes/1042337 "}, {"RefNumber": "1042338", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:UNIT fields cannot be selected in 7.x DataSource", "RefUrl": "/notes/1042338 "}, {"RefNumber": "1042388", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems when transporting deletions", "RefUrl": "/notes/1042388 "}, {"RefNumber": "1042389", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting incorrect dialog for InfoSource", "RefUrl": "/notes/1042389 "}, {"RefNumber": "1042451", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:APO:Planning: Dialog boxes when planning requ is closed", "RefUrl": "/notes/1042451 "}, {"RefNumber": "1042452", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14: Generic InfoPackage deletion with searching for package", "RefUrl": "/notes/1042452 "}, {"RefNumber": "1042453", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: CheckMan: Domain exchange; package concept", "RefUrl": "/notes/1042453 "}, {"RefNumber": "1042599", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when creating a transformation between ISet and InfSrc", "RefUrl": "/notes/1042599 "}, {"RefNumber": "1042675", "RefComponent": "BW-WHM-AWB", "RefTitle": "Mapping is missing, search in workbench terminates, RSAR 203", "RefUrl": "/notes/1042675 "}, {"RefNumber": "1042677", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Replication in application components is slow", "RefUrl": "/notes/1042677 "}, {"RefNumber": "1042790", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "P14: Real-time: QM action for real-time PSA request", "RefUrl": "/notes/1042790 "}, {"RefNumber": "1042816", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "BIA monitor: No messages displayed in summary", "RefUrl": "/notes/1042816 "}, {"RefNumber": "1042881", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P14:SDL:No InfoPackage scheduler call without active DS", "RefUrl": "/notes/1042881 "}, {"RefNumber": "1042893", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "Problems with large queries and quantity conversion", "RefUrl": "/notes/1042893 "}, {"RefNumber": "1042895", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "RRS_VARIANT_CREATE: Incorrect variant values", "RefUrl": "/notes/1042895 "}, {"RefNumber": "1043035", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "DTP: Monitor does not display errors", "RefUrl": "/notes/1043035 "}, {"RefNumber": "1043053", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "Data type in open hub destination is incorrect", "RefUrl": "/notes/1043053 "}, {"RefNumber": "1043103", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "A299: System error in CL_RSR_CHABIT and SET_BIT0-01-", "RefUrl": "/notes/1043103 "}, {"RefNumber": "1043170", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Missing impact for currency translation and unit conversion", "RefUrl": "/notes/1043170 "}, {"RefNumber": "1043192", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: English texts in report RSREQARCH_CHECK_HASH_ARCHIVE", "RefUrl": "/notes/1043192 "}, {"RefNumber": "1043295", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corrections: Mannually interrupted run is continued", "RefUrl": "/notes/1043295 "}, {"RefNumber": "1043343", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Deleting the rule from the technical group deletes the group", "RefUrl": "/notes/1043343 "}, {"RefNumber": "1043724", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump nach Eintragung eines InfoObjektes bei Stammdaten nachl", "RefUrl": "/notes/1043724 "}, {"RefNumber": "1043971", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Termination in RSDRI_CUBE_WRITE_PACKAGE for incorrect data", "RefUrl": "/notes/1043971 "}, {"RefNumber": "1044052", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: DBC attribute can be changed in the DTP", "RefUrl": "/notes/1044052 "}, {"RefNumber": "1044054", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Shadow DTP with DataSource cannot be displayed", "RefUrl": "/notes/1044054 "}, {"RefNumber": "1044181", "RefComponent": "BW-WHM-DST-MON", "RefTitle": "Correction: Too many requests selected for DTP from DWWB", "RefUrl": "/notes/1044181 "}, {"RefNumber": "1044197", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Rule is created twice / Note 1036440 was implemented", "RefUrl": "/notes/1044197 "}, {"RefNumber": "1044398", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE 007 \" No auth.\" for several authorizations", "RefUrl": "/notes/1044398 "}, {"RefNumber": "1044494", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Text variable replaced by incorrect value", "RefUrl": "/notes/1044494 "}, {"RefNumber": "1044708", "RefComponent": "BW-PLA-IP", "RefTitle": "IP: Performance of generic time derivation", "RefUrl": "/notes/1044708 "}, {"RefNumber": "1044808", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "DataSource: Error msg in the after import/during replication", "RefUrl": "/notes/1044808 "}, {"RefNumber": "1044860", "RefComponent": "BW-WHM-DBA-OHS", "RefTitle": "ESH Open Hub", "RefUrl": "/notes/1044860 "}, {"RefNumber": "1045063", "RefComponent": "BW-BEX-OT-OLAP-UOM", "RefTitle": "No quantitiy conversion (target quantity unit from variable)", "RefUrl": "/notes/1045063 "}, {"RefNumber": "1045174", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformation/routine missing when you collect content", "RefUrl": "/notes/1045174 "}, {"RefNumber": "1045300", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Display of non-postable values shows SIDs w/out master data", "RefUrl": "/notes/1045300 "}, {"RefNumber": "1045560", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "No text symbol with value for hierarchy variable", "RefUrl": "/notes/1045560 "}, {"RefNumber": "1045784", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "P14: DSO: DTP: Check on full REQU requests not required", "RefUrl": "/notes/1045784 "}, {"RefNumber": "1045861", "RefComponent": "BW-WHM-DST", "RefTitle": "Directly accessing time-dependent master data texts", "RefUrl": "/notes/1045861 "}, {"RefNumber": "1045923", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization for key figure (0TCAKYFNM) in InfoSet", "RefUrl": "/notes/1045923 "}, {"RefNumber": "1045950", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RFC dump when calling FM RSS2_CALL_FCODE_EMPTY_GLOBALS", "RefUrl": "/notes/1045950 "}, {"RefNumber": "1046003", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "BRAIN X299 in SAPLRRK0; form MEGA_SORT_14-01-", "RefUrl": "/notes/1046003 "}, {"RefNumber": "1046066", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "RSECAUTH:Cust exist variables for non-string characteristics", "RefUrl": "/notes/1046066 "}, {"RefNumber": "1046127", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you collect transformations", "RefUrl": "/notes/1046127 "}, {"RefNumber": "1046233", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "A299(BRAIN) in program SAPLRRI2 and form FST_VAR_06-01-", "RefUrl": "/notes/1046233 "}, {"RefNumber": "1046312", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Dump when you assign key figure with fixed unit/currency", "RefUrl": "/notes/1046312 "}, {"RefNumber": "1046544", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Problems: Migrating w/source fields with fixed unit/currency", "RefUrl": "/notes/1046544 "}, {"RefNumber": "1047174", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Incorrect scope when locking in process chains", "RefUrl": "/notes/1047174 "}, {"RefNumber": "1047176", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Undeclared exception in the error handling", "RefUrl": "/notes/1047176 "}, {"RefNumber": "1051055", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "InfoSource remains in the status \"Not Executable\"", "RefUrl": "/notes/1051055 "}, {"RefNumber": "1047735", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: You cannot transport interrupt", "RefUrl": "/notes/1047735 "}, {"RefNumber": "1051246", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Cursor position gets lost in routine", "RefUrl": "/notes/1051246 "}, {"RefNumber": "1050948", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Not enough texts for RSPCVARIANTT", "RefUrl": "/notes/1050948 "}, {"RefNumber": "1051168", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Termination: SUCC_PRED_GET-2 in prog CL_RSR_HIERARCHY_BINCL", "RefUrl": "/notes/1051168 "}, {"RefNumber": "1050868", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "ASSERTION_FAILED in MODIFY_SEGMENT when you activate a DAP", "RefUrl": "/notes/1050868 "}, {"RefNumber": "1049932", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Error \"CX_SY_REF_IS_INITIAL\" when you go from ALV to BEx", "RefUrl": "/notes/1049932 "}, {"RefNumber": "1051036", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "You cannot restart change run", "RefUrl": "/notes/1051036 "}, {"RefNumber": "1050208", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Termination BRAIN 299 _GET_AUTH_RESTRICT-03 in CL_RSMD_RS", "RefUrl": "/notes/1050208 "}, {"RefNumber": "1049209", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Brain A125 Komponente G_S_KENNZ-%F_$P$ unbekannt", "RefUrl": "/notes/1049209 "}, {"RefNumber": "1049261", "RefComponent": "BW-BEX-OT-BIA", "RefTitle": "Short dump for RSRV check to compary keyfigures", "RefUrl": "/notes/1049261 "}, {"RefNumber": "1049564", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Incorrect data with exception aggregation and two structures", "RefUrl": "/notes/1049564 "}, {"RefNumber": "1049141", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "RSDA type group on IF_RSDAI_NEARLINE_CONNECTION interface", "RefUrl": "/notes/1049141 "}, {"RefNumber": "1049691", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Termination in CL_RSR_RRK0_RQTS->_CHECK_DELTAPAIR_01", "RefUrl": "/notes/1049691 "}, {"RefNumber": "1049469", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Switching from ABAP program", "RefUrl": "/notes/1049469 "}, {"RefNumber": "1049623", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Correction: Scheduling if server does not exist", "RefUrl": "/notes/1049623 "}, {"RefNumber": "1049147", "RefComponent": "BW-WHM-DBA-HIER", "RefTitle": "Deadlock on hierarchy table", "RefUrl": "/notes/1049147 "}, {"RefNumber": "1049187", "RefComponent": "BW-WHM-DST-SRC", "RefTitle": "Correction for CTC (creating source system in background)", "RefUrl": "/notes/1049187 "}, {"RefNumber": "1048648", "RefComponent": "BW-WHM-DBA-DSO", "RefTitle": "BI7.0(SP14) DataStore: Deletion of request terminates", "RefUrl": "/notes/1048648 "}, {"RefNumber": "1048345", "RefComponent": "BW-WHM-DST", "RefTitle": "P14: Check function in InfoCube administration", "RefUrl": "/notes/1048345 "}, {"RefNumber": "1048504", "RefComponent": "BW-WHM-AWB", "RefTitle": "P14: Administering PSA to new DS-PSA displays incorrect icon", "RefUrl": "/notes/1048504 "}, {"RefNumber": "1044991", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "OBSOLETE", "RefUrl": "/notes/1044991 "}, {"RefNumber": "1047598", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:Manage:QM action on loaded request in DSO", "RefUrl": "/notes/1047598 "}, {"RefNumber": "1048977", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Probleme bei Zeitstempelfortschreibung", "RefUrl": "/notes/1048977 "}, {"RefNumber": "1048350", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: Incorrect text in error step in error DTP", "RefUrl": "/notes/1048350 "}, {"RefNumber": "1045296", "RefComponent": "BW-BEX-ET-RA", "RefTitle": "RSRA_CLUSTER_TABLE_REORG dumps with an error.", "RefUrl": "/notes/1045296 "}, {"RefNumber": "1048349", "RefComponent": "BW-WHM-DST-PC", "RefTitle": "Corr.: Connection errors with multiple events and collector", "RefUrl": "/notes/1048349 "}, {"RefNumber": "1048423", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "R7 105 during migration of update rules", "RefUrl": "/notes/1048423 "}, {"RefNumber": "1046507", "RefComponent": "BW-WHM-DST-DBC", "RefTitle": "SP 11: Error in DB connect for DB2, DB4 and MS-SQL-Server", "RefUrl": "/notes/1046507 "}, {"RefNumber": "1042883", "RefComponent": "BW-WHM-DST", "RefTitle": "P14:RSBATCH_WRITE_PROT_TO_APPLLOG and incorrect error msg", "RefUrl": "/notes/1042883 "}, {"RefNumber": "1042340", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA admin in DWB called only with maintain authorization", "RefUrl": "/notes/1042340 "}, {"RefNumber": "1042339", "RefComponent": "BW-WHM-DST-PSA", "RefTitle": "P14:PSA:Deleting from PSA terminates with message RSM2 709", "RefUrl": "/notes/1042339 "}, {"RefNumber": "1036257", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Improving runtime for query selected using InfoAreas", "RefUrl": "/notes/1036257 "}, {"RefNumber": "1041697", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "System displays chargeable portions/remote hierarchy", "RefUrl": "/notes/1041697 "}, {"RefNumber": "1043456", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Error in functions BAPI_TD...", "RefUrl": "/notes/1043456 "}, {"RefNumber": "1039792", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "EYE019: Hierarchy node maintenance for 0TCAIPROV not allowed", "RefUrl": "/notes/1039792 "}, {"RefNumber": "1043455", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Converting the query generation", "RefUrl": "/notes/1043455 "}, {"RefNumber": "1024055", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRI2 / CUDIM_FUELLEN_CHFP-02-", "RefUrl": "/notes/1024055 "}, {"RefNumber": "1039979", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "No authorization (EYE 018) for key date variable for date", "RefUrl": "/notes/1039979 "}, {"RefNumber": "1038008", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Attributes are restricted incorrectly", "RefUrl": "/notes/1038008 "}, {"RefNumber": "1039049", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archive file name and offset not in near-line interface", "RefUrl": "/notes/1039049 "}, {"RefNumber": "1034378", "RefComponent": "BW-BEX-OT-AGGR", "RefTitle": "SQL error when you fill an aggregate", "RefUrl": "/notes/1034378 "}, {"RefNumber": "1034206", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "MDX: Variables in the default value of the query", "RefUrl": "/notes/1034206 "}, {"RefNumber": "1034207", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime for .CURRENTMEMBER.LEVEL.ORDINAL", "RefUrl": "/notes/1034207 "}, {"RefNumber": "1031086", "RefComponent": "BW-BEX-OT-RRI", "RefTitle": "Incorrect parameter names when you call transaction RSRR_WEB", "RefUrl": "/notes/1031086 "}, {"RefNumber": "1026843", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception aggregation or replacement path loses unit", "RefUrl": "/notes/1026843 "}, {"RefNumber": "1036433", "RefComponent": "BW-WHM-DST-UPD", "RefTitle": "Deleting formulas and routines in update maintenance", "RefUrl": "/notes/1036433 "}, {"RefNumber": "1037405", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:Hier InfoPackage and RSOSOHIE entry after import", "RefUrl": "/notes/1037405 "}, {"RefNumber": "1035792", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:No access method activated when InfoPackage activate", "RefUrl": "/notes/1035792 "}, {"RefNumber": "1037040", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No DAP impact if you change InfoObject/InfoProvider", "RefUrl": "/notes/1037040 "}, {"RefNumber": "1036861", "RefComponent": "BW-PLA-IP-PQ", "RefTitle": "Changes to IF_RSPLS_CR_CONTROLLER", "RefUrl": "/notes/1036861 "}, {"RefNumber": "1037433", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Number of records in an RDA InfoPackage request", "RefUrl": "/notes/1037433 "}, {"RefNumber": "1037813", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "Assignment from DTP to RDA daemon is lost", "RefUrl": "/notes/1037813 "}, {"RefNumber": "1039211", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "DYN_CALL_METH_NOT_FOUND when you verify and delete in dialog", "RefUrl": "/notes/1039211 "}, {"RefNumber": "1037902", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Sequence of variables for multiple queries", "RefUrl": "/notes/1037902 "}, {"RefNumber": "1035916", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "How to change a characteristic constant", "RefUrl": "/notes/1035916 "}, {"RefNumber": "1040278", "RefComponent": "BW-BEX-ET-CTS", "RefTitle": "Return code 12 after query transport", "RefUrl": "/notes/1040278 "}, {"RefNumber": "1036440", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "BI710: Fehler im Segmenthandling", "RefUrl": "/notes/1036440 "}, {"RefNumber": "1036643", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Message BRAIN 404 when you refresh a query", "RefUrl": "/notes/1036643 "}, {"RefNumber": "1038923", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Correction: RSDS198 if request is in write-optimized DSO", "RefUrl": "/notes/1038923 "}, {"RefNumber": "1038478", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Message 'Incorrect FRANGE row in FORM/DIM/FAC...'", "RefUrl": "/notes/1038478 "}, {"RefNumber": "1038044", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "RSZDELETE: cancel button does not work", "RefUrl": "/notes/1038044 "}, {"RefNumber": "1038948", "RefComponent": "BW", "RefTitle": "RSRV: Foreign key relationship between facts and dimensions", "RefUrl": "/notes/1038948 "}, {"RefNumber": "1037447", "RefComponent": "BW-WHM-DST-DS", "RefTitle": "3.x DataSource (hierrarchy): Data request, display", "RefUrl": "/notes/1037447 "}, {"RefNumber": "1024419", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "You can only enter 17 characters for text variables", "RefUrl": "/notes/1024419 "}, {"RefNumber": "1034841", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "X299 in CL_RSR_OLAP and IF_RSR_OLAP~CHECK_INPUTABLE-01-", "RefUrl": "/notes/1034841 "}, {"RefNumber": "1034849", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Variable dialog displayed but all variables are personalized", "RefUrl": "/notes/1034849 "}, {"RefNumber": "1030657", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "System error in CL_RSR_RRK0_CURR and FILL_CUDIM_02-04-", "RefUrl": "/notes/1030657 "}, {"RefNumber": "1030977", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Cannot navigate any further after missing authorization", "RefUrl": "/notes/1030977 "}, {"RefNumber": "1031996", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in SAPLRRK0 and MEGA_SORT_14-01- with EMPTY", "RefUrl": "/notes/1031996 "}, {"RefNumber": "1016067", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Improving performance for MDX and DataStore object", "RefUrl": "/notes/1016067 "}, {"RefNumber": "1034843", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "X299 in CL_RSR_MDX_OLAP_REQUERST and ADD_REQUEST_INST_3", "RefUrl": "/notes/1034843 "}, {"RefNumber": "1038611", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Delivered target or source not refreshed", "RefUrl": "/notes/1038611 "}, {"RefNumber": "1038369", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "No authorization object for data archiving process", "RefUrl": "/notes/1038369 "}, {"RefNumber": "1037789", "RefComponent": "BW-WHM-DBA-ICUB", "RefTitle": "Problem with intial value and GJAHR conversion exit", "RefUrl": "/notes/1037789 "}, {"RefNumber": "1036242", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Filter after leaf and nodes: Leaf is displayed twice", "RefUrl": "/notes/1036242 "}, {"RefNumber": "1037881", "RefComponent": "BW-WHM-DST", "RefTitle": "P13:BATCHDATA: Deadlock during PSA writing to table", "RefUrl": "/notes/1037881 "}, {"RefNumber": "1035527", "RefComponent": "BW-WHM-DST-SDL", "RefTitle": "P13:SDL:Time-dependent delta using incorrect time interval", "RefUrl": "/notes/1035527 "}, {"RefNumber": "1036665", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Unklare Meldung bei Verwendung falscher Quellen und Ziele", "RefUrl": "/notes/1036665 "}, {"RefNumber": "1036581", "RefComponent": "BW-WHM-DST-RDA", "RefTitle": "RDA daemon terminates with OBJECTS_OBJREF_NOT_ASSIGNED_NO", "RefUrl": "/notes/1036581 "}, {"RefNumber": "1030980", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "InfoObject not unique when replacing from query result", "RefUrl": "/notes/1030980 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}