{"Request": {"Number": "2392726", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 300, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002392726?language=E&token=6DE343177C005A5963A9B9DAFE39988D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002392726", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002392726/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2392726"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 33}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "How To"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "07.11.2023"}, "SAPComponentKey": {"_label": "Component", "value": "XX-SER-FORME"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP for Me"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "2392726 - How to unlock a Technical Communication User ID (e.g. \"401\" errors) - SAP for Me"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>How to unlock Technical Communication Users</li>\r\n<li>Technical Communication User gett<span style=\"color: #000000;\">ing&#160;<span style=\"color: #ff0000;\"><em>Error 401 Unauthorized</em></span></span></li>\r\n<li>E<span style=\"color: #000000;\">RROR:&#160;<em>HTTP Code 401 : Unauthorized</em></span></li>\r\n<li>How to determine if the&#160;Technical Communication User is locked</li>\r\n<li>Check Asynchronous Channel fails wi<span style=\"color: #000000;\">th <em>error 7</em></span></li>\r\n<li>Solution Manager connectivity error due to&#160;Technical Communication User issue</li>\r\n<li>Followed steps to Set Up Connections to SAP but getting errors with technical user&#160;</li>\r\n<li>Pinging transaction SOAMANAGER fails with <em>401 Unauthorized Error</em></li>\r\n<li>Error: Web-Service-ping fehlgeschlagen (RC=401). Service-Ping-Fehler: Unauthorized</li>\r\n<li>SOLMAN_SETUP issues with Support Hub Connectivity with&#160;Technical Communication S-User ID</li>\r\n<li>Unsupported xstream f<span style=\"color: #000000;\">ound: (\"<em>HTTP Code 401 : Unauthorized</em>\") E</span>rror while testing call</li>\r\n<li>Port fails with log message Web service ping failed for logical port LP_SISE_SUPPORTHUB, proxy CO_SISEHUB_MI_O_S_SHB_LIST</li>\r\n</ul>\r\n<p><span style=\"color: #ff0000;\"><em>\"Image/data in this&#160;KBA&#160;is from SAP internal systems, sample data, or demo systems. Any resemblance to real data is purely coincidental.\"</em></span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP for Me&#160;</li>\r\n<li><a target=\"_blank\" href=\"https://me.sap.com/app/techuser\">Technical Communication Users</a>&#160;application</li>\r\n<li>SAP Solution Manager 7.2 (and later)</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<ol>\r\n<li>Customer created and activated a Technical Communication User in the&#160;Technical Communication Users app.&#160;according to KBA <a target=\"_blank\" href=\"/notes/2174416\">2174416</a></li>\r\n<li>Customer configured&#160;the&#160;Support Hub Connectivity (Step 3.2)&#160;in SAP Solution Manager using the Technical Communication User (S-user ID)</li>\r\n<li>Various errors (e.g. <em>Error 401 Unauthorized)&#160;</em>occur and the Technical Communication User cannot connect&#160; &#160;</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>The&#160;Technical Communication User is <strong>locked</strong>.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p><em>Technical Communication Users</em>&#160;can become&#160;<span style=\"text-decoration: underline;\">locked</span>&#160;when an <em>invalid password</em>&#160;is being used or stored in the configuration settings. These locked&#160;Technical Communication Users will often result in&#160;<span style=\"color: #ff0000;\"><em><strong>401&#160;errors</strong></em><span style=\"color: #000000;\">.<br /></span></span></p>\r\n<p><span style=\"color: #000000;\"><span style=\"background-color: #ffff00;\"><strong>How to check the lock status (and unlock) the Technical Communication User</strong></span><br /></span></p>\r\n<p><span style=\"text-decoration: underline;\">Prerequisite</span>: Any Super Administrator or User Administrator can perform these steps.</p>\r\n<ol>\r\n<li>Open the&#160;&#160;<a target=\"_blank\" href=\"https://me.sap.com/app/techuser\">Technical Communication Users</a>&#160;application</li>\r\n<li>Locate the Technical Communication User (S-user ID) in the list of users&#160;</li>\r\n<li>If locked, the user will display a padlock in the Active for Data Transfer column</li>\r\n<li>Click anywhere in that user's row</li>\r\n<li>Click&#160;Unlock<strong><br /><br /><img class=\"img-responsive\" alt=\"Tech Comm Users Unlock 2023.png\" id=\"00109B36D62A1EDDB4E51A1065848FC5\" src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABsoAAACkCAYAAAA+LnvBAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAB3RJTUUH5wQEFCc0XSn9cQAAIABJREFUeJzs3Xt8j/X/x/GHfezENmwzhkxOy2mSs2UmTPo6RkQN5Ut9c0rIIZT4JkUUqcg3zI+IShSGWmrOwxia88RmswO22faZD78/PsPMTrQTe95vNzfbdXi/X9f1vj7Xde16fd7vqwRwExEREREREREREREREZFipiRAUlJSYcchIiIiIiIiIiIiIiIiUmBsbW3NibKSJUsWdiwiIiIiIiIiIiIiIiIiBaokgIWFRWHHISIiIiIiIiIiIiIiIhlM+imCrwNjSEy5UdihPNRKW1vwb08npnd1vWu6EmUiIiIiIiIiIiIiIiJF0DvrIvhm52USjUAJ5XL+iUQjfLPzMiVKWPDfbneSZdqrIiIiIiIiIiIiIiIiRdCiwBiuJpsKO4xHxtVkE4sCY+6apkSZiIiIiIiIiIiIiIhIEaThFvNexn2qRJmIiIiIiIiIiIiIiIgUS0qUiYiIiIiIiIiIiIiISLGkRJmIiIiIiIiIiIiIiIgUS0qUiYiIiIiIiIiIiIiISLFUQImyGH4c5cmc4Ptdz8gZ/w3sjXnAaqM2MKL5fA494OoPi5igDWw6ZcybwpLOsmlDEDGmvClORERERERERERERESkqMo2UTaiuSeNM/s3L6SAwgsncPFsNh1OyKfyjZzxn8/YPh3N2+XdlVfeW/ngiblCkUDwL7P5emf4A5cQ4z+RERvSNjp8J1/P8if4Wh6FBxyal9kx86DJUxERERERERERERERkbxRMruZ/9282fzD4YV4r3RjxQcdqQRgbZf/kQFQjZdX/ZZPZRs5NO8VXvmzPuMmLmGKmz0Qz5ltS5gzYSXj5/fF3Sqfqs5Tdjwz+Tee+QclJF9LuHMk1OjLmoA8CEtERERERERERERERPJZFfZ/6oh7+kk3TATvuEC/7y5ztpCiysmIYfWYUQt+/+EIzwWkn3adpSNDeaMAY8m2R5l9WTvzPwcrsLbG+dbvtkZCl0+kV0dzL6wRi9MN1WeKIHDeGHy8M5kXGcScQebeW96D5rP3MtzuWeSfft5SDiWlmxecQ9kxQSwe9xLezT1p3PE1Zm44S44DEZ5fz5zlrkyeO4HeDV3TttUVj54T+GZhWpLMlMChW9vZvCO9xq0kNMm8+qF5noxY6c/yMV1p2dwT7+ErCT0fwuLhHWncvC0+k/25YOL28I8/bvuEVzp60tj7FaZtO8uZjTPo522Od3Gw8U6ZG9J1ZwueT+NRG4i5NW/1Tn6cbK6vZZepbIrg3vWy2kdJZ/nxo9fumR6zYQxdZwQROK0rjZvP51CG4SrjQ1be7nHn3Wciy48Z08W2lsAfp94u852NETnt9SxduLV/mrfFZ8zSO736In5n5pC0fTzoE348lb7+paye+wotuy8l1BTBrx+9Zj4GMh53IiIiIiIiIiIiIiKPsqvXWL33Mqv3XiE4yUDDpyvxQdNCjsm7OuGf1uMX70KOIwcP9I6ymI0Tee1gKz5eH0jQtm/oHjODMT9GAEYOLR7DzKQurNwWSNC2r3gmyp/gtKTHav+zPDd3M0E7NvNf97VMW3f8dpl3z1vCpNXHM9SaVdlGDm1YydXOnxGwO5CgNUOw+noGP57PYRsO7+RQax9au2a3ne/d2c4da5lSO4CBH/xOfNr8wFUhVHr7J3bu+D/+fW0+AyfswOODzQTt+Ir+f89g3bFbJa3k16jOfPZLIAHTGvDLtNdYkDKQFQGBBEyoxgK/AHIz2uPetTtxGP4TO3cHsqJfOO8suBNL9vsIYnYsZW+10axNm/50yBi+DjLi1HkW37wMnpN/Imj3MDzSFxe1gXeGBeE5cz1BuwNZP9mdbf+Zwa+X0+Yf+J4/yw7DPyCQoMV9CX9vIb/eHVDuGHeyfGIKvmsCCdqxma+eDmfveSMYQ1jw5kKs/r2CnbsDWT/OlV8HzWfvrSzojvWcefID/P368vjulYxN6cf6HYEE+X/GM1FBXMij17aJiIiIiIiIiIiIiBRpScm8svwcrywPo1VIClCSJ2oAzmX5ZHQ9Ej/1IPFTD8JHV6YjgHN5vn+3gXn6R0+wY3I9Ej91ZwHmnl2J6RJcCyZ6kPhhdUYADTs+zpGPPG6v9307K8CKEYOfIGaOeXrMu9WZUb8K+3vYUQYDbXp4sL/f/W1OtXZ31/NLV3vIsv4q7P/Ug/C3a3F6jgfhw8rfV10PkCiLYe+2nXTr25nHrQCDE88858NffwYRw3G2LXbiVd82OBkAgyvdJ0zgGRfzmt2f74K7PWCw44m6jblw9U4mI7t5ZlmVbYXHgFmMau1kXsy+Ac1ahXAhp8yTCXCrhFN22xmwk963t9MOj979eMY/gL1pySD3fs+bt81QDY8noWHPLjS1Bwy18Whs5K+zt4LoSZ+etbE3gL17bZomdaF3F3OGzr5uAzz/OM6FHMIFeLzn87f35eN1G0BCfIaec1nvf6d27/Lf3uYYMLjStGU1LkRln9WKCQogsHtfulczj0FpX78nvq392RaU9s64as/T2zttD9ZogAfxXE3KxYZkZHDC2TWEQP8QLhiteLz7BN5oaAXHd+Dn1JfezcxDfdrX9uG5Jmv581YC0rUL3b1dsbe3wqqcE5VDdrAxJAKjoRrdJwzBw/YBYhEREREREREREREReWjZMsPVCrjO2VMwoEclXqtiYvW3Z3j+23iSqzrxSX87BvQsT0dHOHvoIq9sSaJMGUPORTu78kUHe8pdiOL5RWf4KtKSjh2r8jYuDKxvxZWjF3hlbQzBWFKGGCYcNAImdm87x4Q/728r3m5qT7WkeN5afoGvIqCiXXb1m5VxteTioct8e+z+EhXZvqMscxEc+wOW/+HJ8vSTbQdwIQrOYMXjWexPq9JZv/Qru3kARJ3NsmzjqQ3MmbmQH4/D49WcMEaAZ8ccNsMAnD5LDPWzSJaZtxPfdJPsnXBOSwY5A86l7O/ehpJZbYMV9hlmWefimMsoY333yGYfERPE4o8+wS8gGuc6lbCKOYvza9kXd+HkTjAMTDfFDmcXiE9KMf9azhqHXEefDUNtBi2ewI8rVjCy++9EV+nJlOlv0fDscYxBS+nafMZdi3u2i4FKwONOON+aWGcA30zewDK/MfgERPN4z4lMH92Gyg+wn0VEREREREREREREHioVHEn81PH2r5Ghl3hrL7zdoSRYQO8XH6d32rwrDrY0dygJSQl8tTiK1UCd+g14u1oOddS3pZollKnuwvfVb000UA0TyalQoUZ5xpdKIeDPC3wWksRZj5sAJF+9zOZz97c5V1JvQoVSjGhnIPREDKN+j4f65bOoP01sAv2+OXff72V7gESZK3Vaw8t9AxnVOOO8EJ6wjcF47f5LzZFLtczLNoXw+aC12H+6gp0N7QAjgR+1ZU8OxTk1aInHe+vZdrYzvaulm5EUxJz3Qmg3qTN1WsOx6+nmxccQjT0NC6qn0vWcF7lLVvuIGH6cPoK/fH4iYKY5LXhm9SvMyaG4yjVbQmhKuikJREeBfV3r+wrL6bHGcNzc++12vtAUQ8wZqzsJQ6f6dB/+Ad3fMHJm/UT6zfZnw8DaWDX2Zs2CzlTOWGhwxgngVL8zo2Z2ZpTxLD9OfIWZ/mv4rFPWfQZFRERERERERERERB4JV6+xOjQV97plaGiVxKrVl+4kjJISmDD+NJ+lW3zBRFewsKBcJkWl3Mi+qrN7/6Le8rvHu6v21XVGPFMG74qleK1rdbpXO0f1bDp2fRZ7nRlYYVPq1hQrqpUycCsxMmHpGSLalKd7LWtaPO1KR49SvPJbVvVXYT+A6cZ9J8nggYZedKJpu5asW7mW0HjAZOSM/wzmbEsAavN0PyPLV/9OjAkwRhA4dwY/RjxAZPfIouzz8RiTwMHWPDxf/PH1rPolbRWDFWAkPrN3VVXpwqhBCcx8bSLLgyOIv5xA/PkgFowcQ2CVxjxhf2s7N3DBBJgS2LtkIb/6eJuHV8wHlWu2JHBbgLm++OOsXrueHPrZZZDV/o/HGAcOtmkJrqidrF6b7h1w1lZEx8ebh6NMx6mxD54bV95+31t80BIW/OFDu8Z297ddrTrj+ct8Zm44TszlBOKjzrJp7lQWOA2jUx0gyp9pM/w5YwQMVjhXqkZ5gNqt8I1ZyfI/0oawvBzC4skrCTXdW0fMxhlM8z+L0QQYnKlcRQkyERERERERERERESkmkpLN7ydbF0+kpS1D/l2FNsDumOtgW5qBLzvR26syv0yuxQL3tOnWtvQe5ELvDm70rlridlFfxV4HDDzZuCoLXq7Fs7de+RWSwtkbUK1uFd5tWpa3X3bnyBvlwbki/+vnSLnT0Xz4y2WCU8DG2gCXTSRjwK2GEx2rZoj3lJFIStDcy53vX67KipE1GVAZiExkFVa8+1IVBtom8uW2i3x7/iZYGiiXVf3/0AMkysCp0wd89WQQ7/TypHGrLry/ozbtmtkBVngMmsWbrKBvO08a+7zGKnsfWrv84zizLrtKS15+rxrLhnjSuHlHRvzozDPdMScdnRrzXLvNjGg9hk33vLPMCo8h37BmdCWCPxiId8eOeA9ayIWO8/jmjfpY3d7OHYx8zpPGrXqyIKkvKya2IZ/yZDh5D2GUYSm9Wnni/eYGnHsOp2kKJOe6hKz2fzW6jxrC6Rkdady8LT4fnaVh95YYr5sziE+0Goj1kpdo3G4+e1PTFefiw6y5jQkc3ZHGzT3pssDIy4sn8EzZ+9wwFx9mLe6L1U9j6NyxI969R7DK1JcVc3uah4l0asyzLgGM7eJJ4+Zt6fm1kTdH++BkVZ835g4Bv1do2dyTlr5LuOrjjXsmwyk6NfOh0rZ38GnlSeN2/VhgHM44HyXLRERERERERERERKQY2X2GqSHXsXEtx5x+9ixdG8nqiBu4N63MNz2daG5h4oolLF17ic2xUM2jIt90sOVKbLoeKltj+f0qlKlalgF14eB5E9ha8kT0Bab+lsDZkna8/XJV3m1ckpSrN6hW6gYXU0vSu3NVvnnZCffrSXy7PQb2JBKcAtU8KvNFR8d74vzPtgTOYk3HpmXpVs1ASuQVpi4/z+8YSEq4ScXGFfnm5aq85nqD4KBLfJVV/f9wl5UAbt68efMfFiMiIiIiIiIiIiIiIiJ5qfTIQwVSz4hh9ZhR6zpLR4byRoHUWLgSP/UAoESJEg/Wo0xERERERERERERERETkYaceZSIiIiIiIiIiIiIiIkVQQfUoK27Uo0xERERERERERERERESKPSXKREREREREREREREREpFhSokxERERERERERERERESKJSXKREREREREREREREREpFhSokxERERERERERERERKQIKm2tNE5ey7hPtYdFRERERERERERERESKoMGeTjjYGAo7jEeGg42BwZ5Od01TokxERERERERERERERKQI+m83V15t5aieZXmgtLUFr7Zy5L/dXO+aXgK4efPmzcKJSkRERERERERERERERKQQlChRQj3KREREREREREREREREpHhSokxERERERERERERERESKpZIA586dK+w4RERERERERERERERERApUSYAyZcoUdhwiIiIiIiIiIiIiIiIiBaokgMFgKOw4RERERERERERERERERApUSQALC72qTERERERERERERERERIoXJcpERERERERERERERESkWFKGTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIqlkoUdgIiIiIiIiIiIPDzeX32IZQGniYlPKexQ7ouTvTX9vaszpbdHYYdS5Pyxcx8HDh8r7DBEJJ81alCH1i2bFHYYIkWOEmUiIiIiIiIiIpIr768+xPHweH5+719UdrYr7HDuy4XoBKat2MP7qw8pWZbBgcPHeGvoq5Q0GAo7FBHJJ9dNJj75/H9KlIlkQkMvioiIiIiIiIhIriwLOM3kfs0euiQZQGVnOyb3a8aygNOFHUqhOHfuXLbzlSQTebTpMy55KadrysNGiTIREREREREREcmVmPiUhzJJdktlZ7uHbshIERERyV9KlImIiIiIiIiIiIiIiEixpESZiIiIiIiIiIiIiIiIFEslCzsAERERERERERGR/HDz5s17pllEHaN3n8GULl2K/r4v4O3tmW/1lyhRIt/KFhERkbyhHmUiIiIiIiIiIlJsWIbvZ8zoofT37c0yv+8KOxwREZECEeW/i72FHUR2oiOJKqSqlSgTEREREREREZFiw+LyOTw9m9KunRe//R5IampqYYckIiIPImQr5XxXUW7J0cKOJJ1I1kxdZY7LdxX9/SMLO6A7noLtvutYE13YgWTh4mHGFNL+0tCLIiIiIiIiIiJF0I2bNzHduImlQd9zzksluInBYMBgMJBqTMXS0rKwQxIRydHuRUvx9r/z+3uTBzDOowADiN5F/1FhrM/VwjYsmtONXs75GE/IVsrts2NRTaDSCcotgbiBdfOxwuxF+a/D3S+ZSe2cmH7SCX+/9jQN2Uq5mYeZHR7A9G0FsE8y2LtkFT7bMpmx4Si9CnFfFUV5niiL2TiRrr+15qePOuGUfsaljYzpFcbA31+nfl5Xep8iNk5kYtjLfPV6XawATLHs/HICE1c7MXrNB3Qun8VyIiIiIiIiRU3sUeZ/dZSvj5oIszTQoUkVpg9sQe1SR5ntG46XX3uaFmJ4tx4a3FK7Sjmmv9mODhUMhRhVNkK20j+8Act8KuRvPVm2W/5Wm5mU49sZNjeCQ0CKfTkmve5Jr8dL355/ZfsGqi0ysG5RJ7xszNNy264PXfvnk+RUE1uCI3BzLo1HtXK5Xu9CzDVOXUzg6ToulDQ8PO+6urTlO96hNQs7VLw9LWjpMnY07s/wLB8KHWbegPO0WtqJxnlQX76K3sWAt+48LK5V2YEhfZsxyMMpm5WKxjlZRB5Gh5j5wgEOvOpD0neuadMiWPXOUmx3NyJpcAFly5wfp2vNBIa/m9N57Cizp17FKx8TQlH+63APr0XcQCfWTD2Mm083Qv3XFVqy7HY8fk6s8Y8hzi8thvrtifM7yhr/BsT5xTDbdx1rCiJZlpbUxNebOL/097RHme17ArfOSpJlVPy+knTy/xizoh4TBqclvy79wYwhr7AwyolqGRZ17TSRgVETmOgfW/BxioiIiIiI5CQ5lNlTj3LZuzUH/foQt6gzH9ePY/6GE6QUdmzpdPH1Js6vD3F+vfDvDdPn7iKssIMqTPnZbpG76H9fww+FMv/DRHrP7MXuBX04ONKB4B3n08URyZbfDHzW08j6oLi71sxtuxbV9g/btI7ZIQVTV0x8Cs721sQlGklJNWW77NG/r3Ah9hoAjzmXxrt+hftOksUmpLDvVMwDx1vcDBs+gbdGT+Fg8BEOhxzj6NHjOa9Uw42/lvUhdlkfdo+uRMTi3/jkWHLO62XHdJTpU3cV2vtZ7tf18F0sm/MuE6a8y9hJH7JsX1zOKz2gsA0fMn9HfL6VL1KU7V50ACYP4NtOrummutLnvwMI4AAzDxVUJBXo9W4ltvtuzeY9V2lfCni3BS75GcpTLe5JiLn4dCOuCXdiiz7K3gIaYtClUmkIu0oUFejlkzEJVZdePhUg+irBlMYt33uUHWX2qEi6zulzzxe/9i4Jx8uvYHu1PSyK2dCLCfj7LafxsHXUvvXltUsXsR/yDd9U382YgD8yLG9Hm4GvsmLsakK8X6e+upWJiIiIiEgRciXoBD81a8jvLdP+CDbY4ObVic8AOArcIGzbRqZvTCTsiiWjJ3XG181g7s30+VG2XIGkUi7MGu+FR6mjzPY9j9tAE34bEwm7YsO06Z3oUsFAyplAxn4WQZgBMAGUZtjkTnSwOcHXs4NZfwWSDA6MfSunnkIGyjSqQtdPwokCKp7ZxfSF5zmUDNbVq/PxG0/hlnKU+VOP8pPJgrKWdgwe70OHMons/b/NvLMTbO2haec2+F76na8rPce0ZjYQspUnPoHV/2uPB8lsX7CZqN7d6GXaz+S5pzmUDEmOLswafWs7w8AniUWnK7H93QZc/vk3hm1MxtbSki5NrKB8vjZbju2WMb6La7cy+U8TSanQvG9Lpnm6gimGvd/+yfR9JpJSDfQe0Z5/1w7n62lhrE8x0C0kmmmzPCkTuI2xa66SkgoubRsyv2ctrO+KxgSpgCGt3Sq1YNpL6WaHn2R1eVf8nk5my5IzXPEsR5l7tujudnXLcstz0f6Ge9t6kqdNJtMime0bhnV3I1v+TOViiiWD32zPv2uXBhI5lMk+i/Jfx5iw0pTdE4NbT1c2r00myvp7tns1YF3vWg/cnjm5cRPOR1+jpqs9ZyITuJyYSoWy5v0dl2hkx1+XSEq5TvkyNtSsaM/Bs7GUNFhQs6I9FcracO5SIuXL2HAl0UjjGuYeSycvxnMl0UjDao4cCovjbFQCAC3dy+NoZ8Wu49FcTjQSl2CkdV0Xzkdf4/C5y5hMN6hVyYEGbmWxKFFYPdQOM29AGI8NMPHtpgTOXbFl8vvd6JSxE2fkbqZ9dpIjyZDsWJHpo9pRv1Ta9DknCUoFKj3OvDdbYXN7JRNX92xkzK7yLBzRPNcRPftsO1JTU1m2bBV/HT/FDdMNunTuQFvvpwGoW7d29gU4N2TSsDja/HSMQXUaUSbyAJPnnuJwMiQ5VUg796RfwUTYPZ9NB7Z/epTlYbB3zEaGTexEh9TMzmG53qz8ZQxm6eLTPPXmVPrbw/XwLSzeEcX1JuXy4WFjPBfOl6RmU/s8L1mk6DtEwNnqDByc+dzm3asz58dD4FFQYzDWZbQfzJ66C7d7kmGRrJlaMD1nXZyz6Plfv+6dui+GMy/cKf9HCQCoaEeXk5Fsj4ZezncPeThpXB9G14eo/ZGsr1mBWfkcSpT/CYJ9WzA6k2RY04Ht87n2h1fhJMpMsQQt/4xP1v7B2StWOLXqxrAhr+LzeFom6sI2PpnxJesPx0L5unQZ9g5veZu7z4d82Y4ljmNo8NuXLLR/nZ9mNOdsdmWld2Eba3d3Ysg76ebVfYFhAJeyiLVqa7q4vYD/gVep31yZMhERERERKTqOn0jEq+5j2Sxxlb+cO7FuVmlSdm6k4m+h+A6sS1RwFBUHdWNdJUtSdm6kt38Y67pnsvzGUOIGOuG/PJKmo3vxWRVI2bkR39gGdHBMZvuXR7F+pQfrqhjgfCC9/YJpNuapTBIpt6RyZd95fqpfjn+TTGiwka4TezDNHo6vXcfXQXUZfvkEe9p64v9sum9NRx9m3rFKLJrf4k4S5qQju/88Dc3qcigkmS4NIfg8eLieZu/fzvRyPo/fhL9p+GYPplUwkLJnI82XHWb36wbgKpfdO/OXb2k4s52X9zmz6NMWuBkS2b5gA9vzOVGWm3a7FV/KPn/GGJ5k3Rw3MIXhNymILR6d6ZBymt3lG7NuThW4dpCxUw4SNsuTf78exth9lVg3sC6EB9I/2AW/OT5Yk8j2BRtZdLIWw2qmr8udXgNP023YGpo2caWrT0261LzzQCnsQCTuTZ7D2vkKXRP3sv3KU3S5p4HTt2t2cm7/adUzaevoXfdOIxK4SmSlTqyb42DuSTd1L80WeOO+L5AxsbVYP6cu1qYw/CbtxO/x5+kIrD9ty8Ev++BmAK+Lq9je5HlG5/P7IZJSrpNy/QZlSllR2akU52MSqVDWhlTTDQ6eiaNFbWec7K05eTGeUjYledzFjqrlS1PZsdTtnmUuDjacj76G8foNDBYluBiXhHvlMiQkp+JQypIeLaqSmHydoFMxtHQvT8Nq5Th3KZGW7uWJiU8hJj6Fbs2qALDreDSXrqRQoaxNdmHnsyscd+7Gtx/ZkbJrHTU3HeXvAennn+Pbz87RYMSLTK5gIGXPOtouP8BvQ5z44bNzNBj1IpMrwN9b/AmJgyZpa6WE+DNkSxk+Hp/7JBnAC726ADB8xAQu/B1MdHQcnf71Ius3bAHguU7tGDE8+6Obx8rhdfQS57hA8LyLdJjci2mlwLh3E11+OoH/i+mWDd/F5Hs+m70Y1r8KzT6HWe+2wIXz+E2KvF1Oyp6NaeXkX1L3vpwK5a/y1emdlrsqWakDr/Uy/3w9/E+WrvyTC8lJJFk8Sb83e9DAFmKDf2DxhlCSbqRCnecZ26setsZ9LJ5zFAfncPbbdGFGn3LsWLWarWeTSUqxp/3QobQrF8qxyEpU2PkVM46EE5tUlR5jB9Eq64uNyKPj0hUOVCvDuKzmly9Do7NXiAQKIB2Upi6jh5p7sC9L16tr75JdMLRbsRte9lZSbNK4PvRyNg/DOK/SrSEPI1kzdSt767enqU83/MNX4e4bdjt5lvci2b6zNMPfLbij4VFROImy0B8ZE1iPr9a8R21DAsf9v2TtX7H4PF4RknbzyaDlMGUem5tXhEt/MH/kBBa6fMWQuuZE1c7//UHLT79hc2U77EKX0yursjJIOHGEkFatcb+vfJcjtT0qMvvISd5qrrE7RURERETkYeJAx/rmd01Z1ypHl1Dz1DKVLdk9bx1+qRaQnMpp4pI9AAAgAElEQVT2JonZLG+FjcMNwi5dhSq2hIUnYe6+cZq9gclsPrmONbfrcyYMyPid5vV+AZTzAywNeNVyYd4wczKtTCUj06f8wHSDBVxLpUz3K7g0rID11J2MjalE7/ZP0rSCDZRzo6t1IGM/CaC3jztd6rhi/bgLHRfHEUYkx8/b4dsd/EIjITmK3Q0rMzr6PFsMFZiW1sPN2sOVLktiCMUFKI1XHfN2Rp2Io2LzZrgZAErTvIkDX1/O42a4b3fiOxQSx6F9QXT7M+j23C4RQJWylAnZQ7eNe8Bwg7DIsvTm7t5cUSHR7AmJoPeY02lTLPAIj4Sa6R+eGHBr15mDXjEcDznB+pV/8CRu+E9uigthbP/NCq93bQAbmjYxMjs4ji5e5ndsZdWuGd1P+2fa1plNMwA40KGRg7mSCm50ddxDWCykhMTh1aydueecwQ2v5kHMPxNHR8CjoUtaWxec6PgUSlsbsCppQdlSVhwPjyc51cS1lOtYlACHUuaHFDUrZt1bxs7WEmtLC65cS8W6pAWmGzcpW9qS66abXIi5xqGz5iHvDBYlMN24ede6UVeSOXspgfC4pNvTXMrYUIHCTJSVoV09OwCsazrSKTTD7OgwfrWoyOTbn9/KdFp2iRPRCXdNf6xDJx4j7bvPYQcYs8rIkDmteewB29jewR4bGxuqVHElPPwiu3Zs5OrVeNp36Jlzoiz1BsnWJSH6b7ZEJHJlyvd8AlCiBLZu8USl299ZfjbLpt8H59kSbi5ndtokW7ciNPRgtXo0WLOCD2YE06DZ07RvXQ8XK+DKLhZ/n0TnkeOpXBLCfvqQrceu08D9IJtCq/P6hB7YW5zmuym7ONmrHg3C/ibMWJIevcfzgn08O778nJMthzLlJXuS9v/AhuhkuPo3YSVL0qzda3Tufp0937zLX+dRokyKh5wSYTkl0vKLcwuWNdlKf39zj60o/3Vsb9It015Mjz4bFt313rHSdH3qVmtVwM1tF2HR0NQZmg7sQ2ildfjlWywxhGGHV76Vnw+id9F/VCRd53Sj1+2JR5ntexjyLaF4r8JJlNnaU+ncdgL2NcepUVVqdxrDhLRZCbu3sfaJ3qxpVdH8DrGKrXm+z3Je2XWSIXXTElVdXqCnu2OOZWUUHRsGLt2xu89wnZzcMG6PIIa6ZPdaVhERERERkYJUu1Zpvtt/gknNGmQYTu8WC7jngfEJFs2Nw2t6Dz52NEDIVsrty275cjRs4sD6PwPp/38m3No8waJnqwChYOnApOmd8MrheXsXX+97h72J38/8JTB8Xi88DOZv344BqNCCRfMSCTt2GL95G9jcszOTGrnSa3I3uoSfZv3PO/Ha+QTbB1en6WObOXTGlt1lXOhVxYiNfwRhpis0r98aLC5jTVbvgrKhbBY5ieQc3h+VF3JutzvxWRugS/+0ISbTOfRtMHsbtmXdW07ceh/IPQzg7tOUdd2zHgzxNksnajdyYnSjCNwm7TEPHXT5DF9fSabs1O+ZD8ANwuxDifJqAWTRrpm4r/Y3ZNbWdTOZlnV91tkkSdwcHXKMNy+ZbtzkbFQCl66mcC7a3DvMeP0GMfEplLbO/SMZixJQybEUF+OScChliaOdNZYGCw6djcG1nC1t6lUgyWgi8K97325lUaIEdSqXwaNauTzbrly5kf6zlETyNdKdXwyZnGvSsTBk/vnNajqw7xi83cHID7su8EyHyg8Wczr2DvaULl2K0qVLcTEyq2GI7kg5fokNNcozycKEdTlXPpvlSVWgxO0hLtO9OzCrz2b0mTs/WxiwdjSXk4tPcMGzrUf/d6YSdWoXgVvW8/FvwfSb0g/7bVs4G2fJ4o/vJPcbuJcEoyWWURuZM3Mj3Egivkxb2gBhoaE4th1KI3vgxFY2WbdnSkPzCdD2qR68AIRtCMWl7VAa2APEERtdlZpVC2GbRQqFB97VlrLkUGvGZTK64u4fT9Oo+YB7ZxSEinawLwaoQFg4uD1VOGEUvmQGj1rFT2n3Oy6VYPDnu8zvaYvexbywCsxyBnPvsgAGn4RJBZ7ZLMKcW7DM7yizfdexxrc0cIbZvpG4zelToO9SsyiwmpKM3H6N7OMv8NX87vDbx7zSox0d//MBa0ONAKQkJcCBWfRq0w7PtH8vfXoS419ht9dvWT3dLUI2ZWWUEHXyweM3ZV6miIiIiIhIYSnTuBaDj4Xy8c4IUkyAKZmonf70/mQ/V7Jc6wYpyQZsbAxAInt35tx9KuxEHBfLVuDfL7nRoY5D2gNwN5o2u8rXARHmhSJ3MXtbZO6DTzVxuZSVuY+FKYLtO5MBiNq2lTXRNrjVb8HwTqU5fukKRO9ltn801pXc6fViNbzCr3IFGxo2NBC8ORrquoKNK81So/j6kBVNaxrA0RUvUyR7I80P1VMORbDewwn3DGG4VC9D2O4wwkyA6Sp7ghPJb/fTbu51yvHdxoNp8UWwxm8vUQCpJqxLmZNnKcHn+Sn9Sqnm/1zqOGPtH8reawCJ7P12O3uTM1QQe5DJ83aZyweIjyQ40hYXOxOHdkfjNbgz62Y9n/avBaOTw9mcSU7uvmXR/pm2dabtD3CVLQeumteLDOMnkzMejuZ9tn1PKCkApjC27waPxzNPEiWZ8jcxmpB8nRs3oWvTKvRo/hg9mj9Gy9rOhEUlYmtVEtONm1xOTAEg7FIiUVfM++HmzXvLcrK3JjYhhbBLiVR2sgXg+o2bWBrMj3YuXU3mWsqd7blVhLODNedjr5GYcp0bN29y9PwVrqVcz7+NBspXL0PUpkMExZsAE1fP7GDpkfK0ejy7taywtjeZ34PoWAXPGxcJuv35vcBGj/LUcqyC540LBJ5PK3ePP9+eMi/T5NlGvNirFe12BrDwVO7b1dLK8vbPLi4P+EQuOpiPl11l8HN1KOPoipdlBMsOmc8lKSEBfB2SDFhibX8DTDl8NlMhCcznMMsI/IIzllOEWJTEpdbT9HijH62s44iNv0TYGVvaDBnPlAl3/vWodZrvFgTjNsD8+8SONbB3r45L2rvHnqhlToxFnQvDvqpbhm/1p72frEbatwfiT3HyelUq63VlUow0H9wIpi1l5qH0UyNY9c5SvP3hwIWIwgpNMPcSi/NrQEO/w+wFqN+euKEwxncV5W4PpwuEHGawWwPi/PKzl5QTbiQQlosl9y5ZxeyQ/IrjftVltF8twvxiWO8XidtdPfQKRp73KHOqUhWrQyc4a+yEU7ohDmOO7Oa4e/PbvbnsarZjyDvtGGIyErHjS4aPXI77plepZmsHjcawZm4nXDMpP7OPfVZlZTze7Fxqwr1frsodg95PJiIiIiIiRYyNO6PfNTH/q0CaLzARZmmgQ5MqTH/9Kcqk771wl5r0eukEg99ew3wbG7o0Kn07sZK5RMABnwpJHAq5CvGnGbHPio8/7USH/k9xfHYgPpsBKwfGvnUff9E61mVIo80MG/k9to52dHjMnPRxqe9I8Nwf8EuFJIMz/323AljfwOPkTrpthKRU6Pp6R/MDB3dnjn8ZTofO5iSIh1sC/U9WY4wNQBV834xi8twf8EuGJEcXlo1ugHXG/VKzKfPqb6Xb62tws7OkSxMHwmKvkq9v+riPdrNu4snqsK0MHrkGsKB535b0Alx8qpMybSPd1lrg1tKRZqQlPtycGbbgKD6jopg1pzUfv7SNsW+vIQVwaduQ+Rl7/znWwtc9kLHDVxEGpFiUZtjYtnhZnmZ+YGk69Ey/QhU6tg1i2IHzdLTkn8mi/SlX5d62Lnc1k/YPAxyoEL6dbmOSSUq2wXd0W3PPmyaezArbSu9Rf5GUCs37tmRaJYjK8DCodt1yDPvqB/Z6NWDdixlTqHkj6koSTnZWWJW88z3l8mVsOB5+leumGzSq7siOvy6RmHwdZwdrWrmXp2JZWwL/ukTNinZULGd7e71S1iWxNFhwzXgdB1vzM4parvb8cTSKg2diqVjOFmtLcz0OtpZEX01h04FwvOtVoLarA5v2h3Pz5k1qVXLAxiqfx5+s0Y55ff35aML/0T0ealVxZOw7PjTOtvdpVVq12MeQN/yZ95UPL46IYNpn3/JtMiQ7VmThqEZYg3n6nG/pfg2uulVm4X8McGsEQ0Nlur9WleEf+cPsTneVnpqaiqXlvQfugX3bbv+8dfN3t3/+ZvGn2W/jqTCe6G9+FFnVoTSDh7RlaB0boDIvv1mDyZ9sxOcbKOFUgVmjbYDH8GoZTP/XA1i0KIvPpo0LHVL303vYRia93wnfN6uby1kCOLqklVP44oNXsCLKm0EdKlGS68Tv/5PD5RszyqkkeyzjuPB3MlSwIf7YD2xN6kIP+2AO21SljT0QH8yKjaFUe74/GPdxLLIGbdKdbpMS04YIjdvHst9t6f9c0t3LnD5NlHuLotnLTiTfeDDuOw92L1qK7bQ7U9+b7MOSVf4M/J8/L+LDt50ye5r+qKuA11DM94WFKMr/BMG+tfCNBpxJ6yXV4u5lKlZiUdgJ1kTXzcckUAW8Wu5ijH9kDr3+j7I9zA3fgfkVx4Ooy2i/uowupNpLADeTkpJyXDDXTBfZMOkV5lm9ygeD2uFexsjZgOXM/mIb1cZ/x7vedhgP/48Zexsx5OVGuFpBwoH/MXwCjN70KvWTdvNJjy9JGfkOw31qYpdwkg1f/AgDx9C5IoR82Y4lbt8xq5N56MVsy8oQWkLAB3Tc3prNU1rfGX7RmEBCEnBpGxMH/UGbxe/RsTxga4ddWm7s+PKXeM34Dr+9qneUiYiIiIhIMRO9i/5zYdr0Ww8mI1gzaQ+8WfDf9BS5wzzcpJdfe5oWdijFTDnfVfy9tH9hh5Fr/pu28K3fSvbs2csrg15h+FvDaFDLg5hLGV+MljWn8u6cP3fwgeq/M/Ri4Tt37hxVq2Y+ZuFnC/14e0Q2Y5qmFxfMd0u+Z08MlLQoSQX3DrzQpwWVS0LSkR/4eOVBrpe2xbFWJ/p3b4jjjVC++3gF+7Gncp2GOJ44TbU3XqPVxR+Ysqs67/s2NJebdITv5q5m/3VbbO3c6TKwB42i717m5PfT2FFjMv0b5sUeEXkURLDqHX8GHodur+acLPvos0WMGOKbN1VH76L/BgdmVTqBX6VuBfYuqWylf9/Vxa30D2/AMp+Y/H/fVfQu+o9KYPgcO+aNCqNhurr2LlmFT5gboT0TcF9rR+itHmb55iizfU9k0ysrkjVTd8HQf3Yvn9015WFja2ubD4kygKRz+C/9H0t/+YOzV6xwatCavoNep2+jtPeKJZxkw8IvWRlwgLNXwK5uO4a8OYae7mmZqQu7mT9/Fmt3xGIsU5U2PUcw+uVGOBnuTZTlWFZ6F9bx2qAwhvw0gsZps2M2TqTrh7vvWbTl+Ft1xLLh7Zc43nsdbzVRrzIRERERESluItmyIJD5JwADWFsacGtVl+ldamXxfi2RgqBEWWF52BJlr770KqNG/pvGT3kwY+ZnfL34/7hhMilRlsF9JcpEpIi5lSyzZcmC3vQpn/WSeZ4oGxUGuXxnacFJSxT5luYn7GhYUEP53UqW+TUgbOph3N5tT9OQtGRdpcOUK5Ak2S1Hme17mOCMbZPWZg3zIGn4jxJlIVspNzMm5+XSmZSPic78S5QVWQn4v/sCIc+t463muUx6nVvHa2MjGO73OvWVJxMRERERERGRYuxhS5S93MeXSeNH8rSnOaW6ffsuRo2ZTNCeLbkuQ4kyEXlU5GmirEgzJ4qmY8OiQnjf1d4lq/BJG9k3PxM89xMHADXd8ixZ908SZVH+63D3u7/3bnbJx4Ssra1t3r+jrGizw8f3ZZZO/Y7jTV6ido5Dcifw+5L/Uek//6ckmYiIiIiIiIjIQ6bfSy/y2n9G89vW73FxccbLqwUbN6ws7LBERCRfFe77rpoO7EPcwEKqPJ2iEkdGLj7diPMp7CjuZpHzIo+Ymi8xq98RZiw6ijGHRSM2fsASlxlM8LbLYUkRERERERERESlqOnV5jhdefIGBr47AaEwFwMUl910LjMZULC0t8ys8ERERKQKKWY8yM9dOH/BNHi4nIiIiIiIiIiJF09CRQ7ly5SoT3/kvsz5+777WjYqKpnx5p/wJTERERIqE4tejTEREREREREREipWJUyYQn5SK3/Lv7mu9nbv20axpo3yKSkRERIoCJcpEREREREREROSR19f3RcZNmE5o6Mlcr/P14uUM6N87H6MSERGRwqZEmYiIiIiIiIiI5IqTvTUXohMKO4wHUq9+PYaMGMGQ18dw9Wp8jssv/mYFdevUpql6lImIiDzSlCgTEREREREREZFc6e9dnWkr9jyUybLw6ARCTG6UdKlJm7bdCQ4+kulyJpOJGTM/Y/yE6fT3faGAo8w/VatWzXb+dZOpgCIRkcKgz7jkpZyuKQ+bEsDNpKSkwo5DREREREREREQeAu+vPsSygNPExKcUdii5cPP2T0521vh612BK7was/PYHJk2ZyYD+venW9VkcHOyJvhTL4ZCjrPl+A40bNWDUm6/j6Fj2H9VeokSJf7oBBeKPnfs4cPhYYYchIvmsUYM6tG7ZpLDDEClSbG1tlSgTEREREREREZFH082bN7Ocl5ycwvL/W8PPv2wlPj4BJ8dy1K3nzvM9nqNB/Tp5Uv/DkigTEREprpQoExERERERERGRR1Z2ibKCoESZiIhI0WZra0vJ7BY4d+5cQcUiIiIiIiIiIiIiIiIikidy+y61bBNlrq6ueRKMiIiIiIiIiIiIiIiISFFjUdgBiIiIiIiIiIiIiIiIiBQGJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcpERERERERERERERESkWFKiTERERERERERERERERIolJcrk4WMykpB0f6sYE4z5E4uIFC8PcP6RfJaUgNFU2EGIiIjkIV3bRB4tJiN6JCEiIlK05Xmi7MjCZ/kiJK9LvVO2zweBJNw1bTI/X8qf+iT3jix8Fu/2af96DmHKwt8Iy6eHycbDS3lx1m/E5nqFgyweMJOtuV5B/rFLmxk/cfPdbRSyCO+Fx/K0mvTnm4I8BuX+3XttiOPniXl8/r60mfHt0x0H7Z/N/Ji7tJnx7Qfid+ruyQm/TsO7/SKOZFPFnfPPMb7IYVm5P+b9/zm77ushwkV+njyQVcf/Wd3GkKUM7juEwSsf7BwVu2nynWOuS1+GzlzL/jy95sTx88QMx3b7Z3M8Xh/Yxd+Y0rcvA2bex7X2n0o6x8+fjuL5Ls/i3b4XA95bmcf7sGDoXjXvxB5cyZRXeuHd/ll8+o7i003neOieMV7azPj2Q+6+3lzazPi8uB/Kh/uqIq0InyMSfp/J9N8T7r4WtH+WzsM/Z/vFBykxb65tj7L8v+7KQyWzvz3zTN7c80dsnsKLq3N/ztYx/s/c9WygfVeen7yWEw/ds4FjfJHFcf1I3CMVpIzPCXqO4tPfH+gCXahiN03O5Hn7Mb5Iv21d+jL000Ai9GWb7CWdYtXkvnTu25fOPfUZkjuKXo8y08EsLwZQg6cS1uAXosO3KOozdxMBWzcRsPoTfF1+ZegH+fOAzerJwWyY3BbH7Ba6uJm3bj08sHqS/6ydTPtsV5BHQUEdg1KENRvF91vTjoOtmwgYUifz5WqX5sjO9E8uE9i1y0iLJ7MvPlfnH3kAcfy+1ZGRwy6ydWdC9ovedZ9QkX/NWoNvFs2cWyd2rOSpdxayqO+DF9RizErzMbd+JdO8LvLp6KUcye52Jdv7nYzK8a8P0o7puT2h95y0Y3ww9R444qzFHvwV44AFLB1XUMd6HFs/eIs/3Iax7MdNBGxdw+e9YPX/Au//HJ7++l8odK+aF4zHljLq01ie+2gNAVs34b90FPWOLuWH4/e/X/d/UciJymYVOLI4vx7gFhd5eI7Izn2dl28xEnIwgUZ17YB014Ktm/h2oBVffPYgbZ8317ZH3X1fd/+pAri+RPwyPt++dCzZy+997/rch2x4+f4+1Pl7b/nou/1sYOtPfN7sIEP9CvH+MA/b5p57pEXDqBr4FlO2xOVB6Y+w9M8Jlg7AatHnhXp/mLfnnJ58fmvbfpzDc5Ez+UVftsnWkVWjONdxERtWrmTD6nHUPBrICSO5u9brXPtIK1kw1RgJ27SIj344itFoRe2B4xjbpiJc/I3pE1YSZlsaK5eneXPiM0TMmckPh2H/G0fxnT4Kr7ue0DjSeuCzHFi4mbCZXXAz5KKOkEV4b6vI2MRNLA6KxLXHOAZZbeKLbUeJ4Flmzh1APVvgYiCffriCkKRUjC4dmTSxJ7VsC2bvPHIMdtTqPo43Dw/j51Nt8a0BEb9/zvQVRzEaU3HqNI73e9fAKukYfm/PYavJEjuruvSZMhQvx7RllwRjBNz6vc+kDtb8PHEm52qX5uflFZk5F4bueJqAIRXN0xs8RkhAMBFXS9Pm9XGMbBPHqgmL2H/VkcEHO/Lmgrpsb/8nXlsHUy/pFKs++IStMakYDQ0Z9M5QvCqav5nxUeLT1NvzE9vPRULHaXw+sA5Whb0vH1ERW2YyfsU5rMpY4dp6GJN61sAq07aJu7vtc/tQOJNjUIqw7M4FGc8bIYvw3gS+J9ZzosdSPny23IPV+eSzNA/dwwlTDWoZgEuBbHVuRqPzt75Zls01ZcfTBAzJq40XAC78yS8VmjHHM5bdi4JJaOOJ+ZGjkROrpzF9YyxgRfvR43H7Of19wkDiZ30Co8Zhv2gYYX2WpH3eE9j+QdrvpbO/vsf+PoePNkPMzqHs7zSMRb1rcGL1ND7aFovRaMVTA8cxsk1FYjdNZvqZmthtXYnr1E38p37Wm+PYciiTTg1k1c6e1GtjlcmxZM32WXff77S4upkv5v5ESFIqVu4DmDTSE1dD1nXcjj9DXIPsMivnGF+0/5Oq42L58YdTREQ+xpufT6Z9RSNHlo/jo21GrMpYUb/XFEY6/cq4pcGEMYnBR/sx862mxPzD/ZGjU5tYbDWURd1rpLU72NXvy4f172zjR7x1+/N+ZOFkzvaYxr9MGe8j67L/rut/T+plen93jC/a/0rFtxL4xW8vEc49mTTQil8W/0pIJPxr6hwG1bcyf9Pwvq9L93+vGrtpMh9FN8Nt33q2/B3PU4PH0SL0W1YdPElE9aEsmtgWV0MW58R/sNuLrgR2/fAb7ScsoUX5tElWVWn/1mTzz7fPw+YHjrGbJrOqyjT+U//e49nXtJDpvwTDwaEc7TedsW1KZ/n5fuA2yOm6VK0ng+wX4benLSObpW+xOH6e+AmMmsa/yoP5m+Pn6PNBM3ZO/ITYllXZ9f2vhCU/yX/GNOPk6jXsPxmJ2+vzeb9DRQCeKnsOvzfnszUyEqumo3g/7byRr9fPwpDDOSLzz2rW546WQZnd91dk113n5Wc50nsTvHySH453ZKjtZm4M/JwejwEY2T9/GEd8FuJb4xQHTjbjufL3hm3X+Gm8xv1JBOCYdj3LeOyRdI4f5szkl/NA2re/a/cbQ93N/7t9bGT+2c/qvJ6vLVFk3X3dtcvmM2DHf678ydbzscRU6cXMtHsC49msrp23joGm9IvYkOHvy1xcR7K8BmXSdnFrGb/4IDEOt+5HlCn9x2IP4jdnEdtjUjHad2TSe+b2vudv0boHH2DfZ/GZNsWxa/H7LA5Kl9FqN4yZDivSzkcXH+iz+yD3ll5GPeO6xbXF0zw15xyx1MExi2sGsXv54t1F7DJZYlWlGU/FnqTquLRrRmb3oVmdn+/527YfLMpd2xjPruejqesJs7LEqd3TON3zlbVM7pHsatDj9QFsHbeJEx364qRnWzmzexIvr/FsjwSyvM4aCVs/hynfn8PKygqvxlVZzLMEDIEvbj1fBHNvtR+q8uGQOlmc8zP/e+vuc07mf3/eOp98sdOIVZmaeLmnQpUcts2USILRk1qVzCO2jLo6gEXdqwJgDPqcwceeZenLekCG0QiGtE+FoSL/eqsvcCzDs+Se1Lrn/qAuJ2Zl9jwi4/18R1Iye+5ZiJssuVMwiTLjKXZd9WTmgqH8f3tnHl9FdTf87zV6aV5jLQFfkirQVxYNSwlRIQiCaBA0xRJReKBSrDQqxgqoLCLSimJBrAglUk2x8uAjhRZBKVYENwTZCobKJouPQC1QJdQaG7lI5/3jbrOc2W5u1vv78gl37plzzpw758z5bWdmMjjMkp8tY1OPEs57dSbNxrzOFN0K/na330beFzDh8f7qVcxNezKi1yQWre/LlD4Z8XSbY+QDbD1GdlkpLwf3MH/QTDY/+hxlQ4Lsf/E2lmwZTMc+lSx/spzuvyxlTDqEtpRSvOIgC4fJ5JE4GbTrfC5rPz4J31rPrPJuzH62hCAhNs25hyUHn6Nw/0t8cP00Ft6g0wo/Xcljq9sy5bclZHOMVYv2UEEuUM7O7zzHn9a2gp1luuOUszN4N6XPlkBVOfOLF7LusokMvb8/89/vRdkdOcAe1gEQYvuCmVQMnkdZbhCOrKRk+kra/XogTYBN22DM46WM4DBLflbGmsLoRCcklz2smJnJmNUTyYs5EENsX1BG8I5SyloS7pvnN5A3uQOGvveFbgy2aUDOoBSk4l31XKCaN0YAvBui49JXGaUy9LbM5qaC2bGvQ5+2c+C3Jb/bQlbsGUa7TlCxrZyOV/enYmkkUOYkU4Skc3T7W7TvMZPgBV9xVeU8Nn3Rk4LzIbStjMdODKbsd7kEq8pZtLKCfIOecJJVAGSQ1zOHRR8eZkSbVlC5g3V/G8yINsdY/oCzfM/sM44JH61m3ZWljO4UNiAeOzGYsmdzCZ45zPKxM1nedjZ9gO27M1m49HVT8ENN6/ZXsG73EeiBciz1NvyOELveruSqx0oZkwGHlpWwdOctDb4AACAASURBVNsVJqe6PfF2hdi1TFUPwAYOX/AcZc8ECW2czXWv7qGg6DCLygcw43f9yY7VNpiZI8tjDoHQtlIeCxZT9mwrOHOY5WPDcraTz/PhRMX+3WR07kmGe1YDu1R6pEH+H2P59Ndp+1ApY7LC/X/TgnJevqcJsJVj332OssVBdj13I49tmcbCZwYT3LeYYX/cytBOV7AvUbnkU1dtD2zaHWTMr55jdMVqJg1bxqkXZ1A2ppK1j97D2k/6MuJbNnNio1RVj7DrrSvo+oDPYp+9rRjPdzBqdWXMeHW6vhPuA3CWSzSh3aDbYOwy9l82jHaefsxWdqWVUPq7Yipef5ibloVY/HgpYyrfZmrJ2+zvF65n+6uHubWslBHpIbbPv4MF67swpe3bCbaz/uI8Rzjr93Yo9X7DvLyHXbxNqMMfeeO2IKFtRyh+9yBFt7aB0G7e+2ggg+4BDpaz88pujAbLyuKK9W+x6ZpcRhCWZ/NVc+nGMl7rMJGyya2gaiuzJhzj1j7fYfvqSCVR2+iZErLTQmyaM5z5215izGWgnNft7qRPAWJyt+0Bh2uggtaLSynLgKOvPcx9K3JZPKwl+7fZyc74GGDnaV4w2JducqQNa5Q+BlD33WAm9C+L6SNC9ako30PmXaWUXQihjbMpWX2YskFfKWzRNr7PfUxHNcmTon+9ytSq4fzp2SsInjnMkrEraTcoB97Sl07s2vWnW7rrwCnDmUr2r1vP0c63kWnrd7iCfS+Vwah5LMwNwrHVTLr1AI4eCBt7Venn8tQ357D8kS10/eVzTInorT8oz2SU4aA2OtKFbel07CUOVUAzxLflymcbWLuxL12HYu93+NZqntiYy4zfTiQ7LaxnkTbAodJjar9ywQGlvaWfc0LbSpU6Ql75C8w/MzJsC585zPKxd1B5lerYyygpWBb7lj/mBYaeD/QaSKc7N7B/YCvapYXYufEAg36YgnOAgo43TmRJyc3c1KE/I/oOoE+fNmSm5Zh8ySF2WfSD2YxR+iPMqPyeQkOgdgJlaU1p/c9Sxt1dBmlw4kgmo76AvCsH8/T0h5kz/EYG9b6C1ud7q671DSNpdv9idvUodj0GAD2vIC8dIItWna+gWYew46lZ85ZUVp2Gz3aw+aP1nLh/NwsiRYJtc6mgjTxeqxqEqr4io+k5VHy4hZ2bKij56IXInnNon3uSzNxeBMfN5LF/DKBoQF86ZgWp+HALTXqVRFbRZ1F4WxZwEsgiv7NKTWlDQbdIenou3XtO5YMj0FvZooNsXnEF3UdHHI8tu1BwZjH7KwfSCcjr1SVy3FZ0yoVPknUiBBNt6D1kHg//opQRNwykX7dWZKQdZPOKA2zaXcJrsXzXcIgO2Pe9O9ExKNRv7OYC1bxBM6BHLp3snHzdxvGy3UILE9l5Pdm/ag90ymLj9lbk92vC2uhOJ5kiJJnDbPpzFl2fCAJB8npUMH9bJQXXZLB/20rye5SEV16l5zJiCPDZYWUtGT2uodPYHRwa1Ipm29Zz9PqRtP5sB/N9ynfDMdNakXdtiEUHKukDZF/ZwXNQKFRVSbMmQUg718NYCpL53Qrm3F/C/DTg5BGajfwKPK45i7fLrh6AnnTvHIycgw7kfwhkduGq9HE8NvMYNxT2p19OFkHT79u/bSX7Nu6m+I14WsER6OTzfNQE7dz0yM92sLlJL8ZE/BTB3G70m7uHQ/fkoj8f2a26kJ/ZIXy2m2bSurKSENWTS3511ZgOckErWrfvRqcsgAyyWxxjfxVU7LeZE2UhSByP49nu+k64D9zkEkD6FYwoepUn1pxkxmVefkwuV+WFB27mRa1o1y0n3Lbzs8g+diD+DoXevSI2TpC8Hj2ZVH6EiqpqtLNB4qzf2+FN7+9J14jdGMwdQMG819k+pIRO5RvYd91gWgOHPtxCp87DYiU2PTmMq58ECNLummKmjAsH+HbZzKV56ZmEDlRQeaYVGccP84lp3jfaRkHyevRlzr4jkUCZYl5PYaJy11GHLOhGXiTimp3Xi+zSw2GdwEF2RseAFRc5YutjyET6rnbIuCiDD2aVsCIEVB1nf48BqG1R/3XbyZOi1hlkHz/GiTOQXXWcY39HsUAisf73pVuKj4slYwewBIAM8oZMZMaoNsAeG/3OJEuyunBV7nrH+v34uQzY9c1nlXzQzKi3Fq7w83C3DIKRQ4lvS4FuQW2wTV9GPziO3udDxUZ1P1ZUmeVvT9jiUL9dv1bD3jq0ezX5Pcbp5pk2unGrZzCl0TvczlSy/bn7mLX+Kcb3yuWGa5/htfLBjOm8g/c+uoYhLf2dtkZLVl+mLe1Jxd93s/HPL/DTUrj7N49SYMhkY1v/Py8HSI6sEWqfWgmUVa5fyJzgcBY/m0PsMSNAsFMxZQtPcmjbWyyYsIzuU2dQ6MUnFMxhxM2vMvW1w9zqcgwA0vTrCYOcZz5GWhBaDmfKMwNpnfCvFIwcY+f70HpsBnwMrYdOjN3uG6c/M17sy9E9b7NixkjW3baQoWnAf1T1tSTz29VvVdDBORA8y2ndqeCbEJzSf/13KKK4Bel4Rym/rzjM9rULGbesG9OebEUwvS+jny4h33B9nuSThPteNwaF+sE3+i9fEvonnJcGXKCeC5Tzxk6geaa/O08qNjBrykvsA9oPfYzxHSLpF/bi2iMvsf1gK95r1Y3CtFAsUOYoU4TkcnADS48fJGNCCYsAOM3R72yg4pr+xKw9LwS70P2Sl9j+aX+yy49RcHMWpO3xL98djtm6qVfXQoh9H26gY+cSKteXuo+lyg0sKA0yYmEpHdOijwvzTqxdLvU0MSvnaVkUPrqYfp/uYc2rM/mv90by+9GmF/UFg/QbPdtyd1vFIT/nw5nMdh2ofHoHRwe10q201LeT2CPJIMSXkZexK/VIQ7kgOLyWWX8+gv/H2u/Vkks+dVWDDvKdIOeZ67ObExslLel4zWo2f1hM/mWK6zEtaAglfPl1xImkHM+mK9/h+k64DzzKpcx+t9Hx7pfYdGnbWFqTNJ2uFKrUjdZzwuM+QrNvWVpjTzLlZz3BbY6w1e9t5g7wqvdn0ix6wtLaUHDTAZaWn6RybQVFJeHFfDu3tKXrDfES+Q8sZsaAplSseZiJn7aldbRtNnMpFd3IW/c2T08uI/TdAYx6ZCDZ6N4zY2sbhbHM6ylLXO4ScrgGVDjKTt0YUOAoR2x9DHssZYWa4CBLppWTP7eUKZlEHtsLalu0v//q7eRJy1wK0v/I0kdf51B6N274VTF5Qesdp/7736duKT6uyJNFQux6YRyLLmwbe6S5Wr+LPoUoSojQvyNL1uxkia1uZrVtR1+o223XN5+tNlYTCvGl5VfZ6EifHmBn+w7cEJmvxLelILqg9rO3mTTlMO2/F33snrofK143Fg+F4lqaQe/4OsQJcLzmErW3dr1nakPVV7iSlkFety7ct+0I43vl0K7vQPb9cQcVlW9xoqhEbWulKmlBMlvmUnhHLnkXTWLOtpMU6B9t6dFGV+vzalkj57/+c1ZtHCQUqiTjvHPDX45t4b0tACF2LV3MrjNNad1jMCOureBw1CYwOdhVZPQaxlXrF/PacadjeCQzh+5py3itPDycQzsXs0Rewp44ZyrZv6KUBd8ZSWEbyOzQjeAfV7O9CiDErsXL2BWCijULWfVZkOxO/Rlxcy6Hjn1FZodunPrzVg6dIfw83hdXsv+M08EOsnZL5O6CqnI2b+lP1+idxGfMo6gNXftvZfOHkb49soO1aR1o1xA9BvWdzLZ0PLGMFeWV4e+VB1m+YitFl7eB0B6WLN4Dma3IHzycgn+GV3J27b+aFWsjj707tpoF1XkZrWkMCnVP60v6smrZao5GrueK9StZ+p1e5GXazwWqeSMhMnsy/plSyp4pZXwf/d0XTenR8xgvzt9Cx27GgeIuU4JkZMEpx/lJ8ML+javJnziPskgflT0zjVFfvs67n0K7zv1Z9e7WsLJZdZjlSzdQCTZ6QpC8Hm1Zu24Zm48PoM+FJCTf23Xuz6aN5eFjnjnM9jeDdGzrT1BUbCxjzrbBFPXIcB5L0d8RClH57YywA/vMMTau2+rrePH6fNbz2dsseP0YwQtzKBxxM3lHjlNpytKuc39WrXg7fO2eOcaqF95O/ouL2wxg1HcWMmfFQSoj11TlvpVMHbuM/SHIzG7F9o8id9FU7mDzWnDUI6PyPzOH7qfWsz36RNXyLazJz/HoMKq+XEqmrprUObHek0F+0WC2zytj07HIjwwdY9O8cTy2/iQ0zyLvwwMRXfEwm948GM5jO55Pxxxc1bm+q90HaW0YOgoWvBQ5Pk3JbrmDA/8b/la5bYvNo1sc2Fge0ZNDbN+4lcLObRrnWHGcI+z1e/Xc4YKDHZrd+0ZOLJrGknMH0Pt8oHI3H2Tk0knhL8+8poRBB+exaE+4XbZz6d93s+ofmeQPKWZQrw5kB42dldmhG6fW74joTyG2b3yb/PayHNyMXu46XgNrt7A9IuiObl/PqW45ZPqRnRb70oFEfQzfuGcRvBAi9EUGGenh7e3vbYgkq2zRCD7Ova08qTjAro+DtOpfzK0F3Wh3bnJ+jW/dUnxcEYJ0HHoPrf/4FGs/A3v9riUdr9kQlyWfbGXtvvCmnSzx4+cinMW5by5oS8djcb218sNyU/AODDrSZ5GkyoMs/81Csof2TdmgqC8u6MuYogM8/dIeQtj3Y2a7DhyNyt8zlWzfGA1kZpHVeTf7joS/HdryFvvBvl+d7K3InGOnI7S+pC/r9PPM+9H3qTtwppLtW7ZScElEV7iwL0X/eomH/5jBDb3E+RnmJJvmzGbVp5GvZyo5tP8A2RdE5tWorHfSD2L6oo0+7yRrhHpNjdxRFr/FmXDU/uHh9J7wGCPfPIdml/SiXS5AkHa5QaaOLeEEpwm1HsaMwUBaDt1PTaTkJ+Xc/auJFNgtVk5rReFtmSwdW0nXuyCzl+oYXsmi6KFi5ky/h5Eh4P/2Z8pkecWeX2L9nt6UjgW3UTq5b/i2/gsHMqW4lMfuvoP5EH45ZhCCndtyYEYJxaHThNL6MuGJppA+kCk3l/LYT++g8usvaTd8JhPSYJ/tUXPpFFrJfXfv5kRVkPzRU8kLAt/LpWj6TEaW38iEZ6ODIUjeqInsf3wcxc/FX9yajXWFl1BN0tow4pfFzC8t5roHTsIFORQWz2BMpyDQhry0Rym5swJCp2k9fBpDCcKo2ex/fCojXwGCXRj1UH/An1PSdgwKdU5Gn3HM/tdsHhsym11fZNC61zCmTI48ItFuLlDMG66Y3lHm9ijGzB7XkPHiMfLbm9JdZUpL8gq2MnFIBjOXDQs/5kDwz5mDbHr9CroP0XduFn0KM3h4+zGKBhYz++CjFP+kjC+/Ppd+46aSccGXOj3hDkN1wdyetH90EkdLFkf63L98D15WzJSDj1JyZ1n8ZcoXQoXLY3Hij9vKoHWvwYz51TA6BgG7sXSBXt8Zx4grJzL1p28R/L8d6P09X0pMnMye/urJzKHdRzMpXh4iFApScP9MMjHKxOBlxZQefJRJP30VCJ+PQpItN5tSMPkpTj03mx8P2kNFVeQcTh1GuyCQM5AJy+/hpp+0oH3ucK69oYLQ1zZ6ZJVe/g+j6KEBzJlRQnHkpdqzJ+cSjKzmdyZIXnXlUjJ1VRtdqrESzBnJ7DGLefrB4Uw6UknwghwKR45jQq+mQF9GXDaOkltfJ/uSaxh69RUc+3fIZjwH6dTtFOPG3cEHxTOZck1i1zeQlD4IdhvOoBXDmNX8RgA63jiOJSXDGdmiLXlD+1L0T9XqcXvyusGK+0vY98VpgrkjmZIbhLTGOFZc5ggb/V49dzgcxjAv32jdf35Pbmg7m829uhAEQrvLqcwbrr5LLy2LwpIbmfTgYnY9O5KONnNp6Bvo1z2LLz/cwSEqefHx1WSPe4boze9cOJAp/Ut57O5XCYVO0+z6mUxT3WmZgtjKXadrtU8mh54sYcHR04RaD2TauCxIz/QmO5X2pRP+dZDWnQeyZuYdbL9xHGXDUvd9cwmheE/xoHtfZdLdJSxIz6Lgyi7wbyCoskWh0vHcm97/88BiZgxQyxOOQbPL2hI8soMP/h3i6HMT2X/tPGYn8ISU6umWE8XHFSU9h1GjW1E8923yHu1ro99B79uL2fzz4dwUakF2bi86Rc+rnSxp6cfP5aVv2jB0ai6TSm5mafMWZF/bi36HKzgBBls2piNNuDmiI11B0b3zmNJDgiBeye5XwqCHp7JozzxG5djIjDaDmZY7leIhy8hu3oKCq/qG5xCa0m94LiVjh/Faiw4U3NyL/L9VEiJH3a9Btb0VNMw5ah2BXiMZve1Rbhp2muwWXejdIZcTJyrBonno5qj0puQPnciEWFAsg9792/LElp5hX6kANCXvhx2YP2sYPzgOhM4jb+hMplwWhEq9rB+s1g9Mc22BSp+3kTVC/ScAaFVVVcqdp0+frt3WCIIvIo8ZGCcvJhUEQRAEQRAEoQap2sqcsQe44ZlhtEvKY/NOsmryTLh3BoWRd9IcfW0Sc86ayIwB8v7BpLOzjKvf78U7d0gASqg5dj03gHXdXif2ZLXyMq7eIuOu4SG+JsFEg5UhITbNGcf+H5QyQp62JKQw55xzjmue9PT02nlHmSAIgiAIgiAIgiA0RA6tmMTUV+CGB6clKUgG0JQeN7Tk4Z+XsCINCAZpln0No+6VIJkgNFTaXVvMkidKKE4DCBLM7sCM4obmWBcEoVHwyUrue2QlXD+RGRIkEwRPyB1lgiAIgiAIgiAIgiAIgiAIgiAIQqPC6x1lZ9VCWwRBEARBEARBEARBEARBEARBEASh3iGBMkEQBEEQBEEQBEEQBEEQBEEQBCElkUCZIAiCIAiCIAiCIAiCIAiCIAiCkJJIoEwQBEEQBEEQBEEQBEEQBEEQBEFISSRQJgiCIAiCIAiCIAiCIAiCIAiCIKQkEigTBEEQBEEQBEEQBEEQBEEQBEEQUhIJlAmCIAiCIAiCIAiCIAiCIAiCIAgpiQTKBEEQBEEQBEEQBEEQBEEQBEEQhJREAmWCIAiCIAiCIAiCIAiCIAiCIAhCSiKBMkEQBEEQBEEQBEEQBEEQBEEQBCElkUCZIAiCIAiCIAiCIAiCIAiCIAiCkJJIoEwQBEEQBEEQBEEQBEEQBEEQBEFISc522vnll1/WVjsEQRAEQRAEQRAEQRAEQRAEQRAEISlkZmZ6yid3lAmCIAiCIAiCIAiCIAiCIAiCIAgpieMdZZx7QS01QxAEQRAEQRAEQRAEQRAEQRAEQRCSxRlPuZwDZYIgCIIgCIIgCIIgCEnmm29Og2ZN1zRjooYWy6ffF93WIjsN5RT5TbuSQgAgEDCmmb7r08yfbmkEIID/+gVBEARBEAR/SKAsRTnzzTcxg0JPzNiIGhRa2PAwpOvTtPCnpkVq023H9pvr1fTGjkbcdlEYOI4EzDZJxDAIGAyKQED3Xf8ZyRsIxP+i+VXfVdtOn9Z2uafVNJZzqzIcHdKUhqmXfQrjVpXfrR2WPK457LGcfUV3GIxSD/3r1/B1NIoV9fvJ4xXVaVae11hf6pPi80Tsw5ikGwNYx4AhzXpsdduS7tpwwesJSjibJ7yMV1WialioiqrnUuO+gK5wQLfTXF/AnAfVGFY34izx7TRovq76t/q61TQgqi+g2w5/18sFs25hLW8va4zHtKRYG+b1OlJmc5qXPcz7unqiKYHoRWars9jUZdZNonXq9+uOZWirR7ml/p1hLA5tpz7SNKNDW8PS13bb5jFg3a/vd83bGNDrkpY+ChjPWVQ/jOS10wlV2+F6jPqpoRUBOPtsMcuEFERLXF+qlk7o2d5UYFWaEm+Dh7KqOSNVCIVCanvVbHDgLbjq1eb07o/wgF6BjiYlaB86pqn2KY7p2NQa9k+oz6uT70FVTudPin7145fQ77O0y+gD89BUH6hsIaPRZNA/0KUp9EJDfr/Bd5vv5mPbND3peLkGHfMo9UHjGND040b1aR5jhrri6d50y8SHilpHdOg3j3NJQL9PYSeoPo3bdr5Xl98jCzeEBohYZCmK54k7YM3syygJBKpniESP6c2OiOZ2NihqQdB7EQhqd03jJEDA17hRr/xMZtghPEpMCR6DEUIiKKYSD5gNXtcszg2wlE3emKpW27zUb26/crxW52QkViypl4iW7AqFhoXz+DXvdcqtuebwdMh4Ni9jP9Hx60/BEaqB+lQ7d1xye0YmOUFIBM/qlNXPWTNBENNkorL9nO1B+7nAf12NnOr0VVJNR5sFOcpuMTm1E5z3U6XfE+pinWGZXA9BpGIbw9UaI7E5ujmwoV6maPjwTOMfEvUY68BQ+wA8jsqEFzrX/tyQAlORIMQ4q64bINQjVMqA3RyvuLskeveYMcm6mke/dNB6N5l9G1SrNyILz23yGo+nfCSH6kDKXarfYZ+nMeH3VynXiTmt4rMoGzUfJIvW6pbi9FsEezTLhrO+qDzPHvLUF+qqbZ6Om8TFkJ7vPhQEA/5DXDVVe1JJ+AKsZguTdSeDrqzfFedJwddpsOqIzrLYvOJXv9JYkVunLprrtugfJl03MZ1AZk5B8IrZ4ozZc4o0T/UlS4/3cLeDrYQyTiK25b3Yn4IXvNn49mk2QTLbqq1yo3rHFywozr/TFeh4dSrvJFLktzq6HNrnw79hPbzznCbDI4zH6yRRy0I9Btz71Zeer2nW6UI533io08WRavbb+kWmJSGVkECZEEYhoC1ZzLep2wWMzPtMdWuWHXphZDY6zG2I/5nLRstbPi2NM29rVuGBd4OlMSu4Cf0Kh9ve4/XajR1vykLyULXNLYdDpsbR7QnhxT2gT/Oa3ym9rtGo+7Z5On41G6mwH72IDEHA2+jw4eB0KVHrY9FWYLjMcH50BLu4VTIDWvVwmahR34LojOvkuLDqkeoyKl0y7KvQDIPM8tiupOp2MnMKghu+rhKVruIpmFUNXOpX2ba6vQ7VyvxQXawyBLxYeb6CZPZZrIme6pV+15Po6XAq5sUrUe0gmU0eb4uBZQz4JinXjb3ebjd/VDtIpj9O1KkQs7Pd5wf9Yyf9tMNgSznVoahKpighVZBAmeCqvztOjprxfSGqO800/eQfczpoBuPBPkimKf7i+50NEP3viLZLMyYrAjPhujXlthuNWcF1cvu5OS/BdG4USkDt3UlmPYpbikGRwORMSyG8/GSVLunPYFH6Ouod9aldntpittdr8liCYMHPyPEhcxM8QlJxPbBm+HAlYPpMFuZ3mNTl3WRREuo0J/3MqlMaHRxqXdJStYNSY8hebZ1PZlRBsMNssYW3VPact+uoxmw033eWKWRCCiy+rBESUYB16dZFFO5BC9vuUKa712d/HPsxkfJ4PCVGUa76Zrbn7RbjuCU4NcI0P9hZvkqFVjffaSb/Q6oPC5/Xhab6YvBNGndaqzeOjeQFyRQtjKipCcmD6s4XMt8IAiCBMsFBf1NPkyqzxWEiNwVENFtBpDIyvCmTmk6QmO8mMxzdEqixqd/uTjmH4FljN2z8/hKn/I53mLkpIDWGqk1uOewSGyeOfeqWZupe1/wejikYqZavwEtRF4exIFQP76OpXs4VDg4OYz6HliYSo0pGYKsu7ibz6uRS3hHgRecy65d2o8aqh8b0SceAXAI6nu2q3TofvYJQp2iWILamnCvV8SeFEzp2qTnbbdX6M7dYaReabV+zQ9Z5HknEjoy2LyVw+Jnud5N5mdM9jkHXNjnVa/WPpNorHuzw9HMVfivv9qpmSUt6kMymjG2wzKFtQmKY/RF+rQ2lnzLpQTLjMY3VOqd5uqvMsNg7wZbJYBRSjLPrugFCXaFQ1vWfFkVekdFaozqzJb9mmrc1U3aVsQFGL1I4LaB4kbKmQSCgAYFYGlq8uKYBAS38UtVIemy3pqEF4q9bdXtRZkN/ya53ZdJ9n1L5VDic4jk0fVbjPqdgWtIw95v7y7P13zWMY8ipXKPEs6PT6z6jUeDJWPFDwpqh1yyeM7qjGjqaNdE8xDRNNaqxTJ1ehqbmkM9pn1dUp6KRXzEpgd9r2r6svha1rPDtYLTNr7hqzK8Bs1xIoEX1CN0xLfN+XLmwllcc2TM+3lOmlEWKNO8yy89kqpLlpu8uA8M+WKYu7/5YbLXs189rGhoBLRDWDw1yP64rep0EvekDmnKOF4SUxNbIcHIIGu0Ep0B3UgIOMYXLJAMgNjcY7dFwcsQENcgPy7ziML8Y5qNUsDWwkTgeFy843d3nFJzy9r65qE/Bmi1gpy/odYKIjNEf09qfPjWFZCjoDRGl3yC6y5sPwpJP8/ZaDlvdUmGkGXwKej0DtR8qls+iP+rSTEPEWRdthMR8TX6LOVzjKltG0303D5VqB86MPR5QyBYCxnlF1c9OOqp5CBi+q/I7zEepOs0IqYXcUSYATsLF1nNl3atXKAxCKyxQ4ise4mn6isIr9IyGjXWFsEqRNefR74s2RdMdzth4LZpJoRAJCWLRLCMfFiXUqqzW3iMYbQwqF2PJ1sDWbNru4LRLJTTLhm6fzXhxzFNP8NwsP+33mNfGV+SeqJi/Ew5MOuRRz8wpxOereOS6qxkY/XtoFSfruk21jF7kGtLtBoRKDtjMrfG6THkVcsZ5MLtfSFqsTdZ2mZ2zhjsaHA3qxK4K85MZDbZqvbBcHZwPNo7sWJrjSnqrs8IcLLOTt0Z90qpLxuu210ecdEWzvprq8l4QEsZy6WiYp1GzrWlIjcoLG5vCbYGF+u4x8z/dIU12g36eiDdRbY/qf0eiM0ZKzjUuATGvwTG7/J6CZPpuV6oQHu1dOyXJq87j1OZGPjaUp9/x+o7kNqpx6nKWa1J1p6bmfI7tfAqG4+vnKlO7zGX1bfcwjvX53O6Qb6h40zbxNy5016pZJ9TrnbHvlmvdZVyoW2GtwsbmUP+G119IawAAH8pJREFUmDJs+f1GG0nRVoeWGIKDgpBiSKBM8DD1KSS4XUFNN61reuMmPm1bBJhp4lc9NiLmI1EZJTElM/7IRaPzxEOQTPXzGpEikUyUZ0UpoDXDp7EOo6JoNRpMeZ0a4+XPsQJFimWMWQ0efdvMjlplmcaEou/8FrXV561DoN7aep6blUj7PZapbrDMrS5vjUhqtsbB56t4ZPgsWj71DivfCP/N+t4sfpxCwTK9yI2lOdhr5jLWIRvXDewcAuYAm7IRWLNY3aBaXA6YnKAGV6lOFjgGyGwMXItBa85g814xT1TrrrFE8SIcbHSC6LZCBwynx8t6CZap6jI7OMy6o74tFgdWArpio9YDBCHZqC4XzbRhExAw2x6xbYPMUV+Pro8rtNgT5iUSGGSFpk+LfRjnruicYjmqhzmjRu6Qayh4DRIp0u1kiHtduk6Lqgc2WYxJmnnYKHQHY1n7vnT/jeZx15hx/YU6v4ShhKY754br2VjWcC41a3BEPQgc2mLoJ81wfE+6hkG1Neoo6ruZVO3w2N4GiK+fZrvAwlSRbuhoxg1iuqO+Tg8BMuVjew0HNAyM2FE0Q5pRxzU3V/07TT9JUU4/9qwl3NIEofEhgbIUx2U691SBdU41SRnNvG38NEz+CuU/HuxSGCW2Cq+pUZHj6YrYKyOkmNGhwMuvj54/z3qiUcuLpym2lWX0ZY2ahbfG2paxVyTsgmWqthr2mdOcnLuphKo7VUqp3f56hOdmVaf9HstWJ1jmOH07HjSxbPW0O5PO3uWz+MstpdzeKZ52adF4Lt86i+U7665dtYV1Fb9NPju7UjMVsi1vmpdjZe0LehEhlv0xI1ifxxgci7XBogI5yJiaQBcMiwbGaufxXHa/yv7Xmp2Ghn2+gmVWGWsOkOn9ZtZxF3c96fssNg70i8Bc2+gtTRAEHUo5YLXRVPq5Ob9q3rW7Ln1fm5Z5Qx0wM7fTuEjE+LuU9pTPdqXEHONhbrULdKlPj6bsF3Me8+Rve6YdFG+9SmKUe1qs/w2lNH0pq1zzfAdZKowLB2JnULOmxs67YYJRXLuGcvY6i3tjNF1xYz22d5bF2uAyAK1DxbKvsd1V5nQa1AVU15G1tCEAabxwDVdz7BL1EhzTyxqF4WPVLzUwHN84ZowLQuK/zyxLzDLG0jaHNitSbXILQuOkbt5RtnM+A+9bYkm+/P6X+fllG3lk+Cz+ot9xSykrizsCIY6sns/chavY+3mIc1v25qZ7xzKkSyawi+evW8eVb4zmUv1xNvaOlDWyt+xq3u/xjs6JVcGah56AcTPo1xxC+5bwy18s4iAQuqAfo8aNpt/3grFV4qr27S27mvF/iCYGaZo/il88OJSL06txrmoQf3JWpRhosT/lc511q3jMgl51+7g3AR7NFdC99iMq7AKRR7tH3zeh/4zvj9YSfmx8IFyFoenu76pqzDiJQW8iUi+8rYqd17uvPK+Q8ou5w20SYymapnvfgGkcaMSeOx8eU16eN9+ISLA/Grp957lZNTZeFdk06w0knoa6IpO+LlW9ftuWpGINiAqOfAJ8r46bUUe4LZ9wu9ZVTgK8pimMXucUZ6L59W+VIKI7xFPCaYb3V2oRmWB+Ryqqd014vB5Ud4jZ7K/968t8Zp2NbNs7zc1pNsEylZNDFSRzkyGG+U3T0AIBiPRRfJ+q19zle6OX/3VB1SHW/PYJFq3dxcmqDFr2HMpd947g+03rumFCtVCq+5o1g1J2WB2GSie0QzmnNMDDO6i0iFgwGADxdw1FJhOv9qVBJtiUTS1bVXPte6d9+kUV8e+abX5FQX1LnHFQvON2JZZ3lhmLKWxSB11c2e+OynvjwajyaepP3X5D15sDYbZBMnM91Wxx5H2k1jGgfjeqsajx3WQGH4TuM9bOar7yriHgrG1iGAfKrrOMC+OnJWAWq9J9IDjfqRzZFzBe+WBvc8T6V9+PMd01nKSSH46+LH2TUAyZRitXIpw6wOIlk3nhC0DLoqDbA9yb34omdd0uAEKUv34vD/8vDO7/G267WJVnN88+9R7d77uT3NpuXiOnbgJlnUaz8o3RmINTAHwOXDGe/55eiNnuObl2KuM3dOfx597g4gzgi10s/dUC3m81niuTaiTtYsn4w/T7nz/x8wzg76v49dv76fO9jgSxbx9A0VPx4NvxlZO4+8VOLFME6uonZgmBUgao19X6MDoUjg27VRL6PBajJPpSS53maR8s0zk8ogaOQc5oOnnlbHgkTWCozk19FERJiGr4vcvKU+DMr6JqDg4otEdLisnIjZWNKrRW6yacxacDrd4pIY6KnWthz3sclVt/Q6wGcZ7DHJISzITasFEUttrFCmduQFWdcayHh7fZUaO4HnSLDcw42V7e7bJGaMGlHCr9QaFbKIuq99vddeT/kXeawzcjykvQ5ASN6hH6Y1rS9M4Lw8WoOV5PbpgDZAFzep0GzXR4mPSd9D/AuBLXUpn1TgFn3UI1bxI/h9EpSMOyCMtcv0pXVKWrjq9e2FAnAq4BUcG7vyzh/bzZzH+5HeemwclN85m7eBMt785X2mRCw8R1oZxKJNg4x5VPq/ATJNMfyDS3WyzTyIWtd1JqAateFs1rsAMQ7ccWJ7mhT/Pcr3pnt/oAvgJnaoXBkBRAMy7ONXqzwzmUwU+9rDCOkkRsR/N5U4/O+o5NP9h99+xnMObXFGWru4g3ENCFMjTUwTJLkFQVTFct31E1zZhPWU71kwIKm7O+jxW7wCjWvg1vOvimFLaM+U5QVTnn5sUcjZZ2erI5vCyM0CmZZtljkTH64Jl5nyvxEqrfXq98Wi7sefteDnZeyuquGfDNMVa98iYfn/4ROefUdcsADrB5d1celSBYnVA3gbKE2M+ahUFGzysKB8kAzu/IkGk1E4Q6XQXBtMiX7xbysx/5r6NFj6v4/uxPOEnHRmfAaaj1AqfHWCjvKsMoeMyCyXhrccQJYVpJA+gcUPpgmdkhYVQPImpppG1qh1W9C2DUJT6iFHaPCPClSHiozxdWDSHy6WCoKhUNxZ1jkTTDSi8vTXI1ilIDS5d6s09THrWz1dsCQnOa42pVRXkv+1KeP5Qw8A/W5Ja135L6gcHRaXNBa5rBuI2WUzzgSlfE6DVV5zM7O7w11WJU6gJhMVlgzqhwfpirqVaAzOl9ZQ4BMtUha0S/cT/9jmnRx21bdsXSrU7O2LbFK6KrN1ZP+DNg6bOITqiX5QavZrTvrOdMdMVa4OAqFgXHMmdQO86NJDXNH83P8+NZjq+bw5OLdxIKhcgcMIUHb2lHcOd8Bq6GIQeW8/Gg33Dluuc4md+arSvWcKQqj1EP5HNw6RL+evAoLe8s48GCbDhTwV9//wQL1p/gdOg8+k2cQVH7ICdXT2LuV1eRs2UFGw4fhf4zmXVrkBW3vcbFC8ZweRDgKKvGzCdj2jT6nF8H56mBY1ks6TRbm+8I0W+bbcpYEW9pNo2Lo7MrjUnG1f3RYoHIxO91rohOPXoRIvNMGLd+dQ6OWYWD3WILU6JNjdgowu6at9dgR3WprePULAo/gCI47sHLoPxmeYSnqd5wmnmguB7MenSz/heeHDCturJZUePd4vLU543VgHO5fpV9bdO31idmKWSUX9+WyZaIHs46hRj7P7b4X9e3cRXV3rAw7LJbLZbCcuXUNyE4Oxj+cnYWhYPDTv/PNz3EY9zP0/mZAJT/6SE+vXI6hZnwtw9+zfRNH3AKuOiyx5nWLStyZ9rj/KkKOOtaxv/4R+Q2CbHnzUeYdeAEp/4TJDd/MuO7ZsHnbzH1Dy/yt7MzaPKtq7j3v24h5wtz2rV89uqTvEwF68o+4AcF9/Kv5bo7xyr+zNj3W/P0D+rirKUG9TNQtnUWP75uVuxr0VPvcHvWPvac35UejkbHEsZfZ3qk4y29E2hARwonLGHMf91ETsEIru7fhz7tM53b18lUxZlKPl73Hsc7j2ogQTInQyT64aAkmtPdFFZznTrBY5s/YCpDPEBmfPRRNFimX7EdESsRBxax6gI28kFT32WhM1LMd601SnwaC5rhi53HKt7HXp4tr1oNatvcyKdtb5jrCVgTlY9YIm7k6hOVztDItt9xoa++3uosPvvBfgT4rD4BYyRVSGqwzO67fv6N2m+K+oQwJ1cvgXHvsLK5ev/esvns7aR7THSjxuqUim9qahkT3adMdtATsJMXNq7WWB32I9ngF41eDLrgmOHOYtQyIeoHUd3d6UbA9Gm7z+auBWWdtSVcbBVE9zxaTJnQ/+mHRSQttkuL65GadRxYHSEBwxALRDrIdNNfPHhmqyuqZb0X+V9v5Xw95eSBXZzbqXcsSGbh78uZu6M70+ePIUiIv/y6mBUHFzIEYF2InN+/wYj0Ctas28SetDHMWjCak6sn8eNlp/jt9DLuqlzLL+9Zy8cFI7j45Hb2Nh/DnPnZULWJX49fxZF5RWQAf9kGd00vYwiHWD5mPu8WzqDPkH08v6GSy/tmwN838U7bG5guQTLfeLqrUlPkjC6uMDjMNV3+aDZ7e8Q3+rlItyofdEEx87bi2GY7UnQrZ7w+thdUNqZ52+N4cAqS6RPcgmVRZUC5QDO6pfJhCHESNQg1y5Y1hfhcYiltnU8SbUe0f409b6MkKo0uf7NE4wiUJojb/K5baGdZwK/PZuOLcrs5wOHAcVsCsFlXZ1UULYa6gxPAych3Cph5VE4bi/8zN38yL780iIEfXM9tHa+nT5e2NHeKkHz2CtN3tOWhO3/GRRxj1Ru7+ZxMDv75cT7r9hv+59Igp/b+Dy9/GiLn62d59uw7ef7OVvCfwyx7/nnWtZ/Mtzc9TvO+a5mmcwSUK9JyBj/Anqfeo3vxneSym2dr7CwIKupnoEz1aMPPvRQcyizLO8oSa0KLgmn8vncFR3ZuZM2LP2U+dzN/WkG4TQ6PXlx+39UsByCD798yhZ/f3i6xBtQZFs+CnxLGdAeDxCBw9MJJsXJDua0PmmlYglfh7bhjK/pIMvMdP+E8qnY6K6ep9Wx41IqGwjC1Lx/7z+EQiRuw5lzu7k+njKYHqiiMXNVjF5XPB3c6dMxAFmeZI2rdVNCRtGCZjZ1mV1ZQc/zwIbgsvH1y9SR+/KtN6PWTFq0OsXwnXGpeYNOYUHqR7PJ6MDRtimsKI9d4VC9OL4VyYddMXeAk5jcxvTMiLjrU75io7oVkKK4QHrFjmh/JaMpb63eT2SqLNn2nHEPG1f8W55XiWM53GJr0RS3Sn7qnFMSDZOo2+tUFReYngwrWPHQTc7eGvxU99Q5Fn25m78YTjP9oQSRPkLZdKiATyO9KTux90XlceVk2AE0vbM3FV3SiRRpwfjYtju8nBHBua87d8RhjVoQgLcTxffn0AzKA7/fMC+enNTld4AjQoncRTF3D8b5FhLaspU3P2QRr6Uw0Fhznb6dLWLXLoz2RcJDMWpFx5X8kLWC2HVwWWNo9lr1R25c+8CLj7e4mswbGon4Hl7JegmT6HW7BMnQ2pH0hSxOq2/0NO1jibwIw7DHoiOj/i+VWqppuC76rifEuH+xXJerbGVvwHQupVr9PG6tx5zLfx/2RDlUofKKGJ11VW6YYnEjxFLtgaeT45icYxYePTpE177NtgiikNL+Gaff04svju1i37Xf8ZAOMuXW67aMOPz+4hSbtf8ZFZwFkUTggC9jNso+70/vGsObX5NIfMQwo/9Mr7Dmyi9v3xcv3+wd0v/QW5v75IWb940YGd+3OxedBjiJNqFuSGyg7uY5fP7yIA0DbITPpd3wi898FKGD0vE7suedp3gHoM5Y5t/h8ZGLz9uR88TR//XsRLb/rt2G7WK469hl9ni8JfQEZabqkYCYt8wq5PS+PluPn8JfPC+jncqTw3WUh9i4sYcmF7SPGlPH4v2jxGr9Yug9oz9BHx5O5tlh3nobWk1Xm6oCVUWhosT83weD0OD1lkMxNzphmfv27IeIOjUAkPfIoRp1CEc+PoaJoE6yLLOL1m9NTynhx6GflHoNSEv209neiyoaXYQIeA2YWf6bH54Tr01T12Bi+qoPXa30lQTvBax9p5oRqHDMVqak7y5zst2hSY7WzkkHTC1sDm+CKVrSo68bUFs6WpzqDj1WZjo/ndQuQeFnl6TSao84WjPO+hn6RjmmO1+KP8I1dMwnM9frs0frtAmF2Vcfy17Sg8Trxu+SPqpnmzJoW6wiLDmnQLXwok7F+iyQZH61pHBdOuqLtS9Lrs3xvADRt25Gvnt/JyUGtaUom/aa/Qz+Osmb8MI4ApMFFQ6YwZ1BrY8GdQPNmujvRgqCz9TLTrd6Ij1+Zyl/zSpkzIRPYxfPXrYuXTlO84v383hReMo6/HOnEV2/nc91TEibzh2quViWp5IF9fqXtGitXTSenYjW+7bzq8+JPOdvSA34DqUYb036fU5qvIJk+g+9gmWNhQ3NSe1g495G7WuGco0aD6Zbj2gTAXTvZu8XVsIOjCeIxQGbKofNFJC4rEhsrirlBNQTMiQZD3VrAHDMzBmZVDoPEJpdGI6vOCnJedlcKf9CVLu+P58l9FeSeBfwnmiHEqdPRvPp0fR3WpCZnBynoPZfxHc064Z08f1cFH+99kxde+iPdb5pFYVtF2gUOxzgV4l++f6jgh+QGypr25mfz9I86LGPOLfFvl84royjhytvRb+R5jP7NcnLuL+Li84Ev9rPq108TGjKbovZOZTtSZDp2y0sKeGPZKgo7FdIiDU5uWMHy869iVlPg5CZ+s/AERT8L76PyEAc/yebKb4O3ERnk0iFjaXnfE7ybO4M+zc3H78gc/Wm6xXieagWPc7ntS0u92TTqNJeAmJ9nxasnZ+vjCsxBMvVKPhsZYRFOKa+pOmBvtLiW0aeYxoCfxy96OwIWPTOgKcZEbFG5rs/1TjQ/jz7wrEw04LCD8kQ7dJyDg8O+lHlsJI63O/88VubrN/ivy4z6EWDedF9NU4yw6g45RSBNADr1poglHOndo4E8hrmaOPgxVO8cNeazCZZFlQ4bZ5c3g1VxxFgelbPKittCGeVCCn3tNXVhuAXIEjxmTRi/TlNbrJ9VfwlMps7vOzUuDVD+VjtRXC0RHS0cdZTJXOmZNoWMSPsxc1e054GB7Tg3DU5uWsryHXA50LRDd4ITX+Ov/Ufz/fQQe3+/HG5KbPFh6NQJMjLCL8QO7VjH+8CVjiWCfL8gn0VPPk1G3wkMSXPMLLjhEiSzmw40u52JBkqc8KJwRb43YK2+nuBVBvjrw3iXOzvPneq3M3ssCy0Tfm+pypGemHPasPgjSqMZmGaD0nm+sAZZ9b4pzblcNQxPa7/Z6wLxYIdqJ9YVjeZPIWFUssTpFTHO9osZ6xiwxsUClmzWV9m5Pd3KPBD83suaClSwbvnv+LLX/eGg1H8q+fj4fi6+JIPmwVZ88OF+TtGdJl99wLpD0AFo/v+6ceqVzfwt/4dcRAWb17zHt/tdT+73JrNmz0/I7Rjk1Kev8PLn13JTq+v505Y3GZZzfewxjd0H9OJvb/4Bev2I3M63cNs//syaL0KU/9WcBhgCZVlc9J1dHDwKudnw8d432Uw7IEiTJoSDd4pgnZA49fPRi6Z3gMUedVgwjVln5jN39HXs/TzEuS17c9O9jzKkvf+Ve+f2nsDjXzzBk/81i71fZNCy5wgeeDDyOMWmeRTmzOfJET/gOBBKz2Po9Cl8P3oYm/YZSO/IrXe1ZuzctXw/+sjGFMDRCeIUIHNwmDsez+Ko0usSKm3UmmQt52WHoESzdqV/21MRJKtDnNWMmjxSA0Oz2TaneejOuu3xhk0SF4r5PzYNegTXCCdXLwo/jvlXT7Dmshn0s3lvWUqhCJzFdqnuUI/JFRcHhW29DtG72H4vQXNNcRe6yyO0knFB6vPabccTTZ91i9O071u2q5wWpj53qlOzCCn7fjO+M0j9aE2htsmkz4OlhH77BKNv2sVJMrl8yBSG3rScgwDfLeKB4jk8ec9IQkDmgCk8mOCNXZcOmMCfJhcz5sUgWX1604VK90Lte3P1mU3QrXViBxXCeJi+Y7s046eqnJdH8yX+njLjXK6B9X3GprzyOMWawlsfOo0V57p8BuEUtqI5LT4GvAiWaggfkVvu2EU8Dd+qb53Gpwd9gMzYQbbjQtWPqdS3Bj9iDXoKdFEyL33uX34ogt+WFJtEDctrZKJtsFuo5+a3SqUhZCWT7t06MPeVIcytArQMund8koc6BOE/P+ThzXdx4/wscrJupd/FJ/j6FJD9Qx667NdMf/Z2/vVNJW26PMlDZwVpcv2TfLzkLn60rpJ/nX0tU4Zm0OTbd/Kbo48w8dlXgCC5+ZMpJMh5Fwd5+IW7mEuIU9++lZl9g1yQZk6ztrWgV1dG/2EIq9I70C/vKvr980tO0ZXubTYzdl4GM+/9ke0jIwX/BACtqqpKubPilCyJa6x8c/q0RfxDfLKPPtZGI/6IG03TYo6JsDPrPzGnVngfhu/o0qPb5rptP32iFw7h7UB8OxBe2RXbDgRifxAwfI//xcuEy0W2Y3UGYscM2Gzr2+XVGKppo8n6YmMXg1FTjAtdPn3fxrZ1YwTiz/421xGvS/98cJv3j4S/JBfLojrFuTev1I897iq+0ie6bTce/HyGxyOmNOu4UI2TRMeOSr9Tjwv7fDqdMjJnEEs3vkPI+hnf1js9MZYxHtXhmz+8vkvOE6rz47M9flB3d8BxvzktYNgXMCQa/PHmPLr9hkfABcx1Kup3ah9w1lkNX13fWzaJI0Uz6Ne8gr074dJOmUB8e2/Z1bzf4x1ub4TvKKv66itjOMIwz0fmhoiciOsKRt1BHyyL6xcKnUL/GT6EYR7RtUL3Pw4Gtlc5bc3vJPcNnxE9IqZTuMgNs/5h2Y59xnUd5aetzNH/LnuZo/79cfTzvP67ta+sY8CoX1rHgWWMoB4nKn0y1j7b2di53wx6o0onNP3py5nT4/Jd/8SDaJnwt3POOcf2vAv1nCPLeejFZkx+sLfuMY+CF06HThn0yCjKYLgWVyJt7Qt9OZPdYiYpj1Yz2wqRtIBpv+0cE8tmv181dzvN42ed1XiWlp/6ukptgxjm+egYiOwxmLJmezMqJ8zb8XridRnTrG1wJjYKbPRhe/kQ8UXEfBXmfW72pGl86G1WffuSaE96xf+j7Ex+A72s113zxr7X6xomn4TOVxEtqx4P1vlH15xqEO9HMOqUVh+Asc8N/R/R8fTb+k99Pcp5Bus+XROtSXU9Lgz6vu5xiboVEypfk922Os08R+g+Y8nOMsXQfsfBorj2VPsU166jr9HJNnCzJTzKJkObPaTJohChPpHZ5IxrnvT09Hp6R5lQL6juyhlVaZURY/lM5Fi6lRSaBobVObpNYxm1wycsIQPWMrYFUhi15eKnAsO2bZCsHqAfRl7vKjOvHG20K0g1m22/Zb3vElxQTVc1PoXZzLWpxqVFV7Fk+NXMtctwxXj+u7g2W1T3GBZDmPfZXOluj2k2VWJTj9nz6lSRtwEcv47i+Z0egWKoNdkXocJBG3N+6BPrAYm7GiNOsOg/mz60c2Q56xLWPrf0ZbS4Sic049i30WPJRNm4qOQvz4zj+Y/ac+vkIgmSJYrD9K26hh1nDZVpUlNBsnBFnhSuRmsD1CscJI1KRBiye9AhfLfG+e4PbyqBRvSVEtH8vsdSSomduGPJtXvN6YqBURP+CL2vSq1T2ixKQq9PondMNO7+NQXMauWQyjTvQTJvRwi4pKgTHR+7qBs86jtbU2fYCEJ1kECZ4An9SorEytt9ccvsgB+hgVFYOAbJ1Mulq+XcUgnTRmcsKY1Qz4WT2pSaQn1ruyJolrDmkSIqi6NDJAl1qbAxOFyy1FtUPztgSg1PW+Y50W4qS/RsuB8z5WheyM/fKHTP1whJdCb3FCyLJ8YOZnGHGGxpa0jOS0u8EB/nXgItDvqD7T7lcl71diy7eQVq9a9D9bVcfXnty+3t4vR2elyzHweXmw5p2pHUgKesw2rIZHD53WVcXtfNEOqWyEXs2wkpF78rtrO4OtrlWlppm6oTnZrlGbdgmb4JzkOhmjZiozcxffSXxzFQM4t2zQEyfRNcxoVbHzb2Pq7hIJntnauWfMlqR+LBMn1brL4pY3a3Bd6NfdgIQiI0nvvyhQaJ7sbpOm2HE4m42oTGgvS2IAhCYtTS/Nlop2ndnWGGFFUuhwxejuTyOK8GQ6MdC4Ig1Htq6U4HQRBqgZqLnQqCCzLQBKGukUCZUC/QFFv1gfrVGqFu8DgKZLAIgiAItUQDDmclnxqQv3Y+MhH1giA4oXySmyGDpv4UkoxrTwiCd2QICYIgpAwBQKuqqlLurDiVVrutEQRBEARBEFKLql0snfQE75wJ0vSS3rQ5vIuWE2fQr/kunr9uHVe+MZpLzWWOr+XJyYs4kh4kq09vMsoq6ffGaC49c5R3f1nM/P/Npk3uCK4+ZxGhwjIK09fyyOinCd753zxYkBmv5/NVPDIb7p1eSFPTIU6unsRcJvDz/uH8e8smcaRoBv3OqI/dwpDf2PaTqyex/MIZ3N7J+ZiCIAiCIAiCIAiCICSPzCZnXPOkp6dLoEwQBEEQBEEQBEEQBEEQBEEQBEFoXHgNlMmjFwVBEARBEARBEARBEARBEARBEISURAJlgiAIgiAIgiAIgiAIgiAIgiAIQkoigTJBEARBEARBEARBEARBEARBEAQhJZFAmSAIgiAIgiAIgiAIgiAIgiAIgpCSnO2085GX/lJb7RAEQRAEQRAEQRAEQRAEQRAEQRCEpDDnJ1095XMMlN1/bbOkNEYQBEEQBEEQBEEQBEEQBEEQBEEQ6hsBQKvrRgiCIAiCIAiCIAiCIAiCIAiCIAhCbfP/AYhQ2vaIW8rvAAAAAElFTkSuQmCC\" title=\"Tech Comm Users Unlock 2023.png\" /></strong></li>\r\n</ol>\r\n<p><span style=\"background-color: #ffff00;\"><strong><span style=\"font-size: 14px;\">Who to contact for issues / Other Helpful Information</span></strong></span></p>\r\n<ul>\r\n<li>For issues unlocking the Technical Communication User, create a case for component&#160;<strong>XX-SER-SAPSMP-USR</strong>&#160;and provide the S-user ID of the&#160;Technical Communication User and also attach a screenshot of the unlock error.</li>\r\n<li><span style=\"font-size: 14px;\">If the&#160;Technical Communication User results in <em><strong><span style=\"color: #ff0000;\">401 errors</span></strong></em>, but the user does not show as locked, create a case for component&#160;<strong>XX-SER-SAPSMP-USR</strong>&#160;and provide the S-user ID of the Technical Communication User as well as a screenshot of the unlock issue/error.</span></li>\r\n<li>If the Technical Communication User locks multiple times, check all connections in the systems using that technical user and correct the login (password) information - see KBA&#160;<a target=\"_blank\" href=\"/notes/2359837\">2359837</a>&#160;and KBA&#160;<a target=\"_blank\" href=\"/notes/2454045\">2454045</a>. For further assistance, create a case for the SAP Solution Manager experts under&#160;<strong>SV-SMG</strong>*.</li>\r\n<li>See this Guided Answer for more in-depth troubleshooting for the 401 errors here:&#160;<a target=\"_blank\" href=\"https://gad5158842f.us2.hana.ondemand.com/dtp/viewer/#/tree/1423/actions/17822\">SAP Support Backbone Connectivity Troubleshooting&#160;in Solution Manager 7.2</a></li>\r\n<li>Prevent Technical Communication Users from locking&#160;by using a certificate instead of user/password authentication.&#160;For more information, see&#160;KBA&#160;<a target=\"_blank\" href=\"/notes/2969674\">2969674</a>.<em><br /></em></li>\r\n<li>If the Technical Communication User was not locked, or is unlocked in the Technical Communication Users application,&#160;<em>but you see errors with the technical user</em>,&#160;make sure you are using the correct type of S-user for the task. As mentioned in KBA&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/2668288\">2668288</a>,&#160;some&#160;backend functions require a <strong>regular</strong> S-user ID and others require a&#160;<strong>Technical Communication User</strong>&#160;(S-user).</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p id=\"\"></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>Tech-User , TechUser , Technical-User , Technical S-User , PE1 , PE2 ,&#160;Technical Communication User , STC01 Tech User , Support Hub User , 7.2 , support hub connectivity , RFC connection , SM59 ,</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "SV-SMG-RCD (Rapid Content Delivery)"}, {"Key": "Other Components", "Value": "XX-SER-SAPSMP-USR (User Administration)"}, {"Key": "Other Components", "Value": "SV-SMG-SVC (Administration of Service Connections with Solution Manager)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I820243)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I820243)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002392726/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2969674", "RefComponent": "SV-SMG-SVC", "RefTitle": "Your S-user for technical communications is locked at SAP", "RefUrl": "/notes/2969674"}, {"RefNumber": "2820957", "RefComponent": "BC-UPG-NA", "RefTitle": "Destinations SAP-SUPPORT_PARCELBOX and SAP-SUPPORT_NOTE_DOWNLOAD giving error 401 Unauthorized", "RefUrl": "/notes/2820957"}, {"RefNumber": "2809994", "RefComponent": "XX-SER-FORME", "RefTitle": "Seeing \"No Data\" or getting error \"User does not have the sufficient authorizations\" in Technical Communication Users app. - SAP for Me", "RefUrl": "/notes/2809994"}, {"RefNumber": "2716729", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "SAP backbone connectivity - SAP Parcel Box configuration", "RefUrl": "/notes/2716729"}, {"RefNumber": "2704804", "RefComponent": "XX-SER-FORME", "RefTitle": "Are Technical Communication Users created and maintained the same as regular S-user IDs - SAP for Me", "RefUrl": "/notes/2704804"}, {"RefNumber": "2668288", "RefComponent": "SV-SMG-SVC", "RefTitle": "Differences between personalized S-user ID and Technical Communication User", "RefUrl": "/notes/2668288"}, {"RefNumber": "2635280", "RefComponent": "SV-SMG-INS-CFG-SYP", "RefTitle": "Check connectivity and credentials: User s<ID> format is wrong. Must be between S0000000000 and S9999999999 - SolMan Support Hub configuration", "RefUrl": "/notes/2635280"}, {"RefNumber": "2541908", "RefComponent": "XX-SER-FORME", "RefTitle": "Technical Communication Users - Guided Answer", "RefUrl": "/notes/2541908"}, {"RefNumber": "2506382", "RefComponent": "SV-SMG-SVC", "RefTitle": "Error in SEND_SYSTEM_RELATIONSHIP_TO_SUPP", "RefUrl": "/notes/2506382"}, {"RefNumber": "2500061", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Support Hub Connectivity: Configuration Steps - SAP Solution Manager 7.2", "RefUrl": "/notes/2500061"}, {"RefNumber": "2484860", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Setup Support Hub Connectivity with STC01 is failing in step Check connectivity and credentials for SAP Solution Manager 7.2 since SP05", "RefUrl": "/notes/2484860"}, {"RefNumber": "2454045", "RefComponent": "SV-SMG-INS-CFG-SYP", "RefTitle": "SAP Support Backbone Connectivity Troubleshooting in Solution Manager 7.2 - Guided Answer", "RefUrl": "/notes/2454045"}, {"RefNumber": "2393376", "RefComponent": "XX-SER-FORME", "RefTitle": "How to reset the password for a Technical Communication User - SAP for Me", "RefUrl": "/notes/2393376"}, {"RefNumber": "2289984", "RefComponent": "SV-SMG-INS-CFG", "RefTitle": "Configure the synchronous communication channel", "RefUrl": "/notes/2289984"}, {"RefNumber": "2285422", "RefComponent": "SV-SMG-MON-ALR", "RefTitle": "SM_EXTERN_WS user gets locked frequently in Technical Monitoring", "RefUrl": "/notes/2285422"}, {"RefNumber": "2174416", "RefComponent": "XX-SER-FORME", "RefTitle": "Creation and activation of Technical Communication Users - SAP for Me", "RefUrl": "/notes/2174416"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP for Me > User Management", "RefUrl": "https://me.sap.com/userscontacts/usermanagement"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Launchpad release - new unlock feature - November 2019 Wave 8", "RefUrl": "https://support.sap.com/content/dam/support/en_us/library/ssp/my-support/releasenotes/2019/release-notes-wave8-2019.pdf"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Guided Answer: SAP Support Backbone Connectivity Troubleshooting in Solution Manager 7.2", "RefUrl": "https://gad5158842f.us2.hana.ondemand.com/dtp/viewer/#/tree/1423/actions/17822"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "SAP for Me > Technical Communication Users", "RefUrl": "https://me.sap.com/app/techuser"}, {"RefNumber": "", "RefComponent": "", "RefTitle": "Technical Users Online Help", "RefUrl": "https://support.sap.com/content/launchpad/en_us/misc/techuser-help.html"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3323209", "RefComponent": "BC-UPG-NA", "RefTitle": "Authorization error while downloading notes with HTTPS RFC configured using client certificate authentication", "RefUrl": "/notes/3323209 "}, {"RefNumber": "3267931", "RefComponent": "BC-ESI-WS-ABA-RT", "RefTitle": "HTTP 401 - Web service ping failed (RC=401) when S-User is maintained in Logical port", "RefUrl": "/notes/3267931 "}, {"RefNumber": "3023167", "RefComponent": "XX-SER-SAPSMP-SWC", "RefTitle": "Error: 401 Unauthorized while downloading software - SAP for Me", "RefUrl": "/notes/3023167 "}, {"RefNumber": "2933232", "RefComponent": "XX-SER-SAPSMP-USR", "RefTitle": "How to analyze and process Technical User IDs which are locked - INTERNAL ONLY", "RefUrl": "/notes/2933232 "}, {"RefNumber": "2668288", "RefComponent": "SV-SMG-SVC", "RefTitle": "Differences between personalized S-user ID and Technical Communication User", "RefUrl": "/notes/2668288 "}, {"RefNumber": "2737826", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2737826 "}, {"RefNumber": "2885274", "RefComponent": "SV-SMG-SUP", "RefTitle": "HTTP error 401 when sending data to SAP via destination SM_SP_<customer id>_H - Solution Manager", "RefUrl": "/notes/2885274 "}, {"RefNumber": "2393376", "RefComponent": "XX-SER-FORME", "RefTitle": "How to reset the password for a Technical Communication User - SAP for Me", "RefUrl": "/notes/2393376 "}, {"RefNumber": "2785863", "RefComponent": "XX-SER-FORME", "RefTitle": "How many Technical Communication Users should be created for SAP Solution Manager? - SAP for Me", "RefUrl": "/notes/2785863 "}, {"RefNumber": "2704804", "RefComponent": "XX-SER-FORME", "RefTitle": "Are Technical Communication Users created and maintained the same as regular S-user IDs - SAP for Me", "RefUrl": "/notes/2704804 "}, {"RefNumber": "2541908", "RefComponent": "XX-SER-FORME", "RefTitle": "Technical Communication Users - Guided Answer", "RefUrl": "/notes/2541908 "}, {"RefNumber": "2532813", "RefComponent": "XX-SER-FORME", "RefTitle": "Information about the Technical Communication User - SAP for Me", "RefUrl": "/notes/2532813 "}, {"RefNumber": "2286905", "RefComponent": "XX-SER-SAPSMP-USR", "RefTitle": "Troubleshooting the Technical Communication User (formerly Support Hub user) for Data Transfer  - INTERNAL ONLY", "RefUrl": "/notes/2286905 "}, {"RefNumber": "2174416", "RefComponent": "XX-SER-FORME", "RefTitle": "Creation and activation of Technical Communication Users - SAP for Me", "RefUrl": "/notes/2174416 "}, {"RefNumber": "2740667", "RefComponent": "XX-SER-NET", "RefTitle": "SAP is going to close its proprietary RFC communication for automated data exchange between Customers & SAP Support Backbone (SAPOSS) by July 2020.", "RefUrl": "/notes/2740667 "}]}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP Solution Manager 7.2"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "6 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 3.33, "Quality-Votes": 9, "RatingQualityDetails": {"Stars-1": 3, "Stars-2": 0, "Stars-3": 1, "Stars-4": 1, "Stars-5": 4}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}