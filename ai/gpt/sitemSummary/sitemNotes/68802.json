{"Request": {"Number": "68802", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 282, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014494082017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000068802?language=E&token=67CEBE2F2E6AD9F5B2688F039488BB9A"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000068802", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000068802/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "68802"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Help for error analysis"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "12.03.2001"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AA-AA-F"}, "SAPComponentKeyText": {"_label": "Component", "value": "Services"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Asset Accounting", "value": "FI-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AA-AA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Services", "value": "FI-AA-AA-F", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AA-AA-F*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "68802 - Legacy data trnsfr: diff.fields n.ready f.input"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Different fields are not ready for input when you transfer old assets data.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>AS91, AS92, SAPLALTD, RAALTD01, ALTD_FIELD_INPUT_CHECK, GT_TMPVAL</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Avoiding input errors</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following rules exist that are assigned to the fields by the program as listed below:<br />01: The depreciation area has an identical value transfer<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093A-XWRTID = 'x') and an identical takeover of terms<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093A-XINDID = 'X') from another depreciation area<br />02: The depreciation area does not manage any acquisition values<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZANSW = '0')<br />03: The depreciation area manages neither acquisition values nor<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;replacement values &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZANSW = T093-VZINDW = T093-VZINDA = '0')04: The depreciation area does not manage any ordinary depreciations &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZNAFA = '0')05: The depreciation area does not manage any special depreciations &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZSAFA = '0')06: The depreciation area does not manage any unplanned depreciation<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZMAFA = '0')<br />07: The depreciation area does not manage any transfer of reserves &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZMAFA = '0')08: The depreciation area does not manage any revaluations on<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;depreciations &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZINDA = '0')09: The depreciation area does not manage any interest<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZZINS = '0')<br />10: The depreciation area does not manage any investment support &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZINVZ = '0')11: The depreciation area does not manage any revaluations<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093-VZINDW = '0')<br />12: The fixed asset is managed as an asset under construction managed<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;via line items &#x00A0;&#x00A0;&#x00A0;&#x00A0;(ANLA-XOPVW = 'X')13: The fixed asset is assigned to a group asset<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(ANLA-ANLGR is not empty)<br />14: The asset class should not manage any down payments<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(no entry of the asset class together with the chart of<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;depreciation in table TABWI)<br />15: The depreciation key of the fixed asset does not manage any<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;calculation key for ordinary depreciations (T090C-NAFASL)<br />16: The depreciation key of the fixed asset does not manage any<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;calculation key for special depreciations (T090C-SAFASL)<br />17: The replacement value should be calculated when transferred by<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;the system (T093U-XNEUWB = 'x')<br />18: The entry of net book value is carried out instead of the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ordinary depreciation (T093C-XREBUW = 'x')<br />19: The entry of net book value is carried out (T093C-XREBUW = 'x') but<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;the cumulative depreciation should be calculated when<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;transferred by the system (T093U-XNEUAF = 'x')<br />20: No entry of net book value is carried out (T093C-XREBUW = ' ')<br />21: The cumulative depreciation should be calculated when transferred<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;by the system (T093U-XNEUAF = 'x')<br />22: The fixed assets of the company code are copied to a value date<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;which displays the end of a fiscal year &#x00A0;&#x00A0;&#x00A0;&#x00A0;(T093C-DATUM)23: A depreciation run has already been carried out for the company<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;code<br />24: It is not a transaction for creating a fixed asset<br />25: Asset under construction managed via line items (ANLA-XOPVW = 'X'),<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;but the transfer date for old assets (T093C-DATUM) is a fiscal<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;year-end<br />26: Transaction type group of transaction type must permit proportional<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;values &#x00A0;&#x00A0;&#x00A0;&#x00A0;(TABWG-XANTEI = 'X')27: The cumulative depreciation should be calculated when transferred<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;by the system (T093U-XNEUAF = 'x') and it is a transaction whose<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;transaction type group either points to the type 'acquisition<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;value'<br />28: For mid-year transfer, the capitalization date lies in the fiscal<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;year of the transfer date<br />29: The depreciations of the valuation area are not posted in the<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;general ledger (T=93-BUHBKT = '0').<br />30: The transaction was started in the display mode<br />31: The cost-accounting revaluation was not posted to the general<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;ledger. <br />The following rules define the conditions under which the input fields are <B>not ready for input</B>. Note that it is sufficient if one of these conditions is met.</p> <OL>1. Cumulative values</OL> <OL><OL>a) Acquisition value<br />Rules 01, 02, 12, 28, 30</OL></OL> <OL><OL>b) Down payments<br />Rules 01, 02, 12, 14, 28, 30</OL></OL> <OL><OL>c) Revaluation<br />Rules 01, 03, 11, 12, 13, 17, 28, 30</OL></OL> <OL><OL>d) Investment support<br />Rules 01, 10, 12, 28, 30</OL></OL> <OL><OL>e) Cumulative ordinary depreciation<br />Rules 01, 04, 12, 13, 15, 18, 21, 28, 30</OL></OL> <OL><OL>f) Cumulative special depreciation<br />Rules 01, 05, 12, 13, 16, 21, 28, 30</OL></OL> <OL><OL>g) Cumulative unplanned depreciation<br />Rules 01, 06, 12, 13, 28, 30</OL></OL> <OL><OL>h) Cumulative transfer of reserves<br />Rules 01, 07, 12, 13, 28, 30</OL></OL> <OL><OL>i) Cumulative revaluation of the ordinary depreciation<br />Rules 01, 03, 08, 12, 13, 21, 28, 30</OL></OL> <OL><OL>j) Cumulative interest<br />Rules 01, 09, 12, 13, 21, 28, 30</OL></OL> <OL><OL>k) Net book value<br />Rules 01, 12, 13, 19, 20, 28, 30</OL></OL> <OL><OL>l) Posted values<br />Rules 22, 23, for posted depreciations also rule 29, for posted revaluations also rule 31.</OL></OL> <UL><LI>The same rules basically apply to the categories of the posted values (for example ordinary depreciations) as those when you enter the same categories for cumulative values, with the exception of rule 28.</LI></UL> <UL><LI>Exception: Posted values can also be entered for assets under construction managed via line items</LI></UL> <OL>2. Transactions</OL> <OL><OL>a) Transaction type<br />Rule 24</OL></OL> <OL><OL>b) Asset value date<br />Rules 24, 25</OL></OL> <OL><OL>c) Amount posted<br />Rules 01, 02</OL></OL> <OL><OL>d) Proportional value adjustments ordinary depreciation<br />Rules 04, 13, 15, 26, 27</OL></OL> <OL><OL>e) Proportional value adjustments special depreciation<br />Rules 05, 13, 16, 26, 27</OL></OL> <OL><OL>f) Proportional value adjustments unplanned depreciation<br />Rules 06, 13</OL></OL> <OL><OL>g) Proportional value adjustments transfer of reserves<br />Rules 07, 13</OL></OL> <OL>3. Expired useful life<br />Rule 21 as of maintenance level 3.0F, the entered value is overwritten previously</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D018356"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000068802/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000068802/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000068802/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176"}, {"RefNumber": "449162", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS91/92/93:screen layout error with transfer values", "RefUrl": "/notes/449162"}, {"RefNumber": "388282", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS91: incorrect ready for input status for posted values", "RefUrl": "/notes/388282"}, {"RefNumber": "197919", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transf.AuC proport.values not open", "RefUrl": "/notes/197919"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2665996", "RefComponent": "FI-AA-AA", "RefTitle": "\"Ordinary depreciation posted\" field cannot be changed in T-code: AS92", "RefUrl": "/notes/2665996 "}, {"RefNumber": "197919", "RefComponent": "FI-AA-AA-A", "RefTitle": "Legacy data transf.AuC proport.values not open", "RefUrl": "/notes/197919 "}, {"RefNumber": "449162", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS91/92/93:screen layout error with transfer values", "RefUrl": "/notes/449162 "}, {"RefNumber": "550176", "RefComponent": "FI-AA-AA-A", "RefTitle": "FAQ note legacy data transfer asset master records", "RefUrl": "/notes/550176 "}, {"RefNumber": "388282", "RefComponent": "FI-AA-AA-A", "RefTitle": "AS91: incorrect ready for input status for posted values", "RefUrl": "/notes/388282 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}