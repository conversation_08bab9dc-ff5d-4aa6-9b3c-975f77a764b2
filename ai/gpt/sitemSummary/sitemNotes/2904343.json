{"Request": {"Number": "2904343", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2220, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000477492020"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002904343?language=E&token=511011A6C9E0473C39689F4A7E6C73B4"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002904343", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002904343/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2904343"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.06.2021"}, "SAPComponentKey": {"_label": "Component", "value": "CA-GTF-MIG"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP S/4HANA Data Migration Cockpit Content"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "General Application Functions", "value": "CA-GTF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP S/4HANA Data Migration Cockpit Content", "value": "CA-GTF-MIG", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-GTF-MIG*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2904343 - SAP S/4HANA Migration Cockpit: MARCH 2020 - Central correction Note for content issues for SAP S/4HANA 1909"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You have an issue with the SAP S/4HANA Data Migration content delivered for SAP S/4HANA 1909. Error with the content.</p>\r\n<p>This Note is valid for:</p>\r\n<ul>\r\n<li>SAP S/4HANA - LTMC - \"Transferring Data Using Files\"</li>\r\n<li>SAP S/4HANA - LTMC - \"Transferring Data Using Staging Tables\"</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Fixed Asset, SIF_FIXED_ASSET, SIF_MSTR_CHAR, <span style=\"font-family: 'Verdana',sans-serif;\">Master Inspection Characteristics,</span> FI - Accounts payable open item, FI &#8211; Accounts receivable open item, FI &#8211; G/L account balance and open/line item, Customer, Customer&#160;- extend existing record by new org. levels, FI - Historical balance, validation stuck on 65%,&#160;FI_CURRENCY_INFORMATION, <span style=\"font-family: 'Verdana',sans-serif;\">Inspection Plan, SIF_INSP_PLAN</span></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<div class=\"longtext\" style=\"font-size: 100.01%;\">\r\n<p>You have installed SAP S/4HANA 1909 (SP00 or SP01)</p>\r\n<p>You are using SAP S/4HANA migration cockpit</p>\r\n<p>You are using the pre-delivered SAP S/4HANA Data migration content without any modifications</p>\r\n</div>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The TCI included with this SAP Note fixes the following issues listed in the table below. For detailed description on the issues see the linked SAP Notes.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"3\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Technical Name</td>\r\n<td>Type</td>\r\n<td>Issue</td>\r\n<td>SAP Note&#160;</td>\r\n</tr>\r\n<tr>\r\n<td>SIF_FIXED_ASSET</td>\r\n<td>Migration object</td>\r\n<td>\r\n<p>&#160;Multiple entries for \"Investment Support\" not possible</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2902349\">2902349</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_MSTR_CHAR</p>\r\n</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;load master inspection characteristics and getting the error \"Non-relevant fields will be initialized\".</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2901766\">2901766</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_GL_BALANCES</p>\r\n<p>SIF_OPEN_ITEM_AP</p>\r\n<p>SIF_OPEN_ITEM_AR</p>\r\n<p>SIF_CUSTOMER_2</p>\r\n<p>SIF_CUST_EXT_2</p>\r\n<p>SIF_FIXED_ASSET</p>\r\n<p>SIF_HIST_BALANCE</p>\r\n</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>Validation stuck due to missing company code configuration/customizing identified by FM \"FI_CURRENCY_INFORMATION\" like&#160;\"Country/region not defined in system\"</p>\r\n</td>\r\n<td>\r\n<p>&#160;<a target=\"_blank\" href=\"/notes/2900595\">2900595</a></p>\r\n</td>\r\n</tr>\r\n<tr>\r\n<td>\r\n<p>SIF_INSP_PLAN</p>\r\n</td>\r\n<td>Migration Object</td>\r\n<td>\r\n<p>&#160;Add missing field for inspection plan characteristics</p>\r\n</td>\r\n<td>\r\n<p><a target=\"_blank\" href=\"/notes/2902943\">2902943</a></p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>Be aware that the TCI only corrects the related objects of SAP delivered content. As a result, the generated migration objects will be updated automatically.</p>\r\n<p><strong>Note:</strong> If you have modified/copied your object, the correction is not done within your modified/copied object.</p>\r\n<p>You may refer KBA&#160;<a target=\"_blank\" href=\"/notes/2543372\">2543372</a> - \"How to implement a Transport-based Correction Instruction\".</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I055188)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I327011)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002904343/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002904343/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543372", "RefComponent": "BC-UPG-NA", "RefTitle": "How to implement a Transport-based Correction Instruction", "RefUrl": "/notes/2543372"}, {"RefNumber": "2902943", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Inspection Plan and missing some fields for inspection characteristics in migration template", "RefUrl": "/notes/2902943"}, {"RefNumber": "2902349", "RefComponent": "CA-GTF-MIG", "RefTitle": "Multiple entries for \"Investment Support\" not possible / Migration Object \"Fixed asset (incl. balances and transactions)", "RefUrl": "/notes/2902349"}, {"RefNumber": "2901766", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Master Inspection Characteristics\" getting error \"Non-relevant fields will be initialized\" (Message no. QP504), and other quantitative fields issue", "RefUrl": "/notes/2901766"}, {"RefNumber": "2900595", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: AP / AR open item or G/L account balance or Customer or Customer extend or Fixed Asset - Validation stuck due to missing company code configuration/customizing identified by FM \"FI_CURRENCY_INFORMATION\" like Country/region n", "RefUrl": "/notes/2900595"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2537549", "RefComponent": "CA-GTF-MIG", "RefTitle": "Collective SAP Note and FAQ for SAP S/4HANA Migration cockpit - File/Staging (on premise / S4CORE)", "RefUrl": "/notes/2537549 "}, {"RefNumber": "2902943", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Inspection Plan and missing some fields for inspection characteristics in migration template", "RefUrl": "/notes/2902943 "}, {"RefNumber": "2900595", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: AP / AR open item or G/L account balance or Customer or Customer extend or Fixed Asset - Validation stuck due to missing company code configuration/customizing identified by FM \"FI_CURRENCY_INFORMATION\" like Country/region n", "RefUrl": "/notes/2900595 "}, {"RefNumber": "2901766", "RefComponent": "CA-GTF-MIG", "RefTitle": "SAP S/4HANA Migration Cockpit: Migration of Master Inspection Characteristics\" getting error \"Non-relevant fields will be initialized\" (Message no. QP504), and other quantitative fields issue", "RefUrl": "/notes/2901766 "}, {"RefNumber": "2902349", "RefComponent": "CA-GTF-MIG", "RefTitle": "Multiple entries for \"Investment Support\" not possible / Migration Object \"Fixed asset (incl. balances and transactions)", "RefUrl": "/notes/2902349 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "S4CORE", "From": "104", "To": "104", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "S4CORE", "NumberOfCorrin": 1, "URL": "/corrins/0002904343/19773"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}