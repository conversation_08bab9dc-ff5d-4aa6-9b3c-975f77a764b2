{"Request": {"Number": "598177", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1774, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000003043362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=54CE58A95ACECCDA1773493856BFB165"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "598177"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.04.2004"}, "SAPComponentKey": {"_label": "Component", "value": "FS-BP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Business Partner"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Services", "value": "FS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Partner", "value": "FS-BP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FS-BP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "598177 - PAR: Wrong transfer of pers. number (possible inconsist.)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In your system, you carried out the business partner conversion from Treasury Business Partner to SAP Business Partner Financial Services. After the conversion, field Personnel number (BUT000-PERNO) on teh SAP BP side is empty. Re-entering the personnel number into field BUT000-<PERSON>ERN<PERSON> in the SAP BP maintenance deletes the value of the corresponding field of TR BP (BP000-PERS_NR). This may cause inconsistencies and data loss.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Business partner, conversion, parallel maintenance, Treasury, SAP GP FS, BPC2, BPC1, BUP1, BUP2, personnel number, BP000, BUT000, BP001, PERS_NR, PERNO, FTBU_CONV_PARALLEL, external data transfer, direct input</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>During the conversion from TR BP to SAP BP, the personnel number (field BP000-PERS_NR) is transferred into field BUT000-PERNO of SAP BP. This is incorrect. Correct would be to transfer to field BP001-PERS_NR of SAP BP FS. When field BUT000-PERNO is filled in SAP BP, field BP000-PERS_NR is deleted from the Treasury BP, since here the transfer is correctly generated from field BP001-PERS_NR.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. Implement the attached correction. This ensures that the correct field (BP001-PERS_NR) is filled in the conversion and parallel maintenance from TR BP to SAP BP.</OL> <OL>2. Create a report with the following coding in the customer namespace and execute it. The report corrects the inconsistencies in the system that may have occurred.</OL> <p><br />REPORT z_bp000_bp001_persnr_sync .<br /><br />******************************************************************<br />* The report adjusts fields BP000-PERS_NR, BP001-PERS_NR and<br />* BUT000-PERNO with each other for the personnel number which<br />* may contain inconsistencies due to a program error<br />* --&gt; Note 598177<br />******************************************************************<br /><br />******************************************************************<br />* Internal tables<br />****************************************************************** DATA: gt_bp000&#x00A0;&#x00A0;&#x00A0;&#x00A0; LIKE bp000&#x00A0;&#x00A0;&#x00A0;&#x00A0;OCCURS 0 WITH HEADER LINE,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp000_upd LIKE bp000&#x00A0;&#x00A0;&#x00A0;&#x00A0;OCCURS 0 WITH HEADER LINE,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp001&#x00A0;&#x00A0;&#x00A0;&#x00A0; LIKE bp001&#x00A0;&#x00A0;&#x00A0;&#x00A0;OCCURS 0 WITH HEADER LINE,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp001_upd LIKE bp001&#x00A0;&#x00A0;&#x00A0;&#x00A0;OCCURS 0 WITH HEADER LINE,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_but000&#x00A0;&#x00A0;&#x00A0;&#x00A0;LIKE but000&#x00A0;&#x00A0; OCCURS 0 WITH HEADER LINE.</ P> <p><br />******************************************************************<br />* Fields<br />****************************************************************** DATA: flag_eof,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;line&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TYPE i,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;lv_indx&#x00A0;&#x00A0; LIKE sy-tabix,<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gv_partnr LIKE bp000-partnr.<BR/> <BR/> CLEAR:&#x00A0;&#x00A0; gt_bp000, gt_bp000_upd, gt_bp001, gt_bp001_upd, gt_but000.<BR/> REFRESH: gt_bp000, gt_bp000_upd, gt_bp001, gt_bp001_upd, gt_but000.<BR/> <BR/> WHILE flag_eof IS INITIAL.<BR/> * Einlesen eines Blocks von 100 Partnern von der Datenbank<BR/> &#x00A0;&#x00A0;SELECT * FROM bp000 INTO TABLE gt_bp000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;UP TO 100 ROWS<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHERE partnr &lt;&gt; space<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND&#x00A0;&#x00A0; partnr &gt;&#x00A0;&#x00A0;gv_partnr<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;AND&#x00A0;&#x00A0; type&#x00A0;&#x00A0; =&#x00A0;&#x00A0;'2'.<BR/> &#x00A0;&#x00A0;IF sy-subrc = 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;LOOP AT gt_bp000.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SELECT SINGLE * FROM but000 INTO gt_but000<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHERE partner = gt_bp000-partner.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF sy-subrc = 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPEND gt_but000.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SELECT SINGLE * FROM bp001&#x00A0;&#x00A0;INTO gt_bp001<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;WHERE partner = gt_bp000-partner.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF sy-subrc = 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPEND gt_bp001.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDLOOP.<BR/> <BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;DESCRIBE TABLE gt_bp000 LINES line.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;READ TABLE gt_bp000 INDEX line.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;gv_partnr = gt_bp000-partnr.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;IF line &gt; 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LOOP AT gt_bp001.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;READ TABLE gt_bp000 WITH KEY partner = gt_bp001-partner.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF sy-subrc NE 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CLEAR gt_bp000.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;READ TABLE gt_but000 WITH KEY partner = gt_bp001-partner.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF sy-subrc NE 0.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CLEAR gt_bp000.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF gt_bp001-pers_nr IS INITIAL.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF NOT gt_bp000-pers_nr IS INITIAL.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp001-pers_nr = gt_bp000-pers_nr.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPEND gt_bp001 TO gt_bp001_upd.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ELSE.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;IF NOT gt_but000-perno IS INITIAL.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp001-pers_nr = gt_but000-perno.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPEND gt_bp001 TO gt_bp001_upd.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;gt_bp000-pers_nr = gt_but000-perno.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPEND gt_bp000 TO gt_bp000_upd.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDLOOP.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;ELSE.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;flag_eof = 'X'.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;ELSE.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;flag_eof = 'X'.<BR/> &#x00A0;&#x00A0;ENDIF.<BR/> &#x00A0;&#x00A0;CLEAR:&#x00A0;&#x00A0; gt_bp000, gt_bp001, gt_but000.<BR/> &#x00A0;&#x00A0;REFRESH: gt_bp000, gt_bp001, gt_but000.<BR/> ENDWHILE.<BR/> <BR/> END-OF-SELECTION.<BR/> <BR/> &#x00A0;&#x00A0;LOOP AT gt_bp000_upd.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;UPDATE bp000 FROM gt_bp000_upd.<BR/> &#x00A0;&#x00A0;ENDLOOP.<BR/> &#x00A0;&#x00A0;LOOP AT gt_bp001_upd.<BR/> &#x00A0;&#x00A0;&#x00A0;&#x00A0;UPDATE bp001 FROM gt_bp001_upd.<BR/> &#x00A0;&#x00A0;ENDLOOP.<BR/> <BR/> &#x00A0;&#x00A0;COMMIT WORK.<br /><br /><br /><br /><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "TR-TM-TM (Basic Data)"}, {"Key": "Other Components", "Value": "FIN-FSCM-TRM-BF-BP (Please use component FS-BP!)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D033094)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D033094)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "398888", "RefComponent": "FS-BP", "RefTitle": "BP_TR1: Composite note and FAQs about bus partner conversion", "RefUrl": "/notes/398888 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "From": "110", "To": "110", "Subsequent": ""}, {"SoftwareComponent": "BANK/CFM", "From": "463_20", "To": "463_20", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-FINSERV 110", "SupportPackage": "SAPKGPFA08", "URL": "/supportpackage/SAPKGPFA08"}, {"SoftwareComponentVersion": "BANK/CFM 463_20", "SupportPackage": "SAPKIPBJ18", "URL": "/supportpackage/SAPKIPBJ18"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-FINSERV", "NumberOfCorrin": 1, "URL": "/corrins/**********/201"}, {"SoftwareComponent": "BANK/CFM", "NumberOfCorrin": 1, "URL": "/corrins/**********/59"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}