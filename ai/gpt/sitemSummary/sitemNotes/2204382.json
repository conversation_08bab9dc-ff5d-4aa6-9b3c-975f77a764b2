{"Request": {"Number": "2204382", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 259, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018145392017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=5845FAA3D29C6616AE4A92947C119F29"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2204382"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "05.06.2019"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BCT-GEN"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT."}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "BW only - Business Content and Extractors", "value": "BW-BCT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP_BW Add-On Components: BI_CONT & BI_CONT_XT.", "value": "BW-BCT-GEN", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BCT-GEN*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2204382 - SAP HANA-optimized BI Content with BI_CONT 757 SP06 & SP07: Recommended SAP BW 7.40 SP and SAP Notes"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>SAP delivers new SAP HANA-optimized BI Content&#160;with&#160;BI Content AddOn&#160;757 SP06&#160;(SAPK-75706INBICONT) and SP07 (SAPK-75707INBICONT). The new content&#160;is using&#160;the new BW InfoProviders</p>\r\n<p>- Advanced DataStore objects and</p>\r\n<p>- HANA Composite Provider.</p>\r\n<p><a target=\"_blank\" href=\"http://help.sap.com/saphelp_nw74/helpdata/en/28/a1065182d3c557e10000000a44176d/frameset.htm\">http://help.sap.com/saphelp_nw74/helpdata/en/28/a1065182d3c557e10000000a44176d/frameset.htm</a></p>\r\n<p>&#160;</p>\r\n<p>You want to use this SAP HANA-optimized BI Content&#160;shipped with BI_CONT 757 SP06 and SP07.</p>\r\n<p>This SAP Note informs you about the minimal required and recommended&#160;SAP BW 7.40 support package.</p>\r\n<p>In case you cannot&#160;install the recommended SAP BW 7.40 support package, this SAP Note provides you with a list of SAP Notes that need to be installed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP HANA-optimized BI Content, BW Content, BI_CONT, Advanced DataStore objects, ADSO,&#160;HANA Composite Provider, HCPR</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>SAP delivers new SAP HANA-optimized BI Content&#160;as part of&#160;BI Content AddOn&#160;757 SP06&#160;/ SP07&#160;(software component BI_CONT).</p>\r\n<p>This new content&#160;takes use of the new BW InfoProviders</p>\r\n<p>- Advanced DataStore objects and</p>\r\n<p>- HANA Composite Provider.</p>\r\n<p>&#160;</p>\r\n<p>Technically you can&#160;install BI Content AddOn 757 SP06&#160;or higher&#160;only in a SAP BW 7.40 system with support package 10 or higher (minimal required support package).</p>\r\n<p>However, it is recommended to use the latest released&#160;SAP BW 7.40&#160;support package (recommended support package).</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>SAP recommends to use the latest released SAP BW 7.40 support package.</p>\r\n<p>&#160;</p>\r\n<p>If this is not possible, implementation of&#160;further SAP Notes&#160;is recommended&#160;to avoid already known&#160;issues related to</p>\r\n<p>-&#160;Installation and activation of the new BI content objects</p>\r\n<p>- Changing and copying the new BI content objects</p>\r\n<p>- Use of the new BI content objects (e.g. data loading and query execution).</p>\r\n<p><span style=\"text-decoration: underline;\">Note:</span> If you do not use the advanced SAP HANA-optimized BI Content, the SAP Notes&#160;might be&#160;still helpful&#160;if you want to create &amp; use&#160;your own Advanced DataStore objects&#160;and CompositeProviders.</p>\r\n<p>&#160;</p>\r\n<p><span style=\"text-decoration: underline;\">Procedure:</span> You can find a collection of the recommended SAP Notes in csv-files&#160;attached to this SAP Note (see 'Attachments').</p>\r\n<p>- If your system is on <strong>SAP BW 7.40 SP10 (SAPKW74010)</strong>, implement&#160;all SAP Notes as listed in&#160;file <strong>'BW740_SP10_v01.csv'</strong>. The other csv-files are not relevant for you.</p>\r\n<p>- If your system is on <strong>SAP BW 7.40 SP11 (SAPKW74011)</strong>, implement all SAP Notes as listed in&#160;file&#160;<strong>'BW740_SP11_v01.csv'</strong>. The other csv-files are not relevant for you.</p>\r\n<p>- If your system is on <strong>SAP BW 7.40 SP12 (SAPKW74012)</strong>, implement all SAP Notes as listed in&#160;file&#160;<strong>'BW740_SP12_v01.csv'</strong>. The other csv-files are not relevant for you.</p>\r\n<p>&#160;</p>\r\n<p>If you want to take use of the new HANA-optimized&#160;<strong>Inventory Management BI Content</strong>, the following additional SAP Notes are required to fix known issues&#160;with <strong>handling of</strong> <strong>non-cumulatives</strong>:</p>\r\n<p>BW 7.40 SP19 / BW 7.50 SP10: 2543774&#160;Data loading to Non Cumulative Cube Like ADSO doesn't work correctly</p>\r\n<p>BW 7.40 SP10: 20109810 Non-cumulatives: Performance problem (NCUM method = Y)</p>\r\n<p>BW 7.40 SP10 - SP11: 2156886 Performance of non-cumulative queries on advanced datastore object</p>\r\n<p>BW 7.40 SP10 - SP11:&#160;2174261 Query on Composite Provider terminates with non-cumulative Keyfigures and Navigation Attributes / 0REQUID_SID</p>\r\n<p>BW 7.40 SP10 - SP12: 2179831 Non-cumulatives: No non-cumulative data if aDSO in HCPR</p>\r\n<p>BW 7.40 SP10 - SP12: 2179779 Non-cumulatives: No non-cumulative data if aDSO in HCPR (II)</p>\r\n<p>BW 7.40 SP10 - SP15 / BW 7.50 SP03: 2278395 LISTCUBE: 0RECORDTP cannot be selected</p>\r\n<p>BW 7.40 SP10 - SP17 / BW 7.50 SP07: 2385411&#160;Incorrect selection of reference points in queries on SAP HANA CompositeProvider</p>\r\n<p>(after&#160;last two&#160;SAP Notes&#160;it is&#160;recommended&#160;to&#160;refill&#160;the&#160;validity&#160;table&#160;with&#160;the&#160;help&#160;of&#160;the&#160;function&#160;RSDV_VALID_RECREATE&#160;(parameter&#160;I_REBUILD_FROM_FACT&#160;=&#160;X)&#160;as&#160;described&#160;in&#160;note&#160;1548125&#160;-&#160;Interesting&#160;facts&#160;about&#160;Inventory&#160;Cubes)</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-WHM-DBA-HCPR (CompositeProvider)"}, {"Key": "Other Components", "Value": "BW-WHM-DBA-ADSO (DataStore Object (advanced))"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (I037341)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I037341)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "BW740_SP10_v02.csv", "FileSize": "8", "MimeType": "application/vnd.ms-excel", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001550802015&iv_version=0007&iv_guid=0B8F798ABF2B3443967F06C1807C2D0F"}, {"FileName": "BW740_SP11_v02.csv", "FileSize": "3", "MimeType": "application/vnd.ms-excel", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001550802015&iv_version=0007&iv_guid=1A450A8B6E18044B901FCF4DF5AE7349"}, {"FileName": "BW740_SP12_v02.csv", "FileSize": "1135", "MimeType": "application/vnd.ms-excel", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001550802015&iv_version=0007&iv_guid=76A26C4BC717D148A8BB0D39BFAF65FD"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2543774", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "740SP19: Data loading to Non Cumulative Cube Like ADSO doesn't work correctly", "RefUrl": "/notes/2543774"}, {"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2206409", "RefComponent": "BW-BCT-GEN", "RefTitle": "SAP HANA-optimized BI Content for FI-AR: Error during query execution / SAP Exit variable 0F_CLRDATE", "RefUrl": "/notes/2206409 "}, {"RefNumber": "1817520", "RefComponent": "BW-BCT-DOC", "RefTitle": "Collective Note: SAP HANA-optimized BI Content shipped with BI_CONT 737 / 747 / 757", "RefUrl": "/notes/1817520 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "BI_CONT", "From": "757", "To": "757", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}