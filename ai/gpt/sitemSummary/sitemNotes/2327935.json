{"Request": {"Number": "2327935", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 285, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018345202017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002327935?language=E&token=ECD2AFC1C1D5072994374500D2407C2C"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002327935", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002327935/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2327935"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Installation information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.03.2017"}, "SAPComponentKey": {"_label": "Component", "value": "CA-UI5-COR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Core and Runtime"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Cross-Application Components", "value": "CA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAPUI5", "value": "CA-UI5", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-UI5*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Core and Runtime", "value": "CA-UI5-COR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'CA-UI5-COR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2327935 - General Information: FIORI UI Infrastructure Components  for products on SAP Frontend Server 2.0"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>For setting up the system landscape to run SAP Fiori apps&#160;delivered with the products which requires a SAP Frontend Server 2.0.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAP Fiori,&#160;UI, APP, UI Add-on, User Interface</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Product version the Addon SAP FIORI FRONT-END SERVER 2.0&#160; is installed.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>Consider the following general information: Release Notes for <strong>SAP FIORI FRONT-END SERVER 2.0&#160;</strong>&#160;<a target=\"_blank\" href=\"/notes/2219596\">2219596</a>&#160;- SAP <PERSON>ori front-end server 2.0 - General Information</p>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Affected product versions /&#160;support package stacks:</span></strong></p>\r\n<p>SCM PPDS 1.0 ON ERP 6.0 EHP8 -&gt; instance: UI for SCM PPDS -&gt; Software component: UIPPDS01 100&#160;&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160; Stack 00 and Stack 01</p>\r\n<p>SAP ANALYTICAL SERVICES 1.0 -&gt; instance: UI Analytical Services Core* -&gt; Software component:&#160;UISSB001 100&#160;&#160;&#160;&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00</p>\r\n<p>SAP FIORI FOR SAP SFIN 1605 -&gt; instance: UI for FIN -&gt; Software component:&#160;UIAPFI70 300 SP00&#160;&amp; UIAPPL01 100&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00</p>\r\n<p>SAP FIORI FOR SAP SFIN 1605 -&gt; instance: UI for FIN -&gt; Software component:&#160;UIAPFI70 300 SP01&#160;&amp; UIAPPL01 100&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 01</p>\r\n<p>FIORI APPROVE REQUESTS X1 2.0 -&gt; instance:Approve Requests UI 2.0 -&gt; Software component: UIX01CA1 200&#160;&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160; Stack 01</p>\r\n<p>&#160;</p>\r\n<p>requires SAP FIORI FRONT-END SERVER 2.0 SPS01&#160;:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" class=\"confluenceTable\">\r\n<tbody>\r\n<tr><th class=\"confluenceTh\">SAP NetWeaver (Minimum SP level UI &amp; gateway components )</th><th class=\"confluenceTh\">Mininum SP Level</th><th class=\"confluenceTh\">&#160;additional needed SAP notes</th></tr>\r\n<tr>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>Software component:&#160; IW_BEP 200 &amp;&#160;UI_700&#160;200 &#160;&amp; UI_701 200 &amp; UI_702&#160;200 &amp; UI_731 200&#160;all SP01</p>\r\n<p>Software component:&#160; GW_CORE 200 SP11 &amp;&#160;IW_FND 250&#160;all SP11</p>\r\n<p><span style=\"text-decoration: underline;\">Netweaver 7.40 &amp; 7.50</span></p>\r\n<p>User Interface Technology for SAP NetWeaver&#160; ( SAP_UI 750 SP01 )</p>\r\n<p>SAP NW GATEWAY FOUNDATION ( SAP_GWFND 7.50 SP01 )</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.31 SPS13</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.40</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.40 SPS08</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.50</span></span></span></p>\r\n<p>SAP KERNEL 7.45 64-BIT UNICODE&#160; SP015</p>\r\n<p>product version:&#160; SAP NetWeaver 7.50 SPS01</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p>&#160;</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2224749\">2224749</a>&#160;-&#160; SAPUI5 Distribution 1.32 - Central Note</li>\r\n<li><a target=\"_blank\" href=\"/notes/2275285\">2275285</a> - UI2 &amp; USHELL upgrade to version 1.32.10 for UI Add-On 2.0</li>\r\n</ul>\r\n<p><strong>The update process of UI5 libaries&#160;corrections are delivered via files which has to be&#160;downloaded from SMP.</strong></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2118663\">2118663</a>&#160;- Install a patch for SAPUI5 runtime libraries (Information about process)</li>\r\n<li><a target=\"_blank\" href=\"/notes/2103131\">2103131</a>&#160; - SAPUI5 Fixes for update via SMP</li>\r\n<li><a target=\"_blank\" href=\"/notes/2275176\">2275176</a>&#160;-&#160; SAPUI5 upgrade to version 1.32.11</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Affected product versions /&#160;support package stacks:</span></strong></p>\r\n<p>SCM PPDS 1.0 ON ERP 6.0 EHP8 -&gt; instance: UI for SCM PPDS -&gt; Software component: UIPPDS01 100&#160;&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 02 and Stack 03</p>\r\n<p>SAP ANALYTICAL SERVICES 1.0 -&gt; instance: UI Analytical Services Core* -&gt; Software component:&#160;UISSB001 100&#160;&#160;&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 01</p>\r\n<p>SAP FIORI FOR SAP SFIN 1605 -&gt; instance: UI for FIN -&gt; Software component:&#160;UIAPFI70 300 SP02&#160;&amp; UIAPPL01 100&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 02</p>\r\n<p>FIORI APPROVE REQUESTS X1 2.0 -&gt; instance: Approve Requests UI 2.0 -&gt; Software component: UIX01CA1 200 SP02&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 02</p>\r\n<p>SAP FIORI FOR SAP EWM 1.0 -&gt; instance: UI for EWM -&gt;&#160;Software component: UIEWM001 100 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 00</p>\r\n<p>SAP FIORI APPS FOR SAP ERP 1.0 -&gt; instance: UI for ERP&#160;-&gt;&#160;Software component: UIERP001 100 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 00</p>\r\n<p>SAP FIORI FOR SAP MDG 2.0 -&gt; instance: UI * for MDG -&gt; Software component: UIMDC001 200 SP00 &amp;&#160; UIMDG001 100 SP07&#160;&#160;&#160;&#160;&#160; Stack 00</p>\r\n<p>SAP GEF 1.0 -&gt;&#160;instance: UI Content UI 2.0&#160;-&gt; Software component: UIGEF001 100 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 00</p>\r\n<p>SAP FIORI FOR SAP CARAB 2.0&#160; -&gt; instance:&#160;UI for SAP CARAB 2.0 -&gt; Software component: UICAR001 100 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00</p>\r\n<p>SAP FIORI FOR SAP PC 1.0 -&gt; instance:&#160;UI for PC on UI 2.0 -&gt; Software component: UIGRPC01 100&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 00</p>\r\n<p>&#160;</p>\r\n<p>requires SAP FIORI FRONT-END SERVER 2.0 SPS03&#160;:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" class=\"confluenceTable\">\r\n<tbody>\r\n<tr><th class=\"confluenceTh\">SAP NetWeaver (Minimum SP level UI &amp; gateway components )</th><th class=\"confluenceTh\">Mininum SP Level</th><th class=\"confluenceTh\">&#160;additional needed SAP notes</th></tr>\r\n<tr>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>Software component:&#160; IW_BEP 200 &amp;&#160;UI_700&#160;200 &#160;&amp; UI_701 200 &amp; UI_702&#160;200 &amp; UI_731 200&#160;all SP03</p>\r\n<p>Software component:&#160; GW_CORE 200 SP12 &amp;&#160;IW_FND 250&#160;all SP12</p>\r\n<p><span style=\"text-decoration: underline;\">Netweaver 7.40 </span></p>\r\n<p>User Interface Technology for SAP NetWeaver ( SAP_UI 750 SP03&#160;)</p>\r\n<p>SAP NW GATEWAY FOUNDATION ( SAP_GWFND 7.40 SP14)</p>\r\n<p><span style=\"text-decoration: underline;\">Netweaver 7.50</span></p>\r\n<p>User Interface Technology for SAP NetWeaver ( SAP_UI 750 SP03&#160;)</p>\r\n<p>SAP NW GATEWAY FOUNDATION ( SAP_GWFND 7.50 SP03 )</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.31 SPS13</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.40</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.40 SPS08</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.50</span></span></span></p>\r\n<p>SAP KERNEL 7.45 64-BIT &#160;UNICODE&#160; Patch 201</p>\r\n<p>product version:&#160; SAP NetWeaver 7.50 SPS01</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p>&#160;</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2268281\">2268281</a>&#160;-&#160; SAPUI5 Distribution 1.36 - Central Note</li>\r\n<li><a target=\"_blank\" href=\"/notes/2297434\">2297434</a>&#160;-&#160; UI2 &amp; USHELL upgrade to version 1.36.5 for UI Add-On 2.0</li>\r\n<li><a target=\"_blank\" href=\"/notes/2296156\">2296156</a>&#160;-&#160; SAPUI5 upgrade to version 1.36.6</li>\r\n</ul>\r\n<p><strong>The update process of UI5 libaries&#160;corrections are delivered via files which has to be&#160;downloaded from SMP.</strong></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2118663\">2118663</a>&#160;- Install a patch for SAPUI5 runtime libraries (Information about process)</li>\r\n<li><a target=\"_blank\" href=\"/notes/2103131\">2103131</a>&#160; - SAPUI5 Fixes for update via SMP</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Affected product versions /&#160;support package stacks:</span></strong></p>\r\n<p>SCM PPDS 1.0 ON ERP 6.0 EHP8 -&gt; instance: UI for SCM PPDS -&gt; Software component: UIPPDS01 100&#160;&#160;SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 03 (released forSAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP ANALYTICAL SERVICES 1.0 -&gt; instance: UI Analytical Services Core* -&gt; Software component:&#160;UISSB001 100&#160;&#160;&#160;SP02&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 02 (requires minimum SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP SFIN 1605 -&gt; instance: UI for FIN -&gt; Software component:&#160;UIAPFI70 300 SP04&#160;&amp; UIAPPL01 100&#160;SP03&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 03 (requires minimum SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>FIORI APPROVE REQUESTS X1 2.0 -&gt; instance: Approve Requests UI 2.0 -&gt; Software component: UIX01CA1 200 SP03&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 03 (requires minimum SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP EWM 1.0 -&gt; instance: UI for EWM -&gt;&#160;Software component: UIEWM001 100 SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 01 (requires minimum SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI APPS FOR SAP ERP 1.0 -&gt; instance: UI for ERP&#160;-&gt;&#160;Software component: UIERP001 100 SP01</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; -&gt; instance: UI for Analytical ERP -&gt; Software component: UIAERP01 100&#160;&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 01 (requires minimum&#160;SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP MDG 2.0 -&gt; instance: UI * for MDG -&gt; Software component: UIMDC001 200 SP00 &amp;&#160; UIMDG001 200 SP00&#160;&#160;&#160;&#160;&#160; Stack 01&#160; (released for SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP GEF 1.0 -&gt;&#160;instance: UI Content UI 2.0&#160;-&gt; Software component: UIGEF001 100 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Stack 00&#160; (released for SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP CARAB 2.0&#160; -&gt; instance:&#160;UI for SAP CARAB 2.0 -&gt; Software component: UICAR001 100 SP01&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 01&#160; (requires minimum&#160;SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP CARAB 3.0&#160; -&gt; instance:&#160;UI for SAP CARAB 2.0 -&gt; Software component: UICAR001 200 SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00&#160; (requires minimum&#160;SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP PC 1.0 -&gt; instance:&#160;UI for PC on UI 2.0 -&gt; Software component: UIGRPC01 100&#160;&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &#160;&#160;&#160;&#160;Stack 00 (released for SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP ERP TRV 2.0 -&gt; instance: UI for ERP TRV -&gt; Software component: UITRV001 200&#160;&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00 (requires minimum&#160;SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>SAP FIORI FOR SAP OGSD 1.0 -&gt; instance: UI for OGSD -&gt; Software component: UIOGSD01 100&#160;SP00&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; Stack 00 (requires minimum&#160;SAP FIORI FRONT-END SERVER 2.0 SPS04)</p>\r\n<p>&#160;</p>\r\n<p>requirements for SAP FIORI FRONT-END SERVER 2.0 SPS04&#160;:</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" class=\"confluenceTable\">\r\n<tbody>\r\n<tr><th class=\"confluenceTh\">SAP NetWeaver (Minimum SP level UI &amp; gateway components )</th><th class=\"confluenceTh\">Mininum SP Level</th><th class=\"confluenceTh\">&#160;additional needed SAP notes</th></tr>\r\n<tr>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>Software component:&#160; IW_BEP 200 &amp;&#160;UI_700&#160;200 &#160;&amp; UI_701 200 &amp; UI_702&#160;200 &amp; UI_731 200&#160;all SP04</p>\r\n<p>Software component:&#160; GW_CORE 200 SP12 &amp;&#160;IW_FND 250&#160;all SP12</p>\r\n<p><span style=\"text-decoration: underline;\">Netweaver 7.40</span></p>\r\n<p>User Interface Technology for SAP NetWeaver ( SAP_UI 750 SP04&#160;)</p>\r\n<p>SAP NW GATEWAY FOUNDATION ( SAP_GWFND 7.40 SP15 )</p>\r\n<p><span style=\"text-decoration: underline;\">Netweaver&#160;7.50</span></p>\r\n<p>User Interface Technology for SAP NetWeaver ( SAP_UI 750 SP04&#160;)</p>\r\n<p>SAP NW GATEWAY FOUNDATION ( SAP_GWFND 7.50 SP04 )</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.31</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.31 SPS13</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.40</span></span></span></p>\r\n<p>product version:&#160; SAP NetWeaver 7.40 SPS08</p>\r\n<p><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\"><span style=\"text-decoration: underline;\">Netweaver 7.50</span></span></span></p>\r\n<p>SAP KERNEL 7.45 64-BIT UNICODE&#160; SP015</p>\r\n<p>product version:&#160; SAP NetWeaver 7.50 SPS01</p>\r\n</td>\r\n<td class=\"confluenceTd\">\r\n<p>&#160;</p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2357894\">2357894</a>&#160;-&#160; UI2 &amp; USHELL upgrade to version 1.38.7 for UI Add-On 3.0 (SC: UI_700, SAP_UI 750) higher SAPUI5 patch for 1.38 (to find corresponding note, use search term \"UI2 &amp; SAPUI5 upgrade to version 1.38\" )</li>\r\n<li><a target=\"_blank\" href=\"/notes/2361404%20\">2361404</a>-&#160; SAPUI5 upgrade to version 1.38.9 (minimum) or higher SAPUI5 patch for 1.38 (to find corresponding note, &#160;use search term \"SAPUI5 upgrade to version 1.38\" )</li>\r\n</ul>\r\n<p><strong>The update process of UI5 libaries&#160;corrections are delivered via files which has to be&#160;downloaded from SMP.</strong></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2118663\">2118663</a>&#160;- Install a patch for SAPUI5 runtime libraries (Information about process)</li>\r\n<li><a target=\"_blank\" href=\"/notes/2103131\">2103131</a>&#160; - SAPUI5 Fixes for update via SMP</li>\r\n</ul>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p>&#160;</p>\r\n<p>Additional Information:</p>\r\n<p><a target=\"_blank\" href=\"/notes/2364432\">2364432</a>&#160; - General Information: FIORI UI Infrastructure Components for products on SAP Frontend Server 3.0 (SAP Business Suite )</p>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-FES-BUS (Netweaver Business Client)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D028004)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D031799)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002327935/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002327935/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2377081", "RefComponent": "CA-RT-CAR", "RefTitle": "RIN: Release information note SAP FIORI for SAP CARAB 3.0", "RefUrl": "/notes/2377081 "}, {"RefNumber": "2349002", "RefComponent": "CA-MDG", "RefTitle": "SAP S/4HANA Master Data Governance 1610: Release Information Note", "RefUrl": "/notes/2349002 "}, {"RefNumber": "2387107", "RefComponent": "CA-MDG-UI5", "RefTitle": "SAP FIORI FOR SAP MDG 2.0 - Release Information Note", "RefUrl": "/notes/2387107 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}