{"Request": {"Number": "1234331", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 472, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000007212352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001234331?language=E&token=3520B15A18C2FEAD028309FF3E1ADE14"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001234331", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001234331/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1234331"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "01.10.2009"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-TR-IS-U"}, "SAPComponentKeyText": {"_label": "Component", "value": "use FI-LOC-UT-TR"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Turkey", "value": "XX-CSC-TR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-Spec. Component", "value": "XX-CSC-TR-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "use FI-LOC-UT-TR", "value": "XX-CSC-TR-IS-U", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-TR-IS-U*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1234331 - IS-U localization TR: EG88 and EG90 corrections"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Records cannot be selected with regional selection parameters in the<br />periodic replacement report(EG89) because the county, township, municipity/village and district fields of all - with EG88 created - records were empty so nothing can be appeared in the list. Therefore the periodic replacement list creator program (EG88) is to be corrected according to the turkish regional address structure.<br /><br />The regional selection of the \"Create work orders/notifications for device replacement\" report (EG90) doesnt contain the new turkish address fields however these fields are needed for correct functionality of EG90.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>IS-U Turkey address management<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The new fields was not included in the standard code<br />and the transactions (EG88-EG89-EG90) constitute one business process.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This note contains the necessary objects for the localization of the EG88 and EG90 transactions.<br /><br /><br />Check the related notes, whether any further corrections might be<br />available in relation to this functionality.<br /><br />Please download and install the attached transport files in the following order:<br />1. AZ6K000544<br />2. AZ6K000577<br />3. AZ6K000585<br />4. AZ6K000608<br />5. AZ6K000620<br /><br />This will automatically modify program code of standard programs,<br />function modules, and other types of objects (screens, search helps, etc.) which had to be changed for the localization.<br /><br />These modifications contained by the transport files will be automatically transported within the AOP 11.<br /><br />-----------------------------------------------------------------------<br />There is some required modifications that you should carry out manually:<br />Program: REGWDR01<br />Screen number: 100<br /><br />1. Display screen data and supplement the element list:<br />&#x00A0;&#x00A0; In the element list of this dynpro is to be put the following &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;elements:<br /><br />--text fields:<br />&#x00A0;&#x00A0; Position: Between text elements REG90-CITY and REG90-STREET in the &#x00A0;&#x00A0;&#x00A0;&#x00A0; following order:<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_CONC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: text<br />&#x00A0;&#x00A0; Dictionary field: X<br />&#x00A0;&#x00A0; Dictionary modified: 1<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_TWSC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: text<br />&#x00A0;&#x00A0; Dictionary field: X<br />&#x00A0;&#x00A0; Dictionary modified: 1<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_MUVC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: text<br />&#x00A0;&#x00A0; Dictionary field: X<br />&#x00A0;&#x00A0; Dictionary modified: 1<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_DRID<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: text<br />&#x00A0;&#x00A0; Dictionary field: X<br />&#x00A0;&#x00A0; Dictionary modified: 1<br /><br />--i/o. fields:<br />&#x00A0;&#x00A0; Position: Between i./o. elements REG90-CITY and REG90-STREET in the &#x00A0;&#x00A0;&#x00A0;&#x00A0;following order:<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_CONC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: i/o<br />&#x00A0;&#x00A0; Input: X<br />&#x00A0;&#x00A0; Output: X<br />&#x00A0;&#x00A0; Dictionary field: X<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_TWSC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: i/o<br />&#x00A0;&#x00A0; Input: X<br />&#x00A0;&#x00A0; Output: X<br />&#x00A0;&#x00A0; Dictionary field: X<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_MUVC<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: i/o<br />&#x00A0;&#x00A0; Input: X<br />&#x00A0;&#x00A0; Output: X<br />&#x00A0;&#x00A0; Dictionary field: X<br /><br />&#x00A0;&#x00A0; Element name: REG90-/SAPCE/IUTR_DRID<br />&#x00A0;&#x00A0; Values to be set:<br />&#x00A0;&#x00A0; Type of screen element: i/o<br />&#x00A0;&#x00A0; Input: X<br />&#x00A0;&#x00A0; Output: X<br />&#x00A0;&#x00A0; Dictionary field: X<br /><br /><br />2. Display screens data and supplement the flow logic:<br /><br />&#x00A0;&#x00A0; Containing block:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;PROCESS AFTER INPUT.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; MODULE CANCEL_0100 AT EXIT-COMMAND.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; MODULE SAVE_OKCODE.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; LOOP WITH CONTROL TCONT_ADR.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; CHAIN.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;FIELD: REG90-COUNTRY,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REG90-CITY,<br />  ...<br />&#x00A0;&#x00A0; Block to be inserted:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0; * IS-U TR Localization<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REG90-/SAPCE/IUTR_CONC,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REG90-/SAPCE/IUTR_TWSC,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REG90-/SAPCE/IUTR_MUVC,<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REG90-/SAPCE/IUTR_DRID,<br />-----------------------------------------------------------------------<br /><br />Finally please install the correction instructions of this note 1234331 to carry out the required modifications entirely.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I053163)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001234331/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "AZ6K000620.zip", "FileSize": "414", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000429762008&iv_version=0011&iv_guid=A7071491BB762C42A5E3B956E7200A31"}, {"FileName": "AZ6K000608.zip", "FileSize": "540", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000429762008&iv_version=0011&iv_guid=69B840FD6F766549A3A017A5219CAC33"}, {"FileName": "AZ6K000544.zip", "FileSize": "11", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000429762008&iv_version=0011&iv_guid=EEB0834E075A8C448A26AA174EA87768"}, {"FileName": "AZ6K000577.zip", "FileSize": "7", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000429762008&iv_version=0011&iv_guid=E7DA871FA112A14F98411BDBAECBB489"}, {"FileName": "AZ6K000585.zip", "FileSize": "1799", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000429762008&iv_version=0011&iv_guid=CF086BF907FAE3428FDA449E9D89E4A8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1179111", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U Turkey Address management. EG89 correction. CRT trans.", "RefUrl": "/notes/1179111"}, {"RefNumber": "1177875", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U TR localization EG89 Address fields", "RefUrl": "/notes/1177875"}, {"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1122857", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "Turkish functionality for IS-UT 600: Address modification", "RefUrl": "/notes/1122857 "}, {"RefNumber": "1179111", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U Turkey Address management. EG89 correction. CRT trans.", "RefUrl": "/notes/1179111 "}, {"RefNumber": "1177875", "RefComponent": "XX-CSC-TR-IS-U", "RefTitle": "IS-U TR localization EG89 Address fields", "RefUrl": "/notes/1177875 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "CEEISUT", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-UT", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60015INCEEISUT", "URL": "/supportpackage/SAPK-60015INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60011INCEEISUT", "URL": "/supportpackage/SAPK-60011INCEEISUT"}, {"SoftwareComponentVersion": "CEEISUT 600", "SupportPackage": "SAPK-60012INCEEISUT", "URL": "/supportpackage/SAPK-60012INCEEISUT"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "CEEISUT", "NumberOfCorrin": 2, "URL": "/corrins/0001234331/532"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 2, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}