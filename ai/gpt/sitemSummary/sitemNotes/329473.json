{"Request": {"Number": "329473", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 211, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014889212017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=E5D2F95E387E044D99AF0411EDD82BBE"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "329473"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 22}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Customizing"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "26.01.2022"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-KPR-CS"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Content Server"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Knowledge Provider", "value": "BC-SRV-KPR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-KPR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Content Server", "value": "BC-SRV-KPR-CS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-KPR-CS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "329473 - Description of Content Server and Cache Server configuration file"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note provides a description of the Content Server configuration files: cs.conf or ContentServer.INI, depending on OS and Content Server version. It contains detailed information about OS-specific as well as deprecated parameters and specific Cache Server parameters.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>KPRO, content server, ContentServer.INI, cs.conf, cache server</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Description of the file structure and parameters</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>INDEX:</strong></p>\r\n<p><strong><a target=\"_self\" href=\"#general\">&#65279;1. General information</a><br /></strong><strong><a target=\"_self\" href=\"#location\">2. Storage location</a><br /></strong><strong><strong><a target=\"_self\" href=\"#structure\">&#65279;3. Structure</a><br /><strong><a target=\"_self\" href=\"#info\">4. General information about parameters</a><br /><strong><a target=\"_self\" href=\"#deprecated\">5.&#160;Deprecated configuration parameters as of Content Server 7.5 (Windows and Unix)<br /></a></strong></strong></strong></strong><strong><a target=\"_self\" href=\"#CS753\">&#65279;6.&#160;<strong>Detailed Parameters as of<strong>&#160;Content Server 7.5&#160;</strong>(Windows and Unix)<br /></strong></a></strong><a target=\"_self\" href=\"#CS753_global\">6.1 Detailed Parameters for global section [ContentServer]<br /></a><a target=\"_self\" href=\"#CS753_section\">6.2. Detailed Parameters for section [contRep-&lt;RepositoryName&gt;]<br /></a><a target=\"_self\" href=\"#CS753_section_db\">6.2.1 Database repositories specific parameters<br /></a><a target=\"_self\" href=\"#CS753_section_fs\">&#65279;6.2.2 File system repositories specific parameters<br /></a><strong><strong><strong><strong><strong><a target=\"_self\" href=\"#CS650_NT\">&#65279;7. Detailed Parameters&#160;<strong>up to Content Server 6.5&#160;</strong>(Windows)<br /></a><strong><a target=\"_self\" href=\"#CS650_UX\">&#65279;8. Detailed Parameters&#160;<strong>up to Content Server 6.5&#160;</strong>(Unix)<br /></a><a target=\"_self\" href=\"#cache\">9. Detailed Parameters for Cache Server 7.5 (Windows and Unix)</a><br /></strong></strong></strong></strong></strong></strong><a target=\"_self\" href=\"#cacheFS\">9.1 Mandatory Parameters for File System Cache Server</a><br /><a target=\"_self\" href=\"#cacheDB\">9.2 Mandatory Parameters for Database Cache Server</a><br /><a target=\"_self\" href=\"#cacheOther\">9.3 Other parameters</a></p>\r\n<p><strong><a target=\"_blank\" name=\"general\"></a>&#65279;1. General information:</strong></p>\r\n<p>The Content Server configuration file (cs.conf or ContentServer.INI) contains information of the repositories that can be addressed from the Content Server. The file is usually kept up-to-date automatically by the Content Server and manual editing is not required. Its content is edited/accessed via transaction CSADMIN.<br /><br />The existence of this file is vital for the correct operation of the entire SAP KPro. In particular, you must ensure that this file is also saved in the most recent version (consistent with the database backup) in addition to the regular database backup because the Content Server may no longer be able to access a reconstructed database without a current repository description.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"location\"></a>&#65279;2. Storage location:</span></strong></span></p>\r\n<p><em>Content Server Version 7.5 and higher (Windows and Unix)</em></p>\r\n<p style=\"padding-left: 30px;\">The Content Server configuration file (defult is cs.conf) must be placed in&#160;DIR_PROFILE of the installation (i.e. /usr/sap/&lt;SID&gt;/SYS/profile).&#160;If you want to use another file name name (such as ContentServer.ini), the&#160;<strong>icm/HTTP/contentserver_0</strong>&#160;parameter needs to be adjusted accordingly.</p>\r\n<p><em>Content Server Version lower than 7.5 (Windows)</em></p>\r\n<p style=\"padding-left: 30px;\">The ContentServer.ini file must be in the content server installation directory.</p>\r\n<p><em>Content Server Version lower than 7.5 (Unix)</em></p>\r\n<p style=\"padding-left: 30px;\">The cs.conf file must be in the CSConfigPath configured in httpd.conf of Apache in the section to describe the mod_sapcs.cpp module, as per detailed in SAP Note&#160;<a target=\"_blank\" href=\"/notes/2640961\" title=\"2640961  - SAP Content Server / Cache Server 6.50 support for Apache 2.4\">2640961 - SAP Content Server / Cache Server 6.50 support for Apache 2.4</a></p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"structure\"></a>&#65279;3. Structure:</span></strong></span></p>\r\n<p>For Content Server, the file is organized in two main sections:</p>\r\n<p>[ContentServer]: contains global parameters that apply to all repositories, and;</p>\r\n<p>[contRep-&lt;RepositoryName&gt;]: contains parameters valid for each single repository. Please be mindful that a repository can only be accessed if it has a correspondant section in the Content Server configuration file.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><span style=\"font-size: 14px;\"><a target=\"_blank\" name=\"info\"></a>&#65279;4. General information about parameters:</span></strong></span></p>\r\n<ul>\r\n<li>Parameters are Case Sensitive;</li>\r\n<li>Each line must contain a single parameter/value pair;</li>\r\n<li>Each parameter/value pair must be separated by the assignment character '=' with no white spaces;</li>\r\n<li>If you need a parameter to be ignored, please add a semicolon '; ' at the beginning of the line.</li>\r\n<li>Parameters are valid only within their section.</li>\r\n<li>Clean up identical parameters in the same section to avoid system instability.&#160;</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong><a target=\"_blank\" name=\"deprecated\"></a>&#65279;5.&#160;Deprecated configuration parameters as of Content Server 7.5 (Windows and Unix)</strong></span></p>\r\n<ul>\r\n<li>AdminSecurity, AdminSecurityGroup, AuthService: replaced by authentication handler (see section `AdminSecurity` of SAP Note <a target=\"_blank\" href=\"/notes/2786364\" title=\"2786364  - SAP Content Server and Cache Server 7.5 (and higher)\">2786364 - SAP Content Server and Cache Server 7.5 (and higher)</a>)</li>\r\n<li>DBPath&#160;(SAPDBStorage only): obsolete; database credentials are now stored in the instance's secure area (environment variables SAPSYSTEMNAME, RSEC_SSFS_KEYPATH, and RSEC_SSFS_DATAPATH)</li>\r\n<li>Driver: replaced with ContentStorageDriver</li>\r\n<li>PSEDir: obsolete; repository certificates are now stored in the instance's security directory (profile parameter / environment variable SECUDIR)</li>\r\n<li>Storage: replaced with StorageDriver = FSStorage | SAPDBStorage</li>\r\n<li>TracelFull=1: replaced with TraceLevel=Debug</li>\r\n<li>TraceOverride: removed</li>\r\n<li>SQLTrace (SAPDBStorage only): obsolete</li>\r\n<li>DocNameLength&#160;(SAPDBStorage only): obsolete; the document name length is now fixed to the default value of 254</li>\r\n</ul>\r\n<p><span style=\"text-decoration: underline;\"><strong><a target=\"_blank\" name=\"CS753\"></a>&#65279;6. <strong>Detailed Parameters as of<strong>&#160;Content Server 7.5&#160;</strong>(Windows and Unix):&#160;</strong></strong></span></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"CS753_global\"></a>&#65279;6.1 Detailed Parameters for global section [ContentServer]:</strong></p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: TraceLevel&#160;<br /><em>Type</em>: Text<br /><em>Default</em>: Info<br /><em>Values</em>: Debug, Info, Warning and Error<br /><em>Mandatory</em>: no<br /><em>Description</em>: Sets the trace level accordingly.&#160;The Content Server log output is now written to the trace file dev_cs in DIR_HOME.</p>\r\n<p style=\"padding-left: 30px;\"><em>&#160;</em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"CS753_section\"></a>&#65279;6.2. Detailed Parameters for section [contRep-&lt;RepositoryName&gt;]:</strong></p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: StorageDriver<br /><em>Type</em>: Text<br /><em>Values</em>: SAPDBStorage, FSStorage<br /><em>Mandatory</em>: yes<br /><em>Description</em>: StorageDriver specifies the storage medium the repository is created on. SAPDBStorage is to be used when repositories will be created in a MaxDB installation. FSStorage is to be used when the repositories will be stored in the file system.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: </em>Description<em><br /><em>Type</em>: </em>Character<em><br /><em>Default</em>:</em> \"\"<em><br /><em>Values</em>: </em>Free text<em><br /><em>Mandatory</em>: </em>yes<em><br /><em>Description</em>:</em> Description contains a free text line that explains the intended purpose of the repository.<em><br /><br /><em>Name</em>: </em>Security<em><br /><em>Type</em>: </em>Boolean<em><br /><em>Default</em>:</em> 0<em><br /><em>Values</em>: </em>0, 1<em><br /><em>Mandatory</em>: </em>yes<em><br /><em>Description</em>:</em> Activates or deactivates the signature check.<em><br /><em>Caution</em>: </em>If inconsistencies arise between the content server and R/3, you cannot access documents. In this case, all certificates must be deleted both on the content server and in R/3 Customizing. The transactions involved are CSADMIN and OAC0.<em><br /></em></p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;DispositionHeader<br /><em>Type</em>: Text<br /><em>Values</em>: inline, inline-ext, attachment, attachment-ext, never<br /><em>Mandatory</em>: no<br /><em>Description</em>: The response header \"Content-Disposition\" is used to transmit&#160;the real component name to the client. Some clients may require&#160;the end user to confirm the download of a possibly harmful binary&#160;object if they cannot match the filename extension with the content type. On inline (former value: always): Always add the header with value \"inline; filename=... On inline-ext (former value: auto): Only add&#160;the header with value \"inline; filename=...\" when the component name has a file extension (e.g. \".txt\"). On attachment:&#160;Always add the header with value \"attachment; filename=...\". On attachment-ext: Only add&#160;the header with value \"attachment; filename=...\" when the component name has a file extension (e.g. \".txt\"). On never: Never add the header to the response.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;NoSniffCheck<br /><em>Type</em>: Boolean<br /><em>Mandatory</em>: no<br /><em>Description</em>: If set to true, then IE8 does not sniff into the document&#160;content to identify the mime-type. It ignores the mime-type and opens&#160;the document normally.&#160;If set to false or if this parameter is not set at all, then the usual&#160;behavior of IE8 would be that,it would prevent Internet Explorer from&#160;MIME-sniffing a response away from the declared content-type.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;UncompressedMimes<br /><em>Type</em>: Text<br /><em>Values</em>: all, &lt;empty&gt;<br /><em>Default</em>: empty, meaning compression is active.<br /><em>Mandatory</em>: no<br /><em>Description</em>: Disables compression of objects. Only all or no objects at all&#160;could be compressed.<br /><strong>WARNING</strong>: If set in the global section, all repositories store uncompressed&#160;There is no switch to turn on compression for individual repositories again.&#160;So this parameter should be used on a repository level, unless uncompressed&#160;storage for all repositories is intended.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;relaxedDocIdCheck<br /><em>Type</em>: Boolean<br /><em>Description</em>: If set to true, document identifiers are allowed to consist of&#160;any characters from the alphabet [a-zA-Z0-9.]*.&#160;If set to false or if this parameter is not set at all,&#160;document identifiers are checked to be hexadecimal numbers.<br /><em>Mandatory</em>: no<br /><strong><em>WARNING</em></strong>: If this switch is activated, document identifiers may be created&#160;that are incompatible names to existing SAP applications! Make sure that&#160;your applications and scenarios are still intact when using relaxed docIds.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: </em>DefaultDocProt<em><br /><em>Type</em>: </em>Character<em><br /><em>Default</em>: </em>\"\"<em><br /><em>Values</em>: </em>{r|c|u|d}&#160;&#160;r - Read, c - Create, u - Update, d - Delete<em><br /><em>Mandatory</em>: </em>no<em><br /><em>Description</em>: </em>DefaultDocProt specifies the document access protection for this repository.&#160;Restriction of the access rights to this repository. The letters may be concatenated (e.g. \"c\", \"cr\", \"crud\"). See the KPRO HTTP Protocol Definition for further details.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;VirusScan<br /><em>Type</em>: Text<br /><em>Values</em>: all, &lt;empty&gt;<br /><em>Mandatory</em>: no<br /><em>Description</em>: Enables VirusScan if the parameter is set to all.<br /><br /><em>Name:</em>&#160;ScanDirectory<br /><em>Type</em>: Text<br /><em>Mandatory</em>: no<br /><em>Values</em>: Any valid system path<br /><em>Description</em>: Directory used to save temporary file for scanning.<br /><br /><em>Name:</em>&#160;VSConfPath<br /><em>Type</em>: Text<br /><em>Mandatory</em>: no<br /><em>Values</em>: Path in the local file system where the vs.conf file is&#160;present(E.g:VSConfPath=/tmp)<br /><em>Description</em>: The path where the vs.conf file is present.<br /><br /><em>Name:</em>&#160;DataInfoThreshold&#160;<br /><em>Type</em>: Numerical<br /><em>Mandatory</em>: no<br /><em>Values</em>: 0 - 100<br /><em>Description</em>: The serverInfo command returns the data usage of a disk (for file system repositories) or of a database (for database repositories) only when a default threshold of 70% is exceeded.&#160;&#160;The parameter can be set globally for all repositories, or for specific repositories only.&#160;<br /><strong>WARNING</strong>: If set to 0, the data usage will always be returned. This parameter is only available when using Patch Level equal to&#160;or higher&#160;than that&#160;described in SAP Note&#160;2929214 - SAP Content Server serverInfo command returns data usage as part of the repository status.</p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"CS753_section_db\"></a>&#65279;6.2.1 Database repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em></em><em>Name</em>: ContentStorageHost<br /><em>Type</em>: Character<br /><em>Default</em>: localhost<br /><em>Values</em>: Full qualified hostname, IP-Address, localhost<br /><em>Mandatory</em>:&#160; yes<br /><em>Description</em>: ContentStorageHost specifies the host on which the database in which the repository is created exists. See also SAP Note 301361 for information about improving performance.<br /><br /><em>Name</em>: ContentStorageName<br /><em>Type</em>: Character<br /><em>Default</em>: SDB<br /><em>Values</em>: SDB<br /><em>Mandatory</em>: yes<br /><em>Description</em>: The parameter ContentStorageName contains the name of the database instance.<em><br /></em></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContentStorageDriver&#160;<br /><em>Type</em>: Character<br /><em>Values</em>: ODBC driver name&#160;<br /><em>Mandatory</em>: yes (on Windows Installations)<br /><em>Description</em>: Driver contains the ODBC driver name required for the database access. The appropriate ODBC driver must be registered under the driver name in the NT ODBC service layer. Only the ODBC drivers of the SAP database are released for production operation. Running odbcreg.exe -g in the terminal brings a list of currently registered ODBC drivers. The ContentStorageDriver must be the name registered for the&#160;sdbodbcw.dll file.<br /><strong>WARNING</strong>: This parameter is only valid for Windows installations.</p>\r\n<p style=\"padding-left: 60px;\"><em>Name:</em>&#160;LogInfoThreshold&#160;<br /><em>Type</em>: Numerical<br /><em>Mandatory</em>: no<br /><em>Values</em>: 0 - 100<br /><em>Description</em>: The serverInfo command returns the log usage of the database only when a default threshold of 90% is exceeded.&#160;&#160;The parameter can be set globally for all repositories, or for specific repositories only.&#160;<br /><strong>WARNING</strong>: If set to 0, the log usage will always be returned. This parameter is only available when using Patch Level equal to&#160;or higher&#160;than the one described in SAP Note&#160;&#160;3020323 - Content Server requests timeout due to log area full of MaxDB repositories.</p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"CS753_section_fs\"></a>&#65279;6.2.2 File system repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContRepRoot<br /><em>Type</em>: Text<br /><em>Values</em>: valid system path<br /><em>Mandatory</em>: yes<br /><em>Description</em>: ContRepRoot specifies the root directory for this repository. A full, absolute path name to a directory that exists (rsp. is mounted) must be specified. This directory must have sufficient authorizations to create subdirectories for the user ID, who started the content server.<br /><br /><em>Name:</em>&#160;SnapLockDoc<br /><em>Type</em>: Boolean<br /><em>Mandatory</em>: no<br /><em>Values</em>: 0, 1<br /><em>Description</em>:&#160;If enabled, both the document and the property file are set to readonly on file-system level.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><a target=\"_blank\" name=\"CS650_NT\"></a>&#65279;7. Detailed Parameters&#160;<strong>up to Content Server 6.5&#160;</strong>(Windows):</strong></span></p>\r\n<p style=\"padding-left: 30px;\"><strong>7.1 Global section [ContentServer]&#160;:</strong></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: </em>AdminSecurity<em><br /><em>Type:</em>&#160;</em>Boolean<em><br /><em>Default:</em>&#160;</em>0<em><br /><em>Values:</em></em>&#160;0, 1<em><br /><em>Mandatory</em>: </em>no<em><br /><em><em>Description</em>: </em></em>Enables or disables basic authentication for the restricted use of commands that deal with the administration of repositories, certificates exports and traces.&#160;Behaviour: A \"401 (Authorization required)\" response together with an appropriate realm is passed back to the client.<em><br /></em></p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: LogRequests<br /><em>Type:</em> Boolean<br /><em>Default:</em> 0<br /><em>Values:</em> 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: Logging all client requests in the file cs_trace.txt</p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: ResponseTrace<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: Logging all client responses in the file cs_trace.txt<br /><br /><em>Name</em>: Log404Response<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: Logging all NOT FOUND requests in the file cs_trace.txt<br /><br /><em>Name</em>: FullTrace<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: Logging all request and response pairs in their ENTIRE length in the file cs_trace.txt.<br /><em>Caution</em>: You should activate this switch only for diagnosis purposes and only in connection with controlled server requests. This switch may not be activated in a production system.<br /><br /><em>Name</em>: KeepConnection<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: KeepConnection=0 ends the client connection after the response is transferred. KeepConnection=1 keeps this connection open so that the client can send other requests via the same communication channel. This leads to an improvement in performance because you are not required to establish the connection again.<br /><br /><em>Name</em>: MaxTransferBlockSize<br /><em>Type</em>: Integer<br /><em>Default</em>: 65535<br /><em>Values</em>: 1..4294967296<br /><em>Mandatory</em>: no<br /><em>Description</em>: MaxTransferBlockSize determines the blocks to which the IIS sends responses. On systems that have a high load, requests may receive 0 byte responses because no send buffer can be allocated on the server. We recommend that you reduce the block size (for example, up to 4096 bytes). The size specification is in bytes.&#160;maxTransferBlockSize functions as of server build 161. Also see Note: 328809</p>\r\n<p style=\"padding-left: 30px;\"><strong>7.2. Section&#160;[contRep-&lt;RepositoryName&gt;]&#160;&#160;</strong></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: ContRepDescription<br /><em>Type</em>: Character<br /><em>Default</em>: \"\"<br /><em>Values</em>: Free text<br /><em>Mandatory</em>: yes<br /><em>Description</em>: ContRepDescription contains a free text line that explains the intended purpose of the repository.<br /><br /><em>Name</em>: Security<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: yes<br /><em>Description</em>: Activates or deactivates the signature check.<br /><em>Caution</em>: If inconsistencies arise between the content server and R/3, you cannot access documents. In this case, all certificates must be deleted both on the content server and in R/3 Customizing. The transactions involved are CSADMIN and OAC0.<br /><br /><em>Name</em>: DefaultDocProt<br /><em>Type</em>: Character<br /><em>Default</em>: \"\"<br /><em>Values</em>: {r|c|u|d}&#160;&#160;r - Read, c - Create, u - Update, d - Delete<br /><em>Mandatory</em>: no<br /><em>Description</em>: DefaultDocProt specifies the document access protection for this repository.</em></p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: Storage<br /><em>Type</em>: Character<br /><em></em><em>Values</em>: ContentStorage.dll, FileSystemStorage.dll<br /><em>Mandatory</em>: yes<br /><em>Description</em>: The storage parameter contains the name of the storage layer that is required to access a repository.&#160; Use ContentStorage.dll for MAXDB repositories and&#160;FileSystemStorage.dll for file system based repositories.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name:</em>&#160;uncompressedMimes<br /><em>Type</em>: Text<br /><em>Values</em>: all, &lt;empty&gt;<br /><em>Default</em>: empty, meaning compression is active.<br /><em>Mandatory</em>: no<br /><em>Description</em>: Disables compression of objects. Only all or no objects at all&#160;could be compressed.<br /><strong>WARNING</strong>: If set in the global section, all repositories store uncompressed&#160;There is no switch to turn on compression for individual repositories again.&#160;So this parameter should be used on a repository level, unless uncompressed&#160;storage for all repositories is intended.</p>\r\n<p style=\"padding-left: 30px;\"><strong>7.2.1 Database repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContentStorageHost<br /><em>Type</em>: Character<br /><em>Default</em>: localhost<br /><em>Values</em>: Full qualified hostname, IP-Address, localhost<br /><em>Mandatory</em>:&#160;&#160;yes,&#160;if \"storage\" is set to ContentStorage.dll.<br /><em>Description</em>: ContentStorageHost specifies the host on which the database in which the repository is created exists. See also Note: 301361 for information about improving performance.<br /><br /><em>Name</em>: ContentStorageName<br /><em>Type</em>: Character<br /><em>Default</em>: SDB<br /><em>Values</em>: SDB<br /><em>Mandatory</em>:&#160;yes,&#160;if \"storage\" is set to ContentStorage.dll.<br /><em>Description</em>: The parameter ContentStorageName contains the name of the database instance.<br /><br /><em>Name</em>: sqltrace<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: If sqltrace is set to true, the system writes an SQL trace in the directory c:\\winnt\\system32. This trace is to be activated only for diagnosis purposes and is to be deactivated in a production system.<br /><br /><em>Name</em>: driver<br /><em>Type</em>: Character<br /><em>Default</em>: LiveCache<br /><em>Values</em>: ODBC driver name<br /><em>Mandatory</em>: no<br /><em>Description</em>: Driver contains the ODBC driver name required for the database access. The appropriate ODBC driver must be registered under the driver name in the NT ODBC service layer. Only the ODBC drivers of the SAP database are released for production operation.</p>\r\n<p style=\"padding-left: 30px;\"><strong>7.2.2 File system repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContRepRoot<br /><em>Type</em>: Text<br /><em>Values</em>: each valid system path<br /><em>Mandatory</em>: yes, if \"storage\" is set to FileSystemStorage.dll.<br /><em>Description</em>: ContRepRoot specifies the root directory for this repository. A full, absolute path name to a directory that exists (rsp. is mounted) must be specified. This directory must have sufficient authorizations to create subdirectories for the user ID, who started the content server.</p>\r\n<p><span style=\"text-decoration: underline;\"><strong><a target=\"_blank\" name=\"CS650_UX\"></a>&#65279;8. Detailed Parameters&#160;<strong>up to Content Server 6.5&#160;</strong>(Unix):</strong></span></p>\r\n<p style=\"padding-left: 30px;\"><strong>8.1 Global section [ContentServer]&#160;:</strong></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: </em>AdminSecurity<em><br /><em>Type:</em>&#160;Boolean<br /><em>Default:</em>&#160;0<br /><em>Values:</em>&#160;0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: E</em>nables or disables basic authentication for the restricted use of commands that deal with the administration of repositories, certificates exports and traces.&#160;Behaviour: A \"401 (Authorization required)\" response together with an appropriate realm is passed back to the client.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:&#160;</em></em>AdminSecurityGroup<br /><em>Type</em>: TEXT<br /><em>Mandatory</em>: no <br /><em>Description</em>: Restrict the number of users with administrative rights to members of this group. AdminSecurityGroup is only evaluated if AdminSecurity is enabled. AdminSecurityGroup is a mandatory parameter together with AdminSecurity.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:&#160;</em></em>AuthService<br /><em>Type</em>: TEXT<br /><em>Values</em>: NIS, Local<br /><em>Default</em>: Local<br /><em>Mandatory</em>: No<br /><em>Description</em>: Change password verification from local /etc/passwd to Network&#160;Information Service (aka Yellow Pages). If turned to NIS, only NIS maps&#160;will be used to check user authorizations. This requires that a properly&#160;maintained NIS environment is available. Please read the security notes&#160;of your NIS provider to avoid security leaks.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:&#160;</em></em>TraceLevel<br /><em>Type</em>: TEXT<br /><em>Values</em>: emergency, alert, critical, error, warning, notice, info, debug<br /><em>Mandatory</em>: No<br /><em>Description</em>: Set the trace level from very informative to runtime critical. All traces are written into the web server's trace.&#160;To receive a trace entry the corresponding Web server's trace level must&#160;be at least the same or higher.&#160;<br /><em>WARNING</em>: If you set a very high level, the trace file probably grows&#160;to an extent that consumes all of your free blocks in the file system.<br /><br /><em><em><em>Name</em>:&#160;</em>PSEDir<em><br /><em>Type:</em>&#160;</em>Text<em><br /><em>Default:</em>&#160;-</em><em><br /><em>Mandatory</em>: yes</em><em><br /><em><em>Description</em>:&#160;</em></em>Directory where certificates (*.certs and *.pse files) will be stored for repositories that contain Security=1</em></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:&#160;</em></em>TraceOverride<br /><em>Type</em>: BOOLEAN<br /><em>Mandatory</em>: No<br /><em>Description</em>: If set all trace messages will appear in the log file regardless&#160;of the current server log level.<br /><em>WARNING</em>: THIS SWITCH IS INTENDED FOR SUPPORT REASONS ONLY AND SHOULD&#160;NEVER BE TURNED ON FOR PRODUCTION. The trace file very likely grows&#160;to an extend that consumes all of your free blocks in the file system.</p>\r\n<p style=\"padding-left: 30px;\"><strong>8.2. Section&#160;[contRep-&lt;RepositoryName&gt;]&#160;&#160;</strong></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: StorageDriver<br /><em>Type</em>: Text<br /><em>Values</em>: SAPDBStorage, FSStorage<br /><em>Mandatory</em>: yes<br /><em>Description</em>:&#160;</em>StorageDriver specifies the storage medium the repository is created on. SAPDBStorage is to be used when repositories will be created in a MaxDB installation. FSStorage is to be used when the repositories will be stored in the file system.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: Storage&#160;<br /><em>Type</em>: Character<br /><em></em><em>Values</em>: ContentStorage.dll, FileSystemStorage.dll<br /><em>Mandatory</em>: yes (only in case StorageDriver is not used)&#160;<br /><em>Description:&#160;</em>Backward compatibility parameter to keep MS Windows content server configurations compatible with Unix configurations. The storage parameter contains the name of the storage layer that is required to access a repository.&#160; Use ContentStorage.dll for MAXDB repositories and&#160;FileSystemStorage.dll for file system based repositories.<br /><em>WARNING</em>: THIS PARAMETER IS ACCEPTED TO GUARANTEE BACKWARD COMPATIBILITY BETWEEN MS WINDOWS AND UNIX CONFIGURATIONS. WHEN USING UNIX INSTALLATIONS, THE StorageDriver PARAMETER SHOULD BE PREFERRED.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: ContRepDescription<br /><em>Type</em>: Character<br /><em>Default</em>: \"\"<br /><em>Values</em>: Free text<br /><em>Mandatory</em>: yes<br /><em>Description</em>: ContRepDescription contains a free text line that explains the intended purpose of the repository.<br /><br /><em>Name</em>: Security<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: yes<br /><em>Description</em>: Activates or deactivates the signature check.<br /><em>Caution</em>: If inconsistencies arise between the content server and R/3, you cannot access documents. In this case, all certificates must be deleted both on the content server and in R/3 Customizing. The transactions involved are CSADMIN and OAC0.<br /></em></p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:</em></em>&#160;DispositionHeader<br /><em>Type</em>: Text<br /><em>Values</em>: auto, always, never<br /><em>Mandatory</em>: No<br /><em>Description</em>: The response header \"Content-Disposition\" is used to transmit&#160;the real component name to the client. Some clients may require&#160;the end user to confirm the download of a possibly harmful binary&#160;object if they cannot match the filename extension with the content type. On \"auto\" a \"Content-Disposition\" is only sent, if the component name&#160;contains an dot separated extension. \"auto\" is the default behaviour,&#160;if no DispositionHeader is set. \"always\" turns \"Content-Disposition\"&#160;on for all components, \"never\" turns it off.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:</em></em> NoSniffCheck<br /><em>Type</em>: BOOLEAN<br /><em>Mandatory</em>: No<br /><em>Description</em>: If set to true, then IE8 does not sniff into the document&#160;content to identify the mime-type. It ignores the mime-type and opens&#160;the document normally.&#160;If set to false or if this parameter is not set at all, then the usual&#160;behavior of IE8 would be that,it would prevent Internet Explorer from&#160;MIME-sniffing a response away from the declared content-type.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:</em></em> UncompressedMimes<br /><em>Type</em>: Text<br /><em>Values</em>: all, &lt;empty&gt;<br /><em>Default</em>: empty, meaning compression is active.<br /><em>Mandatory</em>: No<br /><em>Description</em>: Disables compression of objects. Only all or no objects at all&#160;could be compressed.<br />WARNING: If set in the global section, all repositories store uncompressed&#160;There is no switch to turn on compression for individual repositories again.&#160;So this parameter should be used on a repository level, unless uncompressed&#160;storage for all repositories is intended.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:</em></em>&#160;relaxedDocIdCheck<br /><em>Type</em>: BOOLEAN<br /><em>Description</em>: If set to true, document identifiers are allowed to consist of&#160;any characters from the alphabet [a-zA-Z0-9.]*.&#160;If set to false or if this parameter is not set at all,&#160;document identifiers are checked to be hexadecimal numbers.<br /><em>Mandatory</em>: No<br /><em>WARNING</em>: If this switch is activated, document identifiers may be created&#160;that are incompatible names to existing SAP applications! Make sure that&#160;your applications and scenarios are still intact when using relaxed docIds.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name</em>: DefaultDocProt<br /><em>Type</em>: Character<br /><em>Default</em>: \"\"<br /><em>Values</em>: {r|c|u|d}&#160;&#160;r - Read, c - Create, u - Update, d - Delete<br /><em>Mandatory</em>: no<br /><em>Description</em>: DefaultDocProt specifies the document access protection for this repository.&#160;</em>Restriction of the access rights to this repository. The letters may be concatenated (e.g. \"c\", \"cr\", \"crud\"). See the KPRO HTTP Protocol Definition for further details.</p>\r\n<p style=\"padding-left: 30px;\"><em><em>Name:</em></em>&#160;VirusScan<br /><em>Type</em>: TEXT<br /><em>Values</em>: all, &lt;empty&gt;<br /><em>Mandatory</em>: No<br /><em>Description</em>: Enables VirusScan if the parameter is set to all.<br /><br /><em><em>Name:</em></em>&#160;ScanDirectory<br /><em>Type</em>: TEXT<br /><em>Mandatory</em>: No<br /><em>Values</em>: Any valid system path<br /><em>Description</em>: Directory used to save temporary file for scanning.<br /><br /><em><em>Name:</em></em>&#160;VSConfPath<br /><em>Type</em>: TEXT<br /><em>Mandatory</em>: No<br /><em>Values</em>: Path in the local file system where the vs.conf file is&#160;present(E.g:VSConfPath=/tmp)<br /><em>Description</em>: The path where the vs.conf file is present.</p>\r\n<p style=\"padding-left: 60px;\"><strong>8.2.1 Database repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em></em><em>Name</em>: ContentStorageHost<br /><em>Type</em>: Character<br /><em>Default</em>: localhost<br /><em>Values</em>: Full qualified hostname, IP-Address, localhost<br /><em>Mandatory</em>:&#160;&#160;yes,&#160;if \"storage\" is set to ContentStorage.dll.<br /><em>Description</em>: ContentStorageHost specifies the host on which the database in which the repository is created exists. See also Note: 301361 for information about improving performance.<br /><br /><em>Name</em>: ContentStorageName<br /><em>Type</em>: Character<br /><em>Default</em>: SDB<br /><em>Values</em>: SDB<br /><em>Mandatory</em>: yes,&#160;if \"storage\" is set to ContentStorage.dll.<br /><em>Description</em>: The parameter ContentStorageName contains the name of the database instance.<br /><br /><em>Name</em>: SQLTrace<br /><em>Type</em>: Boolean<br /><em>Default</em>: 0<br /><em>Values</em>: 0, 1<br /><em>Mandatory</em>: no<br /><em>Description</em>: Sets or unsets the tracing of all sql traffic to the&#160;database server.&#160;This parameter is required only for support reasons by SAP support&#160;personnel. Setting sqltrace to 1 produces very large trace files.</p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: SQLTracePath<br /><em>Type</em>: Text<br /><em>Default</em>: Current Working Directory<br /><em>Mandatory</em>: no<br /><em>Description</em>: Sets the directory in the file system where the SQL trace files&#160;should be written to. The directory must exist and readable, executable and&#160;writeable by the server.</p>\r\n<p style=\"padding-left: 60px;\"><strong>8.2.2 File system repositories specific parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContRepRoot<br /><em>Type</em>: Text<br /><em>Values</em>: each valid system path<br /><em>Mandatory</em>: yes, if \"storage\" is set to FileSystemStorage.dll.<br /><em>Description</em>: ContRepRoot specifies the root directory for this repository. A full, absolute path name to a directory that exists (rsp. is mounted) must be specified. This directory must have sufficient authorizations to create subdirectories for the user ID, who started the content server.<br /><br /><em>Name:</em>&#160;SnapLockDoc<br /><em>Type</em>: Boolean<br /><em>Mandatory</em>: no<br /><em>Values</em>: 0, 1<br /><em>Description</em>:&#160;If enabled, both the document and the property file are set to readonly on file-system level.</p>\r\n<p><a target=\"_blank\" name=\"cache\"></a>&#65279;<strong>9.&#160;Detailed Parameters for Cache Server 7.5 (Windows and Unix)</strong></p>\r\n<p>In Cache Server 7.5, only the global section needs to be setup. Repository definitions will be automatically created once a Cache request is successfully performed.</p>\r\n<p style=\"padding-left: 30px;\"><em>Name</em>: StorageDriver<br /><em>Type</em>: Text<br /><em>Values</em>: SAPDBStorage, FSStorage<br /><em>Mandatory</em>: yes<br /><em>Description</em>: StorageDriver specifies the storage medium the cache repository is created on. It does not affect the Content Server which the Cache server is connecting to. This will determine where Cache Server will store the cached files. If FSStorage is used, you need to configure the mandatory parameters of File System Cache Server. If SAPDBStorage is used, then you need to configure the mandatory parameters for Database Cache Server.</p>\r\n<p>Find bellow the mandatory parameters for both File System and Database repositories:</p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"cacheFS\"></a>&#65279;9.1. Mandatory Parameters for File System Cache Server&#160;</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContRepRoot<br /><em>Type</em>: Text<br /><em>Values</em>: valid system path<br /><em>Mandatory</em>: yes<br /><em>Description</em>: ContRepRoot specifies the root directory for this repository. A full, absolute path name to a directory that exists (rsp. is mounted) must be specified. This directory must have sufficient authorizations to create subdirectories for the user ID, who started the content server.</p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"cacheDB\"></a>&#65279;9.2 Mandatory Parameters for Database Cache Server</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContentStorageHost<br /><em>Type</em>: Character<br /><em>Default</em>: localhost<br /><em>Values</em>: Full qualified hostname, IP-Address, localhost<br /><em>Mandatory</em>:&#160; yes<br /><em>Description</em>: ContentStorageHost specifies the host on which the database in which the repository is created exists. See also SAP Note 301361 for information about improving performance.<br /><br /><em>Name</em>: ContentStorageName<br /><em>Type</em>: Character<br /><em>Default</em>: SDB<br /><em>Values</em>: SDB<br /><em>Mandatory</em>: yes<br /><em>Description</em>: The parameter ContentStorageName contains the name of the database instance.<em><br /></em></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: ContentStorageDriver&#160;<br /><em>Type</em>: Character<br /><em>Values</em>: ODBC driver name&#160;<br /><em>Mandatory</em>: yes <em>(on Windows Installations)</em><br /><em>Description</em>: Driver contains the ODBC driver name required for the database access. The appropriate ODBC driver must be registered under the driver name in the NT ODBC service layer. Only the ODBC drivers of the SAP database are released for production operation. Running odbcreg.exe -g in the terminal brings a list of currently registered ODBC drivers. The ContentStorageDriver must be the name registered for the&#160;<strong>sdbodbcw.dll</strong> file (be attentive of the &#126;<strong>w</strong>&#126; by the end of the dll file name).<br /><strong>WARNING</strong>: <em>This parameter is only valid for Windows installations.</em></p>\r\n<p style=\"padding-left: 30px;\"><strong><a target=\"_blank\" name=\"cacheOther\"></a>&#65279;9.4 Other parameters:</strong></p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: TraceLevel&#160;<br /><em>Type</em>: Text<br /><em>Default</em>: Info<br /><em>Values</em>: Debug, Info, Warning and Error<br /><em>Mandatory</em>: no<br /><em>Description</em>: Sets the trace level accordingly.&#160;The Cache Server log output is now written to the trace file dev_cs in DIR_HOME.</p>\r\n<p style=\"padding-left: 60px;\"><em>Name</em>: CacheThreshold<br /><em>Type</em>: Numerical<br /><em>Values</em>: 0-100<br /><em>Default setting</em>: 70<br /><em>Mandatory</em>: no<br /><em>Description:</em> CacheThreshold specifies the level to which the storage medium can be filled before it is memory is reorganized. You should never set the threshold to 100 percent, since this could result in continual displacement, and the cache server would not be able to process any more requests.<br /><br /><em>Name:</em>&#160;DataInfoThreshold&#160;<br /><em>Type</em>: Numerical<br /><em>Mandatory</em>: no<br /><em>Values</em>: 0 - 100<br /><em>Description</em>: The serverInfo command returns the data usage of a disk (for file system repositories) or of a database (for database repositories) only when a default threshold of 70% is exceeded.&#160;&#160;<br /><strong>WARNING</strong>: If set to 0, the data usage will always be returned. This parameter is only available when using Patch Level equal to&#160;or higher&#160;than that&#160;described in SAP Note&#160;2929214 - SAP Content Server serverInfo command returns data usage as part of the repository status.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-SRV-KPR (Knowledge Provider)"}, {"Key": "Responsible                                                                                         ", "Value": "I501054"}, {"Key": "Processor                                                                                           ", "Value": "I501054"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239"}, {"RefNumber": "719971", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Content Server release strategy", "RefUrl": "/notes/719971"}, {"RefNumber": "3020323", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Content Server requests timeout due to log area full of MaxDB repositories", "RefUrl": "/notes/3020323"}, {"RefNumber": "2929214", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Content Server serverInfo command returns data usage as part of the repository status", "RefUrl": "/notes/2929214"}, {"RefNumber": "2786364", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Content Server and Cache Server 7.5 (and higher)", "RefUrl": "/notes/2786364"}, {"RefNumber": "1764842", "RefComponent": "BC-DB-SDB", "RefTitle": "Problems with connection to SAP Content Server", "RefUrl": "/notes/1764842"}, {"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3139497", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Error ERR_RESPONSE_HEADERS_MULTIPLE_CONTENT_DISPOSITION occurred when accessing the documents in SAP Content Server 6.50 from Chrome", "RefUrl": "/notes/3139497 "}, {"RefNumber": "2324128", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "How to uninstall SAP Content Server", "RefUrl": "/notes/2324128 "}, {"RefNumber": "3074171", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Report RSCMSPWS cannot change the MaxDB user password due to parameter DBPath is not set for SAP Content Server 6.5", "RefUrl": "/notes/3074171 "}, {"RefNumber": "3134834", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Error \"ContRep <repository name> or Storage entry not defined\" occurred when accessing content repository in SAP Content Server.", "RefUrl": "/notes/3134834 "}, {"RefNumber": "3011452", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "[SAP AG][LIBSDBOD SO][SAP MaxDB] Communication link failure;-10709 Connection failed (RTE:[1] unknown host MaxDBHOSTNAME (see /etc/hosts) ec=-10709, rc=-1", "RefUrl": "/notes/3011452 "}, {"RefNumber": "2921967", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Logon pop-up in transaction CSADMIN - SAP Content Server 750 and higher", "RefUrl": "/notes/2921967 "}, {"RefNumber": "3137196", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Content Server Files containing special characters do not download correctly in browser", "RefUrl": "/notes/3137196 "}, {"RefNumber": "3105852", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Crash when receiving malformed requests", "RefUrl": "/notes/3105852 "}, {"RefNumber": "3096879", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Corrupted files from Cache Server", "RefUrl": "/notes/3096879 "}, {"RefNumber": "3084725", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Cache Server fails to retrieve documents from Secure repositories", "RefUrl": "/notes/3084725 "}, {"RefNumber": "3070937", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Cache Server is slow after upgrade to 7.53", "RefUrl": "/notes/3070937 "}, {"RefNumber": "3077452", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "File with leading space in compId cannot be accessed", "RefUrl": "/notes/3077452 "}, {"RefNumber": "2970846", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Cache Server 7.53 - General Information", "RefUrl": "/notes/2970846 "}, {"RefNumber": "3082937", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "<< Template for SAP Content Server 7.53 correction note - select title from a customer's perspective >>", "RefUrl": "/notes/3082937 "}, {"RefNumber": "3020323", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "Content Server requests timeout due to log area full of MaxDB repositories", "RefUrl": "/notes/3020323 "}, {"RefNumber": "2786364", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Content Server and Cache Server 7.5 (and higher)", "RefUrl": "/notes/2786364 "}, {"RefNumber": "2934074", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Cache Server 7.53 does not work properly", "RefUrl": "/notes/2934074 "}, {"RefNumber": "2929214", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "SAP Content Server serverInfo command returns data usage as part of the repository status", "RefUrl": "/notes/2929214 "}, {"RefNumber": "1619726", "RefComponent": "BC-SRV-KPR-CS", "RefTitle": "FAQ: SAP MaxDB Content Server", "RefUrl": "/notes/1619726 "}, {"RefNumber": "822239", "RefComponent": "BC-DB-SDB", "RefTitle": "FAQ: SAP MaxDB interfaces", "RefUrl": "/notes/822239 "}, {"RefNumber": "1764842", "RefComponent": "BC-DB-SDB", "RefTitle": "Problems with connection to SAP Content Server", "RefUrl": "/notes/1764842 "}, {"RefNumber": "1564491", "RefComponent": "BC-DB-SDB", "RefTitle": "Crash in ODBC driver (Version 7.7)", "RefUrl": "/notes/1564491 "}, {"RefNumber": "1419452", "RefComponent": "PLM-WUI", "RefTitle": "Setting for content server in PLM DMZ scenario", "RefUrl": "/notes/1419452 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}