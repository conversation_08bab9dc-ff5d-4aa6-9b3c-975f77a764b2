{"Request": {"Number": "949330", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 307, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005583222017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000949330?language=E&token=6F188AF7334E3ACFB3FB2C9D865E0349"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000949330", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000949330/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "949330"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "29.09.2006"}, "SAPComponentKey": {"_label": "Component", "value": "BW-BEX-ET-QDEF"}, "SAPComponentKeyText": {"_label": "Component", "value": "Queries (Definition and Modification)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Business Explorer", "value": "BW-BEX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Enduser Technology", "value": "BW-BEX-ET", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Queries (Definition and Modification)", "value": "BW-BEX-ET-QDEF", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-BEX-ET-QDEF*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "949330 - NW04s: Query backup & restore"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Queries or query components created using BEx Query Designer version 3.x and changed using BEx Query Designer 2004s can not be opened in the old versions of BEx Query Designer.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>NetWeaver 2004s, query, reusable query component, Query Designer 2004s, backup, restore.<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>If a Query in NW04s is using ONLY features which are also possible in BW3.x&#x00A0;&#x00A0;it is in general possible that the Query definition can be opened with Query Desinger BW3.x as well.<br /><br />The situation is different when a new option of NW04s is used.<br />BEx Query Designer 2004s offers a set of additional features which is <B>NOT</B> available in the 3.x versions of Query Designer.<br /><br />Using one of those features and following saving of a query or other reusable query component using BEx Query Designer 2004s introduces a new infrastructure of query definition.<br />Query definition version 7.x is <B>NOT</B> compatible with the versions 3.x. Saved query or query component can be ONLY edited using the BEx Query Designer 2004s. An attempt to open such query or query component in Query Designer version 3.x will return an error message <B>'This component was edited with a more recent version of the Editor. You also have to use the more recent version to edit further. Further processing not possible. Update your front end'</B>.<br /><br /><B>ATTENTION:</B></p> <UL><UL><LI>The above message appears for any query edited with Query Designer 2004s and opened with Query Designer 3.x.</LI></UL></UL> <UL><UL><LI>For any query component this message just appears, if the component uses any new feature of Query Designer 2004s (i.e. a variable, which uses the new replacement type \"from Variable\" will cause such a message in Query Designer 3.x).</LI></UL></UL> <UL><UL><LI>Any query component, which is created or edited with BEx Query Designer 2004s and just uses features available in Query Designer 3.x IS STILL editable with Query Designer 3.x and does not return above error message.</LI></UL></UL> <p><br />In order to minimize potential problems in the case of occasional and undesired migration of queries or reusable query components from version 3.x to version 7.x, the automatic backup procedure is now available. Backup initially takes place for any query or query component originally created in 3.x BW system during opening this component for edit in Query Designer.<br />Definition of backed-up queries and query components in 3.x compartible format will be stored in the database tables of query definition in 'B' (backup) object version. The backup version will contain the lastest available definition which can be opened with BEx Query Designer version 3.x.<br /><br /><B>ATTENTION:</B> queries and query components originally created in BW 7.x system using BEx Query Designer 2004s have no backup version.<br /><br />In emergency cases of occasional migration of a 3.x query or query component, the existing backup version can be restored using ABAP report <B>COMPONENT_RESTORE</B>. The report overwrites the existing active version using backup version as a source and allows to continue editing of a query with Query Designer version 3.x.<br /><br /><B>ATTENTION:</B> The restored Query, which is editable with Query Designer 3.x has the state of the last edit with Query Designer 3.x. <B>This means any editing with Query Designer 2004s is lost!</B><br /><B>ATTENTION:</B> If the Query is using complex objects (e.g. Calculated Key Figures using Restricted Key Figures) also these objects will be restored and therefore the definition of all queries using these objects will be influenced as well!<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Implement the attached code correction in order to enable the backup functionality.<br /><br /><B>ATTENTION:</B> The code correction can not be implemented completely using SNOTE transaction. Manual activity is required.<br /></p> <UL><UL><LI>1. Run transaction SE37 for function RSZ_X_COMPONENT_SET_NEW.</LI></UL></UL> <UL><UL><LI>2. In Edit mode add Importing parameter <B>I_QD_VERSION</B>, TYPE = CHAR5, Default Value = '700', Optional = true, Pass Value = true.</LI></UL></UL> <UL><UL><LI>3. Save and activate the function.</LI></UL></UL> <p></p> <UL><LI>SAP NetWeaver 2004s BI</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Import Support Package 09 for SAP NetWeaver 2004s BI (BI-Patch 09 or <B>SAPKW70009</B>) into your BI system. The Support Package will be available when note <B>0914303</B> with the short text \"SAPBINews BI 7.0 SP09\", describing this Support Package in more detail, is released for customers.<br /><br /> <br />This note may already be available before the Support Package is released.&#x00A0;&#x00A0;However, the short text will still contain the words \"preliminary version\" in this case.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-BEX-ET (Enduser Technology)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D038659)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON> (D019748)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000949330/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000949330/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000949330/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303"}, {"RefNumber": "832713", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Migration of Web templates from BW 3.x to Netweaver 2004s", "RefUrl": "/notes/832713"}, {"RefNumber": "1327741", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Precalculation of data may cause deadlocks in RSZELTDIR", "RefUrl": "/notes/1327741"}, {"RefNumber": "1159211", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Backup version of query or query component is not deleted", "RefUrl": "/notes/1159211"}, {"RefNumber": "1157651", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Accessing BW from SEM may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1157651"}, {"RefNumber": "1097674", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "NW2004s: Administration of backup object version", "RefUrl": "/notes/1097674"}, {"RefNumber": "1033134", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incomplete F4 list of InfoProviders", "RefUrl": "/notes/1033134"}, {"RefNumber": "1021991", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "COMPONENT_RESTORE generates new GENUNIID", "RefUrl": "/notes/1021991"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1915309", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Query Migration Tool - !!! FOR INTERNAL USE ONLY !!!", "RefUrl": "/notes/1915309 "}, {"RefNumber": "1327741", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Precalculation of data may cause deadlocks in RSZELTDIR", "RefUrl": "/notes/1327741 "}, {"RefNumber": "1097674", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "NW2004s: Administration of backup object version", "RefUrl": "/notes/1097674 "}, {"RefNumber": "1159211", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Backup version of query or query component is not deleted", "RefUrl": "/notes/1159211 "}, {"RefNumber": "1157651", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Accessing BW from SEM may cause deadlocks in RSZ* tables", "RefUrl": "/notes/1157651 "}, {"RefNumber": "914303", "RefComponent": "BW-WHM-DOC", "RefTitle": "SAPBWNews NW BW 7.00 ABAP SP9", "RefUrl": "/notes/914303 "}, {"RefNumber": "1033134", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Incomplete F4 list of InfoProviders", "RefUrl": "/notes/1033134 "}, {"RefNumber": "1021991", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "COMPONENT_RESTORE generates new GENUNIID", "RefUrl": "/notes/1021991 "}, {"RefNumber": "832713", "RefComponent": "BW-BEX-ET-WEB", "RefTitle": "Migration of Web templates from BW 3.x to Netweaver 2004s", "RefUrl": "/notes/832713 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "710", "To": "710", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "From": "700", "To": "700", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BW_VIRTUAL_COMP 700", "SupportPackage": "SAPK-70009INVCBWTECH", "URL": "/supportpackage/SAPK-70009INVCBWTECH"}, {"SoftwareComponentVersion": "SAP_BW 700", "SupportPackage": "SAPKW70009", "URL": "/supportpackage/SAPKW70009"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BW", "NumberOfCorrin": 1, "URL": "/corrins/0000949330/30"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 1, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BW_VIRTUAL_COMP", "ValidFrom": "700", "ValidTo": "700", "Number": "948374 ", "URL": "/notes/948374 ", "Title": "NW04s: Dumps in Query Designer changing condition/exception", "Component": "BW-BEX-ET-QDEF"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}