{"Request": {"Number": "2755139", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 510, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000000296102019"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002755139?language=E&token=2BEAAE6250842EBBC1320A2FA8575142"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002755139", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002755139/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2755139"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "09.03.2023"}, "SAPComponentKey": {"_label": "Component", "value": "BW-B4H-CNV"}, "SAPComponentKeyText": {"_label": "Component", "value": "Conversion to SAP BW/4HANA"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP BW/4HANA Starter Add-On", "value": "BW-B4H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Conversion to SAP BW/4HANA", "value": "BW-B4H-CNV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-B4H-CNV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2755139 - BW4SL & BWbridgeSL - InfoSources"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You use InfoSources (7.x) and want to migrate to SAP BW/4HANA or SAP Datasphere, SAP BW bridge.</p>\r\n<p>In SAP BW InfoSources are organized and authorized by Application Component. In SAP BW/4HANA and SAP Datasphere, SAP BW bridge (and SAP BW Modeling Tools), InfoSources are organized and authorized by InfoArea.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>TRCS, DTRC</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>There is currently no automated analysis available, if the InfoSources are assigned to an InfoArea or not. In SAP BW 7.5, you can check field INFOAREA of table RSKSNEW manually.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>When converting the system to SAP BW/4HANA or SAP Datasphere, SAP BW bridge, existing InfoSources are by default located in InfoArea \"Unassigned Nodes (NODESNOTASSIGNED)\". If you want to retain security for InfoSources you will have to assign InfoSources to InfoAreas.</p>\r\n<p><strong><span style=\"text-decoration: underline;\">Target SAP BW/4HANA:</span></strong></p>\r\n<p style=\"padding-left: 30px;\">For SAP BW 7.5, we recommend to maintain the assignment using the SAP BW Modeling Tools before the technical conversion of the system to SAP BW/4HANA (either the new authorization S_RS_TRCS or the old authorization S_RS_ISNEW are sufficient). There will be no post conversion adjustments of InfoSources and related authorizations necessary (only S_RS_TRCS will be checked).</p>\r\n<p style=\"padding-left: 30px;\">For SAP BW 7.0 to 7.4 (shell conversion) and 7.3 to 7.4 (remote conversion), the assignment must be performed in the target SAP BW/4HANA system.</p>\r\n<p style=\"padding-left: 30px;\">Tip: In the SAP BW Modeling Tools, you can select several InfoSources and assign them together to a new InfoArea.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Related Information</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2468657\">2468657</a>&#160;- BW4SL &amp; BWbridgeSL - Standard Authorizations</li>\r\n</ul>\r\n<p><strong><span style=\"text-decoration: underline;\">Target SAP Datasphere, SAP BW bridge:</span></strong></p>\r\n<p style=\"padding-left: 30px;\">In SAP&#160;Datasphere none of the standard authorization objects are available anymore, but it is possible to create business roles by combining predefined business catalogs. Some of those business catalogs could be restricted on those two restriction types (RSINFOAREA or RSAREASRC_RSAREATGT) which are related on InfoAreas. Therefore, we recommend to assign InfoSources to InfoAreas either already before the migration (for SAP BW 7.5 using the SAP BW modeling Tools) or after the migration (for SAP BW 7.3 to 7.4) in the target SAP Datasphere, SAP BW bridge system.</p>\r\n<p style=\"padding-left: 30px;\">Tip: In the SAP BW Modeling Tools, you can select several InfoSources and assign them together to a new InfoArea.</p>\r\n<p style=\"padding-left: 30px;\"><span style=\"text-decoration: underline;\">Related Information</span></p>\r\n<ul>\r\n<li><a target=\"_blank\" href=\"/notes/2468657\">2468657</a>&#160;- BW4SL &amp; BWbridgeSL - Standard Authorizations</li>\r\n</ul>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "DS-BWB (BW Bridge)"}, {"Key": "Other Components", "Value": "BW-WHM-DST (Data Staging)"}, {"Key": "Responsible                                                                                         ", "Value": "I822646"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D028847)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002755139/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002755139/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2468657", "RefComponent": "BW-B4H-CNV", "RefTitle": "BW4SL & BWbridgeSL - Standard Authorizations", "RefUrl": "/notes/2468657"}, {"RefNumber": "2421930", "RefComponent": "BW-B4H-CNV", "RefTitle": "Simplification List for SAP BW/4HANA", "RefUrl": "/notes/2421930"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "751", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}