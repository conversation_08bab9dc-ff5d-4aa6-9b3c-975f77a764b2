{"Request": {"Number": "1129215", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1274, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006719112017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001129215?language=E&token=492B533B03E50234DA682298AB19A128"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001129215", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001129215/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1129215"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 2}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.05.2010"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-IT"}, "SAPComponentKeyText": {"_label": "Component", "value": "Italy"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Italy", "value": "RE-FX-LC-IT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-IT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1129215 - IRE Status Enhancements (notice, transfer of ownership)"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>In case of <B>Notices</B> of real estate contracts the system currently creates just one IRE (Registration Tax) record with the fixed amount of 67 euros and 'Status of contract' 'N' (Notice).<br /><br />But according to D.L. 223/2006 turned into law n. 248/2006 both the annual IRE amount and the notice fee shall be levied, therefore two records shall be created.<br /><br />For <B>Transfer of ownership</B> we have two cases:</p> <UL><LI>The seller's side</LI></UL> <UL><LI>The buyer's side</LI></UL> <p><br />On the <B>seller side</B>the system creates actually an IRE record with Status of contract' 'N'.<br />On the <B>buyer side </B>the system creates actually an IRE record with Status of contract' 'B'.<br /><br />But according to Decreto 31-07-1998 - allegato 4&#x00A0;&#x00A0;(Transfer of ownership Issue) the year of the transfer should be considered as an intermediate year inside the IRE period for both the seller and the buyer.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>RE-FX, Italy, Localization, IRE, Imposta di Registro, Registration Tax, Contract status, Notice, Transfer of Ownership</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>In case of <B>Notices</B> of real estate contracts the system creates two separate records of the noticed year:</p> <UL><LI>one IRE record with the 'Status of contract' 'I' (Intermediate year) and the amount calculated with the corresponding calculation type,</LI></UL> <UL><LI>another IRE record with the 'Status of contract' 'N' (Noticed) and the fix amount of 67 euros.</LI></UL> <p><br />For <B>Transfer of ownership</B> a new field 'Transfer of Ownership' is added to the IRE master data and displayed in the 'IRE Data' folder.<br /><br />On the <B>seller side</B> the transfer of ownership is triggered by a notice with a special 'Reason for Notice' and the transfer of ownership is registered in the 'Transfer of Ownership' field of the IRE master data automatically with the value 'Retirement' (R).<br />On the <B>buyer side</B> the transfer of ownership has to be registered manually by the user in the 'Transfer of Ownership' field of IRE master data with the value 'Acquisition' (A).<br /><br />In both cases (seller and buyer side) the system creates an IRE record with the 'Status of contract' 'I'.<br /><br /><B>Customizing</B>:<br /><br />To allow the system to be able to determine if a notice reason is to be considered as Transfer of ownership, a new customizing transaction is created:<br />\"<B>Select Reasons for Notice Relevant for Transfers of Ownership</B>\".<br /><br />The fix amount of 67 euros shall be set in the customizing transaction \"<B>Maintain Time-dependent IRE Parameters</B>\", in the \"<B>IRE Advance Resolution</B>\" field.<br /><br /><br /><B>Implementation</B>:<br /><br />First carry out manually the following steps for the package RE_XC_RA_IT before installing the correction instruction:<br /></p> <OL>1. Start the SE11 transaction to create a <B>domain</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCITTRANSFER<br /><U>Short description</U>:&#x00A0;&#x00A0; Transfer of Ownership<br /><br />On the <B>Definition </B>tab:<br /><U>Data Type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; CHAR<br /><U>No. chars</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1<br /><U>Decimal places</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;0<br /><U>Output length</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 1<br /><br />On the <B>Value Range</B> tab enter the following values:<br />R&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0; Retirement<br />A&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; -&#x00A0;&#x00A0; Acquisition<br /><br />Activate the domain.</p> <OL>2. Start the SE11 transaction to create a <B>data element:</B></OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCITTRANSFER<br /><U>Short description</U>:&#x00A0;&#x00A0; Transfer of Ownership<br /><br />On the <B>Data Type</B> tab:<br />a) Check the <B>Elementary Type</B> radiobutton<br />b) Check the <B>Domain</B> radiobutton and<br />c) Enter the domain name:&#x00A0;&#x00A0; REXCITTRANSFER<br /><br />On the <B>Field label</B> tab:<br />Short&#x00A0;&#x00A0;&#x00A0;&#x00A0;length:&#x00A0;&#x00A0;10&#x00A0;&#x00A0;label:&#x00A0;&#x00A0; 'Transfer O'<br />Medium&#x00A0;&#x00A0; length:&#x00A0;&#x00A0;15&#x00A0;&#x00A0;label:&#x00A0;&#x00A0; 'TransferOwnshp'<br />Long&#x00A0;&#x00A0;&#x00A0;&#x00A0; length:&#x00A0;&#x00A0;21&#x00A0;&#x00A0;label:&#x00A0;&#x00A0; 'Transfer of Ownership'<br />Heading&#x00A0;&#x00A0;length:&#x00A0;&#x00A0;21&#x00A0;&#x00A0;label:&#x00A0;&#x00A0; 'Transfer of Ownership'<br /><br />Activate the data element.<br /><br />In change mode press the 'Documentation' button and enter the following text:<br /><br /><B>&amp;DEFINITION&amp;</B><br />Set the indicator when the real estate object is transferred to a third-party.<br /><br /><B>&amp;USE&amp;</B><br />In case of a real estate object transfer, the possible values are as follows:</p> <UL><LI><B>Retirement </B>- when sold</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In case of a sale, the system sets value <B>Retirement </B> automatically to the vendor data, if the contract was terminated with a Reason for Notice customized for transfer of ownerships.</p> <UL><LI><B>Acquisition </B>- when purchased</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In case of a purchase, you must set value <B>Acquisition </B> when you create the first IRE record of the&#x00A0;&#x00A0;real estate object, to indicate that the ownership was transferred.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You are not required to pay notice fees for contracts terminated with transfer of ownership (no IRE record of type 'N' is created in the system).<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For real estate objects purchased with transfer of ownership notice, the first annual IRE tax to be paid is the same as for intermediary years (IRE record of type 'I').<br /><br />Activate the documentatation.</p> <OL>3. Start the SE11 transaction to create the following <B>table</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TIVXCITIRETRF<br /><U>Short description</U>:&#x00A0;&#x00A0; 'Reasons for Notice Relevant for Transfer of Ownership'<br /><br />On the <B>Delivery and Maintenance</B> tab:<br /><U>Delivery Class</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; C - Customizing table<br /><U>Data Browser/Table View Maint</U>:&#x00A0;&#x00A0;Display/Maintenance Allowed with REstrictions<br /><br />On the <B>Fields </B>tab add the following fields:<br /><U><B>Field&#x00A0;&#x00A0;&#x00A0;&#x00A0;Key&#x00A0;&#x00A0;Initial&#x00A0;&#x00A0;Data element </B></U><br />MANDT&#x00A0;&#x00A0;&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;MANDT<br />NTREASON YES&#x00A0;&#x00A0;YES&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RECNNTREASON<br /><br />Create a <B>Foreign key</B> for NTREASON to the table TIVCNNTRE, check required, key fields/candidates.<br /><br />Set the following technical settings:<br /><U>Data class</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPL2<br /><U>Size category</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0<br /><br />Select the radio button 'Buffering switched on'<br />Check the buffering type check box 'Fully Buffered'<br />and the 'Log data changes' check box.<br /><br />Enhancement category:&#x00A0;&#x00A0; 'Can be enhanced (charecter type or numeric)<br /><br />Activate the table.<br /></p> <OL>4. Start the SE11 transaction to create the following <B>view</B>:</OL> <p><br /><U>View type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Maintenance view<br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Short description</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;'Select Reasons for Notice Relevant for Transfers of Ownershi'<br /><br />On the <B>Table/Join conditions</B> tab:<br />Enter the following tables:<br />TIVXCITIRETRF<br />TIVCNNTRE<br />TIVCNNTRET<br /><br />On the <B>View flds</B> tab add the <B>XNTREASON </B>field of the <B>TIVCNNTRET</B>table by pressing the Table fields button so that you shall have the following fields in the view:<br />MANDT<br />NTREASON<br />XNTREASON<br /><br />Activate<br /><br />While you are in <B>change mode</B>, from the menu start <B>Utilities (M) --&gt; Table maintenance generator</B><br /><br />There enter the following:<br /><U>Table/view</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Authorization Group</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;RE<br /><U>Function Group</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 0REXC_ITIRETRF<br /><U>Package</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RE_XC_RA_IT<br /><br /><U>Maintenance type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; one step<br /><U>Maintenance screen No</U>.:<br /> <U>overview s.</U>:&#x00A0;&#x00A0; 100<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;single screen:&#x00A0;&#x00A0; 0<br />The other settings can left on the default.<br /><br />And <B>generate</B>the maintenance view. This way you have also created the function group 0REXC_ITIRETRF.<br />Double klick on the screen number 100 and make sure that the object is activated there.<br /></p> <OL>5. Start the SE11 transaction and create the following <B>structure</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCC_IT_IRE_CNTRNSFR<br /><U>Short description</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE Reason for Notice Relevant for Transfer of Ownership '<br /><br />On the <B>Components </B>tab:<br />enter the <U>component</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;'.INCLUDE'<br /><U>component type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TIVXCITIRETRF<br /><br /><B>Enhancement category</B>:&#x00A0;&#x00A0; 'Can be enhanced (charecter type or numeric)'<br /><br />Activate it.<br /></p> <OL>6. Start the SE11 transaction and create the following <B>structure</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCC_IT_IRE_CNTRNSFR_X<br /><U>Short description</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE Reason for Notice Relevant for Transfer of Ownership '<br /><br />On the <B>components </B>tab:<br />enter the <U>component</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;'.INCLUDE'<br /><U>component type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REXCC_IT_IRE_CNTRNSFR<br /><br /><B>Enhancement category</B>:&#x00A0;&#x00A0; 'Can be enhanced (charecter type or numeric)'<br /><br />Activate it.<br /></p> <OL>7. Start the SE11 transaction and create the following <B>table type</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RE_T_IT_IRE_CNTRNSFR<br /><U>Short text</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE Reason for Notice Relevant for Transfer of Ownership (TI'<br /><br /><U>Line type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REXCC_IT_IRE_CNTRNSFR<br /><U>Access</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Standard table<br /><U>Key</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Standard key<br /><U>Key category</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Non-unique<br /><br />Activate it.<br /></p> <OL>8. Start the SE11 transaction and create the following <B>table type</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;RE_T_IT_IRE_CNTRNSFR_X<br /><U>Short text</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE Reason for Notice Relevant for Transfer of Ownership and'<br /><br /><U>Line type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REXCC_IT_IRE_CNTRNSFR_X<br /><U>Access</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Standard table<br /><U>Key</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Standard key<br /><U>Key category</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Non-unique<br /><br />Activate it.<br /></p> <OL>9. Start the SE11 transaction and <B>change the following structure</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCITIREALV_L<br /><U>Short text</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE Overview'<br /><br />Add to the structure a new component:<br /><U>component</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TRANSFER<br /><U>component type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCITTRANSFER<br /><br />Activate it.<br /></p> <OL>10. Start the SE11 transaction and <B>change the following structure</B>:</OL> <p><br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;VIXCITIREDATA_TAB<br /><U>Short text</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;'IRE data - data fields'<br /><br />Add to the structure a new component:<br /><U>component</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; TRANSFER<br /><U>component type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXCITTRANSFER<br /><br />Activate it.<br /></p> <OL>11. Start the <B>SIMGH</B>transaction to create a<B> new IMG activity</B></OL> <p><br />(You might need two task for this in your transport request)<br /><br />In the IMG structure field search for 'Tax Calculation for Italy'.<br /><U>Language </U>: English<br /><br />Click on the <B>Change </B>button.<br /><br />In the structure that appers open down the menu 'Registration Tax (IRE)' and set the cursor on the last line int he menu: 'Maintain Register Office Master Data' and press the button 'Insert activity on the same level'. (This is the sixth button from links.)<br /><br />On the screen that appears enter the following information:<br /><U>ID</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'Select Reason for Notice Relevant to Transfer of Ownership'<br /><br />a) On the <B>Document </B>tab:<br /><U>Document class</U>:&#x00A0;&#x00A0; SIMG<br /><U>Document name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;V_TIVXCITIRETRF<br /><br />Press the create button to add the <B>IMG activity documentation</B>:<br /><br /><B>&amp;USE&amp;</B><br />'In this IMG activity, you define reasons for notice relevant to transfer of ownership.'<br /><br /><B>&amp;PRECONDITIONS&amp;</B><br />You have entered all the reasons for notice that are related to Italy in the IMG activity <B>Notice Reasons</B>.<br /><br />Here mark the words Notice Reasons, and select the menu Insert --&gt; Link.<br />On the screen appearing enter the following information:<br /><U>Document class</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0; Implementation Guide chapter (SIMG)<br /><U>Chapter</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SIMG<br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; REFXV_TIVCNNTRE<br /><U>Name in document</U>:&#x00A0;&#x00A0; Notice Reasons<br /><br />Accept it and activate the text.<br /><br />b) On the <B>Attribute </B>tab:<br /><br /><U>ID</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'Select Reason for Notice Relevant to Transfer of Ownership'<br /><br />For <B>Business Attributes</B> make following settings:<br />ASAP Roadmap ID: 207<br />Mandatory activity<br />Critical<br /><br /><B>Country dependency</B>:<br />Select the radio button 'Only valid for specified countries' and enter IT Italy.<br /><br />Assigned <B>application components</B>: PEN0000004 for 'Flexible Real Estate Management --&gt; Localization --&gt; Italy'<br /><br />c) On the <B>Maint. objects</B> tab:<br /><U>ID</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; 'Select Reason for Notice Relevant to Transfer of Ownership'<br /><br /><U>Maintenance object type</U>:<br />Select the radio button for 'Customizing object'<br /><br />and enter for assigned objects the line:<br /><U>Customizing object</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0; V_TIVXCITIRETRF<br /><U>Type</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; V<br /><U>Transaction</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SM30.<br /><br />Save (whenever the transaction requires).<br /></p> <OL>12. <B>Changes in documentations and F1-helps:</B></OL> <p><br />a )Start the SE11 transaction and open in change mode the date element REXCITIRERESOL and press the 'Documentation' button.<br /><br />Make sure that the following text is written:<br /><br /><B>&amp;DEFINITION&amp;</B><br />IRE fixed value that you have to pay for the premature termination (notice) of the contract.<br /><br />Activate the documentation and leave both the documentation and the data element.<br /><br />b) Start the <B>SE61 </B>transaction to change the IRE overview help text<br /><br />Make the following settings on the initial screen:<br /><U>Document Class</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; General Text<br /><U>Language</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; English<br /><U>Document Name</U>:&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;REXC_IRE_HELP<br /><br />Press the Change button. On the screen that appears make sure that you change the text to the following (the paragraph format for the first line is U1 Heading, the three other headings written here inbold letters are U3 heading level 3, the other lines are like here):<br /></p> <b>IRE Registration Tax&#x00A0;&#x00A0;for Italy</b><br /> <p>Here you can view a list of all registration tax records of a real estate contract.<br />This screen appears only if:</p> <UL><LI>the RE-FX country-specifics are activated for the country Italy</LI></UL> <UL><LI>the real estate contract belongs to an Italian company code</LI></UL> <p></p> <b>Register Office data</b><br /> <p>Data for the registration of the IRE tax in the register office.<br />Flexible IRE start date management.<br /></p> <b>Overview functions</b><br /> <UL><LI>View of IRE records for each contract status</LI></UL> <p>Note that the records are created automatically after the creation, noticing, noticing with transfer of ownership, or renewal of the contract.<br /></p> <b>Detailed view functions</b><br /> <UL><LI>Data related to the IRE tax records such as IRE date, status of the contract, transfer of ownership, calculation type, official IRE tax codes, and so on.</LI></UL> <UL><LI>Automatic IRE tax determination from the taxable amount with possibility to add more IRE tax codes and registration office fees.</LI></UL> <UL><LI>Possibility to calculate the payback value of the IRE tax to the contract counterpart.</LI></UL> <UL><LI>Possibility to lock the IRE tax record.</LI></UL> <p><br />Activate the document and leave the transaction.<br /></p> <OL>1. And finally carry out the correction instructions of the note.</OL> <p><br /><br /><br /><br /><br /><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON> (I033780)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (I033770)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001129215/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301"}, {"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103"}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557"}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525"}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460"}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091"}, {"RefNumber": "1453166", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrectly calculated IRE payback amount", "RefUrl": "/notes/1453166"}, {"RefNumber": "1400494", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Fix 50 % IRE payback amount in RECN transaction", "RefUrl": "/notes/1400494"}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920"}, {"RefNumber": "1137431", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enh.(syncronization w. notice/renewal changes)", "RefUrl": "/notes/1137431"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1494103", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: N record is not calculated", "RefUrl": "/notes/1494103 "}, {"RefNumber": "1491557", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:amount for advance resolution is incorrectly calculated", "RefUrl": "/notes/1491557 "}, {"RefNumber": "1473525", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE:N record (notice) is not created-->corr for note 1468460", "RefUrl": "/notes/1473525 "}, {"RefNumber": "1468460", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE: the system does not create the new N (notice) record", "RefUrl": "/notes/1468460 "}, {"RefNumber": "1454091", "RefComponent": "RE-FX-LC-IT", "RefTitle": "'N' type IRE record for posted payback amounts", "RefUrl": "/notes/1454091 "}, {"RefNumber": "1453166", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Incorrectly calculated IRE payback amount", "RefUrl": "/notes/1453166 "}, {"RefNumber": "1400494", "RefComponent": "RE-FX-LC-IT", "RefTitle": "Fix 50 % IRE payback amount in RECN transaction", "RefUrl": "/notes/1400494 "}, {"RefNumber": "1399920", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Payback amount calculation incorrectly before rounding", "RefUrl": "/notes/1399920 "}, {"RefNumber": "1137431", "RefComponent": "RE-FX-LC-IT", "RefTitle": "IRE Status Enh.(syncronization w. notice/renewal changes)", "RefUrl": "/notes/1137431 "}, {"RefNumber": "872301", "RefComponent": "RE-FX-LC-IT", "RefTitle": "RE-FX Country Version for Italy", "RefUrl": "/notes/872301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "603", "To": "603", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 600", "SupportPackage": "SAPKGPAD12", "URL": "/supportpackage/SAPKGPAD12"}, {"SoftwareComponentVersion": "EA-APPL 602", "SupportPackage": "SAPK-60202INEAAPPL", "URL": "/supportpackage/SAPK-60202INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 603", "SupportPackage": "SAPK-60301INEAAPPL", "URL": "/supportpackage/SAPK-60301INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "EA-APPL", "NumberOfCorrin": 3, "URL": "/corrins/0001129215/229"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 5, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "979757 ", "URL": "/notes/979757 ", "Title": "RE-FX Italy: IRE enhancements", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122467 ", "URL": "/notes/1122467 ", "Title": "IRE Overview Data records interrupted by contract renewal", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "600", "ValidTo": "600", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "602", "ValidTo": "602", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1035952 ", "URL": "/notes/1035952 ", "Title": "RE-FX Italy: Contracts with VAT and Annual Payment of IRE", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1118059 ", "URL": "/notes/1118059 ", "Title": "IRE rounding parameter does not work", "Component": "RE-FX-LC-IT"}, {"SoftwareComponent": "EA-APPL", "ValidFrom": "603", "ValidTo": "603", "Number": "1122680 ", "URL": "/notes/1122680 ", "Title": "IRE - Incorrect IRE status by renewal before Save + payback", "Component": "RE-FX-LC-IT"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}