

	SAP S/4HANA migration cockpit – Creating and using... - SAP Community











































SAP Community







Products and Technology







Enterprise Resource Planning







ERP Blogs by SAP







SAP S/4HANA migration cockpit – Creating and using...














    	Enterprise Resource Planning Blogs by SAP
    

    	Get insights and updates about cloud ERP and RISE with SAP, SAP S/4HANA and SAP S/4HANA Cloud, and more enterprise management capabilities with SAP blog posts.
    

















All communityThis categoryBlogKnowledge baseUsersManaged tags




Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions
Enter a user name or rankTurn off suggestions
Enter a search wordTurn off suggestions
Enter a search wordTurn off suggestions



cancel



Turn on suggestions







		Showing results for 



			Search instead for 



		Did you mean: 
























 
 















							SAP S/4HANA migration cockpit – Creating and using synonyms for renaming staging tables
							
						






















sommerudo


			Advisor
		






Options



Subscribe to RSS Feed




Mark as New
Mark as Read




Bookmark
Subscribe




Printer Friendly Page
Report Inappropriate Content









‎04-15-2020
2:17 PM













	
			13
		

	Kudos












			
				
					
					
						In this blog, I will briefly explain the use of synonyms for staging tables and will also provide sample code on how to create synonyms for renaming the staging tables of the SAP S/4HANA migration cockpit. These synonyms can be later used in other ETL Tools such as SAP Data Services.

As mentioned in the blog from markus.andres: “Using SAP Data Services to load data to the staging tables“, you can use SAP Data Services to populate the migration cockpit´s staging tables. An overview of the SAP S/4HANA migration cockpit and further details about the migration approach “Transfer Data Using Staging Tables” are explained in Blog: Part 1: SAP S/4HANA migration cockpit – Migrating data using staging tables and methods for populati....

When moving (i.e. import/exporting) the SAP S/4HANA migration cockpit projects from the development environment to a quality environment, the generated names of the staging tables will be different in the different environments. Consequently, you have to adjust the data flows in your ETL tool and update the names of the staging tables. You can avoid these adjustments if you define synonyms for the staging tables and use the synonyms in the data flows. Synonyms are references or aliases for a table or other database objects, so it’s just another name you can use to refer to the table in SQL statements. For more details about synonyms you can refer to:

SAP HANA Developer Guide - Synonyms
SAP HANA Developer Guide - Create a Synonym
SAP HANA Academy - Web IDE for HANA: Synonyms - Create Synonym [2.0 SPS 00]

 

For the SAP S/4 HANA migration cockpit, synonyms can be used to obtain identifying names from the system, project, object and structure instead of having consecutive numbers in the table name. Synonyms have the format S_<sid>__<object>__<structure>, for example S_ABC__Z_CUSTOMER2_US8__S_CUST_GEN.

The names of the staging tables are stored in mapping table /1LT/DS_MAPPING in the schema of the staging tables. Users can use table /1LT/DS_MAPPING to recreate the synonyms. This means that even though the names of the staging tables will change, the destinations used for data export stored procedures or scripts in the extraction tools (for example SAP Data Services) will not change.

The example stored procedure below shows how to create the synonyms for the staging tables. For example, the name of the staging table for source structure S_CUST_SALES_DATA in the staging schema of the development system might be /1LT/DSDEV10001234 and /1LT/DSQAS20004321 in the staging schema of the quality system. In the case below, the SID of the S/4HANA system is used in the synonym. However, you can remove this SID (i.e. <sid> & ABC, sample name which represents this SID) to make the stored procedure system independent and get static synonym names across the landscape (simply comment and uncomment the respective line in the script).

 

……………………………………………………………………………………………….

Stored procedure for all staging tables of a migration project

Important Notes:

The procedure and synonyms are created in the current schema using SQL console in SAP HANA Studio.
Input of the stored procedure is the system id <sid> and the project id <mtid>. There is no output.
A synonym will be created for each staging table in a migration project.
If a synonym already exists but is assigned to another table name, it will be dropped and created with the current staging table name.
The naming convention used for the synonym is: S_<sid>__<object>__<structure>, e.g. S_ABC__Z_CUSTOMER2_US8__S_CUST_GEN
The scripts and stored procedures are examples that should be adjusted to your requirements.

 

Create procedure

--drop procedure create_staging_synonyms;

CREATE PROCEDURE create_staging_synonyms(

  IN SYS_ID        NVARCHAR(8),

  IN MT_ID         NVARCHAR(3))

LANGUAGE SQLSCRIPT

 SQL SECURITY INVOKER

AS

BEGIN

  DECLARE CNT INTEGER;

  DECLARE SYNONYM_NAME NVARCHAR(60);

  DECLARE STAGING_TAB  NVARCHAR(30);

  DECLARE CURSOR c_cursor ( SYSID NVARCHAR(8), MTID NVARCHAR(3)) FOR

   select *

    from "/1LT/DS_MAPPING"

    where SYS_ID       = :SYSID

    and   MT_ID        = :MTID;

--

  FOR wa AS c_cursor(:SYS_ID, :MT_ID)

  DO

    STAGING_TAB  = :wa.STAGING_TAB;

--  synonym name with SID

    SYNONYM_NAME = 'S_' || :wa.SYS_ID || '__' || :wa.COBJ_IDENT || '__' || :wa.STRUCT_IDENT;

--  synonym name without SID

--    SYNONYM_NAME = 'S_' | :wa.COBJ_IDENT || '__' || :wa.STRUCT_IDENT;

--  drop synonym if already exists with another staging table

    SELECT count(*) into CNT FROM SYNONYMS WHERE SYNONYM_NAME = :SYNONYM_NAME;

    if CNT > 0 then

      SELECT OBJECT_NAME into STAGING_TAB FROM SYNONYMS WHERE SYNONYM_NAME = :SYNONYM_NAME;

      if STAGING_TAB  <> :wa.STAGING_TAB then

        EXEC 'DROP SYNONYM "' || :SYNONYM_NAME || '"';

        STAGING_TAB  = :wa.STAGING_TAB;

        EXEC 'CREATE SYNONYM "' || SYNONYM_NAME || '" for "' || STAGING_TAB || '"';   

      end if;

    else

      EXEC 'CREATE SYNONYM "' || SYNONYM_NAME || '" for "' || STAGING_TAB || '"';   

    end if;

  END FOR;

END;
 

Call procedure for a system and project

In SQL console you can call the procedure as follows e.g. for system QKX and project id US8:

CALL create_staging_synonyms(SYS_ID => 'QKX', MT_ID => 'US8');
 

You can display all synonyms for your system and project like this

Replace <MTID> with the project id and <SID> with the system id:

SELECT * FROM SYNONYMS WHERE SYNONYM_NAME LIKE 'S_<SID>%<MTID>%' order by SYNONYM_NAME;
 

If you follow the steps above you will get synonyms with static names that are independent from the generated staging table names and you can use these names in your ETL tools.
					
				
			
			
			
			
			
			
			
			
		




SAP Managed Tags:
SAP Data Services,
SAP S/4HANA,
SAP S/4HANA migration cockpit,
SAP S/4HANA Public Cloud 












SAP Data Services
SAP Data Services












SAP S/4HANA Public Cloud
SAP S/4HANA Cloud












SAP S/4HANA
SAP S/4HANA












SAP S/4HANA migration cockpit
Software Product Function






View products (4)




							Labels:
						



Technology Updates
































		6 Comments
	
 



 
 






















former_member598107


			Participant
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎01-04-2023
10:49 AM












	
			0
		

	Kudos













sommerudo


This is a very useful blog and we saved millions of minutes for the Build. This is quite an easy BASIS task and can be done for generating the synonyms for the data objects based on the LTMC project. Once the staging tables are loaded using SAP Data Services, LTMC picks it up easily and the data migration process becomes a easy step. Lot of development effort was saved due to this implementation.

Thanks!!

Regards
Arun Sasi


























 
 






















taryckbensaili


			Participant
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎09-28-2023
10:04 AM












	
			0
		

	Kudos












			
				
					
					
						Hi sommerudo

Does it require a HANA Full use license or is it allowed with Runtime HANA (aka HANA DB of S/4HANA) ?!?

 

Thanks in advance for this clarification.
					
				
			
			
			
			
			
			
			
			
		























 
 























sommerudo


			Advisor
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎09-28-2023
12:55 PM












	
			2
		

	Kudos












			
				
					
					
						Hello Taryck,

In the blog I focused on the technical aspects.

In the past years, we had some discussions on license topics. Unfortunately, I forgot to add the results in the blog, but I'll now summarize them here:

The runtime license - SAP HANA, runtime edition system – for on-premises and private cloud does not allow the creation of SQL procedures. Instead of the SQL procedures you could use AMDP - ABAP managed database procedures or EXEC SQL in an ABAP program, i.e. you have to create the procedures from the application layer, or you write a simple Z-program with directly creates the synonyms using exec sql statements. The logic remains is very similar

The access to the staging tables with data services is allowed with runtime license, see note 3065011 https://launchpad.support.sap.com/#/notes/3065011:
"Loading data into SAP HANA directly via Data Services (DS) and/or Landscape Transformation Replication Server (SLT) are permitted with the embedded SAP HANA Runtime."

I hope that clarifies...

Regards,
Udo
					
				
			
			
			
			
			
			
			
			
		























 
 






















former_member201636


			Explorer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎10-19-2023
7:40 PM












	
			0
		

	Kudos












			
				
					
					
						Hello Udo.

Thank you for sharing this. I have a follow-on question to that of taryckbensaili's.

My question is regarding the use of S/4HANA Migration Cockpit (on version 2020 FPS02), if using Staging Table concept with Remote HANA DB for the purpose of using SAP Data Services for populating the staging tables, is and additional HANA DB license required for that use-case?

Thank you!

Donald
					
				
			
			
			
			
			
			
			
			
		























 
 






















former_member201636


			Explorer
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎10-19-2023
8:27 PM












	
			0
		

	Kudos












			
				
					
					
						Hello Udo,

I wish to clarify my question here:

Since the S/4H Migration Cockpit requires a Remote HANA DB for the purpose of staging table concept (and no other DB Type is supported based on KB 2733253):



7
Which databases are supported?


SAP S/4HANA: SAP HANA DB
SAP S/4HANA Cloud, public edition: SAP HANA DB via SAP HANA Cloud instance on SAP BTP

Other databases are not supported.



Is the customer entitled to use another Tenant DB or DB Schema as part of the same S/4HANA Runtime (REAB) system to support this Remote HANA DB?

Thank you,

Donald

 
					
				
			
			
			
			
			
			
			
			
		























 
 























sommerudo


			Advisor
		










Mark as Read
Mark as New




Bookmark




Permalink
Print




Report Inappropriate Content








‎10-24-2023
1:36 PM












	
			0
		

	Kudos












			
				
					
					
						Hello Donald,

There's a FAQ document which provides more details on the different options which you can find like this:

Access the SAP S/4HANA Migration Cockpit page in the SAP Community via link https://community.sap.com/topics/s4hana-migration-cockpit, then in the right of the 3 columns you click on one of the FAQ documents. The preview of the FAQ document is displayed. Now you search for the answer to question "Is a license required to use remote database connection the approach Migrate Data Using Staging Tables?".

I hope the answer in the FAQ answers your question.

Best regards,

Udo
					
				
			
			
			
			
			
			
			
			
		























 



						You must be a registered user to add a comment. If you've already registered, sign in. Otherwise, register and sign in.
					

Comment






 Labels in this area





Artificial Intelligence (AI)
1


Business Trends
363


Business Trends​
15


Digital Transformation with Cloud ERP (DT)
1


Event Information
461


Event Information​
19


Expert Insights
114


Expert Insights​
117


Life at SAP
418


Life at SAP​
1


Product Updates
4,688


Product Updates​
161


Roadmap and Strategy
1


Technology Updates
1,505


Technology Updates​
71





 


 




Related Content






Improve SAP user experiences by using IBM Watson Assistant chatbot with odata services
in Enterprise Resource Planning Blogs by Members  06-01-2023


Highlights for Asset Management in SAP S/4HANA Cloud, private edition and SAP S/4HANA | 2022 Release
in Enterprise Resource Planning Blogs by SAP  12-12-2022


General approach for Selective Data Transition projects in SAP S/4HANA - a Finance perspective.
in Enterprise Resource Planning Blogs by SAP  05-24-2022


Part 1: Migrate your Data – Migration Cockpit (SAP S/4HANA 2020 and higher and SAP S/4HANA Cloud, public edition), Migrate data using staging tables and methods for populating the staging tables with data
in Enterprise Resource Planning Blogs by SAP  03-10-2021







 





 




Popular Blog Posts










Useful documents on SCN






by 

Nancy


• Product and Topic Expert



134441 Views
123 comments
220 kudos


01-06-2015








Evolution of ABAP






by 

karl_kessler


• Product and Topic Expert



26120 Views
42 comments
196 kudos


09-01-2022








Analytics in S/4HANA - real shape of embedded analytics and beyond embedded analytics






by 

Masaaki


• Advisor



98996 Views
32 comments
182 kudos


06-08-2019









 



 Top kudoed authors

 



User

			Count
		












FabianAckermann









			7
		










Gerhard_Welker









			6
		










Adeem









			5
		










Marco_Valencia









			5
		










Chr_Vogler









			4
		










MarceGiovanetti









			4
		










Saumitra









			3
		









brennen_fischer12









			3
		










Ying









			3
		










Axel









			2
		




View all
 








































Auto-suggest helps you quickly narrow down your search results by suggesting possible matches as you type.