This SAP Help Document provides information on available migration objects for SAP S/4HANA. The primary focus of the document is to guide users in understanding the different migration objects they can use when transferring data to their SAP S/4HANA system.

The migration objects are categorized and can be sorted alphabetically by their names. Users can navigate to specific documentation for each migration object by selecting its name. Two tips are provided to assist users:
- Columns in the table can be shown or hidden by selecting the Hide/Show Columns option and checking the appropriate boxes.
- Users can filter the "Migration Approach" column to display only the relevant migration objects, either through Direct Transfer or Staging Table approaches.

Additionally, users have the option to download the data in the table in XLSX or CSV formats, with a choice of delimiters such as semi-colon, comma, pipe line, or caret.

There are several types of migration data objects listed in the document spanning various business components like FI (Financials), MM (Materials Management), SD (Sales and Distribution), etc., and supporting different data types such as master data and transactional data. These objects are tagged with their business object type, migration approach, whether they support custom fields, and the associated SAP component.

The document also contains crucial instructions regarding data protection and legal compliance, noting that personal data should be deleted once its purpose is served and is no longer subject to legal data retention requirements. Users are warned about the potential legal implications of migrating data that should have been deleted.

It is also noted that some migration objects may be listed as "restricted" or "deprecated." Restricted objects cover only some fields and structures of the relevant business processes, while deprecated objects have newer versions available and will be deleted after a few releases.

Users are told that migration objects are meant for the initial migration of data to SAP S/4HANA and cannot be used for changing or updating existing data. The standard migration objects provided can be used both in SAP S/4HANA Cloud and on-premise versions, with the possibility of enhancing standard objects or creating new ones based on the customer's project requirements using the migration object modeler (transaction LTMOM).

For more detailed information or guides on specific topics such as migration object templates, data migration status, and unit of measures, the document recommends visiting specific sections of the SAP Help Portal through provided URLs.

The document concludes with a feedback section where users can leave comments or suggestions for improving the page. Additional legal and social media information is also appended at the end of the document.