"GUID","TITLE","TEXTID","SAPNOTE"
"00109B131AF41ED8ADCF3B4412BD60D4","""Custom Fields and Logic"" replaces <PERSON>E<PERSON> and <PERSON><PERSON> as extensibility tool","SI022: CRM","2692845"
"00109B131AF41EDA8796693C11D560E1","ABAP4TWL - Removal of Transceiver Integration","SI18: AS_ABAP_TRANSCEIVER_INTEGRATION","2879911"
"901B0E6D3EE91ED7AEE070F5412900D7","ABAPTWL - AS Java not available","SI05: AS_ABAP_JAVA_SUPPORT","2560753"
"00109B131AF41EDB85EE7A679EEB80F0","ABAPTWL - Change of authorization checks SAP Business Workflow","SI21: AS_ABAP_WORKFLOW_AUTHORIZATION_CHANGES","2979517"
"00109B131AF41EDB8293A6F4A65BC0EE","ABAPTWL - Change of workflow system user and workflow system jobs","SI20: AS_ABAP_WORKFLOW_USER_JOBS","2568271"
"6CAE8B3EA08B1ED79EECB316834F20C7","ABAPTWL - Cleanup of orphaned objects","SI02: AS_ABAP_CLEANUP","2672757"
"0090FA844D321EDD8DFC5CEA1C2E40E2","ABAPTWL - Deprecation of Hana Rules Framework","SI22: AS_ABAP_HANA_RULES_FRAMEWORK","3243120"
"901B0E6D3F551ED7AEE07E6634E6C0CD","ABAPTWL - Dual Stack not supported","SI06: AS_ABAP_DUAL_STACK","2560791"
"6CAE8B3E7F1B1ED88088B672CE1F80CD","ABAPTWL - End of Support for Pool Tables","SI08: AS_ABAP_STOP_POOL_TABLE_SUPPORT","2577406"
"901B0E6D3F551ED7AEE08F46B06E40CD","ABAPTWL - Instances without ICM not supported","SI07_ AS_ABAP_INSTANCE_WITHOUT_ICM","2560792"
"901B0E6D3EED1ED79CD8DCDA32C8E0CD","ABAPTWL - No support for non simplified system flavour","SI01: AS_ABAP_NON_SIMPLE","2656503"
"901B0E6D3F651ED7AEE023BD4EFE80CF","ABAPTWL - SSCR license key procedure is no longer supported","SI03: AS_ABAP_SSCR","2309060"
"6EAE8B27FB391ED7AEE0690FBB5460D1","ABAPTWL - VM Container not supported","SI04: AS_ABAP_VMCONTAINER","2560708"
"00109B1318B61EDAA0FC9A70225CE102","ABAPTWL - Visualization of SAP Business Workflow Metadata","SI09: AS_ABAP_WORKFLOW_VISUALIZATION","2896183"
"00109B131AF41ED9A1D81CBAD50C00DB","AMI integration not supported","SI069: CRM","2840187"
"00109B131AF41ED8ADCC4A60E7CEC0D4","Access Control Engine (ACE) not supported in S4CRM","SI008: CRM","2692751"
"00109B1315FA1ED8ADE47F8B5ED720D3","Account Identification","SI033: CRM","2693504"
"00109B131AF41ED9A1D7F1A6C64340DB","Account Identification changed","SI065: CRM","2840198"
"00109B1318B61ED8ADE86421BE82C0F1","Archived transactions not integrated in Web Client UI applications","SI029: CRM","2693672"
"00109B1315FA1ED8ADE7F9F9722A60D3","Availability check using SAP APO not supported","SI016: CRM","2693662"
"00109B1315FA1ED8AD82A25A0B57C0D3","BAPIs and SOA services not supported","SI001: CRM","2691675"
"6CAE8B3E8C3B1ED792D9EB9E8D08E0C6","BW4SL - Add-ons","BW001: Add-ons","2189708"
"6CAE8B3E8C3B1ED792D9EB9E8D09A0C6","BW4SL - Administration Cockpit and Technical Content","BW801: Technical Content","2467074"
"6CAE8B3E8C3B1ED792D9EB9E8D0820C6","BW4SL - Aggregates","BW107: Aggregates","2470050"
"6CAE8B3E8C3B1ED792D9EB9E8D06E0C6","BW4SL - Aggregation Levels","BW501: Aggregation Levels","2443001"
"6CAE8B3E8C3B1ED792D9EB9E8D02E0C6","BW4SL - Analysis Process Designer","BW900: Analysis Process Designer","2444220"
"6CAE8B3E8C3B1ED792D9EB9E8D0360C6","BW4SL - Analytical Data Source","BW902: Analytical Data Source","2444220"
"6CAE8B3E8C3B1ED792D9EB9E8CFF60C6","BW4SL - Analytical Indexes","BW111: Analytic Indexes","2442062"
"6CAE8B3E8C3B1ED792D9EB9E8D0320C6","BW4SL - Analytical Model","BW901: Analytical Model","2444220"
"6CAE8B3E8C3B1ED792D9EB9E8D0DE0C6","BW4SL - BEx Broadcast Settings","BW404: BEx Broadcast Settings","2444138"
"901B0E6D3F551ED7AFE84DB43D9EA0CD","BW4SL - BEx Precalculation Server","BW408: BEx Precalculation Server","2444138"
"6CAE8B3E8C3B1ED792D9EB9E8D0460C6","BW4SL - BEx Reporting Agent","BW403: Reporting Agent","2444136"
"6CAE8B3E8C3B1ED792D9EB9E8D0F20C6","BW4SL - BEx Themes","BW407: BEx Themes","2444136"
"6CAE8B3E8C3B1ED792D9EB9E8D0E20C6","BW4SL - BEx Web Design Time Item Metadata","BW405: BEx Web Design Time (Items)","2444136"
"6CAE8B3E8C3B1ED792D9EB9E8D0E60C6","BW4SL - BEx Web Design Time Parameter Metadata","BW406: BEx Web Design Time (Parameters)","2444136"
"6CAE8B3E8C3B1ED792D9EB9E8D0420C6","BW4SL - BEx Web Templates (3.x)","BW401: BEx Web Templates (3.x)","2444136"
"6CAE8B3E8C3B1ED792D9EB9E8D04A0C6","BW4SL - BEx Web Templates (7.x)","BW402: BEx Web Templates (7.x)","2444138"
"6CAE8B3E8C3B1ED792D9EB9E8D07A0C6","BW4SL - BEx Workbooks","BW400: BEx Workbooks","2444138"
"6CAE8B3E8C3B1ED792D9EB9E8D1060C6","BW4SL - BI Add-on for SAP GUI","BW003: BI Add-on for SAP GUI","2481504"
"6CAE8B3E8C3B1ED792D9EB9E8D0920C6","BW4SL - BI Content","BW800: BI Content","2400585"
"901B0E6D3F351ED7AFE937C32E2AE0D3","BW4SL - BI Content Analyzer","BW803: BI Content Analyzer","2526601"
"6CAE8B3E8C3B1ED792D9EB9E8D07E0C6","BW4SL - BW Queries","BW200: BW Queries","2479683"
"6EAE8B2630691ED7AFE6FE18D740A0D0","BW4SL - BW Source Systems","BW157: BW Source Systems","2473145"
"6CAE8B3E8C3B1ED792D9EB9E8D03E0C6","BW4SL - Business Planning & Simulation","BW500: Business Planning & Simulation","2468702"
"901B0E6D3F551ED7AFE946D3181C60CD","BW4SL - Change Documents for Analysis Authorizations","BW804: Change Documents for Authorizations","2467074"
"6EAE8B2630691ED7AFE6A3833BBF20D0","BW4SL - Communication Structures","BW141: Communication Structures","2470352"
"6CAE8B3E8C3B1ED792D9EB9E8CFF20C6","BW4SL - CompositeProviders","BW110: CompositeProviders","2442062"
"901B0E6D3F411ED7AFE925D67FA4A0CC","BW4SL - Conversion of DataSource Field into InfoObject","BW802: Conversion of Field to InfoObject","2487597"
"6CAE8B3E8C3B1ED792D9EB9E8D0EA0C6","BW4SL - Crystal Reports","BW410: Crystal Reports","2478346"
"6CAE8B3E8C3B1ED792D9EB9E8D05E0C6","BW4SL - Currency Translations","BW202: Currency Translations","2443281"
"6CAE8B3E8C3B1ED792D9EB9E8D08A0C6","BW4SL - Customer Relationship Management (CRM) BAPI","BW601: CRM BAPI","2463800"
"6CAE8B3E8C3B1ED792D9EB9E8D0160C6","BW4SL - DB Connect Source Systems","BW152: DB Connect Source Systems","2447932"
"6CAE8B3E8C3B1ED792D9EB9E8D0A60C6","BW4SL - Data Archiving (3.x)","BW181: Data Archiving (3.x)","2469514"
"00109B14808E1ED8A3EF9CA3E900C0D0","BW4SL - Data Archiving Processes","BW182: Data Archiving Processes","2469514"
"6CAE8B3E8C3B1ED792D9EB9E8D02A0C6","BW4SL - Data Federator Facade","BW210: Data Federator Façade","2444890"
"6CAE8B3E8C3B1ED792D9EB9E8D03A0C6","BW4SL - Data Mining Model","BW903: Data Mining Model","2444220"
"6CAE8B3E8C3B1ED792D9EB9E8D09E0C6","BW4SL - Data Transfer Processes","BW130: Data Transfer Processes","2464541"
"6CAE8B3E8C3B1ED792D9EB9E8D0CA0C6","BW4SL - DataSources (3.x)","BW136: DataSources (3.x)","2470352"
"6EAE8B2630691ED7AFCEDCD235A280D0","BW4SL - DataStore Objects (advanced)","BW109: DataStore Objects (advanced)","2487023"
"6CAE8B3E8C3B1ED792D9EB9E8CFFE0C6","BW4SL - DataStore Objects (classic)","BW101: DataStore Objects","2451013"
"6CAE8B3E8C3B1ED792D9EB9E8D0FA0C6","BW4SL - Easy Query","BW420: Easy Query","2470206"
"6CAE8B3E8C3B1ED792D9EB9E8D0F60C6","BW4SL - Enterprise Reports","BW412: Enterprise Reports","2478346"
"6CAE8B3E8C3B1ED792D9EB9E8D00A0C6","BW4SL - External SAP HANA Attribute and Analytic Views","BW112: SAP HANA Views","2442775"
"6CAE8B3E8C3B1ED792D9EB9E8D0D20C6","BW4SL - External Source Systems (Partner ETL)","BW156: External Source Systems","2441836"
"6CAE8B3E8C3B1ED792D9EB9E8D0BA0C6","BW4SL - Generic and Export DataSources","BW137: DataSources (Generic/Export)","2470315"
"6CAE8B3E8C3B1ED792D9EB9E8D1020C6","BW4SL - Hierarchy DataSources","BW139: DataSources (Hierarchy)","2480284"
"901B0E6D3F351ED7AFE7756486DA40D3","BW4SL - Hierarchy/Attribute Change Run","BW195: Change Run","2526218"
"6CAE8B3E8C3B1ED792D9EB9E8D01E0C6","BW4SL - HybridProviders","BW106: HybridProviders","2442730"
"6CAE8B3E8C3B1ED792D9EB9E8D0020C6","BW4SL - InfoCubes","BW102: InfoCubes","2443489"
"6CAE8B3E8C3B1ED792D9EB9E8D0060C6","BW4SL - InfoObject Catalogs","BW120: InfoObject Catalogs","2442621"
"6CAE8B3E8C3B1ED792D9EB9E8D01A0C6","BW4SL - InfoObjects","BW100: InfoObjects","2442621"
"901B0E6D3B6D1ED7AFE631C757D080CD","BW4SL - InfoPackage Groups","BW132: InfoPackage Groups","2487598"
"6CAE8B3E8C3B1ED792D9EB9E8D0AA0C6","BW4SL - InfoPackages and Persistent Staging Areas","BW131: InfoPackages (PSA)","2464367"
"6CAE8B3E8C3B1ED792D9EB9E8D0260C6","BW4SL - InfoSets","BW103: InfoSets","2444912"
"00109B1315FA1ED98CB125934FC360D8","BW4SL - InfoSources","BW142: InfoSources","2755139"
"6CAE8B3E8C3B1ED792D9EB9E8D0C60C6","BW4SL - InfoSources (3.x)","BW138: InfoSources (3.x)","2470352"
"6CAE8B3E8C3B1ED792D9EB9E8CFFA0C6","BW4SL - InfoSpokes","BW161: InfoSpokes","2437637"
"6CAE8B3E8C3B1ED792D9EB9E8D0960C6","BW4SL - Interfaces and Customer-Specific ABAP Development","BW600: Interface & Custom Developments","2462639"
"6CAE8B3E8C3B1ED792D9EB9E8D0660C6","BW4SL - Key Date Derivations","BW204: Key Date Derivations","2443281"
"6CAE8B3E8C3B1ED792D9EB9E8CFEE0C6","BW4SL - Local Provider and Local CompositeProvider in BW Workspaces","BW300: Workspaces","2444273"
"6CAE8B3E8C3B1ED792D9EB9E8D0860C6","BW4SL - MultiProviders","BW104: MultiProviders","2444718"
"6CAE8B3E8C3B1ED792D9EB9E8D0D60C6","BW4SL - Myself Source System","BW150: Myself Source System","2479674"
"6CAE8B3E8C3B1ED792D9EB9E8D0720C6","BW4SL - Near-line Storage (NLS) Partner Solutions","BW180: Near-line Storage","2441922"
"6CAE8B3E8C3B1ED792D9EB9E8D0AE0C6","BW4SL - Open Hub Destinations","BW160: Open Hub Destinations","2469516"
"6CAE8B3E8C3B1ED792D9EB9E8D0B20C6","BW4SL - Other Unavailable Objects","BW950: Other Objects","2470123"
"6EAE8B2630691ED7AFE7E6B5EB8360D0","BW4SL - Personalization","BW205: Personalization","2487535"
"6CAE8B3E8C3B1ED792D9EB9E8D06A0C6","BW4SL - Planning","BW502: Business Planning & Consolidation","2443189"
"6CAE8B3E8C3B1ED792D9EB9E8D0560C6","BW4SL - Process Chains and Process Variants","BW145: Process Chains & Variants","2448086"
"6CAE8B3E8C3B1ED792D9EB9E8D05A0C6","BW4SL - Real-Time Data Acquisition (RDA)","BW170: Real-Time Data Acquisition (RDA)","2447916"
"00109B1318B61ED98586DCA26E1A20F4","BW4SL - Report-Report-Interface","BW211: Report-Report-Interface","2739095"
"6CAE8B3E8C3B1ED792D9EB9E8D0FE0C6","BW4SL - Reporting Authorizations","BW701: Reporting Authorizations","2478384"
"6CAE8B3E8C3B1ED792D9EB9E8D10A0C6","BW4SL - Request and Status Management","BW190: Request and Status Management","2482164"
"6CAE8B3E8C3B1ED792D9EB9E8D0120C6","BW4SL - SAP BW Accelerator","BW002: SAP BW Accelerator","2442939"
"6CAE8B3E8C3B1ED792D9EB9E8D04E0C6","BW4SL - SAP Data Services Source Systems","BW154: SAP Data Services Source Systems","2441836"
"6CAE8B3E8C3B1ED792D9EB9E8D0DA0C6","BW4SL - SAP Source Systems","BW151: SAP Source Systems","2473145"
"901B0E6D3F651ED7AFCEAD75AE6E60CF","BW4SL - Semantic Partitioning Objects","BW108: Semantic Partitioned Objects","2472609"
"6CAE8B3E8C3B1ED792D9EB9E8D0A20C6","BW4SL - Standard Authorizations","BW700: Standard Authorizations","2468657"
"901B0E6D3EE91ED7AFE8CDC8493C20D7","BW4SL - Strategic Enterprise Management (SEM) APIs","BW602: SEM API","2526508"
"6CAE8B3E8C3B1ED792D9EB9E8D0C20C6","BW4SL - Transfer Rules","BW134: Transfer Rules","2470352"
"6CAE8B3E8C3B1ED792D9EB9E8D0CE0C6","BW4SL - Transfer Structures","BW135: Transfer Structures","2470352"
"6CAE8B3E8C3B1ED792D9EB9E8D0B60C6","BW4SL - Transformations","BW140: Transformations (Lookup)","2479567"
"6CAE8B3E8C3B1ED792D9EB9E8D0760C6","BW4SL - UD Connect Source Systems","BW153: UD Connect Source Systems","2441884"
"6CAE8B3E8C3B1ED792D9EB9E8D0620C6","BW4SL - Unit of Measure Conversions","BW203: Unit of Measure Conversions","2443281"
"6CAE8B3E8C3B1ED792D9EB9E8D0BE0C6","BW4SL - Update Rules","BW133: Update Rules","2470352"
"6CAE8B3E8C3B1ED792D9EB9E8D00E0C6","BW4SL - VirtualProvider and Query Snapshots","BW201: Snapshots","2442781"
"6CAE8B3E8C3B1ED792D9EB9E8D0220C6","BW4SL - VirtualProviders","BW105: VirtualProviders","2444913"
"6CAE8B3E8C3B1ED792D9EB9E8D0520C6","BW4SL - Web Service Source Systems","BW155: Web Service Source Systems","2441826"
"6CAE8B3E8C3B1ED792D9EB9E8D0EE0C6","BW4SL - Xcelsius Dashboards","BW411: Xcelsius Dashboards","2478346"
"00109B131AF41ED8ADE545FAFE9660D4","Billing performed in SD; CRM Billing not supported","SI003: CRM","2693431"
"00109B1315FA1ED9A1D7FDE0D879C0DA","Business Agreement not supported, use contract account (FI-CA)","SI066: CRM","2841366"
"00109B13199E1ED8ADCC2F3CEB55E0E1","CRM Middleware no longer used for internal or external communication","SI002: CRM","2692759"
"00109B13199E1ED8ADA116A2715280E1","Case Management restricted to Public Sector scenarios","SI039: CRM","2691939"
"00109B1315FA1ED8ADD57B8F04F160D3","Changes in organizational management","SI028: CRM","2692846"
"00109B1315FA1ED8ADE3FC1B0A2E00D3","Changes in payment card handling","SI014: CRM","2693417"
"00109B13199E1ED8ADE945D5897280E1","Changes in the advanced search applications","SI042: CRM","2693645"
"00109B13199E1ED9A1D7D9B15C0800E9","Configure Product using AVC, PF&C not supported","SI063: CRM","2840216"
"00109B131AF41ED9A1D7CC76E30C80DB","Contract Management changed","SI062: CRM","2841422"
"00109B1315FA1ED9A1D82B00BE8DC0DA","Define product using material master, PMU not supported","SI071: CRM","2841413"
"00109B1318B61ED8ADCC83182E32A0F1","Deprecated functions in business partner maintenance","SI012: CRM","2692660"
"00109B13199E1ED9A1D8331B7CBD40E9","Energy Demand Side Management not supported","SI072: CRM","2840314"
"00109B1318B61ED8AD86F5C4849640F1","External reference numbers and input channel not supported","SI015: CRM","2691703"
"00109B13199E1ED8ADE81EC9E546A0E1","Extraction to Business Warehouse (BW) system not supported","SI023: CRM","2693615"
"00109B131AF41ED8ADE4DDCC2D77C0D4","Groupware integration and Desktop Connection not supported","SI038: CRM","2693480"
"00109B1315FA1ED8ADE51C42128480D3","Harmonization of customizing tables","SI043: CRM","2693481"
"00109B13199E1ED8ADE6B19092F960E1","Inbox Search","SI031: CRM","2693249"
"********************************","Individual objects not supported; equipment used as reference object","SI024: CRM","2693595"
"00109B131AF41ED8ADE49C33AB8040D4","Interaction Record simplifications","SI034: CRM","2693478"
"00109B1318B61ED8ADE849E66F4EC0F1","Interactive Reporting and SAP HANA Live are not supported","SI025: CRM","2693656"
"00109B13199E1ED9A1D839B78FFA40E9","Japanese localization not supported","SI073: CRM","2840276"
"00109B1315FA1ED9A1D823535F7620DA","Key account manager role not supported","SI070: CRM","2841394"
"00109B13199E1ED8ABE9A2681F8BE0E0","Listing and Exclusion, Product Determination, Free Goods not supported","SI011: CRM","2691606"
"00109B1315FA1ED8ADCC63A12B1E40D3","Loyalty Management not supported in S4CRM","SI010: CRM","2692789"
"00109B1315FA1ED8ADD4EC58EB9A60D3","Marketing functionality not supported in S/4HANA for customer management","SI027: CRM","2692798"
"00109B1315FA1ED8ADCCE18B3B7580D3","Mobile laptop scenarios not supported","SI018: CRM","2692796"
"00109B1318B61ED8ADD5A25574B3C0F1","Not supported functions in Interaction Center","SI030: CRM","2692821"
"00109B1318B61ED8ADCCA18C06C940F1","Partner function handling in business partner","SI013: CRM","2692810"
"00109B1318B61ED8AD8750A4A8C200F1","Partner product ranges (PPR) not supported","SI019: CRM","2691608"
"00109B13199E1ED8AD8372B7A3BAE0E1","Persistency of business transactions optimized for HANA","SI007: CRM","2691605"
"00109B13199E1ED8ADE573BCDDB5A0E1","Pricing performed in SD; IPC Pricing not supported","SI004: CRM","2693509"
"00109B131AF41ED8ADE5BC96B35BC0D4","Product configuration performed in AVC; IPC not supported as config. engine","SI005: CRM","2693516"
"00109B1315FA1ED9A1D7EB1D39EF60DA","Product packages not supported","SI064: CRM","2843479"
"00109B1315FA1ED9A1D7AE3285C9E0DA","Quotation Management not supported","SI061: CRM","2841393"
"00109B13199E1ED8ADE429402F5C80E1","Redesign of Interaction History","SI032: CRM","2693419"
"00109B13199E1ED8AD87BFD3A60840E1","Restrictions in Activity Management","SI020: CRM","2691609"
"00109B1315FA1ED8ADCEF5B7187000D3","Restrictions in Opportunity Management","SI021: CRM","2692826"
"00109B1318B61ED8ADE684E67A3160F1","Restrictions in Survey Suite","SI035: CRM","2693529"
"00109B13199E1ED8AD830D65B92620E1","S4CRM exclusively uses S/4 material master; SAP product master not supported","SI006: CRM","2691651"
"*********5E21ED5B3E176783917009E","S4TWL - ABAP-List-Based PI-Sheets","SI18: Logistics_PP","2268116"
"00109B1318BE1EDB98A874A17ABF8113","S4TWL - ACM Temporary Lots for Draft Contracts","SI06: ACM_Tmp_Lots_Draft_Contracts","3086524"
"00109B1315FA1EDB95E098336E1D20F5","S4TWL - ACM: Simplification of Application Document Batch Split Process","SI05: ACM Application_Batch_Split Process_Changed","3017200"
"00109B1318B61ED9BDC78C67B5AF00FC","S4TWL - ACM: Simplification of CPED_TERMINPUT table","SI04: ACM_CPED_TERMINPUT_changed","2852476"
"6EAE8B289E691ED78DC84C2CE74FC0C3","S4TWL - ACM: Simplification of Cross Topics","SI03: ACM_function_availability_in_future_roadmap","2468833"
"*********5E21ED5B3E176783922009E","S4TWL - ACTIVITY-BASED COSTING","SI3_FIN_CO","2270408"
"*********5E21ED5B3E176783928009E","S4TWL - AECMA SPEC 2000M Military Version","SI8: Industry_DIMP_AD","2270840"
"901B0E6C72891ED6A5D623D988DDA0CB","S4TWL - AIN","SI42: Logistics_General","2370138"
"*********5E21ED5B3E176783922609E","S4TWL - ALE SCENARIOS","SI6: FIN_CO","2270416"
"*********5E21ED5B3E176783917209E","S4TWL - ANSI/ISA S95 Interface","SI19: Logistics_PP","2268117"
"0090FABF32DE1ED79DB7CAC60721C0D4","S4TWL - API RFC_CVI_EI_INBOUND_MAIN is obsolete","SI26: MasterData_BP","2506041"
"*********5E21ED5B3E176783921409E","S4TWL - ASSET ACCOUNTING","SI2_FIN_AA","2270388"
"0090FABF370E1ED894D809EC916B40DC","S4TWL - ASSET ACCOUNTING","SI2: FIN_AA","2270388"
"*********5E21ED5B3E176783911209E","S4TWL - AVAILABILITY OF TRANSACTIONS IN MM-IM","SI2: Logistics_MM-IM","2210569"
"*********5E21ED5B3E176783911E09E","S4TWL - Access Control Management (ACM)","SI4: Logistics_PLM","2267842"
"*********5E21ED5B3E176783923009E","S4TWL - Accrual/Deferral of Expenses and Revenues","SI3: FIN_TRM","2270462"
"901B0E6D3B6D1ED783C68DC58DCDE0CA","S4TWL - Actual labor and costing","SI10: Industry_DIMP_AD","2445654"
"*********5E21ED5B3E17678391CC09E","S4TWL - Add-On: EHS Web-Interface","SI6: Logistics_PSS","2267426"
"*********5E21ED5B3E17678391CE09E","S4TWL - Add-On: Genifix (Labelling solution)","SI7: Logistics_PSS","2267427"
"*********5E21ED5B3E17678391CA09E","S4TWL - Add-On: SAP Product and REACH Compliance (SPRC)","SI5: Logistics_PSS","2267423"
"00109B1318B61EDBA2F9A47D5D49C115","S4TWL - Add-on for Polish SAF-T regulatory requirements","SI6: GSFIN_PLVAT","3096074"
"901B0E6D3F611ED79A81D0647C2140CF","S4TWL - Adjustments needed due to changed data model for Compliance Requirement","SI22: Logistics_EHS - Changed data model CR","2504323"
"*********5E21ED5B3E17678390C209E","S4TWL - Advanced Order Processing and Billing for Contractors to the public sect","SI8: SD_ADVORD","2267350"
"00109B14826E1ED8A3CA263A8E64A0CA","S4TWL - Advanced Track & Trace","SI1:_Logistics_General_ATT","2668899"
"*********5E21ED5B3E17678390FC09E","S4TWL - Agency Business","SI1: Logistics_AB","2267743"
"6CAE8B3EA9ED1ED791BB4DE298F0A0C9","S4TWL - Allocation Run","SI53: Logistics_General","2467506"
"6CAE8B3EA3231ED791BB4B75175840C7","S4TWL - Allocation Run Master Data","SI51: Logistics_General","2465556"
"6CAE8B28C6E31ED7A2F93790C564E0D3","S4TWL - Allocation of FX Transactions to Financial Documents and Cash Management","SI16: FIN_TRM","2524941"
"00109B1480DE1ED896BA2E9D59BF60C7","S4TWL - Amount Field Length Extension","SI8_FIN_GL","2628654"
"901B0E6D3F411ED6BE897AC9A5E260C9","S4TWL - Annexing Solution for Israel","SI2_GSFIN_LOCIL","2437547"
"6CAE8B28C6E31ED78DCB216ADCBEC0CF","S4TWL - Ariba Cloud Integration","SI01: ARIBA_CLOUD_INTEG","2400737"
"901B0E6D3F351ED694839F8B2189E0CD","S4TWL - Ariba Network Integration","SI6: CT_BNS-ARI-SE","2341836"
"6CAE8B3E7F1B1ED6A5C3570A397740C2","S4TWL - Article Discontinuation","SI19: Logistics_General","2368683"
"6CAE8B3E7F1B1ED6A5D42DFF3948E0C2","S4TWL - Article Hierarchy","SI21: Logistics_General","2368680"
"*********5E21ED5B3E176783911A09E","S4TWL - Assemblies","SI2: Logistics_PLM","2267840"
"*********5E21ED5B3E176783921809E","S4TWL - Asset management transfers in Joint Venture Acccounting (JVA)","SI4_FIN_AA","2270392"
"901B0E6C72791ED6A5D5DB976128C0C8","S4TWL - Assortment List","SI37: Logistics_General","2370146"
"*********5E21ED5B3E17678390F409E","S4TWL - Authorization Concept for Supplier Invoice","SI14: PROC_MM_IV_AUTH","2271189"
"0090FABF370E1ED7A0CC3033FAEE20D5","S4TWL - Authorization Objects in QM","SI9: Logistics_QM","2505099"
"901B0E6D3F411ED6929DFE4B699C40C6","S4TWL - Authorization in Funds Management","SI11: PSM_AUTHORIZATION","2339149"
"*********5E21ED5B3E17678390FA09E","S4TWL - Automatic Document Adjustment","SI17: PROC_ADA","2267742"
"6CAE8B3E8C3B1ED6BE8FCF4DC91740C3","S4TWL - BADI ADDRESS_SEARCH (Duplicate Check) is Not Supported in SAP S/4HANA","SI21: MasterData_BP","2416027"
"0090FA844D321EDBADE536BFE12860C8","S4TWL - BAdI for Article Reference Handling","SI67: Logistics_General","3043856"
"0090FA844D321EDEAEB9F9450BB9A441","S4TWL - BAdIs not supported in EC Payroll","SI35: HCM_EC_Payroll_BAdIs","3394928"
"0090FABF323E1ED798B12B873176E0D2","S4TWL - BI Extractors in SAP S/4HANA","SI18: CT_BW_EXTRACTORS","2500202"
"*********5E21ED5B3E176783926C09E","S4TWL - BI content, Datasources and extractors for DFPS","SI2: Defense&Security","2273294"
"*********5E21ED5B3E176783913609E","S4TWL - BOM, Routing, Production Version","SI16: Logistics - PLM","2267880"
"*********5E21ED5B3E176783927E09E","S4TWL - BOS Cost Transfer to PS removed","SI1_ECO_BOS_BOSPS","2273314"
"*********5E21ED5B3E176783925809E","S4TWL - BRIM - Billing of Event Detail Records (EDR)","SI1: Cross Industry - EDR Billing","2271210"
"*********5E21ED5B3E176783925A09E","S4TWL - BRIM - Common Object Layer (COL)","SI2: Cross Industry - BRIM - Common Object Layer (COL)","2270317"
"*********5E21ED5B3E176783925C09E","S4TWL - BRIM - Integration with FSCM Biller Direct (FI-CA)","SI3: Cross Industry - BRIM - Integration with FSCM Biller Direct","2271233"
"*********5E21ED5B3E176783925E09E","S4TWL - BRIM - Solution Sales and Billing","SI4: Cross Industry - BRIM - Solution Sales and Billing","2271236"
"*********5E21ED5B3E176783926009E","S4TWL - BRIM – Immediate Master Data Replication to SAP Convergent Charging","SI5: Cross-Industry - BRIM - Master Data Replication to SAP Convergent Charging","2271238"
"*********5E21ED5B3E176783929009E","S4TWL - BSP based Dealer Portal","SI8: IS_DIMP_AUT","2273102"
"*********5E21ED5B3E17678391D009E","S4TWL - BW Extractors for Dangerous Goods Management and Specification Managemen","SI8: Logistics_PSS","2267432"
"00109B13199E1EDD8CE5B3A726B3011C","S4TWL - Barcode reader not supported in app Display Safety-Relevant Information","SI28: EHS_HS_BARCODE_DEPRECATED","3253390"
"*********5E21ED5B3E176783919409E","S4TWL - Batch History","SI36: Logistics_PP","2270242"
"901B0E6D3F611ED79DB7B4C99F7A60CF","S4TWL - Batch Input for Customer Master/Supplier Master","SI25: MasterData_BP","2492904"
"*********5E21ED5B3E17678391E409E","S4TWL - Batch Input for Enterprise Asset Management (EAM)","SI5: Logistics_PM","2270107"
"00109B131AF41EDCB48A63860E086105","S4TWL - Behavior change for AVC Sales Order (SET) scenario configuration models","SI33: Logistics_PLM_AVC-SalesOrderSET","3190519"
"*********5E21ED5B3E17678390CE09E","S4TWL - Billing Document Output Management","SI14: SD_BILLING_OM","2790427"
"*********5E21ED5B3E176783929A09E","S4TWL - Billing Process Enhancements","SI4_IS_DIMP_HT","2270851"
"6CAE8B3EA9EC1ED79F89CB391DE740CA","S4TWL - Blocked customer or supplier in Inventory Management","SI11: Logistics_MM-IM","2516223"
"*********5E21ED5B3E176783915C09E","S4TWL - Browser-based Process Instruction-Sheets/Electronic Work Instructions","SI8: Logistics_PP","2268070"
"*********5E21ED5B3E176783909609E","S4TWL - Business Partner Approach","SI2: MasterData_BP","2265093"
"901B0E6C72891ED6A59FE6B9C4C9E0CB","S4TWL - Business Partner BUT000/Header Level Time Dependency","SI20: MASTERDATA_BP","2379157"
"6EAE8B2860E91ED782CD22524F6060C4","S4TWL - Business Partner Hierarchy Not Available","SI22: MasterData_BP","2409939"
"901B0E6D3F351ED6A5F43E85EF20C0CE","S4TWL - Business Partner in SAP Portfolio and Project Management for SAP S/4HANA","SI29: PPM_BUPA","2379542"
"0090FABF32DE1ED692A31DDA5982C0CC","S4TWL - Business Partner in Site Master","SI1: Logistics_General","2339008"
"*********5E21ED5B3E17678390C609E","S4TWL - Business Role Internal Sales Representative","SI10: SD_ROLES","2271150"
"901B0E6C72891ED7B4EDA883AD14C0D3","S4TWL - Business User Management","SI21: BUSINESS_USER","2570961"
"00109B13199E1EDA97F0E52AA53280F3","S4TWL - Business User integration of BPs with user name and personal number","SI10: FIN_MISC_FSBP_BP001_HR","2900607"
"00109B1318B61ED9B68BC8B11C1660FA","S4TWL - CASH MANAGEMENT - Bank Accounts","SI2_FIN_CM","2870766"
"*********5E21ED5B3E176783921A09E","S4TWL - CASH MANAGEMENT - GENERAL","SI1_FIN_CM","2270400"
"00109B13199E1ED9B68BE3C7AC9040EB","S4TWL - CASH MANAGEMENT - Memo Record","SI3_FIN_CM","2781585"
"0090FA844D321EDC9ED8D8D0B3F860D8","S4TWL - CATSXT","SI31: CT_CATSXT","3150537"
"0090FAE68C4C1EEE97BF82074EC65AA4","S4TWL - CI Include Conflicts in EKKO_INCL_EEW_PS and EKPO_INCL_EEW_PS","SI24: PROC_MM_PUR_CI_INCLUDE_CONFLICTS_PO","3092824"
"00109B13199E1EDDBCF9F9218C6DA129","S4TWL - CI Include Conflicts in Table MMPUR_ANA_E","SI22: PROC_MM_PUR_CI_INCLUDE_CONFLICTS","3326565"
"00109B13199E1EDB9AD92C98026A4105","S4TWL - CM: CCTR CPE Commodity Code Harmonization","SI28: CM_CPE_CCTR","3020849"
"6EAE8B28C2591ED78A9D7086648E80C6","S4TWL - CM: CPE Simplification of Routines & Rules","SI04: CM_ROUTINES","2461014"
"6EAE8B28C2591ED78A9D7FE4FB9B60C6","S4TWL - CM: CPE Simplified Data Model","SI05: CM_CPE_DATA_MODEL","2461007"
"6EAE8B2860E91ED78A9D4BE7AD1380C4","S4TWL - CM: CPE Simplified Formula Assembly & Formula Evaluation","SI03: CM_CPE_FA/FE","2461021"
"901B0E6D3F411ED78A9D3D4EB7FB20C9","S4TWL - CM: Simplified CPE Activation","SI02: CM_CPE_ACTIVATION","2461004"
"901B0E6D3F651ED78A9D283BA9FEA0CC","S4TWL - CM: Simplified Market Data","SI01: CM_CPE_MARKET_DATA","2460737"
"901B0E6C72791ED7A3DCBA21E10D20CE","S4TWL - CML-specific functions with regard to collaterals and collateral objects","SI2: Loans_CML_Banking","2369934"
"*********5E21ED5B3E176783922809E","S4TWL - COST ELEMENTS","SI7: FIN_CO","2270419"
"00109B1315FA1ED8B7CF527D06DC80D4","S4TWL - CPM - Forecast from financial plan versions","SI06: CPD_Forecast_FPVersions","2711002"
"00109B13199E1ED8B9CDEF30A94E00E2","S4TWL - CPM - Personal Activity List","SI07: CPD_Personal_Activity_List","2712156"
"901B0E6D3F611ED78EE92443AD3B20CE","S4TWL - CPM - Rate Card","SI01: CPM_RATE_CARD","2318716"
"6EAE8B28C4011ED698E5C3154CFFC0C9","S4TWL - CVI Integration for SAP ISU","SI10: Utilities_CVI","2344100"
"901B0E6D3ED11ED6A1AAAD3EDF9920C7","S4TWL - CWM in SAP S/4HANA","SI9: Logistics_MM-IM","2358928"
"*********5E21ED5B3E176783917E09E","S4TWL - Campaign Weighing and Dispensing","SI25: Logistics_PP","2270200"
"0090FABF32DE1ED690DA7ECD4149C0CA","S4TWL - Central and Eastern European localization for Utility & Telco","SI9: Utilities_Localization for CEE","2338097"
"00109B1315FA1ED9AB8A7C903B3E80DC","S4TWL - Change in default IDoc inbound processing with message type DESADV","SI14: Logistics_MM-IM","2404011"
"6CAE8B3EA08B1ED6AB9BA497BEE740C2","S4TWL - Changed Interfaces","SI15: CT_Integration","2259818"
"901B0E6C72891ED6BD9DFE6E803740CD","S4TWL - Changed data model for Job Steps","SI21: Logistics_EHS - Data model Job Steps","2446055"
"6EAE8B28C4011ED6BD8C1CD2220600CD","S4TWL - Changed data model for Occupational Exposure Limits","SI17: Logistics_EHS - Data model OEL","2442566"
"6EAE8B27FB391ED6A58673BD59C760CA","S4TWL - Changed data model for listed substances","SI16: Logistics_EHS - Data model listed substances","2376165"
"6CAE8B3E7F1B1ED6BD9E0CD0A5F540C4","S4TWL - Changed process for risk identification","SI19: Logistics_EHS - Risk Identification","2442567"
"*********5E21ED5B3E17678391E609E","S4TWL - Changes In List Reports For Order and Notification","SI6: Logistics_PM","2270108"
"6EAE8B2773C91ED69CF05DED31E200C2","S4TWL - Changes compared to older releases than SAP Port. and Proj. Mngt. 6.1","SI4: PPM_SIMPL_BEFORE_S4H_01","2349633"
"00109B1315FA1EDD8D86554F373BA10E","S4TWL - Changes in EBRR backend transactions","SI14: FIN_CO","3226957"
"0090FABF323E1ED78ACC765EC7D760D0","S4TWL - Changes in Process Observer","SI17: CT_PROCESS_OBSERVER","2412899"
"00109B13199E1EDAB4E637ED186E40FA","S4TWL - Check for duplicate internal asset line items (FAAT_DOC_IT)","SI6_FIN_AA","2977180"
"*********5E21ED5B3E17678391A609E","S4TWL - Claim Management","SI8 Logistics_PS","2267169"
"*********5E21ED5B3E17678390EE09E","S4TWL - Classic MM-PUR GUI Transactions replacement","SI11: PROC_MM_DYN","2267449"
"*********5E21ED5B3E176783913209E","S4TWL - Classification","SI14: Logistics - PLM","2267878"
"00109B1318B61EDAB2CEBF1834E90108","S4TWL - Classification - Data Cleanup before Migration","SI32: Logistics_PLM","2949845"
"901B0E6D3EE91ED6918A5D81D28740CF","S4TWL - Closing Cockpit with S/4 HANA OP","SI5_FIN_GL","2332547"
"*********5E21ED5B3E17678390DA09E","S4TWL - Co-Deployment of SAP SRM","SI1: PROCR_SRM","2271166"
"*********5E21ED5B3E17678390DC09E","S4TWL - Co-Deployment of SAP Supplier Lifecycle Management (SAP SLC)","SI2: PROCR_SLC","2267413"
"*********5E21ED5B3E176783914209E","S4TWL - Collaborative Engineering","SI22: Logistics - PLM","2268017"
"*********5E21ED5B3E176783927209E","S4TWL - Collective Processing Pegging","SI1 Industry_DIMP_AD","2270832"
"*********5E21ED5B3E17678390F809E","S4TWL - Commodity Management Procurement","SI16: PROC_CM","2267741"
"*********5E21ED5B3E17678390CA09E","S4TWL - Commodity Management Sales","SI12: SD_CM","2267352"
"*********5E21ED5B3E176783923209E","S4TWL - Commodity Risk Management","SI4: FIN_TRM","2270469"
"00109B131AF41ED998A26D2C9E8120DA","S4TWL - Company code address data in provider of information","SI4: FIN_SLL_ISR_POI_ADDR","2782281"
"*********5E21ED5B3E176783916E09E","S4TWL - Computer Aided Processing Planning (CAP)","SI17: Logistics_PP","2268114"
"*********5E21ED5B3E17678392A009E","S4TWL - Condition Technique in DRM: Rule Configuration","SI7_IS_DIMP_HT","2270855"
"00109B1318B61EDB88844A5730A1210E","S4TWL - Consolidation (EC-CS) and preparations for consolidation","SI02: EC_CS","2999249"
"00109B1318B61EDC878D904C7709211C","S4TWL - Consolidation (EC-CS) and preparations for consolidation","SI03: EC_CS","2999249"
"*********5E21ED5B3E17678391B609E","S4TWL - Construction Progress Report and Valuation","SI16 Logistics_PS","2267289"
"*********5E21ED5B3E176783929809E","S4TWL - Contract Management Enhancements","SI3_IS_DIMP_HT","2270850"
"901B0E6D3F391ED69CF09BCCABFDE0C9","S4TWL - Control Plans in SAP Portfolio and Project Management for SAP S/4HANA","SI6: PPM_FUNC_CNTR_PLAN_01","2353885"
"*********5E21ED5B3E176783918609E","S4TWL - Control Recipes/Instructions","SI29: Logistics_PP","2270233"
"0090FABF32DE1ED6918AFB949C6920CA","S4TWL - Conversion of Employees to Business Partners","SI2: HR_Empl","2340095"
"00109B147E561ED896B5982BB22220C7","S4TWL - Conversion of Season Master Data to SAP S/4HANA 1809","SI62: Logistics_General","2640859"
"901B0E6C72891ED699A8CDF7897980CA","S4TWL - Conversion relevant changes in EHS","SI9: Logistics_EHS - Conversion relevant changes in EHS","2336396"
"0090FABF323E1ED697ADD217896160CB","S4TWL - Conversion to S/4HANA Material Ledger and Actual Costing","SI6: FIN_MISC_ML","2352383"
"*********5E21ED5B3E176783922C09E","S4TWL - Correspondence Functionality","SI1: FIN_TRM","2270450"
"*********5E21ED5B3E176783927409E","S4TWL - Cost Distribution Processing","SI2 Industry_DIMP_AD","2270834"
"00109B1315FA1EDB83C7C0A59908A0F1","S4TWL - Cost of Sales Ledger","SI6_FIN_General","3006586"
"*********5E21ED5B3E17678390BA09E","S4TWL - Credit Management","SI4: SD_CM","2270544"
"*********5E21ED5B3E176783924809E","S4TWL - Credit Management","SI1: FIN_MISC_CR","2270544"
"*********5E21ED5B3E176783923409E","S4TWL - Credit Risk Analyzer Link to Cash Management","SI5: FIN_TRM","2270470"
"00109B13199E1ED9B6DA37C63864A0EB","S4TWL - Cross Docking","SI6: Logistics_WM","2889636"
"*********5E21ED5B3E176783928809E","S4TWL - Cross-System Transit between Two Plants","SI4: IS_DIMP_AUT","2270358"
"0090FABF32DE1ED695F753873D4780CC","S4TWL - Currencies in Universal Journal","SI6_FIN_GL","2344012"
"*********5E21ED5B3E17678391A009E","S4TWL - Current simulation functions in project system","SI5: Logistics_PS","2270264"
"901B0E6C72891ED68CA7CA25484E00C9","S4TWL - Custom Fields","SI13: CT_Custom-Fields","2320132"
"*********5E21ED5B3E17678390D809E","S4TWL - Customer Interaction Center (CIC)","SI1: CS_CIC","2267412"
"00109B1315FA1EDA97C3E40216E820E4","S4TWL - Customer Service","SI01: CS","2962632"
"*********5E21ED5B3E176783920009E","S4TWL - DATA MODEL CHANGES IN FIN","SI1_FIN_General","2270333"
"*********5E21ED5B3E176783911009E","S4TWL - DATA MODEL IN INVENTORY MANAGEMENT (MM-IM)","SI1: Logistics_MM-IM","2206980"
"*********5E21ED5B3E176783921209E","S4TWL - DATA STRUCTURE CHANGES IN ASSET ACCOUNTING","SI1: FIN_AA","2270387"
"*********5E21ED5B3E176783926A09E","S4TWL - DFPS eSOA services","SI1: Defense&Security","2273297"
"0090FAE68D2C1EEE94DC455507EEAB23","S4TWL - DMEE","SI10_FIN_General","3370503"
"*********5E21ED5B3E176783912C09E","S4TWL - DMS - STEP Support","SI11: Logistics - PLM","2267870"
"*********5E21ED5B3E176783912E09E","S4TWL - DMS Documents@WEB","SI12: Logistics - PLM","2267871"
"00109B13199E1EDAAAE18E83D59EC0F8","S4TWL - Data Destruction in Kanban","SI42: Logistics_PP","2935734"
"00109B1318B61EDCB6D67FD874A08124","S4TWL - Data Model Change in the Commodity Condition Types Mapping","SI30: CPE_Commodity_Quantity","3203820"
"*********5E21ED5B3E17678390B609E","S4TWL - Data Model Changes in SD Pricing","SI2: SD_PRIC","2267308"
"0090FABF323E1ED68CADFB50A7CC00C8","S4TWL - Data Model in Oil & Gas Inventory management","SI1: Oil_Data-Model-Inventory","2328419"
"901B0E6D3F351ED6B8DA69F1AA9860CF","S4TWL - Data Model in Upstream Oil Management","SI18: Oil_UOM_Data_Model","2419408"
"*********5E21ED5B3E17678392BA09E","S4TWL - Data Structure changes in BCS","SI6: Public Sector_PSM-FM-BCS","2270445"
"00109B1318B61ED9B6DA3021502860FA","S4TWL - Decentral WM","SI4: Logistics_WM","2889540"
"*********5E21ED5B3E17678390F009E","S4TWL - Dedicated SAP Supplier Lifecycle Management (SAP SLC) business processes","SI12: PROC_SLC","2271188"
"00109B13199E1ED9A1C22A20695020E9","S4TWL - Deletion of obsolete objects (Compat-Packages)","SI04: Insurance_FS-RI","3047902"
"6EAE8B28C1011ED69FFFB8766F0900C3","S4TWL - Deletion of obsolete packages in EPPM","SI28: PPM_OBSOLETE_PACKAGES_01","2368899"
"6CAE8B3E88EB1ED699A8E2EB7488E0C1","S4TWL - Deprecation of 'Inspect Safety Control' Fiori app","SI11: Logistics_EHS - Inspect Safety Control","2343825"
"6CAE8B3E88EB1ED699A8ECB88D9D40C1","S4TWL - Deprecation of 'Retrieve Safety Information' Fiori app","SI12: Logistics_EHS - Retrieve Safety Information","2343826"
"00109B131AF41EDC97E45892FCAEA102","S4TWL - Deprecation of Design Studio Apps","SI30: CT_DS_APPS","3133221"
"6CAE8B3EA08B1ED699A8D9D2DAE4E0BF","S4TWL - Deprecation of EHS Data Series and Amounts","SI10: Logistics_EHS - Data Series and Amounts","2338405"
"0090FAE68D2C1EEE96EF394EEFB54B24","S4TWL - Deprecation of Fiori Apps and Machine Learning Scenarios","SI23: PROC_General","3376141"
"00109B1318B61EDC969982EB7B588120","S4TWL - Deprecation of Incident Management Apps","SI30: Logistics_EHS - Incident Management","3127469"
"0090FA844D321EDCA089E0FB5080E0D8","S4TWL - Deprecation of Manage Compliance Requirement app","SI31: Logistics_EHS - Manage Compliance Requirement","3139973"
"00109B1481CE1ED8A3CAB2040726C0D5","S4TWL - Deprecation of Mapping Workbench in Foundation of EHS","SI26: Logistics_EHS - Mapping Workbench","2408533"
"0090FABF3B3E1ED7ABA04ECCF06AC0D8","S4TWL - Deprecation of Non DCS based Market Data for Financial Transactions","SI17: CM_FINANCIAL_MARKET_DATA","2556220"
"901B0E6D3F551ED8809CEC58CDC880CE","S4TWL - Deprecation of action templates in control master","SI23: Logistics_EHS - Action Templates","2590858"
"0090FA844D321EDCB6D0123B8D13A0DA","S4TWL - Deprecation of app F3987","SI3: Deprec_F3987","3209838"
"901B0E6D3F411ED7ABA05CBFE02320CC","S4TWL - Deprecation of functions using Non DCS based Market Data","SI18: CM_FINANCIAL_MARKET_DATA","2556176"
"00109B13199E1EDB96FC4269008FE104","S4TWL - Deprecation of transaction EA22_TOOL","SI18: Utilities_EA22_TOOL","3014556"
"6EAE8B27FB391ED7959953F1CACC00CF","S4TWL - Determination of default values in Intrastat reporting","SI3: FIN_SLL_ISR_DV","2468294"
"6CAE8B3EABFB1ED78686BF1F713F80C5","S4TWL - Differentiation Category Dependent FS Datasets Not Available in S/4HANA","SI7: FIN_MISC_FSDATA","2448350"
"*********5E21ED5B3E176783917409E","S4TWL - Digital Signature","SI20: Logistics_PP","2268120"
"*********5E21ED5B3E17678391FC09E","S4TWL - Direct Store Delivery","SI1: LE-DSD","2273317"
"00109B1315FA1EDDAEA4312887594117","S4TWL - Direct Store Delivery in SAP S/4HANA","SI2: LE-DSD","2273317"
"*********5E21ED5B3E17678392A409E","S4TWL - Distributor/Reseller Processes","SI9_IS_DIMP_HT","2270858"
"00109B147E561ED88EFE869A37B400C7","S4TWL - Document Flow Consistency for Goods Receipt to Inbound Delivery","SI13: Logistics_MM-IM","2542099"
"901B0E6C72751ED7B5A1B8D9A8F6C0CE","S4TWL - Document Flow Consistency for Goods Receipt to Inbound Delivery","SI12: Logistics_MM-IM","2542099"
"*********5E21ED5B3E17678391DE09E","S4TWL - Download Data in MS Access out of List","SI2: Logistics_PM","2270076"
"*********5E21ED5B3E176783917809E","S4TWL - Downtimes Functionality","SI22: Logistics_PP","2271203"
"*********5E21ED5B3E176783923A09E","S4TWL - Drawable Bonds","SI9: FIN_TRM","2270521"
"*********5E21ED5B3E176783923C09E","S4TWL - Drilldown Reporting in Treasury and Risk Management","SI10: FIN_TRM","2270522"
"00109B131AF41ED8A7944E39088580D3","S4TWL - Duplicate request entries in Output Management","SI5: CT_OM","2679118"
"901B0E6C72891ED68CB00A3EB1EF60C9","S4TWL - Dynamic Scheduling deprecation","SI7: Oil_Dynamic-Scheduling","2328538"
"00109B1315FA1EDD8C846ECE497FC10C","S4TWL - E-Recruiting Localization for Brazil PS in SAP HCM for SAP S/4HANA","SI30: E-Rec_for_Brazil_Public_Sector","3219250"
"00109B13199E1EDB83C78CB7E5EAC100","S4TWL - EC-PCA - Classical profit center accounting","SI13: FIN_CO","2993220"
"*********5E21ED5B3E176783910C09E","S4TWL - EHS Environment Management","SI5: Logistics_EHS","2273316"
"0050569433401ED68786B08A1E7400EE","S4TWL - ENTERPRISE SEARCH","SI5: CT_ES","2318521"
"*********5E21ED5B3E176783916009E","S4TWL - ERP Line-Balancing and Sequencing","SI10: Logistics_PP","2268084"
"00109B13199E1EDA909BCEC6667E40F0","S4TWL - ERP SD Revenue Recognition","SI5: SD_RRn","2267342"
"*********5E21ED5B3E17678390BC09E","S4TWL - ERP SD Revenue Recognition","SI5: SD_RR","2267342"
"*********5E21ED5B3E17678390E209E","S4TWL - ERP Shopping Cart","SI5: PROC_SC","2271168"
"6CAE8B3E7F1B1ED6A7847DA39B8D00C2","S4TWL - ESS and MSS","SI5: HR_JAVA_ESS_MSS","2383879"
"901B0E6D3F651ED695B2D1D7CB34E0C9","S4TWL - ETM General","SI1_ECO_ETM_GENERAL","2326777"
"901B0E6D3F351ED695B0BCF7FD1660CD","S4TWL - ETM Tables obsolete","SI1_ECO_ETM_TABL","2301533"
"00109B1318B61EDBA08688232158E115","S4TWL - Easy Cost Planning for projects","SI23: Logistics_PS","3033658"
"*********5E21ED5B3E176783912809E","S4TWL - Easy Document Management (Easy DMS)","SI9: Logistics - PLM","2267866"
"*********5E21ED5B3E176783918209E","S4TWL - Electronic Batch Record","SI27: Logistics_PP","2270218"
"0090FAE68C4C1EDDB7B7B029875C6AF1","S4TWL - Electronic Payment Integration for China","SI10_GSFIN_EPIC_CN","3087756"
"*********5E21ED5B3E176783917609E","S4TWL - Electronic Records","SI21: Logistics_PP","2268131"
"0090FAE68C4C1EDDBEDBEC2E92CACAF1","S4TWL - Enablement for continuous delivery of listed substance content","SI32: Logistics_EHS - Listed Substance Content","3344198"
"0090FAE68D2C1EEE9099199195B04B22","S4TWL - End-of-Day Memo Record Reconciliation","SI6_FIN_CM","3351947"
"*********5E21ED5B3E176783913E09E","S4TWL - Engineering Change Management Change Sequence Graph","SI20: Logistics - PLM","2267920"
"*********5E21ED5B3E176783913C09E","S4TWL - Engineering Change Management ECR/ECO","SI19: Logistics - PLM","2267918"
"*********5E21ED5B3E176783912609E","S4TWL - Engineering Client Viewer (ECL)","SI8: Logistics - PLM ","2267864"
"*********5E21ED5B3E176783912A09E","S4TWL - Engineering Desktop (EDesk)","SI10: Logistics - PLM","2267868"
"*********5E21ED5B3E176783914C09E","S4TWL - Engineering Record","SI27: Logistics_PLM","2268043"
"901B0E6D3F651ED6AF8C47EEE11560CB","S4TWL - Engineering Record with LAMA","SI29: Logistics_PLM","2401762"
"*********5E21ED5B3E176783914009E","S4TWL - Engineering Workbench","SI21: Logistics - PLM","2267943"
"*********5E21ED5B3E176783927A09E","S4TWL - Enhancements Actual Labour Costing / Time Recording","SI5: Industry_DIMP_AD","2270838"
"*********5E21ED5B3E176783927C09E","S4TWL - Enhancements Subcontracting Special Stock Types Not Available","SI6: Industry_DIMP_AD","2270839"
"*********5E21ED5B3E17678391EA09E","S4TWL - Enterprise Search in EAM","SI8: Logistics","2270123"
"*********5E21ED5B3E176783911809E","S4TWL - Enterprise Search in SAP Product Lifecycle Management (SAP PLM)","SI1: Logistics_PLM","2267836"
"6EAE8B27FB391ED69DB963C75DE720C8","S4TWL - Enterprise Search in SD and LE","SI20: SD_SLS","2355169"
"*********5E21ED5B3E17678392D609E","S4TWL - Enterprise Services for SAP for Utilities (IS-U)","SI8: Utilities_SOA","2270513"
"*********5E21ED5B3E17678392B409E","S4TWL - Expenditure Certification","SI3 Public Sector_PSM-FM - EXPENDITURE","2270418"
"*********5E21ED5B3E176783923609E","S4TWL - Exposure Management 1.0","SI6: FIN_TRM","2340804"
"00109B1315FA1EDBADF8EB29295160F9","S4TWL - Extended Check of Output Device in Output Control","SI28: CT_CHK_ITEM_DEVICE","3093397"
"6EAE8B289E691ED7949B27A45741C0C5","S4TWL - Extended IS-OIL Customizing","SI23: EXT_OIL_CUSTOMIZING","2534360"
"*********5E21ED5B3E176783915209E","S4TWL - External Interfaces in PP","SI3: Logistics_PP","2268048"
"*********5E21ED5B3E176783928609E","S4TWL - External Service Providers","SI3: IS_DIMP_AUT","2270357"
"*********5E21ED5BC88EE331C30009E","S4TWL - FIORI APPLICATIONS FOR SUITE ON HANA ON-PREMISE","SI10: CT_FIORI","2288828"
"6CAE8B3EABFB1ED6B6B13B1C2D2020C3","S4TWL - FMP in S4HANA","SI10: Logistics_MM-IM","2414624"
"0090FA844D321EDDAFCA24A7B41AE0EA","S4TWL - FS-RI - Correction of misleading method names","SI18: Insurance_FS-RI","3310949"
"00109B1318B61EDBA6EE3A07E7284116","S4TWL - FS-RI - Deletion and Replacement of Obsolete CDS Views","SI10: Insurance_FS-RI","3040673"
"00109B13199E1ED9A1C22CB14A2B00E9","S4TWL - FS-RI - Deletion of LSMW Integration","SI05: Insurance_FS-RI","2887844"
"0090FA844D321EDC8CB0476FE30820D5","S4TWL - FS-RI - Deletion of obsolete Claim Methods","SI13: Insurance_FS-RI","3105510"
"00109B131AF41ED9A1C220258DAD00DB","S4TWL - FS-RI - Deletion of obsolete packages","SI01: Insurance_FS-RI","2818762"
"00109B1316021EDE9DFB03174617F826","S4TWL - FS-RI - Deprecation of RAM APIs with SAP S/4HANA 2023","SI19: Insurance_FS-RI","3385743"
"00109B1315FA1EDB8399A34979BC60F1","S4TWL - FS-RI - Ensuring the functionality of the Extension Service","SI08: Insurance_FS-RI","2972152"
"0090FAE68D2C1EECBADEF4EF0CB28CCF","S4TWL - FS-RI - Interface adaptation in the claim","SI14: Insurance_FS-RI","3210235"
"0090FA844D321EDD85965A56FC0D20DE","S4TWL - FS-RI - Interface adaption of the navigation tree","SI16: Insurance_FS-RI","3225429"
"00109B1315FA1ED9A1C226A27EBB20DA","S4TWL - FS-RI - Redesign Data Access Framework","SI03: Insurance_FS-RI","3054946"
"00109B1318B61ED9A1C22FBEF17580F8","S4TWL - FS-RI - Removal of FI-AR support","SI06: Insurance_FS-RI","2816404"
"00109B1318BE1EEE9FE18CA1F8337D2E","S4TWL - FS-RI - Renaming of a field in PMQ","SI21: Insurance_FS-RI","3399310"
"00109B1315FA1EDB8399315AD8BF20F1","S4TWL - FS-RI - Replacement of a Semantic Object in Fiori App and Transaction","SI07: Insurance_FS-RI","2970970"
"00109B1318B61EDB839A12BA98C7210E","S4TWL - FS-RI - Replacement of msg.PM Connection by SAP Standard","SI02: Insurance_FS-RI","2954199"
"00109B131AF41EDBB288F5B5900180FA","S4TWL - FS-RI Deletion of obsolete functionality for FS-RI-B","SI11: Insurance_FS-RI","3061212"
"00109B131AF41EDB87D7AD668D5520F0","S4TWL - FS-RI deletion of obsolete functionality for FI-AR","SI09: Insurance_FS-RI","2964772"
"0090FA844D321EDE9FE17DE278F4243B","S4TWL - FS-RI – Changing “Remote-Enabled Module” to “Normal Function Module”","SI20: Insurance_FS-RI","3400287"
"00109B1318B61EDC85C27BE1DA28011C","S4TWL - FS-RI – Deprecation of RAM APIs with SAP S/4HANA 2021","SI12: Insurance_FS-RI","3104363"
"00109B13199E1EDD95C06971F258E11E","S4TWL - FS-RI – Deprecation of RAM APIs with SAP S/4HANA 2022","SI17: Insurance_FS-RI","3260792"
"00109B1318BE1EDD80BC3DE9488F0127","S4TWL - FS-RI – Replacement of old OA Accounting Reports","SI15: Insurance_FS-RI","3227549"
"00109B13199E1EDB9B8D7011826D2105","S4TWL - FSBP Table Additional Partner Numbers (BPID001) obsolete","SI11: FIN_MISC_BPID001","3004812"
"6CAE8B3EAD6B1ED791BB4CDBD21E40C9","S4TWL - Fashion Contract Disablement","SI52: Logistics_General","2468952"
"901B0E6C72751ED7A2B0234BCB7360CC","S4TWL - Fashion Functionality","SI57: Logistics_General","2516743"
"6CAE8B3EA08B1ED791BB5045D7CB40C6","S4TWL - Fashion Season Conversion (from ERP)","SI54: Logistics_General","2481829"
"00109B1480461ED88B85185C911440C7","S4TWL - Fashion changes in 1709 FPS02","SI59: Logistics_General","2627238"
"6EAE8B28C4011ED69C87706EF85B60CA","S4TWL - Fast entry of characteristic values in sales document","SI8_IS_DIMP_M","2381843"
"0090FABF370E1ED69C872D5A105460CF","S4TWL - Fast entry of characteristic values in trading contract","SI6_IS_DIMP_M","2381873"
"901B0E6C72891ED68CB014E74523C0C9","S4TWL - Financial Based netting deprecation","SI8: Oil_Financial-Based-netting-deprecation","2328017"
"6CAE8B3E8C3B1ED78EE96843DAE940C5","S4TWL - Fiori Apps of SAP Commercial Project Management","SI03: CPD_FIORI_APPS","2320143"
"00109B1315FA1EDD89EDA36576DB210C","S4TWL - Fiori app Adjust Assigned Liquidity Items deprecated","SI5_FIN_CM","3227455"
"6CAE8B3EA9ED1ED69CFF9598114260C2","S4TWL - Fiori apps of UI FOR SAP PORTF PROJ MGMT 6.0 in SAP Portfolio and Projec","SI17: PPM_FIORI_APPS_01","2360044"
"*********5E21ED5B3E176783927809E","S4TWL - Flight Scheduling","SI4: Industry_DIMP_AD","2270837"
"*********5E21ED5B3E176783918C09E","S4TWL - Flow Manufacturing","SI32: Logistics_PP","2270237"
"00109B131AF41EDB8BB00D2BC44920F1","S4TWL - Force organization, personnel and sustainment for DFPS","SI5: Defense&Security","3018378"
"*********5E21ED5B3E176783916809E","S4TWL - Forecast Based Planning","SI14: Logistics_PP","2268095"
"*********5E21ED5B3E17678390B809E","S4TWL - Foreign Trade","SI3: SD_FT","2267310"
"*********5E21ED5B3E176783909E09E","S4TWL - Foreign Trade fields in Material Master","SI6:MasterData_PM","2267225"
"*********5E21ED5B3E17678390F609E","S4TWL - Foreign Trade within SAP S/4HANA Procurement","SI15: PROC_FT","2267740"
"*********5E21ED5B3E17678392B009E","S4TWL - Former Budgeting System (FBS) in Funds Management","SI1: Public Sector_PSM-FM - FBS","2270413"
"00109B1480DE1ED895A1EB09D10580C7","S4TWL - Forwarding Settlement Document Transfer in TM","SI02: TM_CUSTOMIZING_2","2639847"
"0090FABF32DE1ED6A5D4F364F5CA80CE","S4TWL - Fresh Item Procurement","SI26: Logistics_General","2368747"
"*********5E21ED5B3E17678392D209E","S4TWL - Front Office (EC20) and CIC0 Configuration","SI6: Utilities_FrontOffice","2270510"
"*********5E21ED5B3E17678392C009E","S4TWL - Functionality Average Daily Balance-based Distribution","SI9: Public Sector_PSM-FA","2270466"
"*********5E21ED5B3E17678392C209E","S4TWL - Functionality German Local Authorities","SI10: Public Sector_PSM-GLA","2270467"
"*********5E21ED5B3E17678392BE09E","S4TWL - Functionality for BCS Multi-level Budget Structure","SI8: Public Sector_PSM-FM-BCS","2270464"
"*********5E21ED5B3E17678392B209E","S4TWL - Functionality for FACTS I and FACTS II (Federal Agencies Centralized Tri","SI2: Public Sector_PSM-FM - USFED_FACTS","2270415"
"*********5E21ED5B3E17678392B809E","S4TWL - Fund Accounting based on Special Ledger","SI5: Public Sector_PSM-FA - SL","2270444"
"*********5E21ED5B3E17678392BC09E","S4TWL - Funds Management Dimensions as part of the Universal Journal","SI7: Public Sector_PSM-FA - UJ","2270449"
"6EAE8B2773C91ED78DAA91A78104A0C7","S4TWL - Funds Management Portugal","SI3: GSFIN_LOCPTFM","2469984"
"901B0E6D3ED11ED692A34DBD280780C6","S4TWL - GDS simplified Inbound Price Catalog Interface","SI19: SD-GDS","2340264"
"*********5E21ED5B3E176783922209E","S4TWL - GENERAL COST OBJECTS AND COST OBJECT HIERARCHIES","SI4: FIN_CO","2270411"
"*********5E21ED5B3E176783920409E","S4TWL - GENERAL LEDGER","SI1_FIN_GL","2270339"
"*********5E21ED5B3E17678392D409E","S4TWL - GIS Business Connector","SI7: Utilities_GIS","2270511"
"*********5E21ED5B3E176783925609E","S4TWL - General HCM Approach within SAP S/4HANA","SI1: HR_OP","2273108"
"0090FABF323E1ED692A336C717E1A0CA","S4TWL - Generic Article Harmonization","SI2: Logistics_General","2339010"
"00109B1481AE1ED889EEEB90EB19A0C8","S4TWL - Generic Check for SAP S/4HANA Conversion and Upgrade","SI22: GENERIC_CHECKS","2618018"
"6EAE8B2630691ED784824B62C802C0CD","S4TWL - Geoenablement","SI16: CT_GEOENABLEMENT","2447290"
"901B0E6C72891ED69CFF829F9760C0CB","S4TWL - Global Settings in SAP Portfolio and Project Management for SAP S/4HANA","SI16: PPM_GLOBAL_SETTINGS_01","2359662"
"*********5E21ED5B3E176783910E09E","S4TWL - Global Trade Management","SI1: Logistics_GT","2267785"
"*********5E21ED5B3E176783925209E","S4TWL - Globalization – Financials","SI1_GSFIN","2270311"
"*********5E21ED5B3E176783925409E","S4TWL - Globalization – Logistics","SI1_GSLOG","2270312"
"00109B13199E1ED993FB1C133283A0E8","S4TWL - Golden Tax Interface for China","SI4: GSFIN_GTI","2771861"
"0090FABF32DE1ED6A5D51ECD336120CE","S4TWL - Goods Receipt Capacity Check","SI28: Logistics_General","2368785"
"901B0E6D3F651ED690D572C04278C0C9","S4TWL - Goods movements without exclusive locking by material valuation","SI7: Logistics_MM-IM","2338387"
"*********5E21ED5B3E176783915409E","S4TWL - Graphical Planning Table","SI4: Logistics_PP","2268050"
"901B0E6D3EE91ED780D563EE2DAC40D3","S4TWL - Graphical display of available quantity in MRP evaluations","SI39: Logisticis_PP","3018123"
"00109B1315FA1EDABF8056D998BAA0EF","S4TWL - Group Assets","SI7_FIN_AA","3014869"
"0090FABF32DE1ED6A393F6450E7040CE","S4TWL - HANA-based Analytics for Master Data Governance","SI19: MDG_HBA","2375319"
"901B0E6D3EE91ED69CF0A8F2C873A0D0","S4TWL - HTTP-Based Document Management in SAP Portfolio and Project Management f","SI7: PPM_DOC_HTTP_01","2353984"
"901B0E6D3EE91ED6939B2538DEE0A0CF","S4TWL - Handling Large Projects Business Functions","SI21 Logistics_PS","2352369"
"901B0E6D3ED11ED68CB0457BB640E0C5","S4TWL - Handling Oil & Gas obsolete t-codes Oil","SI10: Oil_Obsolete-Transaction-Codes","2328674"
"00109B147DFE1ED890E1E391B015C0C8","S4TWL - Handling Unit Management - Obsolete Transactions","SI11: IS_DIMP_AUT","2633572"
"00109B147E561ED890A8C04AAE4760C7","S4TWL - Handling of actual date in scheduling (EPPM)","SI30: PPM_SCHEDULE","2630630"
"*********5E21ED5B3E17678391C809E","S4TWL - Hazardous Substance Management","SI4: Logistics_PSS","2267422"
"6EAE8B2630691ED7ABA00C6F9F7E40D0","S4TWL - Hedge Management for logistics transactions","SI11: CM_LOGISTICS_RAW_EXPOS_HM","2556106"
"00109B1315FA1EDB8D8F42905E2560F2","S4TWL - Hierarchy Graphics in Project Systems","SI22: Logistics_PS","3000979"
"901B0E6D3ED11ED7B6E7A9665583E0D0","S4TWL - IDE Outgoing Payment Processing","SI14: Utilities_IDE_OUTGOING_PAYMENT","2574483"
"*********5E21ED5B3E176783921609E","S4TWL - INTEGRATION TO LEASE ACCOUNTING ENGINE (LAE)","SI3_FIN_AA","2270391"
"0050569433401ED68786B3052E26E0EE","S4TWL - INTERNET PRICING AND CONFIGURATOR (SAP IPC)","SI7: CT_IPC","2318509"
"*********5E21ED5B3E176783913409E","S4TWL - IPPE-BOM Converter","SI15: Logistics - PLM","2267879"
"*********5E21ED5B3E176783926809E","S4TWL - IS-Beverage (Empties Management)","SI3: IS_BEV","2531901"
"*********5E21ED5B3E176783926409E","S4TWL - IS-Beverage (Excise Duty)","SI1: IS_BEV","2531942"
"901B0E6D3EE91ED7819B525F90E320D3","S4TWL - IS-Beverage (Pendulum List)","SI4: IS_BEV","2531962"
"*********5E21ED5B3E176783926609E","S4TWL - IS-Beverage (Sales Returns)","SI2: IS_BEV","2531884"
"0090FABF370E1ED7949B59CA31FB20D5","S4TWL - IS-OIL - Commodity Management Integration","SI24: OIL_CMM_INTEGRATION","2534268"
"6EAE8B27FB391ED7949B6A2A2E3980CF","S4TWL - IS-OIL Data Archiving For Exchanges","SI25: OIL_ARCHIVE_EXCHANGES","2534442"
"901B0E6D3F391ED6969767C18423E0C8","S4TWL - IS-OIL Proper sign operation for ACDOCA","SI3: IS-OIL Proper sign operation for ACDOCA","2350150"
"*********5E21ED5B3E17678392CE09E","S4TWL - IS-U Sales Processing","SI4: Utilities_ISU_Sales","2270507"
"*********5E21ED5B3E17678392CC09E","S4TWL - IS-U Stock, Transaction and Master Data Statistics (UIS, BW)","SI3: Utilities_UIS","2270505"
"00109B1318B61ED8AE8024284D99E0F1","S4TWL - IS-Utilities Settlement Control","SI16: Utilities_ISU_SETTLEMENT_CONTROL","2694441"
"*********5E21ED5B3E17678391EE09E","S4TWL - ITS services in QM","SI_2_Logistics_QM","2270126"
"6EAE8B27FB391ED7949B0CDD2068A0CF","S4TWL - Improved update logic for excise duty related movements in MBEW","SI21: EXCISE_DUTY_MBEW_UPDATE","2534551"
"901B0E6C72751ED6A5D5ED6B556CA0C6","S4TWL - In-store food production integration","SI38: Logistics_General","2370148"
"*********5E21ED5B3E176783910409E","S4TWL - Industrial Hygiene and EC interfaces","SI1:  Logistics_EHS - IH and EC","2267781"
"*********5E21ED5B3E176783929609E","S4TWL - Installed Base Management for IS-SW","SI2_IS_DIMP_HT","2270849"
"00109B131AF41EDBB48388EA41B080FC","S4TWL - Insurance localization for Russia - deprecation","SI7: GSFIN_Insurance_RU","2978033"
"00109B1318BE1EDD80A67456DF224127","S4TWL - Insurance localization for Turkey","SI8: GSFIN_Insurance_TR","3164537"
"*********5E21ED5B3E176783927009E","S4TWL - Integration of DFPS with Environmental Compliance","SI4: Defense&Security","2273311"
"*********5E21ED5B3E176783926E09E","S4TWL - Integration of DFPS with Investigative Case Management (ICM)","SI3: Defense&Security","2273299"
"0090FABF3B3E1ED7ABA09DDB99DF00D8","S4TWL - Integration to GTM not supported","SI22: CMM_GTM","2551942"
"*********5E21ED5B3E176783914609E","S4TWL - Integration to SAP Portfolio and Project Management","SI24: Logistics - PLM","2268020"
"6CAE8B3EA08B1ED69CFF3B0A32C060C0","S4TWL - Integration to SAP cFolders in SAP Portfolio and Project Management for","SI12: PPM_INTGR_CFOLDERS_01","2358921"
"*********5E21ED5B3E176783921009E","S4TWL - Integration with FSCM Biller Direct (FI-AP)","SI3_FIN_AP/AR","2270386"
"*********5E21ED5B3E176783920E09E","S4TWL - Integration with FSCM Biller Direct (FI-AR)","SI2_FIN_AP/AR","2270384"
"*********5E21ED5B3E176783922E09E","S4TWL - Interest Rate and Yield Curve Functionality","SI2: FIN_TRM","2270461"
"6CAE8B3EABFB1ED78EE9525964EE60C6","S4TWL - Interface of SAP CPM with SAP Cloud for Customer","SI02: CPM_C4C_INTERFACE","2318733"
"*********5E21ED5B3E17678390E409E","S4TWL - Internet Application Components (IAC)","SI6: PROC_MM_IAC","2267441"
"*********5E21ED5B3E176783915E09E","S4TWL - Internet Kanban","SI9: Logistics_PP","2268082"
"00109B1315FA1ED9868012A6881FC0D7","S4TWL - Invoice Forecasting Worklist not available in SAP S/4HANA","SI2: Logistics_GT","2739164"
"00109B131AF41EDCBAE105F82940E107","S4TWL - JIT Inbound","SI12: IS_DIMP_AUT","3159740"
"00109B1318B61EDCBAE1288579824126","S4TWL - JIT Outbound","SI13: IS_DIMP_AUT","3155820"
"0050569433401ED68786B40425D000EE","S4TWL - JOB SCHEDULING","SI8:CROSS_BC_CCM-BTC","2318468"
"901B0E6D3ED11ED69CFF4D61569BC0C7","S4TWL - KM Documents in SAP Portfolio and Project Management for SAP S/4HANA","SI13: PPM_INTGR_KM_DOC_01","2354985"
"0050569433401ED68786B5054ECAE0EE","S4TWL - LEGACY SYSTEM MIGRATION WORKBENCH","SI9: CT_LSMW","2287723"
"*********5E21ED5B3E17678391DC09E","S4TWL - LIS in EAM","SI1: Logistics_PM","2267463"
"00109B1315FA1ED8ADFF671A02B440D3","S4TWL - Late Payment Change","SI15: Utilities_LATE_PAYMENT","2681020"
"901B0E6C72791ED78EE9792FA8B740CC","S4TWL - Launchpads of Gantt Chart and Commercial Project Inception","SI04: CPD_GANTT_CPI","2318728"
"00109B131AF41EDBA1AFD022282480F7","S4TWL - Lean SAF Interface","SI66: Logistics_General","3035561"
"00109B13199E1EDD88DEF2970843E11A","S4TWL - Location Geo-Coding Adjustments","SI03: TM_CUSTOMIZING_3","3290708"
"*********5E21ED5B3E17678392C409E","S4TWL - Lock and unlock services using IST_TDATA","SI1: Telecommunications","2270468"
"0090FAE68D2C1EED89F0A8AE64C26CD3","S4TWL - Logic changes in Flow Builder","SI4_FIN_CM","3249131"
"*********5E21ED5B3E176783915609E","S4TWL - Logistic Information System in PP","SI5: Logistics_PP","2268063"
"0090FABF32DE1ED6A5D41D1B661420CE","S4TWL - Logistical Variant","SI20: Logistics_General","2368655"
"*********5E21ED5B3E17678390AE09E","S4TWL - Logistics Batch Management","S14: MasterData","2267298"
"*********5E21ED5B3E176783928A09E","S4TWL - Long Material Number","SI5: IS_DIMP_AUT","2270396"
"901B0E6D3F651ED78EFAA34EA32980CC","S4TWL - MDG in Lama","SI23: MasterData_MDG_LAMA","2379272"
"901B0E6D3ED51ED78EFAB7FBCEC660CE","S4TWL - MDG_MDC in Lama","SI24: MasterData_MDG_MDC_LAMA","2442665"
"*********5E21ED5B3E17678390AA09E","S4TWL - MDM 3.0 integration","S12: MasterData","2267295"
"*********5E21ED5B3E17678390E609E","S4TWL - MDM Catalog","SI7: PROC_MDM ","2271184"
"*********5E21ED5B3E17678390EC09E","S4TWL - MM-PUR WebDynpro Applications","SI10: PROC_MM_PR_WD","2267445"
"*********5E21ED5B3E17678390A009E","S4TWL - MRP fields in Material Master","SI7:MasterData_PM","2267246"
"*********5E21ED5B3E176783916209E","S4TWL - MRP in HANA","SI11: Logistics_PP","2268085"
"901B0E6D3EED1ED798CAA09FE9A780CD","S4TWL - MRS Enhancements for Material Master","SI19: CT_MRSS","2493452"
"6CAE8B289E6B1ED7ABA0951C488960C7","S4TWL - MTM and PNL reporting not supported yet in S/4 HANA 1709 SP01","SI21: CM_MTM_PNL","2556103"
"00109B1318B61ED9B8EAAEEE7C4FC0FA","S4TWL - Maintenance of non-billable Point of Delivery services","SI17: Utilities_POD_NON_BILLABLE_SERVICES","2843756"
"901B0E6D3EE91ED7BEBABD4BF3BD20D8","S4TWL - Manual Accruals (Accrual Engine)","SI7_FIN_GL","2582883"
"*********5E21ED5B3E176783927609E","S4TWL - Manufacturer Part Number","SI3: Industry_DIMP_AD","2348023"
"*********5E21ED5B3E17678392A209E","S4TWL - Manufacturer/Supplier Processes","SI8_IS_DIMP_HT","2270856"
"6EAE8B28C1011ED6A6E8C33BAA6CC0C3","S4TWL - Manufacturing Execution Systems","SI10_ IS_DIMP_HT","2383042"
"901B0E6D3F351ED885FD628C4DACE0D4","S4TWL - Mark to Market Accounting Changes","SI25: CMM_MTM_ACCOUNTING","2608098"
"6EAE8B28C6E11ED7BEF498D664EFE0D4","S4TWL - Mark-to-Market & Profit-and-Loss Reporting for Derivatives","SI24: CMM_MTM_Derivatives","2596369"
"901B0E6D3F351ED7BEF467844CC620D4","S4TWL - Mark-to-Market & Profit-and-Loss Reporting for logistics transactions","SI23: CMM_MTM_LOG","2591598"
"0090FABF32DE1ED6A5D43CE4F68500CE","S4TWL - Market Basket Calculation","SI22: Logistics_General","2368716"
"*********5E21ED5B3E17678392D009E","S4TWL - Marketing - IS-U Loyalty Program","SI5: Utilities_ISU_Marketing","2270509"
"*********5E21ED5B3E176783923E09E","S4TWL - Master Agreements","SI11: FIN_TRM","2270523"
"*********5E21ED5B3E176783918A09E","S4TWL - Material Identification/Reconciliation","SI31: Logistics_PP","2270236"
"*********5E21ED5B3E176783911409E","S4TWL - Material Ledger Obligatory for Material Valuation","SI3: Logistics_MM-IM","2267834"
"00109B13199E1EDB9CDAC68A041DA105","S4TWL - Material Ledger: deprecated table for currency and valuation types","SI12: FIN_MISC_ML","3017994"
"*********5E21ED5B3E176783909A09E","S4TWL - Material Number Field Length Extension","SI4: MasterData_PM","2267140"
"*********5E21ED5B3E17678390A209E","S4TWL - Material Type SERV","SI8:MasterData_PM","2267247"
"*********5E21ED5B3E176783911609E","S4TWL - Material Valuation - Statistical moving average price","SI4: Logistics_MM-IM","2267835"
"6CAE8B3E8C8B1ED6A5A7A744BAAE80C2","S4TWL - Material Valuation Data Model Simplification in S/4HANA 1610 and Higher","SI6: Logistics_MM-IM","2337383"
"*********5E21ED5B3E176783928C09E","S4TWL - Material Version","SI6: IS_DIMP_AUT","2270398"
"901B0E6D3EED1ED6A4AB5A2CCDCCC0C8","S4TWL - Material classification with commodity codes","SI1: FIN_SLL_CLS","2376556"
"6CAE8B3E88EB1ED78ECEBFA0EF3F40C7","S4TWL - Media Product Master","SI02: MEDIA_PRODUCT_MASTER","2499057"
"6CAE8B3EA3231ED6A5D454C5A70660C1","S4TWL - Merchandise and Assortment Planning (ERP based)","SI23: Logistics_General","2368741"
"6EAE8B27FE591ED69CF0CEACB6ACC0C2","S4TWL - Microsoft Project Import/Export in SAP Portfolio and Project Management","SI9: PPM_INTGR_MSPC_01","2358463"
"00109B1315FA1EDA94AC3F105EB600E4","S4TWL - Migration of Business Partner Data specific to GST India","SI24: MasterData_BP","2877717"
"*********5E21ED5B3E17678392AC09E","S4TWL - Mill specific enhancements to scheduling PRTs","SI4: IS_DIMP_M","2270410"
"*********5E21ED5B3E176783917A09E","S4TWL - MiniApps PP-PI","SI23: Logistics_PP","2271204"
"*********5E21ED5B3E17678391E209E","S4TWL - Mobile Asset Management (MAM)","SI4: Logistics_PM","2270080"
"*********5E21ED5B3E17678392C809E","S4TWL - Monitoring Profile Value Import","SI1: Utilities_EDM1","2270502"
"6EAE8B28C1011ED69BBDC6ECA53E40C1","S4TWL - Multichannel Foundation for Utilities","SI11: Utilities_MCF","2329190"
"*********5E21ED5B3E176783919A09E","S4TWL - Navigation to Project Builder instead of special maintenance functions","SI2: Logistics_PS","2270261"
"00109B1315FA1EDBA0868C1C30D340F8","S4TWL - Network Costing","SI24: Logistics_PS","3033711"
"6CAE8B3EA08B1ED880BBD9C3C3A380CA","S4TWL - New Access Control Management","SI30: Logistics_PLM","2601548"
"00109B1315FA1EDBABE3C27A5909A0F9","S4TWL - New Customizing for Exchange Rate Difference Handling","SI21: PROC_MM_IV_ERD","3053636"
"00109B13199E1EDABEB3D6792B1B60FE","S4TWL - New Default Security Settings for SAP S/4HANA","SI27: CT_SECURE_BY_DEFAULT","2926224"
"*********5E21ED5B3E17678390FE09E","S4TWL - New advanced ATP in SAP S/4HANA – Table VBBS","SI1: Logistics_ATP","2267745"
"*********5E21ED5B3E176783910209E","S4TWL - New fashion solution","SI3: Logistics_ATP","2267749"
"*********5E21ED5B3E17678392A609E","S4TWL - Non ferrous metal processing","SI1_IS_DIMP_M","2270403"
"6EAE8B28C1011ED78F9111916B93C0C7","S4TWL - OGSD - Best Buy","SI07: OGSD_BEST_BUY","2490540"
"0090FABF370E1ED78F808F1A9D1360D2","S4TWL - OGSD - Classic Data Collation","SI01: OGSD_OLD_DATACOLL","2477777"
"0090FABF370E1ED78F9152CD2F8420D3","S4TWL - OGSD - Classic Method Framework","SI10: OGSD_OLD_METHOD_FRAMEWORK","2491700"
"6EAE8B28C1011ED78F82034C6B6E20C7","S4TWL - OGSD - Classic OGSD Interfaces","SI02: OGSD_OLD_INTERFACES","2489544"
"901B0E6D3F651ED78F91015DCB7DC0CD","S4TWL - OGSD - Collective Orders","SI06: OGSD_COLLECTIVE_ORDERS","2490624"
"901B0E6D3F411ED78F9121F8A652A0CA","S4TWL - OGSD - Formroutines Telesales","SI08: OGSD_TELESALES","2491727"
"6EAE8B2630691ED78F90ED14B3E1E0CE","S4TWL - OGSD - Infosheet","SI05: OGSD_INFOSHEET","2491123"
"6EAE8B2630691ED78F91323E605D80CE","S4TWL - OGSD - Intercompany Processes","SI09: OGSD_INTERCOMPANY_PROC","2491134"
"6EAE8B28C2591ED78F824580137740C7","S4TWL - OGSD - Liquid Petrol Gas","SI04: OGSD_LIQUID_PETROL","2489643"
"0090FABF32DE1ED78F93D59F8D0760D3","S4TWL - OGSD - Old Notes","SI11: OGSD_OLD_NOTES","2491245"
"6EAE8B2773C91ED78F822DCC79A7E0C8","S4TWL - OGSD - Sales Logistics Cockpit","SI03: OGSD_SALES_COCKPIT","2489567"
"6CAE8B3EABFB1ED78F93E5690A7A20C6","S4TWL - OTAS - Integrated Dispatch Managment","SI01: OTAS_DISPATCH","2490435"
"901B0E6D3EE11ED78F940B5ADDD840CD","S4TWL - OTAS - Integrated Petroleum Report","SI02: OTAS_PETROLEUM","2490467"
"901B0E6D3ED11ED78F941651F9B940CC","S4TWL - OTAS - Sales Activities","SI03: OTAS_SALES_ACTITIVITIES","2490447"
"0050569433401ED68786AC0996D300EE","S4TWL - OUTPUT MANAGEMENT","SI4: CT_OM","2470711"
"*********5E21ED5B3E17678390A809E","S4TWL - Object Identifier Type Code","SI11_MasterData_KM","2267294"
"6EAE8B26C2811ED69CF0B6087CF160C6","S4TWL - Object Links to SAP Environmental Compliance in SAP Portfolio and Projec","SI8: PPM_INTGR_EC_01","2359065"
"0090FABF370E1ED897EA2B836AA040DC","S4TWL - Object List Number Field Length Extension","SI10: Logistics_PM","2580670"
"00109B1315FA1ED9AE8A3467481D20DC","S4TWL - Object links to marketing campains in SAP CRM","SI32: PPM_INTGR_CRM_01","2823375"
"901B0E6D3EE91ED695B291AC3ACE60CF","S4TWL - Obsolete BAPIS of Bill of Services (BOS)","SI1_ECO_BOS_BAPI","2301522"
"0090FAE68D2C1EEE92DE5FEE523F2B22","S4TWL - Obsolete POWL Applications in Maintenance Management","SI12: Logistics_PM","3372889"
"0090FAE68C4C1EEE9083363E5AE4BAA1","S4TWL - Obsolete predictive scenarios in PM","SI11: Logistics_PM","3259029"
"901B0E6D3B6D1ED783C68942C504E0CA","S4TWL - Obsolete transactions and Enhancements in GPD","SI9: Industry_DIMP_AD","2445653"
"*********5E21ED5B3E176783910609E","S4TWL - Occupational Health","SI2: Logistics_EHS - Occupational Health","2267782"
"*********5E21ED5B3E176783924009E","S4TWL - Offers","SI12: FIN_TRM","2270526"
"0090FABF32DE1ED68CB03B910808E0CA","S4TWL - Oil & Gas inconsistency reports deprecation","SI9: Oil_Inconsistency-reports-deprecation","2328561"
"6EAE8B28C4011ED6B7DE3BB04998A0CD","S4TWL - Omnichannel Promotion Pricing","SI47: Logistics_General","2417861"
"6CAE8B3EA3231ED6A5D584338943E0C1","S4TWL - Online Store","SI29: Logistics_General","2368835"
"*********5E21ED5B3E17678390C409E","S4TWL - Operational Data Provisioning","SI9: SD_ODP","2267351"
"0090FABF323E1ED68CB08ECE5061E0C8","S4TWL - Optimization in Oil & Gas - TD module for STO and sales order with POD b","SI17: Oil_TD-Module-STO_SO-with-POD","2328639"
"901B0E6D3EE91ED6969754C58335C0CF","S4TWL - Optimization in Oil&Gas - Inter company sales - Cross border excise duty","SI2: Oil_Cross_Border_Excise_Duty","2347421"
"*********5E21ED5B3E17678390BE09E","S4TWL - Optimization of SD Rebate Processing for TPM Customers","SI6: SD_REBATES","2267344"
"*********5E21ED5B3E17678390EA09E","S4TWL - Output Management Adoption in Purchase Order","SI9: PROC_MM_OUTPUT","2267444"
"*********5E21ED5B3E176783913A09E","S4TWL - PH_Sample iView","SI18: Logistics - PLM","2271201"
"901B0E6D3ED11ED6AA836C4D8053E0C7","S4TWL - PLM Labeling Usage","SI28: Logistics_PLM_LBL-Labeling Usage","2387911"
"*********5E21ED5B3E17678392C609E","S4TWL - PORTAL ROLES FOR NETWORK LIFECYCLE MANAGEMENT (NLM)","SI2: Telecommunications","2273101"
"6CAE8B3EA08B1ED6A5C2AB8C891F80C1","S4TWL - POS Outbound via Idoc WP_PLU","SI8: Logistics_General","2371149"
"*********5E21ED5B3E176783914E09E","S4TWL - PP-MRP - Subcontracting","SI1: Logistics_PP-MRP","2268044"
"0090FABF32DE1ED6A5D5F7D97C6B20CE","S4TWL - PRICAT Outbound","SI39: Logistics_General","2370164"
"*********5E21ED5B3E176783921E09E","S4TWL - PROFIT AND LOSS PLANNING AND PROFIT CENTER PLANNING","SI2_FIN_CO","2270407"
"*********5E21ED5B3E17678391C609E","S4TWL - PS&S Enterprise Search Model","SI3:  Logistics_PSS","2267421"
"*********5E21ED5B3E17678391D609E","S4TWL - PS&S SARA Reports","SI11: Logistics_PSS","2267438"
"00109B147F121ED8969B352CC7B5C0CD","S4TWL - Performance optimizations for publication of planning orders","SI1: PPDS_GENERAL","2643483"
"901B0E6D3F651ED68FF3B6F4823880C8","S4TWL - Performance optimizations within Material Document Processing - lock beh","SI5: Logistics_MM-IM","2319579"
"901B0E6D3ED11ED6A5D4E67DF06640C7","S4TWL - Perishables Procurement","SI25: Logistics_General","2368739"
"6CAE8B3E8C8B1ED78ECF3229B4B520C5","S4TWL - Personal Data in Provider of Information","SI2: FIN_SLL_ISR_POI_CP","2471388"
"00109B1318B61ED99DE1BD875DA360F8","S4TWL - Plan data is not migrated to SAP S/4HANA","SI3_FIN_General","2474069"
"*********5E21ED5B3E176783916409E","S4TWL - Planning File","SI12: Logistics_PP","2268088"
"*********5E21ED5B3E176783919209E","S4TWL - Planning Horizon","SI35: Logistics_PP","2270241"
"*********5E21ED5B3E176783928E09E","S4TWL - Planning Regular Route Shipments","SI7: IS_DIMP_AUT","2270399"
"6CAE8B3EA08B1ED69CFF9F7CE2C400C0","S4TWL - Portfolio Structure Graphic in SAP Portfolio and Project Management for","SI18: PPM_UI_PORT_STR_01","2360046"
"901B0E6D3F651ED7AB9FF9E569FB60CF","S4TWL - Position Reporting on versioned pricing data","SI10: CM_LOGISTICS_POSITION","2547347"
"901B0E6D3F551ED7ABA01A0527B2E0CD","S4TWL - Position Reports delivered by Agricultural Contract Management","SI12: CM_LOGISTICS_AGRI_POS","2556134"
"0090FABF32DE1ED6A5D5D04652CF00CE","S4TWL - Prepack Allocation Planning","SI36: Logistics_General","2370151"
"00109B1316021EDEA2D4E77E8E155827","S4TWL - Prepayment solution for the utilities industry","SI20: Utilities_Prepayment","3375865"
"*********5E21ED5B3E17678390E809E","S4TWL - Pricing Data Model Simplification","SI8: PROC_MM_PRICING","2267442"
"*********5E21ED5B3E176783917C09E","S4TWL - Process Data Evaluation (PP-PI-PEV)","SI24: Logistics_PP","2270267"
"*********5E21ED5B3E176783918E09E","S4TWL - Process Flow Scheduling (PP-PI)","SI33: Logistics_PP","2273079"
"*********5E21ED5B3E176783918409E","S4TWL - Process Messages","SI28: Logistics_PP","2270231"
"*********5E21ED5B3E176783918809E","S4TWL - Process Operator Cockpits","SI30: Logistics_PP","2270235"
"00109B1318B61ED8B38B9F76AED5C0F1","S4TWL - Process Route in PLM Web UI","SI31: Logistics_PLM","2704849"
"*********5E21ED5B3E17678392AE09E","S4TWL - Process batch","SI5_IS_DIMP_M","2270412"
"0090FABF323E1ED781FA9FB22965C0D0","S4TWL - Product Design Cost Estimate","SI7: FIN_MISC_PDCE","2442292"
"*********5E21ED5B3E176783911C09E","S4TWL - Product Designer Workbench","SI3: Logistics_PLM","2267841"
"*********5E21ED5B3E17678390A609E","S4TWL - Product catalog","SI10: MasterData_PC","2267292"
"*********5E21ED5B3E176783918009E","S4TWL - Production Campaign (ERP)","SI26: Logistics_PP","2270216"
"*********5E21ED5B3E176783919C09E","S4TWL - Production Resources and Tools functions for projects","SI3: Logistics_PS ","2270262"
"00109B1315FA1EDBBF881894AFAB20FE","S4TWL - Profit Center and Segment Reorganisation","SI10_FIN_GL","3087195"
"901B0E6C72891ED695F736275F1A80CA","S4TWL - Profitability Analysis","SI9: FIN_CO","2349278"
"00109B131AF41EDDBA9F2B7D9C4FC117","S4TWL - Profitability Segment Number Change of Type","SI15: FIN_CO","3320010"
"*********5E21ED5B3E17678391AE09E","S4TWL - Progress Tracking & Monitoring Dates","SI12 Logistics_PS","2267190"
"*********5E21ED5B3E17678391B009E","S4TWL - Project Claim Factsheet","SI13 Logistics_PS","2267283"
"0090FAE68D2C1EEDA0BDF00258D5ACDB","S4TWL - Project Management Integration with DMS","SI34: Logistics_PLM","3300448"
"*********5E21ED5B3E176783919E09E","S4TWL - Project Planning Board, Gantt-Chart, Hierarchy and Network Graphics","SI4: Logistics_PS","2270263"
"*********5E21ED5B3E17678391B209E","S4TWL - Project Reporting","SI14 Logistics_PS","2267286"
"00109B1318B61EDD96EC86DF4FBE812D","S4TWL - Project System - Compatibility Scope","SI27: Logistics_PS","3265838"
"0090FA844D321EDC98ECA60C24B3C0D7","S4TWL - Project System Analytical Apps","SI26: Logistics_PS","3112420"
"*********5E21ED5B3E17678391B809E","S4TWL - Project System ITS Services","SI17 Logistics_PS","2267331"
"901B0E6D3ED11ED69D80981F0CE4E0C7","S4TWL - Project simulation in SAP Portfolio and Project Management for SAP S/4H","SI26: PPM_FUNC_PROJ_SIM_01","2361201"
"*********5E21ED5B3E17678391A409E","S4TWL - Project texts (PS texts)","SI7 Logistics_PS","2270266"
"EFF03C90DE251EEE86B16FBBBBA7BF86","S4TWL - Public Sector localization for Hungary Deprecation","SI12: PSM_FI-LOC-PS-HU","3347307"
"*********5E21ED5B3E17678391C209E","S4TWL - QM Interface","SI1:  Logistics_PSS","2267387"
"*********5E21ED5B3E17678391F009E","S4TWL - QM WEB Workplace (MiniApps) based on ITS Services","SI_3_Logistics_QM","2270129"
"*********5E21ED5B3E17678391F409E","S4TWL - Quality Management Information System (QMIS)","SI5: Logistics_QM","2270193"
"*********5E21ED5B3E176783924409E","S4TWL - Quantity Ledger Always Active for Money Market Transactions","SI14: FIN_TRM","2270529"
"*********5E21ED5B3E176783929209E","S4TWL - REAL ESTATE SALES (HOMEBUILDING)","SI1_IS_DIMP_ECO","2197116"
"*********5E21ED5B3E176783920209E","S4TWL - REPLACED TRANSACTION CODES AND PROGRAMS IN FIN","SI2_FIN_General","2742613"
"901B0E6D3ED11ED690BC431F5981E0C5","S4TWL - RFQ Simplified Transaction","SI18: PROC_RFQ Simplified Transaction","2332710"
"*********5E21ED5B3E176783919009E","S4TWL - Rate and Rough-Cut Planning","SI34: Logistics_PP","2270240"
"*********5E21ED5B3E176783924C09E","S4TWL - Real Estate Classic","SI3_FIN_MISC_RE","2270550"
"*********5E21ED5B3E176783912409E","S4TWL - Recipe Management","SI7: Logistics_PLM","2267863"
"901B0E6D3F391ED8809D3500454E60CF","S4TWL - Reduction of Redundant Business Partner Data Storage in the 'BUTADRSEARC","SI30:MasterData_BP_BUTADRSEARCH","2576961"
"901B0E6D3EE91ED7A0CBB4AFA32CC0D5","S4TWL - Redundant Transactions in QM","SI7: Logistics_QM","2338215"
"0090FABF323E1ED695F73C7DA76820CB","S4TWL - Reference and Simulation Costing","SI10: FIN_CO","2349294"
"00109B131AF41EDDB7F1DAA9B0472117","S4TWL - Refund Claim for Germany","SI19: Utilities_EPB_REFUND_CLAIM","3326146"
"*********5E21ED5B3E176783914A09E","S4TWL - Release Management Framework","SI26: Logistics_PLM","2268042"
"*********5E21ED5B3E176783916C09E","S4TWL - Release status for different RFC function modules revoked","SI16: Logistics_PP","2268112"
"6CAE8B3E7F1B1ED699A8C40FF77DA0BF","S4TWL - Remote Integration Scenarios with EHS Health and Safety Management","SI8: Logistics_EHS - Remote System Integration","2329574"
"00109B1318B61EDBBEBD23441D43E11B","S4TWL - Remote Logistics Management","SI31: OIL_RLM","3046934"
"00109B13199E1ED9ABF173AACC3EE0EA","S4TWL - Removal of Add-On CTS_PLUG","SI24: CT_CTS_PLUG","2820229"
"901B0E6D3EE91ED7A9F02AC8E4A560D7","S4TWL - Removal of Business Application Accelerator","SI03: DB_BAA","1694697"
"00109B1315FA1EDAB4999DD4844E40EA","S4TWL - Removal of D/C Indicator from Editing Options","SI9_FIN_GL","2865285"
"6CAE8B3E88EB1ED699A9055A261A40C1","S4TWL - Removal of EHS Home Screens","SI14: Logistics_EHS - Home Screens","2350447"
"901B0E6D3ED11ED699A8A63565E1E0C6","S4TWL - Removal of EHS workflows for requesting controls, impacts, agents","SI6: Logistics_EHS - HS Workflows","2330017"
"00109B131AF41EDAA7E81EBAB77220E6","S4TWL - Removal of Gender Domain Fixed Values","SI27: MasterData_BP","2928897"
"00109B131AF41ED9BC8192D0CD7F00DF","S4TWL - Removal of obsolete Data Modeler (SD11) content","SI24: CT_SD11_CONTENT","2850096"
"0090FABF323E1ED6BD9DE9C8E53E20CF","S4TWL - Removal of task und document tab pages on incidents","SI18: Logistics_EHS - Incident Tabs","2429619"
"6EAE8B2630691ED7A0CBFE58B4B960CF","S4TWL - Removed Navigation to Transactions for Evaluations in QM","SI8: Logistics_QM","2340996"
"6EAE8B2773C91ED78298382F2866C0C7","S4TWL - Renovation of EHS authorization concept","SI20: Logistics_EHS - Authorization Concept","2442150"
"00109B13199E1EDA8DEDDC96FA2300F0","S4TWL - Renovation of Grouping, Pegging and Distribution Functionality","SI11: Industry_DIMP_AD","2933435"
"00109B131AF41ED9A289E61A1D9B00DB","S4TWL - Renovation of Investigation Lifecycle Workflow","SI27: Logistics_EHS - Investigation Lifecycle Workflow","2801072"
"901B0E6C72891ED887C5F8DD303980D3","S4TWL - Renovation of task management","SI25: Logistics_EHS - Renovation Task Management","2612110"
"00109B1318B61EDCB1AB9FAC5F168124","S4TWL - Replacement of Event Type Linkages","SI15: Logistics_PSS","3196565"
"00109B1315FA1EDBA391A65076E840F8","S4TWL - Replacement of Excel integration in Exposure Assessment","SI29: Logistics_EHS – Office Integration: RAS (2)","3039679"
"00109B1318B61EDBA391A01641F4E115","S4TWL - Replacement of Office integration in Risk Assessment Step","SI28: Logistics_EHS – Office Integration: RAS (1)","3036325"
"00109B1315FA1ED9A8DD0D0ECC95A0DB","S4TWL - Replacement of legal reports with SAP S/4HANA ACR","SI5: GSFIN_LEG_REP","2835816"
"0090FABF32DE1ED6A5D5A544D1D0E0CE","S4TWL - Replenishment","SI32: Logistics_General","2370106"
"00109B1315FA1EDB83C7D9F116C6C0F1","S4TWL - Report Writer / Report Painter in Finance and Controlling","SI8_FIN_General","2997574"
"0090FABF32DE1ED695F72DA47C32C0CC","S4TWL - Reporting/Analytics in Controlling","SI8: FIN_CO","2349297"
"6EAE8B26C2811ED69CF02720AAF320C6","S4TWL - Resource Management Web Dynpro Views in SAP Portfolio and Project Manage","SI1: PPM_RES_UI_01","2359330"
"6CAE8B3E7F1B1ED699A8B8C8D664A0BF","S4TWL - Responsible person for Planned Maintenance at EHS locations","SI7: Logistics_EHS - Location PM responsibles","2329611"
"*********5E21ED5B3E17678391EC09E","S4TWL - Results Recording","SI_1_Logistics_QM","2270124"
"6CAE8B3EA08B1ED6A5C33F912191E0C1","S4TWL - Retail Additionals","SI18: Logistics_General","2371631"
"0090FABF32DE1ED6A5C30A4CF09A80CE","S4TWL - Retail Bonus Buy","SI14: Logistics_General","2371559"
"0090FABF323E1ED692A343766099E0CA","S4TWL - Retail Business Function Harmonization","SI3: Logistics_General","2339317"
"6CAE8B3EA08B1ED6A5C2E9BFD4C060C1","S4TWL - Retail Closed BW Scenarios","SI12: Logistics_General","2371195"
"6CAE8B3EA08B1ED6A5C2BF241BFC40C1","S4TWL - Retail Demand Management Integration","SI9: Logistics_General","2371157"
"6CAE8B3EA08B1ED6A5D643004359C0C1","S4TWL - Retail Factsheets","SI45: Logistics_General","2377780"
"6CAE8B3EA08B1ED6A5D5B01EDB8CE0C1","S4TWL - Retail Information System","SI33: Logistics_General","2370131"
"6CAE8B3E8EC31ED6A5D58FE52723E0C3","S4TWL - Retail Labeling","SI30: Logistics_General","2368870"
"0090FABF32DE1ED6A5D4FE9327AD20CE","S4TWL - Retail Ledger / Profit Center Analytics","SI27: Logistics_General","2368803"
"901B0E6C72891ED6A5C2F6B5905A80CB","S4TWL - Retail Markdown Plan","SI13: Logistics_General","2371590"
"6CAE8B3E7F1B1ED6A5D4CF9215EC40C2","S4TWL - Retail Method of Accounting","SI24: Logistics_General","2368736"
"901B0E6C72891ED6A5D60A1663A5E0CB","S4TWL - Retail Obsolete Transactions","SI40: Logistics_General","2370655"
"6CAE8B3EA08B1ED6A5C324437E6280C1","S4TWL - Retail Order Optimizing: Load Build, Investment Buy","SI16: Logistics_General","2371602"
"0090FABF32DE1ED6A5C2DD3F6990C0CE","S4TWL - Retail Power Lists","SI11: Logistics_General","2371193"
"901B0E6D3F311ED6A5C33214A9E280CB","S4TWL - Retail Rapid Replenishment","SI17: Logistics_General","2371539"
"00109B1481CE1ED890AA92D21772A0D4","S4TWL - Retail Revenue Management Integration","SI60: Logistics_General","2630840"
"6CAE8B3EA9EB1ED6A5C3175878A880C4","S4TWL - Retail Sales Forecasting","SI15: Logistics_General","2371616"
"0090FABF32DE1ED6A5D62E3109BC00CE","S4TWL - Retail Season Conversion","SI43: Logistics_General","2365665"
"901B0E6D3ED11ED6A5D64FE472FBE0C7","S4TWL - Retail Short Text Replacement","SI46: Logistics_General","2377816"
"901B0E6C72891ED692A3704FF85D60CA","S4TWL - Retail Store Fiori App","SI6: Logistics_General","2342914"
"901B0E6D3F651ED786A3538AF3C680CC","S4TWL - Retail Store Fiori App - Order Products (F&R enhancements)","SI49: Logistics_General","2451873"
"901B0E6D3ED11ED6BFD3DBD07E3C80CB","S4TWL - Retail Store Fiori Apps - Transfer Products/Order Products (F&R enhanc.)","SI48: Logistics_General","2436143"
"6CAE8B3EA08B1ED6A5D5C60CCB3DE0C1","S4TWL - Retail iViews","SI35: Logistics_General","2370183"
"0090FAE68D2C1EECBAE2AAEBD12AACCF","S4TWL - Returnable Packaging Logistics","SI16: IS_DIMP_AUT","3213566"
"901B0E6D3F551ED7ABA025B67F1B40CD","S4TWL - Risk Determination and Pricing for MM Contracts not supported yet","SI13: CM_LOGISTICS_RDP_AND_PRICING_MM_CONTRACT","2560298"
"901B0E6D3F411ED7ABA03978275BC0CC","S4TWL - Risk Determination on a schedule line level","SI15: CM_LOGISTICS_POSITION_SCHED_LINE","2556063"
"0090FABF32DE1ED6A6E93A33350100CE","S4TWL - RosettaNet","SI11_ IS_DIMP_HT","2383076"
"*********5E21ED5B3E176783914809E","S4TWL - Routing Component Assignment","SI25: Logistics_PLM_DI","2268041"
"00109B1318B61EDC93D68584194E411F","S4TWL - Russia Manage Additional Payment Attributes app","SI8: GSFIN_Tax_Pay_Attr_RU","3120536"
"00109B1315FA1EDD95A1D1A1E9EA4110","S4TWL - SAF-T RO: Replacement of ABAP Report Solution with DRC Solution","SI9: GSFIN_SAFT_RO","3256877"
"0090FABF32DE1ED6A5C298B1BDE200CE","S4TWL - SAP AFS Integration","SI7: Logistics_General","2371161"
"0090FABF32DE1ED69D808F02A15E60CE","S4TWL - SAP BusinessObjects Explorer Integration in SAP EPPM","SI25: PPM_INTGR_BOBJ_EXPL_01","2361202"
"6EAE8B28C4011ED6A6EA23078FA9A0CC","S4TWL - SAP E-Recruiting","SI4: HR_EREC","2383888"
"6CAE8B3EA3231ED7939DAED88DD940C7","S4TWL - SAP Graphics in EAM Transactions","SI9: Logistics_PM","2482659"
"*********5E21ED5B3E176783920A09E","S4TWL - SAP HANA LIVE REPORTING","SI4_FIN_GL","2270382"
"*********5E21ED5B3E176783920809E","S4TWL - SAP HANA ODP ON HANA CALC VIEW-BASED REPORTING","SI3_FIN_GL","2270360"
"0090FABF32DE1ED6A6E96982839540CE","S4TWL - SAP Learning Solution","SI3: HR_LEARN","2383837"
"6CAE8B3E8C3B1ED78ECE6BB83AC7A0C4","S4TWL - SAP Media availability in SAP S/4HANA","SI01: MEDIA","2506999"
"*********5E21ED5B3E176783913809E","S4TWL - SAP PLM Recipe Development","SI17: Logistics - PLM","2267893"
"00109B1318B61ED9948C90888AA360F7","S4TWL - SAP Patient Management (IS-H) is not available in SAP S/4HANA","SI01: IS_HEALTHCARE","2689873"
"0090FABF323E1ED69D80584F215D20CC","S4TWL - SAP Portal Integration in SAP Portfolio and Project Management for SAP S","SI21: PPM_INTGR_PORTAL_01","2361179"
"6CAE8B3EA08B1ED6A5D5BBF8CB0500C1","S4TWL - SAP Retail Store","SI34: Logistics_General","2370133"
"0050569433401ED5BFEA4840BD9460EC","S4TWL - SAP S/4HANA AND SAP BUSINESS WAREHOUSE CONTENT","SI11: CT_ANA - BI Content","2289424"
"901B0E6D3ED11ED6A5D638A11B9040C7","S4TWL - SAP Smart Business for Retail Promotion Execution","SI44: Logistics_General","2377767"
"6CAE8B3EA08B1ED69D80447610A4C0C0","S4TWL - SAP Smart Forms in SAP Portfolio and Project Management for SAP S/4HANA","SI19: PPM_FUNC_SMARTFORMS_01","2359421"
"00109B1318B61ED98FE4EB3F046500F6","S4TWL - SAP Support Desk ($$-Messages)","SI15: CT_SUPPORT_DESK","2852082"
"6CAE8B3EA08B1ED795E717ED2207E0C6","S4TWL - SAP Travel Management in SAP S/4HANA Compatibility Scope","SI6: HR_TRAVEL","2976262"
"00109B1318B61EDD91AEB082BCBD012B","S4TWL - SAP Travel Management in SAP S/4HANA Suite","SI33: HR_TRAVEL","2719018"
"901B0E6C72891ED780E81CEC275C20CE","S4TWL - SAP Waste and Recycling: Activation","SI13: Utilities_WASTE_GENERAL","2437332"
"0090FAE68D241EED8CD3DA70FD020CD5","S4TWL - SAP Waste and Recycling: Service orders in EWACONTS","SI14: Utilities_WASTE_GENERAL","3245321"
"00109B131AF41ED8BFDABDF68542E0D7","S4TWL - SD Billing Document Draft","SI22: SD_BILLING_DRAFT","2768887"
"*********5E21ED5B3E17678390D409E","S4TWL - SD Complaint Handling","SI17: SD Complaints","2267398"
"*********5E21ED5B3E17678390D009E","S4TWL - SD Rebate Processing replaced by Settlement Management","SI15: SD_Rebate_All","2267377"
"*********5E21ED5B3E17678390B409E","S4TWL - SD Simplified Data Models","SI1: SD_GENERAL","2267306"
"*********5E21ED5B3E17678390D609E","S4TWL - SD – FI/CA Integration with distributed systems","SI18: SD-FICA","2267400"
"0050569433401ED686A36BBEA2C980EE","S4TWL - SELECTED BUSINESS FUNCTIONS IN FINANCIAL ACCOUNTING (FI-GL AND FI-AA)","SI5_FIN_AA","2257555"
"*********5E21ED5B3E176783926209E","S4TWL - SEM Banking","SI1: IS_BANKING","2270318"
"0090FABF32DE1ED69CFF5B7F469100CE","S4TWL - SRM Integration in SAP Portfolio and Project Management for SAP S/4HANA","SI14: PPM_INTGR_SRM_01","2359354"
"*********5E21ED5B3E17678390AC09E","S4TWL - SRM Product Master","S13: MasterData","2267297"
"*********5E21ED5B3E17678390CC09E","S4TWL - Sales Activities","SI13: SD_CAS","2267375"
"*********5E21ED5B3E17678392A809E","S4TWL - Sales Order Versions","SI2_IS_DIMP_M","2270405"
"00109B1315FA1EDCBAE29B4EE7A12109","S4TWL - Sales Scheduling Agreement with TAM processing","SI15: IS_DIMP_AUT","3192264"
"*********5E21ED5B3E176783915809E","S4TWL - Sales and Operation Planning","SI6: Logistics_PP","2268064"
"00109B13199E1EDB8ED8FA2371CB4103","S4TWL - Schedule Manager","SI9_FIN_General","3003364"
"00109B131AF41EDAB5F9ADB8FD9360EA","S4TWL - Scheduling beyond year 2048","SI43: Logistics_PP","2941708"
"*********5E21ED5B3E17678391E009E","S4TWL - Scheduling of Maintenance Plan","SI3: Logistics_PM","2270078"
"901B0E6D3F391ED7A6C44B9E65D8C0CE","S4TWL - Season Active in Inventory Management","SI58: Logistics_General","2535093"
"6CAE8B3EA08B1ED791BB5257952E40C6","S4TWL - Season Conversion (1610 to 1709)","SI55: Logistics_General","2481891"
"00109B147DFE1ED897E4C5CC8DC240C8","S4TWL - Season Field Length Extension","SI64: Logistics_General","2624475"
"0090FABF32DE1ED6A5D59A99227C20CE","S4TWL - Seasonal Procurement","SI31: Logistics_General","2368913"
"901B0E6D3EE91ED7A2940B451CE3A0D6","S4TWL - Segment Field Length Extension","SI56: Logistics_General","2522971"
"901B0E6C72891ED791BB4865822C80D0","S4TWL - Segmentation","SI50: Logistics_General","2465612"
"00109B1480DE1ED892C50CD200F900C7","S4TWL - Segmentation (Upgrade SAP S/4HANA 1709 to 1809)","SI61: Logistics_General","2629241"
"00109B1481CE1ED89AC6652CCF5F80D5","S4TWL - Segmentation (from 1809 onwards)","SI50a: Logistics_General","2652842"
"*********5E21ED5B3E176783919609E","S4TWL - Selected Business Functions in PP area","SI37: Logistics_PP","2271206"
"*********5E21ED5B3E17678391BE09E","S4TWL - Selected Logistics Capabilities","SI19 Logistics_PS","2267384"
"*********5E21ED5B3E17678391BA09E","S4TWL - Selected PS Business Function Capabilities","SI18 Logistics_PS","2267333"
"*********5E21ED5B3E17678391C009E","S4TWL - Selected Project System BAPIs","SI20: Logistics_PS","2267386"
"*********5E21ED5B3E17678391B409E","S4TWL - Selected Project System Interfaces","SI15 Logistics_PS","2267288"
"*********5E21ED5B3E17678391A809E","S4TWL - Selected project financial planning and control functions","SI9 Logistics_PS","2267182"
"0090FABF3B3E1ED69C8748929133A0CE","S4TWL - Selection by characteristics in mass processing of orders","SI7_IS_DIMP_M","2381891"
"0090FAE68D2C1EECBAE1267C9BA84CCF","S4TWL - Self-Billing Cockpit","SI14: IS_DIMP_AUT","3168667"
"00109B1318B61EDB8EF8C230DF962111","S4TWL - Service Station Retailing","SI27: OIL_SSR","3009925"
"*********5E21ED5B3E176783924609E","S4TWL - Several Kinds of Financial Objects Disabled","SI15: FIN_TRM","2270530"
"901B0E6D3B6D1ED6929DD93C405360C6","S4TWL - Side Panel functionality in SAP S/4HANA (on-premise)","SI14: CT_SIDE_PANELS","2340424"
"*********5E21ED5B3E176783910A09E","S4TWL - Simplification in Incident Management and Risk Assessment","SI4: Logistics_EHS - Simplification in Incident Management and Risk Assessment","2267784"
"901B0E6D3F551ED7A8C4A002CFAFC0CD","S4TWL - Simplification in Position Reporting for Financial Transactions","SI08: CM_FINANCIAL_POSITION","2556089"
"*********5E21ED5B3E17678391DA09E","S4TWL - Simplification in Product Compliance for Discrete Industry","SI13: Logistics_PSS","2267461"
"*********5E21ED5B3E17678390C009E","S4TWL - Simplification in SD Analytics","SI7: SD_ANA","2267348"
"0090FABF32DE1ED699A8FB460222A0CC","S4TWL - Simplification of Authorizations in Incident Management","SI13: Logistics_EHS - Authorization IM","2350330"
"901B0E6D3F411ED7ABA04487239360CC","S4TWL - Simplification of DCS based Market Data Handling for Fin Transactions","SI16: CM_DCS_EXTENSION","2556164"
"*********5E21ED5B3E17678391AA09E","S4TWL - Simplification of Date Planning Transactions","SI10 Logistics_PS","2267188"
"901B0E6D3ED11ED699A91010BAB660C6","S4TWL - Simplification of Incident Management workflows","SI15: Logistics_EHS - IM Workflows","2350795"
"901B0E6D3F351ED884C6444E2A9DA0D4","S4TWL - Simplification of Risk Assessment Lifecycle","SI24: Logistics_EHS - Assessment Lifecycle","2603300"
"*********5E21ED5B3E176783909C09E","S4TWL - Simplification of copy/reference handling","SI5: MasterData_PM","2330063"
"*********5E21ED5B3E176783919809E","S4TWL - Simplification of maintenance transactions","SI1: Logistics_PS","2270246"
"00109B1480DE1ED88FB39D910072A0C7","S4TWL - Simplifications in Management of Change","SI01: Logistics_MOC - Simplifications in MOC","2616203"
"0090FABF370E1ED69CF03A19AF1D40CF","S4TWL - Simplified ACLs (Access Control Lists) concept in SAP Portfolio and Proj","SI2: PPM_FUNC_ACL_01","2343374"
"901B0E6D3F411ED7A8C47F4D4BCF00CC","S4TWL - Simplified Commodity Curves","SI06: CM_COMMODITY_CURVES","2551913"
"0090FAE68D2C1EECB5D1E74AB7916CCD","S4TWL - Simplified Commodity Pricing Engine Data Model","SI29: CM_SCPE","3203753"
"901B0E6D3F411ED7A8C49300D38780CC","S4TWL - Simplified DCS Access","SI07: CM_DCS_ACCESS","2551960"
"901B0E6D3F651ED7A98DA70A03ABA0CF","S4TWL - Simplified Data Flow of logistics data for Risk Reporting Database","SI09: CM_LOGISTICS_SIMPLIFIED_ERROR_HANDLING","2547343"
"901B0E6D3B6D1ED7ABA08CAA526400CD","S4TWL - Simplified Data Model/Master Data","SI20: CM_DATA_MODEL","2554440"
"*********5E21ED5B3E176783909809E","S4TWL - Simplified Product Master Tables Related to OMSR Transaction","SI3: MasterData_PM","2267138"
"*********5E21ED5B3E176783915A09E","S4TWL - Simplified Sourcing","SI7: Logistics_PP","2268069"
"901B0E6D3EED1ED78CFA20C4590EA0CB","S4TWL - Simplified customizing of Transportation Manangement (TM) integration","SI01: TM_CUSTOMIZING","2465978"
"00109B1318B61EDABBBDF01300B4C10B","S4TWL - Simplified data model in Joint Venture Accounting","SI26: CT_JVA","2967210"
"*********5E21ED5B3E176783924209E","S4TWL - Simulation in Transaction Management","SI13: FIN_TRM","2270527"
"901B0E6D3ED51ED68BDDE6E1FB54E0CA","S4TWL - Social Intelligence","SI12: CT_SI","2323655"
"*********5E21ED5B3E176783929409E","S4TWL - Software License Management","SI1_IS_DIMP_HT","2270848"
"*********5E21ED5B3E176783929C09E","S4TWL - Software Maintenance Processing","SI5_IS_DIMP_HT","2270852"
"00109B131AF41EDB83C7A87FB0B340F0","S4TWL - Special Purpose Ledger","SI5_FIN_General","3015013"
"*********5E21ED5B3E17678391A209E","S4TWL - Specific PS archiving transactions","SI6 Logistics_PS","2270265"
"*********5E21ED5B3E17678392B609E","S4TWL - Specific fields on Business Partner","SI4: Public Sector_PSM-FG - BP","2270420"
"6EAE8B2773C91ED78A99A48A53DF40C7","S4TWL - Specification Workbench Adjustments","SI14: Logistics_PSS","2439345"
"*********5E21ED5B3E17678392AA09E","S4TWL - Stock Overview and Object Search by Characteristics","SI3_IS_DIMP_M","2270409"
"*********5E21ED5B3E176783915009E","S4TWL - Storage Location MRP","SI2: Logistics_PP-MRP","2268045"
"*********5E21ED5B3E17678390E009E","S4TWL - Subsequent Settlement - Vendor Rebate Arrangements","SI4: PROC_MM_VM_SET","2267415"
"*********5E21ED5B3E17678391D809E","S4TWL - Substance Volume Tracking Selection Criteria based on tables VBUK and VB","SI12: Logistics_PSS","2267439"
"00109B1318BE1EDD80C20D27C39C8127","S4TWL - SuccessFactors Employee Central Integration","SI28: HR_SuccessFactors Employee Central Integration","3255864"
"901B0E6D3ED11ED695F74906372840C6","S4TWL - Summarization Hierarchies in Controlling","SI11: FIN_CO","2349282"
"*********5E21ED5B3E17678390F209E","S4TWL - Supplier Distribution via MDG-S","SI13: PROC_SLC_DISTRIB","2267719"
"901B0E6D3ED11ED7B8B214CA391CA0D0","S4TWL - Supplier Invoice New Archiving","SI20: PROC_MM_IV_ILM","2578291"
"*********5E21ED5B3E176783928409E","S4TWL - Supplier Workplace","SI2: IS_DIMP_AUT","2270354"
"00109B14808E1ED896B756DFB727C0CE","S4TWL - Supply Assignment Preview (upgrade to SAP S/4HANA 1809)","SI63: Logistics_General","2637163"
"*********5E21ED5B3E176783921C09E","S4TWL - TECHNICAL CHANGES IN CONTROLLING","SI1: FIN_CO","2270404"
"*********5E21ED5B3E176783922409E","S4TWL - TRANSFER PRICES/ PARALLEL DELTA VERSIONS","SI5: FIN_CO","2270414"
"00109B13199E1ED9B6DA2179DA04E0EB","S4TWL - Task & Resource Management","SI2: Logistics_WM","2889253"
"*********5E21ED5B3E176783924A09E","S4TWL - Technical Changes in Material Ledger","SI2: FIN_MISC_ML","2332591"
"6EAE8B2860E91ED69BBE0F44354740C1","S4TWL - Technical Changes in Material Ledger with Actual Costing","SI12: FIN_CO","2354768"
"00109B13199E1ED9A8CB2DA63D6D60EA","S4TWL - Technical changes to Statutory Reporting Tables","SI01: Insurance_FS-SR","2810717"
"00109B131AF41EDCB59E0A6825912105","S4TWL - Time Streams Usage in Kanban","SI38: Logistics_PP","3196066"
"*********5E21ED5B3E176783916609E","S4TWL - Total Dependent Requirements","SI13: Logistics_PP","2268089"
"*********5E21ED5B3E176783928209E","S4TWL - Tracking Inbound","SI1: IS_DIMP_AUT","2270352"
"*********5E21ED5B3E17678391F809E","S4TWL - Transportation (LE-TRA)","SI1: Logistics_TRA","2270199"
"00109B13199E1EDBAFA7C6EB41EBE109","S4TWL - Travel Management SRA004 Simplified Model","SI6: HR_SRA004 Simplified Model","3062197"
"00109B1315FA1ED99083169DB5D0E0D8","S4TWL - UI5 GANTT replacing JGANTT","SI31: PPM_GANTT","2768224"
"6EAE8B2630691ED7ABA0308FD4D3A0D0","S4TWL - Unification of supported technologies in analytical data provisioning","SI14: CM_REPORTING_TECHNOLOGIES","2555990"
"0090FABF32DE1ED68CAFE2AACF36E0CA","S4TWL - Unit of Measure (UoM) Group enahncement","SI5: Oil_UOM","2328459"
"901B0E6D3EE91ED7949B1A76CEFC40D5","S4TWL - UoM Group Extension Improvements","SI22: UOM_GROUP_EXT_IMPROVE","2534454"
"6CAE8B3E8C8B1ED78AFEB623EEB000C5","S4TWL - Usage of long Material Number in EC-CS","SI01: EC_CS_MATNR","2471287"
"00109B1315FA1EDB93EA1F76EEC0A0F4","S4TWL - Usage of obsolete links in tables BD001 / BC001","SI28: MasterData_BP","3010257"
"*********5E21ED5B3E176783929E09E","S4TWL - User Exits in DRM","SI6_IS_DIMP_HT","2270853"
"*********5E21ED5B3E17678392CA09E","S4TWL - Utilities Customer Electronic Services (UCES)","SI2: Utilities_UCES","2270504"
"901B0E6D3F651ED78B83DB38ED4AE0CC","S4TWL - VBFA - Indirect Docflow Relationships","SI21: SD_VBFA_STUFE","2469315"
"6CAE8B28C6E31ED69E9EF1EA21FA40C8","S4TWL - Valuation in Retail","SI8: Logistics_MM-IM","2365503"
"00109B131AF41ED9B6DA40FF90F320DD","S4TWL - Value Added Services","SI8: Logistics_WM","2889638"
"0090FABF32DE1ED6A5D616B5A5F9A0CE","S4TWL - Value and Quota Scales","SI41: Logistics_General","2340247"
"*********5E21ED5B3E176783913009E","S4TWL - Variant Configuration","SI13: Logistics - PLM","2267873"
"901B0E6D3F311ED787BB4A1369E400CE","S4TWL - Vehicle search using SAP TREX as external search engine","SI10: IS_DIMP_AUT","2456102"
"00109B131AF41EDA909E358613B9E0E2","S4TWL - Vendor Characteristic Values","SI65: Logistics_General","2885547"
"*********5E21ED5B3E17678390DE09E","S4TWL - Vendor evaluation based on LIS","SI3: PROC_LIS","2267414"
"00109B1318B61ED9B6DA2B56B81080FA","S4TWL - Warehouse Control Interface","SI3: Logistics_WM","2889468"
"*********5E21ED5B3E17678391FA09E","S4TWL - Warehouse Management (WM)","SI1: Logistics_WM","2270211"
"0090FABF323E1ED6ABFE70D7E0F860CF","S4TWL - Waste & Recycling - General","SI12: Utilities_WASTE_GENERAL","2232552"
"*********5E21ED5B3E176783910809E","S4TWL - Waste Management","SI3: Logistics_EHS - Waste Management","2267783"
"00109B1318B61ED9B6DA33FBCC8720FA","S4TWL - Wave Management","SI5: Logistics_WM","2889652"
"0090FABF370E1ED69CF0DB34ADC200CF","S4TWL - Web Dynpro Applications for Resource Maintenance in SAP Portfolio and Pr","SI10: PPM_UI_RES_02","2358376"
"0090FABF32DE1ED6B0A69C83831020D0","S4TWL - Web Dynpro based Dealer Portal","SI9: IS_DIMP_AUT","2352485"
"0090FABF32DE1ED69D804DB1B5E040CE","S4TWL - WebDAV and EasyDMS Integration in SAP Portfolio and Project Management f","SI20: PPM_INTGR_EDMS_WEBDAV_01","2359479"
"*********5E21ED5B3E17678391E809E","S4TWL - WebDynpro Application for Maintenance Planner (<= EhP4)","SI7: Logistics_PM","2270110"
"6CAE8B3EA08B1ED79A8228210EE100C7","S4TWL - Webdynpro Applications for Purchase Requisition Transfer from ERP to SRM","SI19: PROC_CPPR_SPPR","2200412"
"*********5E21ED5B3E17678390C809E","S4TWL - Webshops by Internet Sales or Web Channel Experience Management","SI11: SD_IS","2271161"
"6CAE8B3E7F1B1ED78EE98D917F1680C7","S4TWL - Workbooks in Project Cost and Revenue Planning","SI05: CPD_PCRP_WORKBOOKS","2323806"
"901B0E6D3F611ED69CFF2A1B64AD40C9","S4TWL - Workforce Deployment (WFD) integration in SAP Portfolio and Project Mana","SI11: PPM_INTGR_WFM_WFD_01","2358519"
"*********5E21ED5B3E17678391AC09E","S4TWL - Workforce Planning","SI11: Logistics_PS","2267189"
"901B0E6D3F311ED69D808542B4E180CB","S4TWL - Xcelsius Integration in SAP Portfolio and Project Management for SAP S/4","SI24: PPM_INTGR_XCELSIUS_01","2361194"
"00109B131AF41ED9B6DA3BDF6E3FA0DD","S4TWL - Yard Management","SI7: Logistics_WM","2889637"
"901B0E6D3F351ED695B2B0F98402C0CD","S4TWL - ZDEP ETM CATS package","SI1_ECO_ETM_CATS","2350332"
"*********5E21ED5B3E176783912209E","S4TWL - cDesk","SI6: Logistics_PLM","2267850"
"*********5E21ED5B3E176783912009E","S4TWL - cFolders","SI5: Logistics_PLM","2267845"
"6CAE8B3EA08B1ED69CFF6D3CFD58E0C0","S4TWL - cFolders not available anymore in SAP Portfolio and Project Management f","SI15: PPM_FUNC_CFOLDERS_01","2348430"
"901B0E6D3F611ED69D806190FFE6C0C9","S4TWL - cRoom Integration in SAP Portfolio and Project Management for SAP S/4HAN","SI22: PPM_INTGR_CROOM_01","2361186"
"00109B1318B61ED989A6D7D8A23D80F5","S4TWL - eDocument Framework Migration to SAP S/4HANA","SI2_GSLOG_EDOC","2523749"
"0090FABF32DE1ED68CAFEB9FEF00E0CA","S4TWL - eNetting Deprecation","SI6: Oil_eNetting","2328548"
"*********5E21ED5B3E176783914409E","S4TWL - iPPE Time Analysis","SI23: Logistics - PLM","2268018"
"901B0E6D3F311ED69D807BF7EA7180CB","S4TWL - xPD Integration in SAP Portfolio and Project Management for SAP S/4HANA","SI23: PPM_INTGR_XPD_01","2361181"
"*********5E21ED5B3E17678390D209E","S4TWL -Miscellaneous minor functionalities in SD Area","SI16: SD_Miscellaneous minor deprecations in SD Area","2267395"
"6CAE8B28C6E31ED6AB83009DB6BD80CB","S4TWL Business Package for Subcontracting (EC&O)","SI2_ECO_Subcontracting","2389447"
"00109B1315FA1EDD8C83FC80C3C9610C","S4TWL – Australia Public Sector ESS Not Supported in JAVA Technology","SI29: AU_PS_not_supported_in_ESS_JAVA","3238213"
"0090FA844D321EDD80A6A73DDF3060DE","S4TWL – Business Consolidation (SEM-BCS)","SI05: SEM-BCS","3205515"
"0090FA844D321EDCB3A75AFAF9E8C0DA","S4TWL – Compensation Management","SI10: HR_Compensation_Management","3224317"
"00109B1318B61EDDB8E88B9071806137","S4TWL – Configuration Changes in Group Reporting","SI06: Configuration Changes in Group Reporting","3326805"
"901B0ED2F3661ED8A3E6E43836A940CF","S4TWL – Contract Processing Monitor & Logistical Options w/ GTM not supported","SI22_ CMM_GTM","2672696"
"0090FAE68C4C1EDCB2DA098D9D34CAC8","S4TWL – Country Version Denmark (PY-DK)","SI8: HR_PY-DK","3213848"
"00109B13199E1EDD80B76CF945D4C118","S4TWL – Deprecation of 4 fields from FSBP table BP1010","SI14: FIN_MISC_FSBP_BP1010","3216733"
"00109B1318B61EDCB3A77C70F3B38124","S4TWL – Enterprise Services Personnel Administration","SI13: HR_Enterprise_Services_Personnel_Administration","3224335"
"0090FA844D321EDCB3A7944AEEA520DA","S4TWL – Expert Finder","SI15: HR_Expert_Finder","3224305"
"00109B1318B61EDD8C8619B9E5FB4129","S4TWL – Flexible Benefits not supported in ESS technologies","SI31: Flex_Benefits_not supported_in_ESS","3221404"
"00109B13199E1EDBA7BF4E2E294BE107","S4TWL – Freight Costs","SI30: OIL_Freight_Costs","3050713"
"00109B1480DE1ED8A3E75D4C755A80C9","S4TWL – Functionalities on roadmap for CM-GTM Integration","SI27: CMM_GTM","2671010"
"00109B1315FA1ED8B79CFA13BF6160D4","S4TWL – Functionalities on roadmap for CM-GTM Integration (Period-End Valuation)","SI27: CMM_GTM 2","2710843"
"0090FAE68D2C1EECB3A7985337654CCD","S4TWL – Funds and Position Management","SI16: HR_Funds_and_Position_Management","3224336"
"0090FAE68D241EED8CD362CBE6FD6CD5","S4TWL – HCM Obsolete Package - US Federal Public Sector Functions","SI34: HCM_Obsolete_Packages_for_US_Public_Sector","3238079"
"00109B1318B61EDCB3A7B86C0B560124","S4TWL – HCM Obsolete Packages","SI19: HR_Obsolete_Packages","3224319"
"00109B1318B61EDCB3A7A610234A8124","S4TWL – HCM Processes and Forms JAVA and Adobe","SI17: HR_HCM_P&F_JAVA_and_Adobe","3224318"
"00109B1318B61EDCB3A94336F41A6124","S4TWL – HIS and Structural Graphic","SI25: HR_HIS_and_Structural_Graphic","3224340"
"00109B131AF41EDCB3A772BCE8B70105","S4TWL – HR Employee Interaction Center","SI12: HR_Employee_Interaction_Center","3224334"
"0090FAE68D2C1EED8C86FF66CA8D8CD3","S4TWL – HR Policy Management - India – Not supported Apps","SI32: HR_Policy_Mngt_India_not_supported_Apps","3218356"
"0090FA844D321EDCB3A91A0F5AFEA0DA","S4TWL – HR Shared Service Framework","SI22: HR_Shared_Service_Framework","3224338"
"00109B1480DE1ED8A3E73F10F4B480C9","S4TWL – Integration of Pricing and Payment Events in GTM with Commodity Mngmt.","SI26: CMM_GTM","2671009"
"00109B1318B61EDCB3A7AF755EC4E124","S4TWL – Landing Pages and Suite Page Builder","SI18: HR_Landing_Pages_and_Suite_Page_Builder","3224337"
"00109B131AF41EDC94D686E4223A4101","S4TWL – Managers Desktop","SI7: HR_MANAGERS_DESKTOP","3224315"
"00109B13199E1EDAA7E676DBBC6740F6","S4TWL – Migration from account solution to ledger solution","SI4_FIN_General","3042755"
"00109B1315FA1EDCB3A7D0FEE61D8107","S4TWL – Obsolete Payroll Reports","SI21: HR_Obsolete_Payroll_Reports","3224325"
"0090FAE68D2C1EECB3A9518A90E4CCCD","S4TWL – Parts of Time Management","SI26: HR_Parts_of_Time_Management","3224341"
"0090FAE68C4C1EDCB3A762C730ECEAC8","S4TWL – Personnel Cost Planning","SI11: HR_Personnel_Cost_Planning","3224288"
"00109B1315FA1EDCB3A636D1639EE107","S4TWL – Personnel Development","SI9: HR_Personnel_Development","3224316"
"0090FAE68C441EDD80A607D8043A0ACC","S4TWL – Real-Time Consolidation (RTC)","SI04: RTC","3205500"
"0090FAE68D2C1EECB3A7BF1E77C06CCD","S4TWL – Recruiting","SI20: HR_Recruiting","3224353"
"00109B13199E1EDBA7BEA9AD2CF36107","S4TWL – Shipment Cost Processing","SI29: OIL_Ship_Cost_Proc","3051698"
"00109B131AF41EDCB3A921983500E105","S4TWL – Training and Event Management","SI23: HR_Training_and_ Event_Management","3224320"
"00109B1315FA1EDD8C871E5C060AE10C","S4TWL – US Effort reporting – Not supported in ESS and MSS technologies","SI33: US_Effort_reporting_not_supported_ESS_MSS","3218111"
"0090FA844D321EDCB3A93214D77920DA","S4TWL – Workforce Viewer","SI24: HR_Workforce_Viewer","3224339"
"901B0E6D3ED51ED7B5CE121584AB20D2","S4TWL – iTSW available only for ACM customers","SI26: OIL_ITSW","2572363"
"6EAE8B2630691ED78DC8489CD72280CD","S4TWL- ACM - Simplification for Contract Approval","SI01: ACM_Functional_Equivalent_Available","2468737"
"901B0E6D3ED51ED78DC84B7FE51640CE","S4TWL- ACM: Simplification for Pre-payment and External Interfacing","SI02: ACM_functionality_not_available.","2468819"
"00109B1315FA1EDCBFDA58A50B70C109","S4TWL- Legacy MDI Cost Center Replication","SI11_FIN_Legacy_Cost_Center_Replication_MDI","3247070"
"0050569433401ED681AC130D322CC0ED","S4TWL: Business partner data exchange between SAP CRM and S/4HANA","SI17: Business partner data exchange between SAP CRM and S/4HANA","2285062"
"00109B1318B61ED8AD882EB1DBB800F1","SAP JAM Integration not supported","SI037: CRM","2691695"
"00109B13199E1ED8ADA144753526A0E1","Sales orders and contracts not supported in S4CRM","SI040: CRM","2692033"
"00109B1318B61ED9A1D8137BECCA80F8","Service orders / service notifications not supported","SI068: CRM","2840196"
"00109B13199E1ED8ABE95E69DEF9E0E0","Territory Management not supported","SI009: CRM","2691669"
"00109B131AF41ED8ADE611F77E7340D4","Transaction item number shortened to six digits and not changeable","SI026: CRM","2693573"
"00109B1318B61ED8ADE7147DBBC640F1","Unsupported authorization objects","SI041: CRM","2693548"
"00109B1318B61ED9A1D806C1581080F8","Use technical master data in IS-U instead of IBase","SI067: CRM","2841320"
"00109B131AF41ED8ADCCC3DBDE0F40D4","Web Channel not supported for S4CRM","SI017: CRM","2692791"
"00109B1315FA1ED8ADE4BD0C37BD40D3","Worklist restricted to alerts and workflow","SI036: CRM","2693390"