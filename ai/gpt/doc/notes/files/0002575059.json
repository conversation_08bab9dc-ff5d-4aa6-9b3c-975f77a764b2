{"Request": {"Number": "0002575059", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 578, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000020374092017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002575059?language=E&token=A2FC3B0A796CD9E558ECFDD17E8CB556"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002575059", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002575059/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2575059"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 49}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.01.2024"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Service", "value": "SV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Support Technology Cloud Services", "value": "SV-SCS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "SAP Readiness Check", "value": "SV-SCS-S4R", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SV-SCS-S4R*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2575059 - SAP BW Note Analyzer Files for SAP Readiness Check for SAP BW/4HANA and SAP Data DataSphere, SAP BW Bridge"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Before you start to convert your SAP BW 7.x system (releases 7.0 to 7.5) to SAP BW/4HANA, you want to use the SAP Readiness Check to&#160;check the following aspects of your SAP BW system:</p>\r\n<ul>\r\n<li>Relevant simplification items</li>\r\n<li>SAP BW/4HANA sizing</li>\r\n<li>Compatibility of BW&#160;objects with SAP BW/4HANA</li>\r\n<li>Compatibility of installed Add-Ons with SAP BW/4HANA</li>\r\n</ul>\r\n<p>To perform the check, install some supporting APIs in the managed system, where the necessary information is collected. Refer to the&#160;<a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_READINESS_CHECK\">SAP Help&#160;Portal</a>&#160;and SAP Note <a target=\"_blank\" href=\"/notes/3061594\">3061594</a> for additional information.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>BW4HANA, RC, simplification item, object compatibility</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p><span style=\"font-size: 14px;\">Choose the system in which you want to run the check</span><span style=\"font-size: 14px;\">. </span><strong style=\"font-size: 14px;\">We strongly recommend using the production system here</strong><span style=\"font-size: 14px;\">. Otherwise the results of SAP Readiness Check might be inaccurate or incomplete.</span></p>\r\n<p><strong><span style=\"font-size: 14px;\">Guided Installation:</span></strong></p>\r\n<p><strong>Your system must be of one of the following support package levels or higher</strong> in order to be able to install the tool:</p>\r\n<p>SAP_BW 7.30 SP08<br />SAP_BW 7.31 SP05<br />SAP_BW 7.40 SP09<br />SAP_BW 7.50 SP05<br />SAP_BW 7.51 SP08<br />DW4CORE 3.00 SP00</p>\r\n<p>Install SAP BW Note Analyzer (program Z_SAP_BW_NOTE_ANALYZER) and run it with the XML file for the SAP BW/4HANA Readiness Check. Then follow the instructions provided by Note Analyzer.</p>\r\n<p>Program and XML file are included in the ZIP file attached to this SAP Note. For more details, please see the <a target=\"_blank\" href=\"https://help.sap.com/viewer/p/SAP_BW4HANA\">User Guide for SAP BW Note Analyzer</a>, which&#160;is available on the SAP Help Portal.</p>\r\n<p><strong>All SAP Notes must be first implemented in&#160;the development system and then transported across the transport landscape to the target system for analysis. Make sure that software component versions like SAP_BASIS support package level are the same across the entire landscape before transporting any corrections.</strong></p>\r\n<p>To implement a new note version, please always de-implement the old version and then implement the new one. For possible ABAP class&#160;inconsistency, please try to clean up the class header&#160;in transaction SE24 with Utilities -&gt; Regenerate sections.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><em><strong>For SAP Readiness Check for SAP BW/4HANA, see SAP Note&#160;<a target=\"_blank\" href=\"https://me.sap.com/notes/3061594\">3061594</a>.&#160;</strong></em></p>\r\n<p><strong><em>For SAP Readiness Check for SAP Datasphere, SAP BW bridge, see SAP Note&#160;<a target=\"_blank\" href=\"/notes/3061594\">3352301</a>.&#160;</em></strong></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BW-B4H-CNV (Conversion to SAP BW/4HANA)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (I333244)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (D027026)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0002575059/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002575059/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BW4HANA_Readiness_Custom_Code_2023-02-18.zip", "FileSize": "441", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800003322612017&iv_version=0049&iv_guid=00109B36DBDE1EEDABEA985103C67C0F"}, {"FileName": "SAP_BW4HANA_Readiness_Check_2024-01-30.zip", "FileSize": "63", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=002075125800003322612017&iv_version=0049&iv_guid=00109B36BC261EDEAFEBF97BEA441E72"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3061594", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP BW/4HANA - Central Note", "RefUrl": "/notes/3061594"}, {"RefNumber": "2659524", "RefComponent": "BW-WHM-DBA-IOBJ", "RefTitle": "Namespace for InfoObject is not maintained", "RefUrl": "/notes/2659524"}, {"RefNumber": "2591784", "RefComponent": "BW-B4H-CNV", "RefTitle": "Preparation for SAP Readiness Check for SAP BW/4HANA", "RefUrl": "/notes/2591784"}, {"RefNumber": "2567324", "RefComponent": "BW-B4H-CNV", "RefTitle": "SAP Readiness Check for SAP BW/4HANA - Object Compatibility", "RefUrl": "/notes/2567324"}, {"RefNumber": "2408911", "RefComponent": "BC-UPG-MP", "RefTitle": "Uploading of System Information into Maintenance Planner", "RefUrl": "/notes/2408911"}, {"RefNumber": "2296290", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/2296290"}, {"RefNumber": "2189708", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA Add-On Handling and Usage", "RefUrl": "/notes/2189708"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3141688", "RefComponent": "DS-BWB", "RefTitle": "Conversion from SAP BW or SAP BW/4HANA to SAP Datasphere, SAP BW Bridge", "RefUrl": "/notes/3141688 "}, {"RefNumber": "3037237", "RefComponent": "BW-B4H-CNV", "RefTitle": "SAP BW Note Analyzer Z_SAP_BW_NOTE_ANALYZER - For which topics are Note Analyzer XML files available?", "RefUrl": "/notes/3037237 "}, {"RefNumber": "2732337", "RefComponent": "BW-B4H-CNV", "RefTitle": "SAP BW and SAP BW/4HANA Note Analyzer", "RefUrl": "/notes/2732337 "}, {"RefNumber": "2673085", "RefComponent": "BW-B4H-CNV", "RefTitle": "SAP Readiness Check for SAP BW/4HANA - Object Compatibility (2)", "RefUrl": "/notes/2673085 "}, {"RefNumber": "2296290", "RefComponent": "XX-SER-SIZING", "RefTitle": "New Sizing Report for SAP BW/4HANA", "RefUrl": "/notes/2296290 "}, {"RefNumber": "2591784", "RefComponent": "BW-B4H-CNV", "RefTitle": "Preparation for SAP Readiness Check for SAP BW/4HANA", "RefUrl": "/notes/2591784 "}, {"RefNumber": "2567324", "RefComponent": "BW-B4H-CNV", "RefTitle": "SAP Readiness Check for SAP BW/4HANA - Object Compatibility", "RefUrl": "/notes/2567324 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BW", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BW", "From": "750", "To": "750", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "750", "To": "752", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 1, "URL": "/corrins/0002575059/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 1, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}