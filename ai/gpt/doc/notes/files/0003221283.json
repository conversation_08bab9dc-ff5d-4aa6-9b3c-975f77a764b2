{"Request": {"Number": "0003221283", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 197, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003221283?language=E&token=2EA0CD2A05207A92E2DF62225C52273E"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003221283", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003221283/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3221283"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "19.10.2023"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3221283 - Dumps or Warnings/Errors/Inactive objects when implementing Note 2758146"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The dump is triggered when executing report RC_COLLECT_ANALYSIS_DATA after implementing SAP note&#160;<a target=\"_blank\" href=\"/notes/2758146\">2758146</a>.&#160;Or there are many inactive objects, or warnings, or errors when applying note&#160;<a target=\"_blank\" href=\"/notes/2758146\">2758146</a>.</p>\r\n<p><strong><strong>Symptom&#160;</strong>1</strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"10\" cellspacing=\"0\" style=\"width: 562px; height: 182px;\">\r\n<tbody>\r\n<tr>\r\n<td>Category&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;ABAP&#160;Programming&#160;Error&#160;<br />Runtime&#160;Errors&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;SYNTAX_ERROR<br />ABAP&#160;Program&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;/SDF/SAPLBPM_7X<br />Application&#160;Component&#160;&#160;SV-SMG-SDD&#160;<br />...<br />Error Analysis<br />&#160; &#160;The following syntax error was found in the program /SDF/SAPLBPM_7X<br />&#160; &#160;A Function with the name \"/SDF/BPM_7X_BPA_S4_READI\"<br />&#160; &#160;\"NESS\"</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Symptom</strong>&#160;2<br /></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"10\" cellspacing=\"0\" style=\"width: 781px; height: 182px;\">\r\n<tbody>\r\n<tr>\r\n<td>Runtime Errors &#160; &#160; &#160; &#160; SYNTAX_ERROR<br /><br />Short text<br />&#160; &#160; Syntax error in program \"CL_CVI_READINESS_CHECK========CP \".<br />...<br />Error analysis<br />&#160; &#160;The following syntax error was found in the program<br />&#160; &#160;CL_CVI_READINESS_CHECK========CP :<br />&#160; &#160; \"\"ICF_SERV_STAT\" is not defined in the ABAP Dictionary as a table, projection view, or database view.\"</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Symptom</strong>&#160;3<br /></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"10\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>Category &#160; &#160; &#160; &#160; &#160; &#160; &#160; ABAP Programming Error<br />Runtime Errors &#160; &#160; &#160; &#160; OBJECTS_OBJREF_NOT_ASSIGNED<br />Except. &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;CX_SY_REF_IS_INITIAL<br />managed system ECP<br />SAP_APPL 605 0007<br />ABAP Program &#160; &#160; &#160; &#160; &#160; CL_CVI_READINESS_CHECK========CP<br />Application Component &#160;AP-MD-BF-SYN<br />...<br />Error analysis &#160;&#160;<br />&#160; An exception occurred that is explained in detail below.&#160;<br />&#160; The exception, which is assigned to class 'CX_SY_REF_IS_INITIAL', was not &#160; &#160;&#160;<br />&#160; &#160;caught in procedure \"PREPARE_XML\" \"(METHOD)\", nor was it propagated by a RAISING clause.<br />&#160; Since the caller of the procedure could not have anticipated that the<br />&#160; exception would occur, the current program is terminated. &#160;<br />&#160; The reason for the exception is: &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;&#160;<br />&#160; You attempted to use a 'NULL' object reference (points to 'nothing') &#160; &#160; &#160; &#160; &#160;<br />&#160; access a component (variable: \"LX_TRANSFORMATION_ERROR\"). &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160; &#160;&#160;<br />&#160; An object reference must point to an object (an instance of a class) &#160; &#160; &#160; &#160; &#160;<br />&#160; before it can be used to access components. Either the reference was never set or it was set to 'NULL' using the &#160; &#160; &#160; &#160; &#160;<br />&#160; CLEAR statement.&#160;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Symptom&#160;</strong>4<br /></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"10\" cellspacing=\"0\">\r\n<tbody>\r\n<tr>\r\n<td>\r\n<p>Category ABAP Programming Error<br />Runtime Errors SYNTAX_ERROR<br />ABAP Program RC_COLLECT_ANALYSIS_DATA<br />Application Component SV-SMG-CM<br />...<br />Short Text <br /> Syntax error in program \"RC_COLLECT_ANALYSIS_DATA\"<br />...<br />Error analysis <br />&#160; The following syntax error has occurred in program RC_COLLECT_ANALYSIS_DATA <br />&#160; The data object \"C_FILE_DATA\" does not have a component called \"SCENARIO_S4_UPGRADE\".</p>\r\n</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong>Symptom&#160;</strong>5:<br /></strong></p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\" border=\"1\" cellpadding=\"10\" cellspacing=\"0\" style=\"width: 780px; height: 190px;\">\r\n<tbody>\r\n<tr>\r\n<td>Category &#160; &#160; &#160; &#160; &#160; &#160; &#160; ABAP Programming Error<br />Runtime Errors &#160; &#160; &#160; &#160; SYNTAX_ERROR<br />ABAP Program &#160; &#160; &#160; &#160; &#160; TMW_RC_DOWNLOAD_ANALYSIS_DATA<br />Application Component &#160;SV-SMG-CM<br />Date and Time &#160; &#160; &#160; &#160; &#160;06/22/2022 13:24:28<br />...<br />Error analysis &#160;<br />&#160; The following syntax error has occurred in program&#160;<br />&#160; TMW_RC_DOWNLOAD_ANALYSIS_DATA :&#160;<br />&#160; Each ABAP program can contain only one \"REPORT\", \"PROGRAM\", or \"FUNCTION-POOL\" statement&#160;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<p><strong><strong><strong>Symptom&#160;</strong>6: The warnings/errors/<strong>inactive objects</strong>&#160;when implementing Note&#160;2758146<br /></strong></strong>Example error: The data object \"C_FILE_NAME\" does not have a component called \"FDQ\"<strong><br /><img class=\"img-responsive\" alt=\"example errors.png\" height=\"65\" id=\"00109B36BC261EDCBF9AD27DA7905E55\" src=\"data:image/png;base64,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\" title=\"example errors.png\" width=\"649\" /><br /></strong></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<ul>\r\n<li>SAP ERP</li>\r\n<li>SAP S/4HANA</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>t-code SE38-&gt;RC_COLLECT_ANALYSIS_DATA</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>The following causes are for different dumps. For example, if you have got dump one, please check reason 1 for the cause.</p>\r\n<ol>\r\n<li>Obsolete Note&#160;2443236 is applied.&#160;</li>\r\n<li>The mapping of vendor contact person to business partners is not supported below SAP APPL 605.</li>\r\n<li>Program error.</li>\r\n<li>The dump is triggered when SAP note&#160;2758146&#160;has been incompletely implemented or there are inconsistencies with objects. The same solution works for the other similar syntax errors for objects imported by note&#160;2758146.</li>\r\n<li>Duplicated code in&#160;program TMW_RC_DOWNLOAD_ANALYSIS_DATA.</li>\r\n<li>Same as cause 4.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p><strong>Solution 1&#65306;<br /></strong>De-implementing SAP Note&#160;<a target=\"_blank\" href=\"/notes/2443236\">2443236</a>.&#160;The note must be de-implemented before the implementation of SAP Note <a target=\"_blank\" href=\"/notes/2745851\">2745851</a>.</p>\r\n<p><strong>Solution 2:<br /></strong>Perform the manual activities in Note&#160;<a target=\"_blank\" href=\"/notes/2383051\" title=\"2383051  - Development of vendor contact person mapping to business partner\">2383051</a>&#160;to import the missing DDIC elements.</p>\r\n<p><strong>Solution 3:<br /></strong>Applying correction note&#160;<a target=\"_blank\" href=\"/notes/2901485\">2901485</a>.</p>\r\n<p><strong>Solution 4 &amp; 5 &amp; 6:<br /></strong></p>\r\n<ol>\r\n<li>Implement the latest version SAP Note <a target=\"_blank\" href=\"/notes/1668882\">1668882</a> and <a target=\"_blank\" href=\"/notes/2971435\">2971435</a> if they are missing to avoid SNOTE bugs.</li>\r\n<li>Completely de-implement SAP note <a target=\"_blank\" href=\"/notes/2758146\">2758146</a> and its predecessor <a target=\"_blank\" href=\"/notes/2310438\">2310438</a> if it has been implemented in your system.&#160;</li>\r\n<li>During de-implementation, if any objects from Note 2758146 are not deleted in the system, delete those objects manually in the system. Mostly the objects or reports cannot be deleted after note&#160;2758146&#160;is de-implemented:&#160;<br /><br />a) programs and reports in SE38:&#160;<br />&#160; &#160; TMW_RC_COLLECT_BPA<br />&#160; &#160; TMW_RC_COLLECT_CUSTOM_CODE<br />&#160; &#160; TMW_RC_INITIALIZE<br />&#160; &#160; *TMW_RC*<br /><br />b) objects in SE24:<br />&#160; &#160;&#160;IF_TMW_RC_COLLECT_BPA<br />&#160; &#160; CL_TMW_RC_COLLECT_BPA<br />&#160; &#160; CL_RC_COLLECT_CLIENT_HANDLER<br /><br />For error messages \"<span style=\"text-decoration: underline;\">This activity is not allowed in Modification/enhancement mode</span>\" or \"<span style=\"text-decoration: underline;\">You cannot perform this action in the modification/enhancement mode</span>\" prevents object deletion, please&#160;switch off \"Modification Assistant\" via Edit-&gt;Modification Operation-&gt;Switch off assistant. KBA&#160;<a target=\"_blank\" href=\"/notes/2629624\">2629624</a> states more reasons.&#160;<br /><br /></li>\r\n<li>Then download and implement the latest version of SAP note 2758146. If there are any yellow warnings in SNOTE during the process they have to be manually activated to override the objects to the latest version.</li>\r\n</ol>\r\n<h3 data-toc-skip class=\"section\" id=\"See Also\">See Also</h3>\r\n<p><a target=\"_blank\" href=\"/notes/3205320\">3205320</a> - Repair report for syntax errors preventing implementation of SAP note 2745851 - Business Process Improvement Content for \"SAP Readiness Check 2.0\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Keywords\">Keywords</h3>\r\n<p>/SDF/BPM_7X_BPA_S4_READINESSNESS,&#160;TMW_RC_COLLECT_BPA,&#160;TMW_RC_COLLECT_CUSTOM_CODE,&#160;TMW_RC_INITIALIZE,&#160;*TMW_RC*,&#160;CL_TMW_RC_COLLECT_BPA,&#160;IF_TMW_RC_COLLECT_BPA,&#160;CL_RC_COLLECT_CLIENT_HANDLER<br /><br /></p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (I071739)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON> (I043942)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003221283/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2977422", "RefComponent": "SV-SCS-S4R", "RefTitle": "Process Discovery, SPIDE & SAP Pathfinder report - troubleshooting guide", "RefUrl": "/notes/2977422"}, {"RefNumber": "3205320", "RefComponent": "SV-SMG-MON-BPM-ANA", "RefTitle": "Repair report for syntax errors preventing implementation of SAP note 2745851 - Business Process Improvement Content for \"SAP Readiness Check 2.0\"", "RefUrl": "/notes/3205320"}, {"RefNumber": "2913617", "RefComponent": "SV-SCS-S4R", "RefTitle": "SAP Readiness Check for SAP S/4HANA", "RefUrl": "/notes/2913617"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": [{"Product": "SAP ERP 6.0"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}