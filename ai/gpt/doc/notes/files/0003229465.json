{"Request": {"Number": "0003229465", "LanguageSource": "EN", "LanguageTarget": "EN"}, "Response": {"_elapsedTime": 273, "Error": {}, "SAPNote": {"_type": "00200720410000000235", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0003229465?language=E&token=1A7819CF437F93480BEA17CD463F338F"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0003229465", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0003229465/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "3229465"}, "Type": {"_label": "Type", "value": "SAP Knowledge Base Article"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Problem"}, "Priority": {"_label": "Priority", "value": "Normal"}, "Status": {"_label": "Release Status", "value": "Released to Customer"}, "ReleasedOn": {"_label": "Released On", "value": "23.09.2022"}, "SAPComponentKey": {"_label": "Component", "value": "SV-SCS-S4R"}, "SAPComponentKeyText": {"_label": "Component", "value": "SAP Readiness Check"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}]}, "Title": {"_label": "Title", "value": "3229465 - RC_VALUE_DISCOVERY_COLL_DATA data collection job BUZ_SCENARIO_RECOMMENDATION_COL does not generate TTCODE.XML at all"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>You are aiming for a&#160;Process Discovery for SAP S/4HANA transformation (evolution of SAP Business Scenario Recommendations)&#160;or a SAP Innovation and Optimization Pathfinder report, but the resulting data collection ZIP file is stated not to contain a TTCODE.XML file at all.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Environment\">Environment</h3>\r\n<p>Managed Systems fulfilling all prerequisites as of SAP Notes ...<br /><a target=\"_blank\" href=\"/notes/2758146\">2758146</a> - \"SAP Readiness Check for SAP S/4HANA &amp; Process Discovery (evolution of SAP Business Scenario Recommendations) or SAP Innovation and Optimization Pathfinder\"<br /><a target=\"_blank\" href=\"/notes/2918818\">2918818</a> - \"Usage and Performance Data Collection for Process Discovery (evolution of SAP Business Scenario Recommendations) and SAP Innovation and Optimization Pathfinder on Spotlight\"</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reproducing the Issue\">Reproducing the Issue</h3>\r\n<p>You have triggered the data collection background job&#160;BUZ_SCENARIO_RECOMMENDATION_COL via report&#160;RC_VALUE_DISCOVERY_COLL_DATA and the data collection got finished without issues or errors. However the request for the report got denied with an information, that TTCODE information are either not complete or not present. Checking the data collection ZIP file as downloaded via&#160;report&#160;RC_VALUE_DISCOVERY_COLL_DATA confirmes, that it does not contain any TTCODE.XML file at all.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Cause\">Cause</h3>\r\n<p>Certain program parts/objects responsible for creating the TTCODE.XML file are missing.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Resolution\">Resolution</h3>\r\n<p><strong>Condition One<br /></strong><br />For verifying the respective program objects, goto transaction SE80, in \"Repository Browser\" select program \"RC_VALUE_DISCOVERY_COLL_DATA\" and in its objects list, check \"Subroutines\" for object \"COLLECT_TTCODE_DATA\".</p>\r\n<p>Referring to the lined out Symptom,&#160;object \"COLLECT_TTCODE_DATA\" is expected to be missing. Therefore de-implement SAP Note&#160;<a target=\"_blank\" href=\"/notes/2758146\">2758146</a> completely and re-implement it again afterwards in its latest available version. When getting prompted with a list of objects marked yellow and a message stating, those objects will get overwritten, ensure to select ALL those objects and continue with the implementation.</p>\r\n<p>As soon as the re-implementation has been finished completely and without errors, you may verify the state in SE80 again. The&#160;object \"COLLECT_TTCODE_DATA\" should now be present. Therefore, you may re-trigger the data collection once again, which then also should collect TTCODE data respectively.<br /><br /><strong>Condition Two</strong><br /><br />Subroutines COLLECT_TTCODE_DATA exists, but FM&#160;/SDF/SAPWL_T_UCOUNT_AGGREGATE called by&#160;COLLECT_TTCODE_DATA is missing due to the low version of ST-PI. Please apply the latest SP of the current ST-PI version to import the missing FM definition. You can test by executing&#160;FM&#160;/SDF/SAPWL_T_UCOUNT_AGGREGATE in t-code SE37.</p>\r\n"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D073708)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (I071739)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0003229465/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}, "Validity": {"_label": "Products", "_columnNames": {"Product": "Products"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 4.5, "Quality-Votes": 2, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 1, "Stars-5": 1}, "RatingQuestions": [{"Key": "URF_QA1", "Title": "This article is inaccurate", "Description": ""}, {"Key": "URF_QA2", "Title": "This article is confusing", "Description": ""}, {"Key": "URF_QA3", "Title": "This article doesn't solve my problem", "Description": ""}, {"Key": "URF_QA4", "Title": "I do not like the feature described", "Description": ""}]}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}