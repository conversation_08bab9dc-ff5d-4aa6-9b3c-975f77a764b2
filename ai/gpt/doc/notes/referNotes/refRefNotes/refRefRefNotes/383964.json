{"Request": {"Number": "383964", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2081, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014976812017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/**********?language=E&token=84F6F2C4539CE311A70C63A5BB990E35"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/**********", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/**********/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "383964"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 11}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "13.05.2008"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP"}, "SAPComponentKeyText": {"_label": "Component", "value": "Accounts Payable"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "383964 - Expiring currencies: Advance implementation"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>You want to implement the complete expiring currencies function or parts of it before the Support Package specified in Note 350530 or in releases that are not supported.<br /></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Expiring currencies, euro, CURREXP, preliminary transport<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The national currencies were replaced by the euro.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>The following transports provide you with the application basis functions from Note 314121 only. That is,<br />you receive the function modules for the date check with the warning and error data<br />and for translation for the subsequent processes and the Customizing tables. To connect these function modules to the application, you also have to implement the correction instructions from the application-specific notes for the individual objects and processes for which you want to use the expiring currencies functions. You can find these in the related notes listed in the attachment. The correction instructions for the payment process are an exception. These corrections can only be implemented by an authorized SAP consultant in conjunction with SAP development (see Note 354720).<br /><br />SAP recommends that you use the following procedure:<br />1. Select the processes or objects for which you want to use the function<br />&#x00A0;&#x00A0; for expiring currencies.<br />2. Implement the advance transport from SAPSERV3 (see the description<br />&#x00A0;&#x00A0; below).<br />3. Implement the application-specific Notes with the<br />&#x00A0;&#x00A0; attached correction instructions for the processes.<br /><br />The advance corrections are in the following directories<br />in sapserv3:<br />Files for Release 3.1i<br />~ftp/general/R3server/abap/note.0383964/rel31i<br />Files for Release 4.0b<br />~ftp/general/R3server/abap/note.0383964/rel40b<br />Files for Release 4.5b<br />~ftp/general/R3server/abap/note.0383964/rel45b<br />Files for Release 4.6B<br />~ftp/general/R3server/abap/note.0383964/rel46b<br />Files for Release 4.6C<br />~ftp/general/R3server/abap/note.0383964/rel46c<br /><br />In the directories referred to you can find the<br />cofiles (files K*) and the data files (R*,D*).<br /><br />You must enhance the structure B0SG:<br /><br />Create the following data element (transaction SE11):<br /><br />Data element&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; XCURR<br />Short description&#x00A0;&#x00A0;&#x00A0;&#x00A0;Indicator: Suppress Currency Translation?<br />Last changed&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;USER&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Date&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Original language&#x00A0;&#x00A0;D<br />Status&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;active&#x00A0;&#x00A0;&#x00A0;&#x00A0;saved&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Development class&#x00A0;&#x00A0;FREP<br /><br />Domain&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; XFELD<br />Data type&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;CHAR Character string<br />No. characters&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br />Output length&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;1<br /><br />Save and activate the data element.<br /><br />Add the following field to the structure B0SG (transaction SE11):&#x00A0;&#x00A0;&#x00A0;&#x00A0;:<br /><br />Field name&#x00A0;&#x00A0;&#x00A0;&#x00A0; Data elem.<br />...<br />...<br />XCURR&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;XCURR<br /><br />Save and activate the structure.<br /><br /><br />You can find more information about \"Expiring Currencies\" with a detailed description of the individual objects and processes in the euro information portal on the SAP Service Marketplace under http://service.sap.com/euro in Directory -&gt; Euro Library -&gt; Expiring Currencies.<br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-GL (General Ledger Accounting)"}, {"Key": "Other Components", "Value": "FI-AR (Accounts Receivable)"}, {"Key": "Other Components", "Value": "FI-BL (Bank-Related Accounting)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D002552)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON> (D002552)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/**********/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/**********/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "458754", "RefComponent": "BC-SRV-BSF", "RefTitle": "Currencies to be discontinued: missing processes in table TP", "RefUrl": "/notes/458754"}, {"RefNumber": "455877", "RefComponent": "FI", "RefTitle": "Manual implementation of the 'expiring currencies' functions", "RefUrl": "/notes/455877"}, {"RefNumber": "446606", "RefComponent": "BC-SRV-BSF", "RefTitle": "Expiring currencies: Missing objects in V_TCURO", "RefUrl": "/notes/446606"}, {"RefNumber": "444785", "RefComponent": "LO-MD-BP-CM", "RefTitle": "Expiring currency in the customer master", "RefUrl": "/notes/444785"}, {"RefNumber": "444784", "RefComponent": "LO-MD-BP-VM", "RefTitle": "Expiring currency in vendor master", "RefUrl": "/notes/444784"}, {"RefNumber": "443627", "RefComponent": "FI-GL-GL-D", "RefTitle": "Expiring currencies with drill-down for line items", "RefUrl": "/notes/443627"}, {"RefNumber": "407198", "RefComponent": "FI-AR-AR-G", "RefTitle": "RFKORD80/FBCJ: Expiring currencies (cash document)", "RefUrl": "/notes/407198"}, {"RefNumber": "383589", "RefComponent": "PSM-FM-PO-CA", "RefTitle": "IS-PS cash desk: Expiring currencies as of 2002", "RefUrl": "/notes/383589"}, {"RefNumber": "382355", "RefComponent": "PSM-FM-PO-RE", "RefTitle": "IS-PS: Expiring currency in requests", "RefUrl": "/notes/382355"}, {"RefNumber": "382089", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110: payment run termination with error message F1806", "RefUrl": "/notes/382089"}, {"RefNumber": "368817", "RefComponent": "FI-GL", "RefTitle": "Expiring currencies reporting", "RefUrl": "/notes/368817"}, {"RefNumber": "366042", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies processes FI", "RefUrl": "/notes/366042"}, {"RefNumber": "365531", "RefComponent": "FI-BL-PT-CJ", "RefTitle": "FBCJ: Expiring currencies in the EMU", "RefUrl": "/notes/365531"}, {"RefNumber": "364157", "RefComponent": "FI-AP", "RefTitle": "Object Type for Expiring Currencies FI", "RefUrl": "/notes/364157"}, {"RefNumber": "363522", "RefComponent": "FI", "RefTitle": "Checks of AC interface at end of dual currency phase", "RefUrl": "/notes/363522"}, {"RefNumber": "363276", "RefComponent": "FI-BL-PT", "RefTitle": "F111: Expiring currencies", "RefUrl": "/notes/363276"}, {"RefNumber": "363211", "RefComponent": "FI-AP-AP-B", "RefTitle": "Trtmnt of expiring currencies for pymt advice notes", "RefUrl": "/notes/363211"}, {"RefNumber": "362496", "RefComponent": "FI-SL-SL-A", "RefTitle": "Warning message discontinuing currencies GB01/GB11", "RefUrl": "/notes/362496"}, {"RefNumber": "362277", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies: correspondence", "RefUrl": "/notes/362277"}, {"RefNumber": "361608", "RefComponent": "FI-AR-AR-C", "RefTitle": "RFDZIS00: Exipiring currencies", "RefUrl": "/notes/361608"}, {"RefNumber": "360145", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies: Bills of exchange", "RefUrl": "/notes/360145"}, {"RefNumber": "359818", "RefComponent": "FI", "RefTitle": "Post/clear expiring currencies", "RefUrl": "/notes/359818"}, {"RefNumber": "359230", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies in recurring entry original documents", "RefUrl": "/notes/359230"}, {"RefNumber": "359028", "RefComponent": "FI-AP-AP-D", "RefTitle": "Line items: Expiring currencies", "RefUrl": "/notes/359028"}, {"RefNumber": "357700", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies: Balance confirmations", "RefUrl": "/notes/357700"}, {"RefNumber": "357028", "RefComponent": "FI-GL-GL-A", "RefTitle": "Automatic clearing and expiring currencies", "RefUrl": "/notes/357028"}, {"RefNumber": "355613", "RefComponent": "FI-AR-AR-C", "RefTitle": "RFDUZI00: Expiring currencies", "RefUrl": "/notes/355613"}, {"RefNumber": "355295", "RefComponent": "FI-AR-AR-C", "RefTitle": "Dunning: expiring currencies as of 2002", "RefUrl": "/notes/355295"}, {"RefNumber": "354720", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110: Expiring currencies", "RefUrl": "/notes/354720"}, {"RefNumber": "350530", "RefComponent": "FI-AR", "RefTitle": "Functions in FI for end of dual currency phase", "RefUrl": "/notes/350530"}, {"RefNumber": "321671", "RefComponent": "CA-EUR-CNV-RE", "RefTitle": "Local currency changeover RE: expiring currencies", "RefUrl": "/notes/321671"}, {"RefNumber": "314121", "RefComponent": "CA-EUR", "RefTitle": "Expiring currencies in Release 3.1I to 4.6C and higher", "RefUrl": "/notes/314121"}, {"RefNumber": "207127", "RefComponent": "CA-GTF-BS", "RefTitle": "Invalid currencies: Incorrect date", "RefUrl": "/notes/207127"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "314121", "RefComponent": "CA-EUR", "RefTitle": "Expiring currencies in Release 3.1I to 4.6C and higher", "RefUrl": "/notes/314121 "}, {"RefNumber": "458754", "RefComponent": "BC-SRV-BSF", "RefTitle": "Currencies to be discontinued: missing processes in table TP", "RefUrl": "/notes/458754 "}, {"RefNumber": "361608", "RefComponent": "FI-AR-AR-C", "RefTitle": "RFDZIS00: Exipiring currencies", "RefUrl": "/notes/361608 "}, {"RefNumber": "365531", "RefComponent": "FI-BL-PT-CJ", "RefTitle": "FBCJ: Expiring currencies in the EMU", "RefUrl": "/notes/365531 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "382355", "RefComponent": "PSM-FM-PO-RE", "RefTitle": "IS-PS: Expiring currency in requests", "RefUrl": "/notes/382355 "}, {"RefNumber": "383589", "RefComponent": "PSM-FM-PO-CA", "RefTitle": "IS-PS cash desk: Expiring currencies as of 2002", "RefUrl": "/notes/383589 "}, {"RefNumber": "363522", "RefComponent": "FI", "RefTitle": "Checks of AC interface at end of dual currency phase", "RefUrl": "/notes/363522 "}, {"RefNumber": "359028", "RefComponent": "FI-AP-AP-D", "RefTitle": "Line items: Expiring currencies", "RefUrl": "/notes/359028 "}, {"RefNumber": "444785", "RefComponent": "LO-MD-BP-CM", "RefTitle": "Expiring currency in the customer master", "RefUrl": "/notes/444785 "}, {"RefNumber": "407198", "RefComponent": "FI-AR-AR-G", "RefTitle": "RFKORD80/FBCJ: Expiring currencies (cash document)", "RefUrl": "/notes/407198 "}, {"RefNumber": "355613", "RefComponent": "FI-AR-AR-C", "RefTitle": "RFDUZI00: Expiring currencies", "RefUrl": "/notes/355613 "}, {"RefNumber": "359230", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies in recurring entry original documents", "RefUrl": "/notes/359230 "}, {"RefNumber": "443627", "RefComponent": "FI-GL-GL-D", "RefTitle": "Expiring currencies with drill-down for line items", "RefUrl": "/notes/443627 "}, {"RefNumber": "363211", "RefComponent": "FI-AP-AP-B", "RefTitle": "Trtmnt of expiring currencies for pymt advice notes", "RefUrl": "/notes/363211 "}, {"RefNumber": "368817", "RefComponent": "FI-GL", "RefTitle": "Expiring currencies reporting", "RefUrl": "/notes/368817 "}, {"RefNumber": "350530", "RefComponent": "FI-AR", "RefTitle": "Functions in FI for end of dual currency phase", "RefUrl": "/notes/350530 "}, {"RefNumber": "357028", "RefComponent": "FI-GL-GL-A", "RefTitle": "Automatic clearing and expiring currencies", "RefUrl": "/notes/357028 "}, {"RefNumber": "362277", "RefComponent": "FI-AP", "RefTitle": "Expiring currencies: correspondence", "RefUrl": "/notes/362277 "}, {"RefNumber": "321671", "RefComponent": "CA-EUR-CNV-RE", "RefTitle": "Local currency changeover RE: expiring currencies", "RefUrl": "/notes/321671 "}, {"RefNumber": "455877", "RefComponent": "FI", "RefTitle": "Manual implementation of the 'expiring currencies' functions", "RefUrl": "/notes/455877 "}, {"RefNumber": "360145", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies: Bills of exchange", "RefUrl": "/notes/360145 "}, {"RefNumber": "359818", "RefComponent": "FI", "RefTitle": "Post/clear expiring currencies", "RefUrl": "/notes/359818 "}, {"RefNumber": "362496", "RefComponent": "FI-SL-SL-A", "RefTitle": "Warning message discontinuing currencies GB01/GB11", "RefUrl": "/notes/362496 "}, {"RefNumber": "366042", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies processes FI", "RefUrl": "/notes/366042 "}, {"RefNumber": "357700", "RefComponent": "FI-AR", "RefTitle": "Expiring currencies: Balance confirmations", "RefUrl": "/notes/357700 "}, {"RefNumber": "446606", "RefComponent": "BC-SRV-BSF", "RefTitle": "Expiring currencies: Missing objects in V_TCURO", "RefUrl": "/notes/446606 "}, {"RefNumber": "354720", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110: Expiring currencies", "RefUrl": "/notes/354720 "}, {"RefNumber": "444784", "RefComponent": "LO-MD-BP-VM", "RefTitle": "Expiring currency in vendor master", "RefUrl": "/notes/444784 "}, {"RefNumber": "207127", "RefComponent": "CA-GTF-BS", "RefTitle": "Invalid currencies: Incorrect date", "RefUrl": "/notes/207127 "}, {"RefNumber": "444698", "RefComponent": "CA-GTF-TS", "RefTitle": "Auslaufende Währungen: unterstützte Objekte", "RefUrl": "/notes/444698 "}, {"RefNumber": "355295", "RefComponent": "FI-AR-AR-C", "RefTitle": "Dunning: expiring currencies as of 2002", "RefUrl": "/notes/355295 "}, {"RefNumber": "364157", "RefComponent": "FI-AP", "RefTitle": "Object Type for Expiring Currencies FI", "RefUrl": "/notes/364157 "}, {"RefNumber": "363276", "RefComponent": "FI-BL-PT", "RefTitle": "F111: Expiring currencies", "RefUrl": "/notes/363276 "}, {"RefNumber": "382089", "RefComponent": "FI-AP-AP-B", "RefTitle": "F110: payment run termination with error message F1806", "RefUrl": "/notes/382089 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "31I", "To": "31I", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "40A", "To": "40B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "45A", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46A", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}