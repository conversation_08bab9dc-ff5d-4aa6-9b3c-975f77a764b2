{"Request": {"Number": "1125923", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 349, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000016611982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001125923?language=E&token=6C90AB5494AAB573521538C688774171"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001125923", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001125923/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1125923"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "30.03.2010"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1125923 - Support for Oracle database flashback in BR*Tools"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>1. Introduction<br />=============<br />In patch 6 of BR*Tools 7.10, a support for the Oracle 10g feature \"Flashback database\". This new function encompasses two areas:<br /> - the BRSPACE function \"mfback\" (manage flashback database) to activate and deactivate database flashback and to manage restore points.<br /> - BRRECOVER enhancements in point-in-time recovery of the database and in database reset, that allow you to reset the database to an earlier point in time using database flashback and restore points without reloading the database files.<br /><br />2. Prerequisites and basic information<br />=========================================<br />Database flashback is a new Oracle 10g feature supported by BR*Tools as of Oracle 10.2. The main prerequisite for using it is implementing a flash recovery area. (For more information, see Note 966073.) The new feature is described in detail in Note 966117. The description explains the basic terms (such as flashback logging, normal and guaranteed restore points), further prerequisites and restrictions and the use of Oracle tools, such as SQLPLUS and RMAN.<br />For further information about the database flashback, refer to the Oracle documentation.<br />Caution:<br />After you have installed the flash recovery area and start using Spfile, create a new init&lt;DBSID&gt;.ora:<br />SQL&gt; connect / as sysdba<br />SQL&gt; create pfile from spfile;<br />This is not required if the relevant Oracle parameter changes were performed using the BRSPACE function \"dbparam\".<br /><br />3. BRSPACE function \"mfback\" (manage flashback database)<br />========================================================<br />You can call this new BRSPACE function using the BRTOOLS/BRGUI menu:<br />brtools/brgui -&gt; Restore and recovery -&gt; Manage flashback database<br />or directly using the command line:<br />brspace -f mfback<br />The main menu of this function provides the following actions:<br /> - Switch on flashback database&#x00A0;&#x00A0;&#x00A0;&#x00A0; (action code: \"fbon\")<br /> - Switch off flashback database&#x00A0;&#x00A0;&#x00A0;&#x00A0;(action code: \"fboff\")<br /> - Create restore point&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (action code: \"rpcreate\")<br /> - Drop restore point&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; (action code: \"rpdrop\")<br /> - Show flashback status&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(action code: \"fbshow\")<br /> - Show restore points&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;(action code: \"rpshow\")<br /><br />3.1. Activating the database flashback<br />------------------------------------<br />The action \"fbon\" (Switch on flashback database) activates the database flashback feature. Here, you can determine the requires retention time for the flashback data (flashback retention target).<br /><br />3.2. Deactivating database flashback<br />------------------------------------<br />The action \"fboff\" (Switch off flashback database) deactivates the database flashback feature.<br /><br />3.3. Creating a restore point<br />--------------------------<br />The action \"rpcreate\" (Create restore point) creates a new normal or guaranteed restore point. The restore point name in BR*Tools is restricted to 30 characters. However, for reasons of clarity we recommend that you use shorter names.<br /><br />3.4. Deleting a restore point<br />--------------------------<br />The action \"rpdrop\" (Drop restore point) deletes an existing normal or guaranteed restore point.<br /><br />3.5. Displaying flashback status<br />------------------------------<br />The action \"fbshow\" (Drop restore point) displays the current status of database flashback. The system issues the following information:<br /> - Status of database flashback (on/off)<br /> - Oldest flashback time in flashback logs<br /> - Oldest flashback SCN in flashback logs<br /> - Requires flashback retention time<br /> - Current size of flashback data<br /> - Estimated size of flashback data<br /> - Location of flashback recovery area<br /> - Used space in the flashback recovery area<br /> - Space limit in the flashback recovery area<br /> - Reusable space in the flashback recovery area<br /> - Number of files in the flashback recovery area<br /><br />3.6. Displaying restore points<br />----------------------------<br />The action \"rpshow\" (Show restore points) displays a list of existing restore points. The system issues the following information:<br /> - Name of the restore point<br /> - Type of restore point (guaranteed: yes/no)<br /> - Time of restore point creation<br /> - SCN of restore point creation<br /> - Size of restore point data in the flashback recovery area (only for guaranteed restore points)<br /><br />4. BRRECOVER enhancement for database flashback<br />==================================================<br />The BRRECOVER enhancement concerning the database flashback refers to two recovery scenarios:<br /> - Point-in-time recovery of the database<br /> - Database reset:<br /><br />4.1. Point-in-time recovery of the database with flashback: brrecover -t dbpit<br />-----------------------------------------------------------------------<br />If the database flashback is active, BRRECOVER checks whether the specified recovery time or recovery SCN is in the flashback data, in other words that it is more current or larger than the oldest flashback time or flashback SCN in the flashback logs. If this is the case, BRRECOVER proposes flashback as the method for the for the database point-in-time recovery and, if required, performs it. If you do not want to use flashback, you can select a backup in the relevant menu and execute the point-in-time recovery with the classic method (full restore and forward recovery). You can determine such a backup already in the command line in the option \"-b|-backup &lt;log_name&gt;|last\".<br /><br />4.2. Database reset with flashback:&#x00A0;&#x00A0;brrecover -t reset<br />------------------------------------------------------<br />If restore points are defined, BRRECOVER displays them in the list of appropriate backups and restore points that you can select for the process. You can always use a guaranteed restore point, but you can only use a normal restore point if its point in time or SCN are in the flashback data. When you select a restore point for the database reset, BRRECOVER uses flashback to reset the database to the status it had before the restore point.<br />You can already specify the restore point to be used on the command line in a new BRRECOVER option:<br />-o|-rpt|-point &lt;restore_point&gt;|last<br />where &lt;restore_point&gt; - Name of restore point<br />If you do not want to use flashback but want to perform a classic database reset (full restore), select a backup in the list mentioned above or specify it in the option \"-b|-backup &lt;log_name&gt;|last\".<br />If you have neither selected a restore point nor a backup, BRRECOVER proposes the latest restore point or backup.<br /><br />Important<br />=======<br />Since the use of the database flashback feature is of critical importance for the data consistency, we recommend that you test the planned recovery scenarios in detail before going live.<br />Using database flashback cannot replace regular backups.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Flashback database, BR*Tools, BRSPACE, BRRECOVER</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This is an advance development.<br />A known Oracle error that refers to the flashback functions (bug 7513673) was corrected in a special patch for Oracle ******** (see Notes 1020937 and 1137346). Use this release if you want to use flashback.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Enhanced in BRSPACE 7.10 patch level 6.<br />For more information about downloading patches, see Notes 12741 and 19466.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D000674"}, {"Key": "Processor                                                                                           ", "Value": "D000674"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001125923/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001125923/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117"}, {"RefNumber": "966073", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flash Recovery Area/Fast Recovery Area", "RefUrl": "/notes/966073"}, {"RefNumber": "1060305", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 7.10", "RefUrl": "/notes/1060305"}, {"RefNumber": "1020937", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-38754 for FLASHBACK DATABASE", "RefUrl": "/notes/1020937"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1060305", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Corrections in BR*Tools Version 7.10", "RefUrl": "/notes/1060305 "}, {"RefNumber": "966117", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flashback Database technology", "RefUrl": "/notes/966117 "}, {"RefNumber": "966073", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle Flash Recovery Area/Fast Recovery Area", "RefUrl": "/notes/966073 "}, {"RefNumber": "1020937", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-38754 for FLASHBACK DATABASE", "RefUrl": "/notes/1020937 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}