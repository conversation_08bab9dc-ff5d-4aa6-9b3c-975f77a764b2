{"Request": {"Number": "1917231", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 426, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000011321382017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001917231?language=E&token=622F3A6CC15C8BCB86B9BC793D122ED9"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001917231", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001917231/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1917231"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 7}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.05.2017"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DWB-UTL-INR"}, "SAPComponentKeyText": {"_label": "Component", "value": "Repository Infosystem"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "ABAP Workbench, Java IDE and Infrastructure", "value": "BC-DWB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Workbench Utilities", "value": "BC-DWB-UTL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Repository Infosystem", "value": "BC-DWB-UTL-INR", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DWB-UTL-INR*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1917231 - Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>The where-used list has a long runtime. Database accesses to the table WBCROSSGT take an extremely long time. The report EU_INIT runs for an extremely long time.</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>Class CL_WB_CROSSREFERENCE, job EU_INIT</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>Unfavorable database accesses</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>The database accesses have been optimized. The runtime of the job EU_INIT has been reduced by around 30%. The call of the where-used list has been accelerated.</p>\r\n<p>Import the specified Support Package or implement the corrections contained in this SAP Note.</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D030328)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001917231/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001917231/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2954176", "RefComponent": "BC-UPG-TLS-TLA", "RefTitle": "Error in MAIN_SHDIMP/SUBMOD_SHD2_RUN/XPRAS_SHD_AIMMERGE", "RefUrl": "/notes/2954176 "}, {"RefNumber": "2234970", "RefComponent": "BC-DWB-UTL", "RefTitle": "Job EU_INIT", "RefUrl": "/notes/2234970 "}, {"RefNumber": "1923499", "RefComponent": "SV-SMG-TWB-BCA", "RefTitle": "STATIC or Semi-dynamic TBOM has a very small content", "RefUrl": "/notes/1923499 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "702", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "730", "To": "730", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "731", "To": "731", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "740", "To": "740", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70031", "URL": "/supportpackage/SAPKB70031"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70030", "URL": "/supportpackage/SAPKB70030"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70116", "URL": "/supportpackage/SAPKB70116"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70115", "URL": "/supportpackage/SAPKB70115"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70215", "URL": "/supportpackage/SAPKB70215"}, {"SoftwareComponentVersion": "SAP_BASIS 702", "SupportPackage": "SAPKB70216", "URL": "/supportpackage/SAPKB70216"}, {"SoftwareComponentVersion": "SAP_BASIS 730", "SupportPackage": "SAPKB73011", "URL": "/supportpackage/SAPKB73011"}, {"SoftwareComponentVersion": "SAP_BASIS 731", "SupportPackage": "SAPKB73111", "URL": "/supportpackage/SAPKB73111"}, {"SoftwareComponentVersion": "SAP_BASIS 740", "SupportPackage": "SAPKB74006", "URL": "/supportpackage/SAPKB74006"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "NumberOfCorrin": 13, "URL": "/corrins/0001917231/41"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 13, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 11, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "710", "Number": "1527757 ", "URL": "/notes/1527757 ", "Title": "Deleting multiple-use includes: Where-used list", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "640", "ValidTo": "711", "Number": "1362302 ", "URL": "/notes/1362302 ", "Title": "Performance problems for tables CROSS and WBCROSSI", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "861182 ", "URL": "/notes/861182 ", "Title": "Database deadlocks when updating tables WBCROSSGT or CROSS", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1240187 ", "URL": "/notes/1240187 ", "Title": "Termination in CL_WB_CROSSREFERENCE->SAVE_INDEX, COMMIT WORK", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1355022 ", "URL": "/notes/1355022 ", "Title": "Where-used list incorrect after renaming methods", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1569531 ", "URL": "/notes/1569531 ", "Title": "Deadlock on WBCROSSGT", "Component": "BC-DWB-UTL"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1884418 ", "URL": "/notes/1884418 ", "Title": "Program termination TIME_OUT during execution of EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "700", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "700", "ValidTo": "731", "Number": "1801578 ", "URL": "/notes/1801578 ", "Title": "Program terminates when index set up for where-used list", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1355022 ", "URL": "/notes/1355022 ", "Title": "Where-used list incorrect after renaming methods", "Component": "BC-DWB-TOO"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1884418 ", "URL": "/notes/1884418 ", "Title": "Program termination TIME_OUT during execution of EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "701", "ValidTo": "701", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1884418 ", "URL": "/notes/1884418 ", "Title": "Program termination TIME_OUT during execution of EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "702", "ValidTo": "702", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1884418 ", "URL": "/notes/1884418 ", "Title": "Program termination TIME_OUT during execution of EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "730", "ValidTo": "730", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1882100 ", "URL": "/notes/1882100 ", "Title": "SAPRSLOG: DB locks on table SEDI_NAV_OO_LOCA(I)", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1897870 ", "URL": "/notes/1897870 ", "Title": "Refresh Workbench object type registry in job EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "731", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "731", "ValidTo": "740", "Number": "1884418 ", "URL": "/notes/1884418 ", "Title": "Program termination TIME_OUT during execution of EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1897870 ", "URL": "/notes/1897870 ", "Title": "Refresh Workbench object type registry in job EU_INIT", "Component": "BC-DWB-UTL-BRR"}, {"SoftwareComponent": "SAP_BASIS", "ValidFrom": "740", "ValidTo": "740", "Number": "1917231 ", "URL": "/notes/1917231 ", "Title": "Runtime improvement for where-used list; job EU_INIT, table WBCROSSGT", "Component": "BC-DWB-UTL-INR"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}