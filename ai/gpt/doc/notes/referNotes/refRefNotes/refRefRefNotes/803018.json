{"Request": {"Number": "803018", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 691, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": false, "url": ""}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000803018?language=E&token=67719430210C388C5FE42EF2723EB7BD"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000803018", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000803018/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "803018"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 17}, "Recency": {"_label": "Recency", "key": "", "value": ""}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Release planning information"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "In Process"}, "ReleasedOn": {"_label": "Released On", "value": "15.12.2005"}, "SAPComponentKey": {"_label": "Component", "value": "BC-INS"}, "SAPComponentKeyText": {"_label": "Component", "value": "Installation Tools (SAP Note 1669327)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Installation Tools (SAP Note 1669327)", "value": "BC-INS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-INS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "803018 - Central note for NetWeaver04 High Availability capabilities"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>High Availability Solutions operating SAP NetWeaver are not explicitly supported by SAP, although SAP is taking care that SAP software is capable of being installed and operated in a high available environment.<br /><br /><B>SAP NetWeaver 2004 components are generally released for high availability setups with SAP NetWeaver Support Package Stack 13 and higher, product support requires support by your high availability solution provider.</B><br /><br /><B>The content of this note is grouped in the sections:</B></p> <OL>1. <B>Support and Registration of High Availability Implementation Projects</B></OL> <OL>2. <B>Reference to further documentation on high available NetWeaver'04 implementations</B></OL> <OL>3. <B>Collection of important notes on high availability aspects.</B></OL> <p><B></B></p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>High Availability, HA, Switch Over, SPOF<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>This note is the central note for all SAP NetWeaver'04 high availability aspects and contains important advice, respectively links to other SAP notes dealing with aspects and/or known problems of NetWeaver components in HA environments.<br /><br />Note: For more information about high availability aspects with SAP solutions, see SAP Service Marketplace at <U>http://service.sap.com/ha</U>.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. <B>Support and Registration of High Availability Implementation Projects</B></OL> <OL><OL>a) <B>Implementing one or several SAP NetWeaver'04 components based on Support Package Stack Level 13 or higher:</B></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;High Availability (HA) solutions are offered and supported by our Solution Partners. SAP's role is to ensure that SAP products are capable of being installed and operated in SAP recommended HA setup types. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The configuration and operation of a specific HA solution has to be supported by its vendor. SAP is only giving generic advice, which is documented in the 'SAP WebAS in Switchover Environments'-Guide (see <U>http://service.sap.com/ha</U> --&gt; Media Library --&gt; Documentation --&gt; Switchover), and in SAP NetWeaver High Availability online documentation. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please note the references to further SAP notes regarding single HA topics and known problems at the end of this note. <OL><OL>b) <B>Implementing one or several SAP NetWeaver'04 components based on Support Package Stack Level 12 or lower:</B></OL></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP recommends applying Support Package Stack 13 or higher. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception: <U>This does not apply for implementations based on SAP Web Application Server ABAP only stack and not for dual stack (ABAP and Java) implementations</U>. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Project based support for new implementations on earlier Support Package levels than Support Package Stack 13 has ended. <p></p> <OL>2. <B>Reference to further documentation on high available NetWeaver'04 implementations</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Generally, SAP provides only generic advice regarding the implementation of SAP applications on specific operatings system (OS) and database (DB) platforms. This description can be found in the documentation 'WebAs in Switchover Environments'-Guide at <U>http://service.sap.com/ha</U> --&gt; media library --&gt; documentation --&gt; switchover), and in the SAP online documentation. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Detailed installation documentation for installing into HA Environments has to be requested by your HA solution provider. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Exception: HA installation manuals for installing on MS Windows with MS Cluster Service are provided by SAP, see SAP Service Marketplace at <U>http://service.sap.com/instguidesNW04</U> --&gt; Installation --&gt; SAP Web AS 6.40 SR1 Installation and Related Documentation. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For additional component specific HA related documentation like the Technical Infrastructure Guides or dedicated HA-Guides, see SAP Service Marketplace <U>http://service.sap.com/NW04Doc</U> --&gt; Planning. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can find further up-to-date information on SAP's High Availability site <U>http://service.sap.com/ha</U> . <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;High Availability in SAP Online Documentation: See SAP Help Portal at <U>http://help.sap.com</U> --&gt; documentation --&gt; SAP NetWeaver --&gt; Release SAP NetWeaver '04 --&gt; English --&gt; SAP Library --&gt; SAP NetWeaver --&gt; Solution Lifecycle Management --&gt; High Availability. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Please read the NetWeaver Support Package Stack Guide carefully prior to applying a certain SP Stack in a HA Environment. <p></p> <OL>3. <B>Collection of important notes on high availability aspects.</B></OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you consider to use your HA implementation as target system for the EP Migration tools delivered by SAP, please read SAP Notes 732461 (EP 5.0 to NW04) or 732458 ( EP6/SP2 to NW04) for further details. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See section \"Related notes\" for notes dealing with single HA aspects of SAP NetWeaver'04 components. <p><br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D019168)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000803018/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000803018/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "969660", "RefComponent": "BC-INS-RMP", "RefTitle": "6.40 Patch Collection Installation : Unix", "RefUrl": "/notes/969660"}, {"RefNumber": "965037", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "6.20/6.40 Patch Collection Installation: SAP MaxDB / Win", "RefUrl": "/notes/965037"}, {"RefNumber": "965036", "RefComponent": "BC-INS-UNX", "RefTitle": "6.20/6.40 Patch Collection Installation: SAP MaxDB / UNIX", "RefUrl": "/notes/965036"}, {"RefNumber": "894919", "RefComponent": "BC-JAS-ADM", "RefTitle": "Hochverfügbarkeit für den SAP NetWeaver Administrator", "RefUrl": "/notes/894919"}, {"RefNumber": "886296", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/886296"}, {"RefNumber": "828103", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/828103"}, {"RefNumber": "824579", "RefComponent": "BC-INS", "RefTitle": "KMC Problems in HA setups (on Windows)", "RefUrl": "/notes/824579"}, {"RefNumber": "823742", "RefComponent": "BC-INS-UNX", "RefTitle": "startsap SCS fail during sapinst in HA environment", "RefUrl": "/notes/823742"}, {"RefNumber": "803923", "RefComponent": "EP-SYS-INS", "RefTitle": "installing Portal&KM with separate SCS instance (HA)", "RefUrl": "/notes/803923"}, {"RefNumber": "792910", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/792910"}, {"RefNumber": "790880", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "SAP Web AS 6.40 SR1 Installation on Windows: MaxDB", "RefUrl": "/notes/790880"}, {"RefNumber": "790879", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "SAP Web AS 6.40 SR1 Installation on UNIX: MaxDB", "RefUrl": "/notes/790879"}, {"RefNumber": "787451", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/787451"}, {"RefNumber": "786608", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/786608"}, {"RefNumber": "786224", "RefComponent": "BC-XI", "RefTitle": "SAP XI 3.0 Installation in a MSCS Environment", "RefUrl": "/notes/786224"}, {"RefNumber": "785925", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 ABAP Installation on UNIX", "RefUrl": "/notes/785925"}, {"RefNumber": "785888", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/785888"}, {"RefNumber": "785850", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 Java Installation on UNIX", "RefUrl": "/notes/785850"}, {"RefNumber": "778479", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/778479"}, {"RefNumber": "774116", "RefComponent": "BC-XI", "RefTitle": "SAP XI 3.0 Installation in HA environments", "RefUrl": "/notes/774116"}, {"RefNumber": "768727", "RefComponent": "BC-OP-LNX", "RefTitle": "Automatic restart functions in sapstart for processes", "RefUrl": "/notes/768727"}, {"RefNumber": "766765", "RefComponent": "EP-SYS-INS", "RefTitle": "Central Note-SAP EP 6.0 on Web AS 6.40 Support Release 1 SR1", "RefUrl": "/notes/766765"}, {"RefNumber": "757692", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/757692"}, {"RefNumber": "732461", "RefComponent": "EP-SYS-UPG", "RefTitle": "Migration of EP & KM from 5.0 to NW '04 - Central Note", "RefUrl": "/notes/732461"}, {"RefNumber": "732458", "RefComponent": "EP-PIN-MIG-EP6", "RefTitle": "Central Note - Migration Enterprise Portal 6.0 SP2 to NW '04", "RefUrl": "/notes/732458"}, {"RefNumber": "711093", "RefComponent": "BC", "RefTitle": "Release Restriction Note for Web AS 6.40", "RefUrl": "/notes/711093"}, {"RefNumber": "697535", "RefComponent": "BC-INS", "RefTitle": "Add. Info. on applying Support Packages (Web AS Java 6.40)", "RefUrl": "/notes/697535"}, {"RefNumber": "538081", "RefComponent": "BC-CST-SL", "RefTitle": "High-availability SAPLICENSE", "RefUrl": "/notes/538081"}, {"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843"}, {"RefNumber": "1404751", "RefComponent": "EP-SYS-INS", "RefTitle": "NW04 Portal installation on High Availability system fails", "RefUrl": "/notes/1404751"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "527843", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle RAC support in the SAP environment", "RefUrl": "/notes/527843 "}, {"RefNumber": "965037", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "6.20/6.40 Patch Collection Installation: SAP MaxDB / Win", "RefUrl": "/notes/965037 "}, {"RefNumber": "697535", "RefComponent": "BC-INS", "RefTitle": "Add. Info. on applying Support Packages (Web AS Java 6.40)", "RefUrl": "/notes/697535 "}, {"RefNumber": "965036", "RefComponent": "BC-INS-UNX", "RefTitle": "6.20/6.40 Patch Collection Installation: SAP MaxDB / UNIX", "RefUrl": "/notes/965036 "}, {"RefNumber": "785850", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 Java Installation on UNIX", "RefUrl": "/notes/785850 "}, {"RefNumber": "785925", "RefComponent": "BC-INS-UNX", "RefTitle": "SAP Web AS 6.40 SR1 ABAP Installation on UNIX", "RefUrl": "/notes/785925 "}, {"RefNumber": "538081", "RefComponent": "BC-CST-SL", "RefTitle": "High-availability SAPLICENSE", "RefUrl": "/notes/538081 "}, {"RefNumber": "711093", "RefComponent": "BC", "RefTitle": "Release Restriction Note for Web AS 6.40", "RefUrl": "/notes/711093 "}, {"RefNumber": "766765", "RefComponent": "EP-SYS-INS", "RefTitle": "Central Note-SAP EP 6.0 on Web AS 6.40 Support Release 1 SR1", "RefUrl": "/notes/766765 "}, {"RefNumber": "768727", "RefComponent": "BC-OP-LNX", "RefTitle": "Automatic restart functions in sapstart for processes", "RefUrl": "/notes/768727 "}, {"RefNumber": "790879", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "SAP Web AS 6.40 SR1 Installation on UNIX: MaxDB", "RefUrl": "/notes/790879 "}, {"RefNumber": "790880", "RefComponent": "BC-DB-SDB-INS", "RefTitle": "SAP Web AS 6.40 SR1 Installation on Windows: MaxDB", "RefUrl": "/notes/790880 "}, {"RefNumber": "1404751", "RefComponent": "EP-SYS-INS", "RefTitle": "NW04 Portal installation on High Availability system fails", "RefUrl": "/notes/1404751 "}, {"RefNumber": "732461", "RefComponent": "EP-SYS-UPG", "RefTitle": "Migration of EP & KM from 5.0 to NW '04 - Central Note", "RefUrl": "/notes/732461 "}, {"RefNumber": "732458", "RefComponent": "EP-PIN-MIG-EP6", "RefTitle": "Central Note - Migration Enterprise Portal 6.0 SP2 to NW '04", "RefUrl": "/notes/732458 "}, {"RefNumber": "789188", "RefComponent": "BC-INS-AS4", "RefTitle": "INST: SAP Web AS 6.40 SR1 on IBM eServer iSeries", "RefUrl": "/notes/789188 "}, {"RefNumber": "803923", "RefComponent": "EP-SYS-INS", "RefTitle": "installing Portal&KM with separate SCS instance (HA)", "RefUrl": "/notes/803923 "}, {"RefNumber": "774116", "RefComponent": "BC-XI", "RefTitle": "SAP XI 3.0 Installation in HA environments", "RefUrl": "/notes/774116 "}, {"RefNumber": "824579", "RefComponent": "BC-INS", "RefTitle": "KMC Problems in HA setups (on Windows)", "RefUrl": "/notes/824579 "}, {"RefNumber": "786224", "RefComponent": "BC-XI", "RefTitle": "SAP XI 3.0 Installation in a MSCS Environment", "RefUrl": "/notes/786224 "}, {"RefNumber": "823742", "RefComponent": "BC-INS-UNX", "RefTitle": "startsap SCS fail during sapinst in HA environment", "RefUrl": "/notes/823742 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_BASIS", "From": "640", "To": "640", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}