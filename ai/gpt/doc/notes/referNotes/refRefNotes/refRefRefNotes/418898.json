{"Request": {"Number": "418898", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 497, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015037982017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000418898?language=E&token=90DA553DC70570C772E9A5408ACB0842"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000418898", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000418898/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "418898"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 28}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.09.2015"}, "SAPComponentKey": {"_label": "Component", "value": "FI-AP-AP-B1"}, "SAPComponentKeyText": {"_label": "Component", "value": "Payment transfer (w/o DE,  US)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Financial Accounting", "value": "FI", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Accounts Payable", "value": "FI-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basic Functions", "value": "FI-AP-AP", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment transfer (w/o DE, US)", "value": "FI-AP-AP-B1", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'FI-AP-AP-B1*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "418898 - Foreign payments with LUM2 format, Finland"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>Amendment of existing DME solution LUM2 for payments in foreign currency:</p>\r\n<ul>\r\n<li>Enhancement of the LUM2 format, to enable it for the payment order functionality</li>\r\n</ul>\r\n<ul>\r\n<li>Clearing of payments in foreign currency using the exchange rates given in the electronic bank statement</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RFFOFI_U, RFEBFILUM00, payment order, DMEE</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>New and changed requirements</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>Creating the payment file</strong></p>\r\n<p>The new solution replaces the program RFFOFI_U. It works using the Payment Medium Workbench (PMW) and the DME Engine. Note that with the DME Engine, changes to the file layout can be done via Customizing, and no code changes are required.<br />In order for the Payment Program to support format LUM2, there are a number of Customizing settings that are required. Some of the settings are provided for you, some of them you have to make yourself. The standard system comes complete with the following:</p>\r\n<ul>\r\n<li>A Payment Medium Workbench format, FI_LUM2, which selects the payment data required by format LUM2</li>\r\n</ul>\r\n<ul>\r\n<li>A DME Engine format, FI_LUM2, which formats the payment data in LUM2 format You can either use these formats themselves or make copies of them.</li>\r\n</ul>\r\n<p>You now have to make a number of settings, all under Customizing for Financial Accounting by choosing \"Settings for Payment Medium Formats from Payment Medium Workbench:\"</p>\r\n<ol>1. Adjust Note to Payee</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In this activity, define a Note to Payee to be used with payment orders. The LUM2 format allows for up to four fields in the note to payee. When you run the payment program, the system fills these fields with various information for your vendors, depending on your company's requirements.</p>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;The example below creates a note to payee with the reference document number in the first line and the line item text in the second line.</p>\r\n<div class=\"table-responsive\"><table class=\"table table-bordered table-striped col-resizeable\">\r\n<tbody>\r\n<tr><th>Type&#160;&#160; Line no.&#160;&#160;&#160;&#160;Segm.Text&#160;&#160;&#160;&#160; Note to payee text</th></tr>\r\n<tr>\r\n<td>1&#160;&#160;&#160;&#160;&#160;&#160;1&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;No. &amp;FPAYP-XBLNR&amp;</td>\r\n</tr>\r\n<tr>\r\n<td>1&#160;&#160;&#160;&#160;&#160;&#160;2&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;x&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160; &lt; item text &gt;</td>\r\n</tr>\r\n</tbody>\r\n</table></div>\r\n<ol>2. Assign Payment Medium Format and Note to Payee to Payment Method in Country</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;In this activity, you specify that the payment method you use for foreign payments is to use the PMW format and the note to payee that you have created.</p>\r\n<ol><ol>a) Double-click the payment method that you use for foreign payments.</ol></ol><ol><ol>b) In the Payment medium group box, select Use payment medium workbench and enter the format (in the standard system, FI_LUM2).</ol></ol><ol><ol>c) Double-click Note to Payee by Origin and enter the note to payee that goes with the payment method.</ol></ol><ol>3. Create/Assign Selection Variants</ol>\r\n<p>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Create a selection variant for the program. The Payment Program uses this variant to create the payment media for foreign payment<br /><strong>Clearing of Foreign Payments by Using Electronic Bank Statement</strong></p>\r\n<p>Program RFOFILUM00 converts the LUM2 file received from the bank into MultiCash format. Once you have converted it, you can process the information using the Electronic Bank Statement Program.<br />You must add a new function module to the Electronic Bank Statement program to enable it post payments in foreign currency from a bank account kept in a currency different from your house currency. For sample code, see Note 418855.</p>\r\n<p><strong>SAPSERV transports</strong></p>\r\n<p>You will find a transport request on the SAPServ in directory<br />/general/R3server/abap/note.0418898<br />Import first transport request P9CK178586, before you import transport request P9CK195320.<br /><br />With importing these transports, it might happens on earlier patch levels, that program RFEBFILUM00 can not be activated, since table FEBPDO can not be activated.<br />Reason for this are new fields in table FEBPDO, where only the domain of the Finnish specific field FORMT_EBCST is part of the transports on the SAPSERV. The domaine of the other new fields PROCESS_CSB and X_HISTORY are not included.<br />You can either assign the two fields PROCESS_CSB and X_HISTORY to an existing 1 Character domain, or delete these two fields from the structure.<br /><br />For further information concerning SAPSERV transports see attached note 13719<br /><br /></p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-CSC-XX (Please use FI-LOC-I18 for I18N and cross-country issues)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON><PERSON> (D021573)"}, {"Key": "Processor                                                                                           ", "Value": "I313886"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000418898/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "486672", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 Format Finland, incorrect currency amounts", "RefUrl": "/notes/486672"}, {"RefNumber": "442212", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 Format Finland, selection of method of payment", "RefUrl": "/notes/442212"}, {"RefNumber": "433419", "RefComponent": "FI-AP-AP-B1", "RefTitle": "DMEE: Incorrect display of currency amounts", "RefUrl": "/notes/433419"}, {"RefNumber": "418855", "RefComponent": "FI-AP-AP-B1", "RefTitle": "RFEBFILUM00 - Posting in foreign currency Finland", "RefUrl": "/notes/418855"}, {"RefNumber": "2168103", "RefComponent": "FI-AP-AP-B1", "RefTitle": "Collective note for payment format: FI_LUM2", "RefUrl": "/notes/2168103"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "418855", "RefComponent": "FI-AP-AP-B1", "RefTitle": "RFEBFILUM00 - Posting in foreign currency Finland", "RefUrl": "/notes/418855 "}, {"RefNumber": "442212", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 Format Finland, selection of method of payment", "RefUrl": "/notes/442212 "}, {"RefNumber": "486672", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 Format Finland, incorrect currency amounts", "RefUrl": "/notes/486672 "}, {"RefNumber": "586078", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 format Finland, incorrect invoice type and instruction", "RefUrl": "/notes/586078 "}, {"RefNumber": "599798", "RefComponent": "FI-AP-AP-B1", "RefTitle": "LUM2 format Finland, errors in DMEE format FI_LUM2", "RefUrl": "/notes/599798 "}, {"RefNumber": "433419", "RefComponent": "FI-AP-AP-B1", "RefTitle": "DMEE: Incorrect display of currency amounts", "RefUrl": "/notes/433419 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_APPL 45B", "SupportPackage": "SAPKH45B45", "URL": "/supportpackage/SAPKH45B45"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C23", "URL": "/supportpackage/SAPKH46C23"}, {"SoftwareComponentVersion": "SAP_APPL 46C", "SupportPackage": "SAPKH46C26", "URL": "/supportpackage/SAPKH46C26"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}