{"Request": {"Number": "911049", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2285, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000005260932017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000911049?language=E&token=5E40AF886AC4F9496EA291FF720519F5"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000911049", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000911049/D/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "911049"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 3}, "Recency": {"_label": "Currentness", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Advance development"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10.01.2006"}, "SAPComponentKey": {"_label": "Component", "value": "XX-CSC-AT-IS-H"}, "SAPComponentKeyText": {"_label": "Component", "value": "Hospital"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Country/Region-Specific Developments", "value": "XX-CSC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Austria", "value": "XX-CSC-AT", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Industry-specific component", "value": "XX-CSC-AT-IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "XX-CSC-AT-IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-CSC-AT-IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "911049 - IS-H AT: Add \\&quot;Without IR/SI\\&quot; to Report RNWATKUELIST"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=911049&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/911049/D\" target=\"_blank\">/notes/911049/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>This SAP Note is relevant only for the country version Austria (AT).<br />The Austria-specific report IS-H AT: Insurance Verification Evidence List (RNWATKUELIST) only processes insurance verification requests. You cannot list cases without insurance relationships.</p><h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3><p>Insurance verification, RNWATKUELIST</p><h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3><p>Advance development<br />Prerequisites:</p> <UL><LI>For IS-H Version 4.63B AOP 01 - 29</LI></UL> <UL><LI>For IS-H Version 4.72 AOP 01 - 13</LI></UL> <UL><LI>For IS-H Version 6.00 AOP 01 - 03</LI></UL><h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3><OL>1. See the source code corrections.</OL> <p><br />After you implement the source code corrections, you must perform the following manual tasks.<br />Note that passages for third-party payers only apply as of IS-H Version 4.72.</p> <OL>2. Implement the attached attachment as follows:</OL> <p>              Unpack the attached files RNWATKUELIST_Doku.zip, RNWATKUELIST_Selektionstexte.zip, and RNWATKUELIST_TextSymbole.zip. <p>              Note that you cannot download the attached files using OSS, but only from SAP Service Marketplace (see also SAP Notes 480180 and 13719 for information about importing attachments). <OL>3. Adjust the selection texts of the report RNWATKUELIST:</OL> <UL><UL><LI>Go to transaction SE38.</LI></UL></UL> <UL><UL><LI>In the &quot;Program&quot; input field, enter the value RNWATKUELIST. Choose &quot;Display&quot;.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Selection Texts&quot;.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;. Change the selection texts of the report in accordance with the description in the unpacked file RNWATKUELIST_Selection Text.doc.</LI></UL></UL> <UL><UL><LI>Save the changes actively.</LI></UL></UL> <OL>4. Adjust the text symbols of the report RNWATKUELIST:</OL> <UL><UL><LI>Go to transaction SE38.</LI></UL></UL> <UL><UL><LI>In the &quot;Program&quot; input field, enter the value RNWATKUELIST. Choose &quot;Display&quot;.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Goto -> Text Elements -> Text Symbols&quot;.</LI></UL></UL> <UL><UL><LI>Choose &quot;Change&quot;. Change the text symbols of the report as described in the unpacked file RNWATKUELIST_TextSymbole.doc.</LI></UL></UL> <UL><UL><LI>Save the changes actively.</LI></UL></UL> <OL>5. Adjust the documentation of the report RNWATKUELIST:</OL> <UL><UL><LI>Call transaction SE61.</LI></UL></UL> <UL><UL><LI>Select the document class &quot;Report, Function Group, Logical DB&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;Report/Module Pool&quot; input field, enter the value RNWATKUELIST. Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>Delete all previous documentation for the report.</LI></UL></UL> <UL><UL><LI>In the menu bar, choose &quot;Document -> Upload&quot; and then the format &quot;ITF&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;ITF File&quot; input field, enter the unpacked file RNWATKUELIST_Doku.itf. Click on &#39;Transfer&#39; button.</LI></UL></UL> <UL><UL><LI>Save the change as active.</LI></UL></UL> <OL>6. Create the following new message:</OL> <UL><UL><LI>Go to transaction SE91.</LI></UL></UL> <UL><UL><LI>Enter the value NV in the &quot;Message class&quot; input field and the value 404 in the &quot;Number&quot; input field. Choose &quot;Change&quot;.</LI></UL></UL> <UL><UL><LI>In the &quot;Message Short Text&quot; field, enter the text &quot;For &quot;Only Cases Without IR/SI&quot;, you cannot restrict the insurance provider&quot;.</LI></UL></UL> <UL><UL><LI>Set the &quot;Self-Explanatory&quot; indicator.</LI></UL></UL> <UL><UL><LI>Save the change as active.</LI></UL></UL> <p><br />See also the information in the new documentation for the IS-H AT: Insurance Verification Evidence List (RNWATKUELIST) report.<br />You can now display cases without insurance relationships or cases without social insurance relationships.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "IS-H (Hospital)"}, {"Key": "Owner                                                                                    ", "Value": "<PERSON> (C5025082)"}, {"Key": "Processor                                                                                          ", "Value": "<PERSON> (C5025082)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000911049/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "RNWATKUELIST_Doku.zip", "FileSize": "2", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440692005&iv_version=0003&iv_guid=C99D177502F99840B637F8E737B96BD8"}, {"FileName": "RNWATKUELIST_Textsymbole.zip", "FileSize": "361", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440692005&iv_version=0003&iv_guid=B0E59F28C6DFA64A8E9183D5FC9DE63E"}, {"FileName": "RNWATKUELIST_Selektionstexte.zip", "FileSize": "367", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000440692005&iv_version=0003&iv_guid=D92AD5C5EBAB6A4F9B9FC8C2FE8E2B3B"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "IS-H", "From": "463B", "To": "463B", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "472", "To": "472", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "IS-H 463B", "SupportPackage": "SAPKIPHD30", "URL": "/supportpackage/SAPKIPHD30"}, {"SoftwareComponentVersion": "IS-H 472", "SupportPackage": "SAPKIPHF14", "URL": "/supportpackage/SAPKIPHF14"}, {"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60004INISH", "URL": "/supportpackage/SAPK-60004INISH"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 3, "URL": "/corrins/0000911049/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisite", "value": 4, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "792097 ", "URL": "/notes/792097 ", "Title": "IS-H AT: Insurance Verification - Performance IV Document List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "844919 ", "URL": "/notes/844919 ", "Title": "IS-H AT: Error in Insurance Verification Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "463B", "ValidTo": "463B", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "792097 ", "URL": "/notes/792097 ", "Title": "IS-H AT: Insurance Verification - Performance IV Document List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "844919 ", "URL": "/notes/844919 ", "Title": "IS-H AT: Error in Insurance Verification Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "472", "ValidTo": "472", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "879321 ", "URL": "/notes/879321 ", "Title": "IS-H AT: Insurance Verification - Evidence List", "Component": "XX-CSC-AT-IS-H"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "885633 ", "URL": "/notes/885633 ", "Title": "IS-H AT: Insurance Verification Evidence List for Rejections", "Component": "XX-CSC-AT-IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=911049&TargetLanguage=EN&Component=XX-CSC-AT-IS-H&SourceLanguage=DE&Priority=03\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/911049/D\" target=\"_blank\">/notes/911049/D</a>."}}}}