{"Request": {"Number": "1647554", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 2297, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "D", "_masterLanguage": "D", "_loadedLanguage": "D", "_machineTranslationLanguage": "E", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Acknowledge", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "_type": "EVENT", "_visible": true}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000009784162017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001647554?language=E&token=C162AFC965ADFE413472935F4E1282B7"}, "ShareByEmail": {"_label": "Share via Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001647554", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": false, "url": ""}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1647554"}, "Type": {"_label": "Type", "value": "SAP-Hinweis"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Currentness", "key": "NEW", "value": "New"}, "Language": {"_label": "Language", "value": "German"}, "MasterLanguage": {"_label": "Original Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with low priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.11.2015"}, "SAPComponentKey": {"_label": "Component", "value": "RE-FX-LC-CH"}, "SAPComponentKeyText": {"_label": "Component", "value": "Switzerland"}, "SAPComponentPath": [{"_label": "Services &amp; Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs &amp; Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Real Estate Management", "value": "RE", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Flexible Real Estate Management", "value": "RE-FX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Real Estate Localization", "value": "RE-FX-LC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Switzerland", "value": "RE-FX-LC-CH", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'RE-FX-LC-CH*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1647554 - National Register of Buildings and Dwellings (GWR)"}, "LongText": {"_label": "Description", "value": "<div id=\"DISCLAIMER\" class=\"sapMMsgStrip sapMMsgStripWarning sapUiSmallMarginTop sapUiMediumMarginBottom\"><div class=\"sapMMsgStripMessage\"><span dir=\"auto\" class=\"sapMText sapUiSelectable sapMTextMaxWidth\" style=\"text-align: left;\"><strong>ATTENTION!</strong> This is a machine-translated document that has not been reviewed for accuracy. To provide feedback regarding the machine translation, please <a style=\"color:var(--sapLinkColor);\" href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1647554&TargetLanguage=EN&Component=RE-FX-LC-CH&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.<br/>You can access the original document via the following link: <a style=\"color:var(--sapLinkColor);\" href=\"/notes/1647554/D\" target=\"_blank\">/notes/1647554/D</a>.</span></div></div><div><div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>According to the register harmonization legislation enacted, the cantons and municipalities must harmonize their registers by 2010. This includes the introduction of identifiers such as the new 13-digit AHV insurance number as well as recording a minimum set of mandatory attributes in the register of residents. It also includes allocation of national building and dwelling identifiers (EGID / EWID) to the people listed in this register.<br /><br />In view of the future census, the municipalities deliver data to the Federal Statistics Office (BFS), so they are able to determine the households by the assignment of the national dwelling identifier to a person in the register of residents.<br /><br />At the same time, a logical dwelling number is being introduced, which will be hold in the National Register of Buildings and Dwellings (GWR) and by real estate agencies. This number will be mandatory for each relocation declared to the residents&#39; registry office (EWR). Doing so, the data quality of the EWR can be assured.<br /><br />The following legal requirements were given to the cantons:</p>\r\n<ul>\r\n<li>An administrative dwelling number needs to be assigned to every existing dwelling in the canton, which follows the directives of the Federal Office of Statistics.</li>\r\n</ul>\r\n<ul>\r\n<li>The administrative dwelling numbers must be noted on all new rental and purchase agreements, as well as on reporting forms. These numbers must not be changed with a change in ownership or management of the property.</li>\r\n</ul>\r\n<ul>\r\n<li>The dwelling numbers are performed by administrative authorities in the GWR and are made available to the residents&#39; register office for the purpose of assignment of persons to dwellings.</li>\r\n</ul>\r\n<ul>\r\n<li>The initial allocation of EWID to residents (so-called initial survey) wants to be carried out by the Swiss Post Office on behalf of the cantonal administration and on the basis of the framework agreements prepared for this purpose by the Federal Office of Statistics.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Weitere Begriffe\">Other Terms</h3>\r\n<p>RE-FX CH, building and flat register, GWR</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Ursache und Voraussetzungen\">Reason and Prerequisites</h3>\r\n<p>Leagual Change</p>\r\n<h3 data-toc-skip class=\"section\" id=\"L&ouml;sung\">Solution</h3>\r\n<p>Implement the support package indicated.</p></div></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Owner                                                                                    ", "Value": "C5011197"}, {"Key": "Processor                                                                                          ", "Value": "C5011197"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001647554/D"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "MIME Type", "URL": "URL"}, "Items": [{"FileName": "GWR.rar", "FileSize": "94", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000492022011&iv_version=0004&iv_guid=0E22578D6F3E604DAE922566316DC502"}, {"FileName": "GWR1.rar", "FileSize": "60", "MimeType": "application/octet-stream", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012003146900000492022011&iv_version=0004&iv_guid=59D4939D2E10F946A52D10F86AD1EA47"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document references", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "New archiving tool SAPCAR", "RefUrl": "/notes/212876"}, {"RefNumber": "1728580", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Request from register harmonization, GEAK and land register", "RefUrl": "/notes/1728580"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "Managing the risks when using .SAR/.CAR files", "RefUrl": "/notes/1318389"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "URL"}, "Items": [{"RefNumber": "1728580", "RefComponent": "RE-FX-LC-CH", "RefTitle": "Request from register harmonization, GEAK and land register", "RefUrl": "/notes/1728580 "}, {"RefNumber": "212876", "RefComponent": "BC-INS", "RefTitle": "New archiving tool SAPCAR", "RefUrl": "/notes/212876 "}, {"RefNumber": "1318389", "RefComponent": "BC-CTS", "RefTitle": "Managing the risks when using .SAR/.CAR files", "RefUrl": "/notes/1318389 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Advance transports to customer (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And the following"}, "Items": [{"SoftwareComponent": "EA-APPL", "From": "604", "To": "604", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "606", "To": "606", "Subsequent": ""}, {"SoftwareComponent": "EA-APPL", "From": "616", "To": "616", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software-Komponentenversion", "SupportPackage": "Support Package", "URL": "URL"}, "Items": [{"SoftwareComponentVersion": "EA-APPL 604", "SupportPackage": "SAPK-60413INEAAPPL", "URL": "/supportpackage/SAPK-60413INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60511INEAAPPL", "URL": "/supportpackage/SAPK-60511INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 605", "SupportPackage": "SAPK-60510INEAAPPL", "URL": "/supportpackage/SAPK-60510INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 606", "SupportPackage": "SAPK-60607INEAAPPL", "URL": "/supportpackage/SAPK-60607INEAAPPL"}, {"SoftwareComponentVersion": "EA-APPL 616", "SupportPackage": "SAPK-61602INEAAPPL", "URL": "/supportpackage/SAPK-61602INEAAPPL"}]}, "SupportPackagePatch": {"_label": "Support Packages and Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "URL"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "URL"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": "\r\n<p><code><br />\r\n<br />\r\n------------------------------------------------------------------------<br />\r\n|Manual Activity                                                    |<br />\r\n------------------------------------------------------------------------<br />\r\n|VALID FOR                                                            |<br />\r\n|Software Component   EA-APPL                        SAP R/3 Enterpr...|<br />\r\n| Release 604          SAPK-60401INEAAPPL - SAPK-60412INEAAPPL         |<br />\r\n| Release 605          SAPK-60503INEAAPPL - SAPK-60509INEAAPPL         |<br />\r\n| Release 606          SAPK-60601INEAAPPL - SAPK-60606INEAAPPL         |<br />\r\n------------------------------------------------------------------------<br />\r\n<br />\r\n<b>Caution</b>: You must perform this manual activity separately in each system into which you import the SAP Note to be implemented.<br />\r\n<br />\r\nRefer to the attachment for the following additional information about the SAP Note and maintain this data in your system:<br />\r\n<br />\r\n1. Maintenance by SAP Basis<br />\r\n<b>-------------------------</b><br />\r\n<b>.RAR Document Description</b><br />\r\nGWR GWR Data element DE Translation Data element<br />\r\nGWR GWR Messages DE Translation Messages<br />\r\nGWR1 GWR System table land register<br />\r\n<br />\r\n2. Maintenance by SAP consultants<br />\r\n---------------------------<br />\r\n<b>.RAR Document Description</b><br />\r\nGWR GWR BDT Building BDT Settings Building<br />\r\nGWR GWR BDT Land BDT Settings Land<br />\r\nGWR GWR BDT Country Register BDT Settings Land Register<br />\r\nGWR GWR BDT Rental Object BDT Settings Rental Object<br />\r\n<br /></code></p>\r\n<code><br /></code>"}, "CorrectionsInfo": {"_label": "Information about correction instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 1, "state": "Error"}, "Prerequisites": {"_label": "Prerequisite", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisite", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "URL", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated as helpful", "Helpful-Yes": "0 persons"}, "RatingQuality": {"_label": "Quality Assessment"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document eliminates side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}, "SideEffectsSolving": {"_label": "This document causes side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "URL"}, "Items": []}}, "DisclaimerForMachineTranslation": {"Header": "ATTENTION!", "Message": "This is a machine-translated document that has not been reviewed for accuracy.", "Survey": "To provide feedback regarding the machine translation, please <a href=\"https://sapinsights.eu.qualtrics.com/jfe/form/SV_6nI2C1MOwEviVQG?NoteNumber=1647554&TargetLanguage=EN&Component=RE-FX-LC-CH&SourceLanguage=DE&Priority=04\" target=\"_blank\">click here</a>.", "Request": "If the quality of the machine translation is not sufficient for your purposes, click here to request a revised version.", "Origin": "You can access the original document via the following link: <a href=\"/notes/1647554/D\" target=\"_blank\">/notes/1647554/D</a>."}}}}