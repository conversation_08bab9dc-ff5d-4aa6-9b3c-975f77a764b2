{"Request": {"Number": "541538", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 342, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015261372017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000541538?language=E&token=492C60FEF6B6A598C17B10B211508DDF"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000541538", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000541538/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "541538"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 80}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "04.11.2013"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA-DBA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Database Administration"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Administration", "value": "BC-DB-ORA-DBA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA-DBA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "541538 - FAQ: Reorganization"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ol>1. What is reorganization?</ol><ol>2. Which objects can I reorganize?</ol><ol>3. In which situations is reorganization useful?</ol><ol>4. In which situations does reorganization not make sense?</ol><ol>5. Which tools are used in reorganization?</ol><ol>6. How does an online reorganization with BRSPACE work?</ol><ol>7. What are the advantages and disadvantages of online reorganizations?</ol><ol>8. Can the SAP application run during the reorganization?</ol><ol>9. What runtimes should I expect for a reorganization?</ol><ol>10. How can I optimize the performance of a reorganization?</ol><ol>11. How can I determine how many rows of a table have already been exported or imported?</ol><ol>12. To save time, can I release the R/3 system even if indexes are still being created during reorganization?</ol><ol>13. What do I have to do if a restore is required?</ol><ol>14. Can I use a target tablespace with the name of the source tablespaces during an online reorganization?</ol><ol>15. What do I need to remember with regard to TABART changes?</ol><ol>16. What error scenarios are possible in the context of reorganizations?</ol><ol>17. Is it possible that performance deteriorates after an online reorganization?</ol><ol><ol>18. Where can I find further information about reorganization?</ol></ol>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>FAQ, frequently asked questions</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<ol>1. What is reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The term reorganization refers to the reconstruction of objects in the database. A distinction is made between offline and online reorganization:</p>\r\n<ul>\r\n<li>Offline reorganization</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It is not always possible to access the objects during an offline reorganization. In general, it is therefore necessary to close the SAP system during the reorganization.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;During an offline reorganization, data is exported from the database into an export dump file. The objects are then set up again based on the export dump.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of BRSPACE Patch 7.00 (28) or 7.10 (4), you can also perform an offline reorganization based on ALTER TABLE MOVE for tables without LONG and LONG RAW columns (SAP Note 1080376). In this case, the table is restructured without an export or import being required.</p>\r\n<ul>\r\n<li>Online reorganization</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can always access the affected segments during the reorganization. In general, the SAP system can therefore be run in parallel to the reorganization.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;By default, online reorganizations are carried out based on the Oracle package DBMS_REDEFINITION. This means that no access locks occur when segments are copied in the database.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Non-SAP tools sometimes execute online reorganizations based on a different system (for example, the contents of the Redo log).</p>\r\n<ol>2. Which objects can I reorganize?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following objects can be reorganized:</p>\r\n<ol><ol>a) Tables: Individual tables or groups of tables can be reorganized, and restructured in the same or a new tablespace. The corresponding indexes are also set up implicitly.</ol></ol><ol><ol>b) Indexes: The reconstruction of indexes is not a reorganization in the actual sense. Instead, commands such as DROP / CREATE, REBUILD or COALESCE can be used to set up an index again. For more information, refer to Note 332677. For this reason, this note does not deal with the rebuilding of indexes.</ol></ol><ol><ol>c) Tablespaces: If all segments in a tablespace are reorganized, the tablespace itself can also be modified at the same time (for example, it can be made smaller or its data file structure can be adjusted).</ol></ol><ol>3. In which situations is reorganization useful?</ol>\r\n<ul>\r\n<li>Tablespace reorganizations can be useful in the following situations:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Activating Oracle features such as LMTS or ASSM: To activate features such as LMTS (Note 214995) and ASSM (Note 620803), it is necessary to completely restructure the tablespaces in a reorganization.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Corrupt block in the freespace area: If DBV reports a corrupt block in accordance with SAP Note 354293 in the freespace area of a data file, you can remove this block using a tablespace reorganization (including data files).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Incorrectly created data file: If you created an unnecessary data file or a data file with an incorrect size by mistake, you cannot simply delete this file with Oracle 9i or lower (even if it does not contain yet any data). Apart from resizing it, your only other option is to reorganize the tablespace.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Recovering disk space: If a tablespace contains a lot of freespace that is never used, you can reduce the size of the tablespace with a tablespace reorganization so that space is recovered on the hard disk.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changing the number of data files: Tablespace reorganizations may be useful in certain cases to increase the number of the data files to avoid inode lock-problems (see Note 793113), or to reduce the number of data files to avoid long BEGIN BACKUP runtimes (see Note 875477).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Conversion to the new tablespace layout: If you decide that it makes sense to convert to the new tablespace layout in a certain case (Note 355771), you can carry out this conversion by reorganizing the corresponding tablespaces.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>The reorganization of tables can be useful under the following circumstances:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Hot spots at disk level: If certain hard disk areas are accessed much more often than other areas as a result of poor data distribution, which then leads to performance problems due to the higher load, it can help to relocate certain tables using a reorganization. However, before doing this, you must carefully check the Oracle I/O configuration in accordance with Note 793113.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Low table fill level: If blocks of tables are largely empty (for example, because data was archived and deleted earlier), this means that an unnecessary number of blocks are read to the Oracle buffer for a small amount of data. In addition, the table occupies more space on the hard disk than necessary. In this case, reorganizing the table can regain free space in the tablespace, and reduce the number of blocks that need to be read to the buffer. For more information, see Note 821687.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changing the storage parameters of blocks that were already allocated: If you want to change memory parameters such as INITRANS (Note 84348) or FREELIST_GROUPS (Note 619188), you can only do so within a reorganization process.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Moving segments to another tablespace: You can only move segments to another tablespace by carrying out a reorganization. This is useful, for example, if you want to store large tables in a separate tablespace.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reducing chained or migrated rows: If a table contains an unnecessary number of chained or migrated rows, these can be reduced by reorganization. Note, however, that many tables with long records (for example, tables based on LONG and LONG RAW columns) generally contain chained rows. In these cases, a reorganization does not cause a reduction.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Reducing extents: If tables were created in DMTS with a large number of extents (more than 1000), you should create these tables again with a lower number of extents to avoid unforeseen ST enqueue problems (see Note 787533).</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Converting from LONG columns to LOB columns (Oracle 10 g or higher): As of Oracle 10g, you can use a reorganization to convert LONG columns to the LOB data type.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Change to the table entries: In certain cases, it may make sense to rebuild a table with a particular sorting, for example, to improve the clustering factor of an index (Note 832343). You can do this during BRSPACE online reorganizations as described in SAP Note 1016172. This is generally based on an SQL analysis, as the one provided by SAP as part of &quot;Technical Performance Optimization - Oracle&quot; services.</li>\r\n</ul>\r\n</ul>\r\n<ol>4. In which situations does reorganization not make sense?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In most cases, other than those mentioned above, reorganization does not make sense.</p>\r\n<ol>5. Which tools are used in reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We recommend that you carry out reorganizations in the SAP environment using the BRSPACE tool (SAP Note 647697). This tool has a user-friendly interface, its parameters can be set in various ways and it warns users of any potential risks that may result from their actions. It is primarily intended for online reorganization using DBMS_REDEFINITION, but if required can also be used for offline reorganizations using EXP and IMP or, as of Oracle 10g, using Data Pump (Note 1013049).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of BRSPACE Patch 7.00 (28), you can also perform an offline reorganization based on ALTER TABLE MOVE for tables without LONG and LONG RAW columns (SAP Note 1080376). This is preferable to the other offline methods, because the data has to be moved only once, and parallelization is possible.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP Note 646681 contains detailed information about reorganizing with BRSPACE.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the reorganization is used mainly to correct table fragmentation and to achieve more space in a tablespace, you can use Segment Shrinking as of Oracle 10g (SAP Note 910389).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;As of SAP 4.6x, you can carry out a reorganization of tables with LONG and LONG RAW fields while the system is running using transaction ICNV. This transaction is usually used to convert tables incrementally as part of an upgrade. The procedure described in Note 96515 is also suitable for Oracle databases. In addition, refer to Note 806554 for ICNV-related possibilities for optimization. The following tables cannot be converted with ICNV, since they are needed to execute ICNV: VBDATA, TST03, TBATG, TICNV and TCNV.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In some cases, a table conversion with transaction SE14 can be an alternative to a BRSPACE offline reorganization. It has the advantage that you can continue using the SAP system while the conversion is running. However, you cannot access the table that is being converted. In addition you must ensure that no unexpected situations occur (for example, an error in the structure of the primary index) that lead to further problems (for example, duplicate keys).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In addition, SAP also provided the SAPDBA tool for carrying out reorganizations. With regard to reorganization, this tool has been replaced by BRSPACE. For this reason, this note does not deal with SAPDBA.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In some cases, it may be helpful to use Oracle functions such as ALTER TABLE MOVE, EXP/IMP or Data Pump directly. SAP permits you to use these Oracle commands, but we do not offer support if problems or errors occur as a result.</p>\r\n<ol>6. How does an online reorganization with BRSPACE work?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You start an online reorganization of tables or entire tablespaces using the BRSPACE function TBREORG as described in SAP Note 646681. During reorganzation, BRSPACE carries out the following steps:</p>\r\n<ul>\r\n<li>BRSPACE uses DBMS_REDEFINITION.CAN_REDEF_TABLES to check whether the selected tables can be reorganized online.</li>\r\n</ul>\r\n<ul>\r\n<li>It increases the DB_FILE_MULTIBLOCK_READ_COUNT parameter to 128, to ensure an optimal Full Table Scan performance (see Note 806554).</li>\r\n</ul>\r\n<ul>\r\n<li>It determines the CREATE TABLE statement and the CREATE INDEX statement for creating target segments with DBMS_METADATA.GET_DDL.</li>\r\n</ul>\r\n<ul>\r\n<li>It determines dependent objects such as grants, constraints, triggers and comments using DBMS_METADATA.GET_DEPENDENT_DDL.</li>\r\n</ul>\r\n<ul>\r\n<li>It creates the target table with the naming convention &lt;source_table&gt;#$.</li>\r\n</ul>\r\n<ul>\r\n<li>It exports statistics of the source table using DMBS_STATS.EXPORT_TABLE_STATS.</li>\r\n</ul>\r\n<ul>\r\n<li>It calls DBMS_REDEFINITION.START_REDEF_TABLE (the central function of the DBMS_REDEFINITION-package).</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It waits until all open changes are closed on the table to be reorganized.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It copies the source table data into the target table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It logs all changes to the source table until the later DBMS_REDEFINITION.FINISH_REDEF_TABLE in a Materialized View Log (MLOG$_&lt;source_table&gt;), which is created in the default-tablespace of the user.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>It creates indexes on the target table in accordance with the naming convention &lt;source_index&gt;#$.</li>\r\n</ul>\r\n<ul>\r\n<li>It imports the previously exported statistics for the target table using DBMS_STATS.IMPORT_TABLE_STATS. (with BRSPACE 6.40 (below 49), 7.00 (below 34) and 7.10 (below 10), this step was performed at the end after renaming the indexes.)</li>\r\n</ul>\r\n<ul>\r\n<li>It finishes the online reorganization using DBMS_REDEFINITION.FINISH_REDEF_TABLE:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It copies the changes to the source table that occurred during the online-reorganization.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>It swaps the names of the source table and the target table.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>It drops the source table.</li>\r\n</ul>\r\n<ul>\r\n<li>It changes index names from &lt;index_name&gt;#$ to &lt;index_name&gt;.</li>\r\n</ul>\r\n<ol>7. What are the advantages and disadvantages of online reorganizations?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Compared with an offline reorganization, an online reorganization has the following advantages and disadvantages:</p>\r\n<ul>\r\n<li>Advantages:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>The reorganization can be carried out in parallel with the current operation. Lock situations or access errors do not occur.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>All data must only be copied once. When you carry out an offline reorganization (based on import or export), two copy processes are always required (source object -&gt; export-dump; export-dump -&gt; target object). This often results in an improved performance.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<li>Restrictions:</li>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>With Oracle 9i or lower, an online reorganization of tables with LONG columns or LONG RAW columns is not possible. As of 10g, you can avoid the problem if you carry out a LONG2LOB conversion, as described in Note 646681.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Changes to the structure of a table (for example by adding columns or partitions) cause problems if they are implemented while the online reorganization is running. This is particularly the case for BW-based systems in which a large number of these types of DDL operations is carried out.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Twice the space is needed in the database because the source table or the source tablespace is only deleted once the target objects have been successfully created.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>Up until Oracle 9i, the tablespace name always changes if a tablespace reorganization is carried out. As of 10g, it is possible to carry out a tablespace reorganization without having to change the tablespace name due to the RENAME TABLESPACE feature.</li>\r\n</ul>\r\n</ul>\r\n<ul>\r\n<ul>\r\n<li>If you reorganize tables without a unique index (for example, BW fact tables), the system creates a ROWID column in the target table, which in individual cases can lead to an increase in the target table of up to 30% compared to the source table.</li>\r\n</ul>\r\n</ul>\r\n<ol>8. Can the SAP application run during the reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;You can carry out an online reorganization with BRSPACE and use the SAP system at the same time. However, bear in mind that DDL operations (such as CREATE or DROP, which are used on a large scale when loading data in BW) may cause errors for reorganized objects. For this reason, we recommend that you carry out online reorganizations when no DDL operations are running.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For offline reorganizations, the SAP system always needs to be closed.</p>\r\n<ol>9. What runtimes should I expect for a reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;We cannot make any general statements regarding the runtime of a reorganization in relation to the volume of data, since the runtime is determined by many external factors (in particular, by the hardware used).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Therefore, we can only provide runtime estimates based on experiences with previous reorganizations on the same system.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note that during offline reorganizations based on import or export, the data import can take three times longer than the data export.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;In addition, the final creation of the indexes is relatively time-consuming.</p>\r\n<ol>10. How can I optimize the performance of a reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Note the following points when analyzing and optimizing the performance of a reorganization:</p>\r\n<ul>\r\n<li>It is not always required to try to achieve the maximum reorganization performance. For example, if you carry out an online reorganization while the system is running, the duration of the reorganization is less important. Instead, make sure that there are still enough system resources available to the productive operation.</li>\r\n</ul>\r\n<ul>\r\n<li>See Note 806554, which describes the options available for optimizing the reorganization performance (for example, parameter setting, parallel processing).</li>\r\n</ul>\r\n<ul>\r\n<li>During runtime, carry out a wait event analysis as described in Note 619188.  This analysis allows you to make a precise prediction about the potential for optimization. It also makes it possible to determine the typical causes for performance problems based on a wait event analysis.</li>\r\n</ul>\r\n<ul>\r\n<li>A higher export runtime during the offline backup can be triggered by a poor I/O performance of the file system in which the dump file is created. This is mainly the case if a wait event analysis in accordance with Note 619188  shows that the main processing time of the export disappears outside of the database.</li>\r\n</ul>\r\n<ul>\r\n<li>Long-running exports and imports may also be linked to the activated concurrent I/O for the file system of the dump file. You must therefore ensure that no CIO is used for the dump file system. </li>\r\n</ul>\r\n<ul>\r\n<li>If the data import hangs, this may be due to an archiver being stuck (Note 391).</li>\r\n</ul>\r\n<ul>\r\n<li>If the index creation is very slow, the reason may be incorrect parameterization of the temporary tablespace (for example, if the default size of the extent is too small) or the PGA.  In this case, refer to Note 659946.</li>\r\n</ul>\r\n<ul>\r\n<li>If the extent is too small for the index itself, this may also result in an unnecessarily long runtime (due to a large number of space transactions) if the index is not created in an LMTS. You should therefore check whether the dimensions of the NEXT value are large enough.</li>\r\n</ul>\r\n<ul>\r\n<li>If performance problems occur because the system reorganizes many small tables and DBMS_METADATA.GET_DDL requires a lot of time, you can change to BRSPACE 7.00 (20) or higher. As of this release, BRSPACE only calls DBMS_METADATA.GET_DDL when it is truly necessary. For example, if a table has no dependent objects, the corresponding call is no longer executed.</li>\r\n</ul>\r\n<ul>\r\n<li>If the performance is negatively affected when you create NOT NULL constraints in Oracle 10g, you must ensure that you are using at least BRSPACE 7.00 (20).</li>\r\n</ul>\r\n<ul>\r\n<li>As of Oracle 11g, setting event 10995 to level 2 can prevent frequent reparsing during online reorganization; this particularly speeds up the reorganization of a number of small tables (see SAP Note 1565421).</li>\r\n</ul>\r\n<ol>11. How can I determine how many rows of a table have already been exported or imported?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To determine how many rows of a table have already been exported or imported during a lengthy export or import, you can manually extend the &quot;FEEDBACK=&lt;rows&gt;&quot; parameter in the generated EXP or IMP command before you start the reorganization. As a result, EXP or IMP always issues a new point when a &lt;rows&gt; row is exported or imported. Note that BRSPACE does not deal with this feature correctly and therefore, it incorrectly reports &quot;0 tables exported / imported&quot;.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an import with commits is carried out, you can also use the<br/>SELECT COUNT(*) FROM &lt;table&gt;;</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;command to determine how many rows of the table have already been imported and committed.</p>\r\n<ol>12. To save time, can I release the R/3 System even if indexes are still being created during reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;No, you must not release the R/3 System unless all indexes have been created. There are two reasons for this:</p>\r\n<ul>\r\n<li>If unique indexes are missing, the R/3 application may use existing key field combinations to write entries in tables. This situation is usually prevented by the existence of unique indexes, which do not allow these kinds of duplicate entries. As a consequence of entries that are not unique , the unique indexes can no longer be created later on and serious application inconsistencies may occur.</li>\r\n</ul>\r\n<ul>\r\n<li>If some indexes are missing, database access is very slow in certain circumstances, as the system must either carry out full table scans or use suboptimum indexes. In extreme cases, these kinds of long-running programs can shut down an entire system.</li>\r\n</ul>\r\n<ol>13. What do I have to do if a restore is required?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;It should never be necessary to carry out a restore during an online reorganization. In the case of an offline-reorganization, however, various reasons can make a restore necessary:</p>\r\n<ul>\r\n<li>The time window for completing the reorganization is not sufficient.</li>\r\n</ul>\r\n<ul>\r\n<li>Critical errors such as corrupt export dumps occur (for example, Note 535675).</li>\r\n</ul>\r\n<ul>\r\n<li>Problems occur that cannot be corrected during the import process.</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;A restore is usually only necessary if objects were already deleted from the database, and if this data cannot be restructured in a different way.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you have decided to carry out a restore to reset the database to the time before the reorganization, you must restore (and if necessary, incompletely recover) the whole database. It is not sufficient to restore only the tablespaces concerned, as their time stamps (SCN) would no longer match the other tablespaces.</p>\r\n<ol>14. Can I use a target tablespace with the name of the source tablespaces during an online reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If you want to create a tablespace with a changed data file layout (&quot;Tablespace reorganization with data files&quot;), you can do this easily with the offline reorganization. If you use the online reorganization, source and target tablespaces exist at the same time. This means that the target tablespace cannot have the same name as the source tablespace. Up until Oracle 9i, this that the target tablespace always has a different name to the previous tablespace. As of Oracle 10g, you can rename tablespaces. Therefore the final name of the target tablespace can correspond to the name of the source tablespace. See the section on tablespace reorganization under Oracle 10g in Note 646681.</p>\r\n<ol>15. What do I need to remember with regard to TABART changes?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;TABARTs are defined in the TAORA and IAORA tables. Each TABART refers to exactly one tablespace. If you now create a new tablespace, BRSPACE automatically carries out the corresponding changes based on the &quot;-1&quot; parameter to TAORA and IAORA.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;BRSPACE also changes the TABART belonging to a segment in the DD09L table if there are any differences after a reorganization. From an SAP-DDIC point of view, this change is not advisable because the technical settings in DD09L are supposed to reflect the default settings and not the current status. In order to avoid problems with system copies with R3LOAD for which the DD09L entries are evaluated, this adjustment may still be advisable.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For more details on TABARTs, see SAP Note 666061.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The following points must be considered with regard to these settings:</p>\r\n<ul>\r\n<li>Since upgrades and transports may undo the BRSPACE changes without being noticed, it is important to refer to the BRSPACE changes according to Note 778784 before a system copy with R3LOAD.</li>\r\n</ul>\r\n<ul>\r\n<li>In BW systems, TABARTs are also defined in other tables: RSDCUBE for infocubes, aggregates, and dimension tables; RSTS for PSA tables. When TABARTs are changed, the mappings must also be adjusted in these tables in accordance with Note 771191.</li>\r\n</ul>\r\n<ul>\r\n<li>When you use partitioned tables with globally partitioned indexes, the default tablespace must be adjusted on Oracle level as well using &quot;ALTER TABLE ...  MODIFY DEFAULT ATTRIBUTES TABLESPACE &lt;tablespace&gt;&quot; (Note 666061). This action is carried out automatically by BRSPACE.</li>\r\n</ul>\r\n<ul>\r\n<li>If the tablespace layout is only changed for one system of the system infrastructure (development, testing, production), you do not need to carry out this change in all involved systems in the same way. Instead, you only have to define the same TABARTs in TAORA and IAORA, but these do not have to refer to the same tablespaces in all systems.</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;From a transport point of view, you do not have to change the technical settings of already existing tables (DD09L-updates), since the DD09L settings are only relevant when new objects are created.</p>\r\n<ol>16. What error scenarios are possible in the context of reorganizations?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Characteristic errors, causes and solutions are:</p>\r\n<ul>\r\n<li>Materialized view-related errors</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Refer to SAP Note 741478, which contains the characteristic errors in the materialized views environment.</p>\r\n<ul>\r\n<li>Tablespace overflow (Note 3155)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the SYSTEM tablespace is affected, make sure that PSAPTEMP rather than SYSTEM is assigned to the user SYS as a temporary tablespace.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the default tablespace of the table owner is affected, make sure that as few data changes as possible are made to the tables to be reorganized, avoid reorganizing tables without a primary index, increase the default tablespace sufficiently, or temporarily select another default tablespace.<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If the target tablespace for reorganization is affected, make sure that no INITIAL extents that are too large have been allocated (Option &quot; -l 2&quot;), and configure the target tablespace with sufficient size.</p>\r\n<ul>\r\n<li>MAXEXTENTS error (Note 533455)</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Where possible, always use Locally Managed Tablespaces (LMTS). For DMTS, the solutions described above under &quot;Tablespace overflow&quot; are relevant. Instead of increasing the affected tablespaces, however, you must increase MAXEXTENTS or the extent size.</p>\r\n<ul>\r\n<li>ORA-00018: maximum number of sessions exceeded</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;With large-scale parallel processing, the SESSIONS limit may be reached and the error described above may occur. In this case, increase the SESSIONS parameter sufficiently.</p>\r\n<ul>\r\n<li>BR0602E No valid SAP license found - please contact SAP</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this error occurs when you carry out an import during an offline reorganization with BRSPACE, this may be connected to the drop of the license table MLICHECK after the export. The reason for this is that MLICHECK was not moved into a help tablespace in accordance with Note 646681 (14). Apart from an individual manual procedure for reconstructing MLICHECK, the only thing you can do in this case is to restore a backup.</p>\r\n<ul>\r\n<li>ORA-00069: cannot acquire lock -- table locks disabled for &lt;table&gt; ORA-08116: can not acquire dml enough lock(S mode) for online index<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0; build</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See SAP Note 737810.</p>\r\n<ul>\r\n<li>ORA-00955: name is already used by an existing object</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this error occurs when you are creating the target table (with the suffix &quot;#$&quot;), it is probably because a preceding reorganization was canceled without the target table being deleted. In this situation, perform a cleanup:<br/>brspace -f tbreorg -t &quot;*&quot; -a cleanup</p>\r\n<ul>\r\n<li>ORA-01452: cannot CREATE UNIQUE INDEX; duplicate keys found</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If ORA-01452 occurs when you create indexes after a BRSPACE offline reorganization, it is likely that the same data was imported several times. This may be the case if the first import experiences an error such as a tablespace overflow and if you then try to restart the import without taking further action (the BRSPACE import cannot be restarted). Instead, you should first drop all tables that have already been imported. Afterwards, the offline reorganization process can be restarted when the tables are created using ddl.sql .</p>\r\n<ul>\r\n<li>BR1110W Table &lt;owner&gt;.&lt;table&gt; has a LONG column<br/>BR1113E All tables have been skipped for reorganization</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;These messages are issued when you try to reorganize one or several tables with LONG or LONG RAW columns online with Oracle 9i or lower. Instead, reorganize these tables offline.</p>\r\n<ul>\r\n<li>ORA-04098: trigger &#39;&lt;user&gt;./BI0/05000...#$&#39; is invalid and failed<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;re-validation</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Triggers that follow the naming convention /BI0/05 are left over from BW compressions. Delete these triggers at Oracle level or on the basis of Notes 982120 (BW 7.00) and 1061807 (BW 3.x) and repeat the reorganization process.</p>\r\n<ul>\r\n<li>ORA-01792: maximum number of columns in a table or view is 1000</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This error occurs on 10g due to Oracle bug 4421811, if you want to reorganize tables with more than 501 columns online. As a workaround, you can set event 10995 at BRSPACE session level. For more information, see Note 1290097.</p>\r\n<ul>\r\n<li>ORA-14404: partitioned table contains partitions in a different tablespace</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If this error occurs during a DROP on a tablespace, even though this was meant to be blank due to the reorganization, partitions for tables from other tablespaces still exist in this tablespace. This situation should not occur under normal circumstances. If this situation does occur, you can use MOVE to move the partitions to the correct tablespace:<br/>ALTER TABLE &quot;&lt;table_name&gt;&quot; MOVE PARTITION &quot;&lt;partition_name&gt;&quot;<br/>TABLESPACE &quot;&lt;correct_tablespace_name&gt;&quot;;<br/>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See SAP Notes 666061 and 722188 and be aware that the MOVE command sets enqueues and that global indexes will become unusable (therefore, a rebuild is required).</p>\r\n<ul>\r\n<li>ORA-00600 [kdlm_merge_lobs:1]</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;This error may occur with Oracle 10.2.0.4 and below if you reorganize a table with LOB columns online in parallel. See Note 1292525 and, if possible, do not use the parallelism.</p>\r\n<ul>\r\n<li>Error due to HIDDEN/UNUSED columns</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;See SAP Note 1129347, which describes how to handle problems in relation to HIDDEN columns or UNUSED columns (for example, ORA-00947).</p>\r\n<ul>\r\n<li>Incorrect results or ORA-12096/ORA-00942 errors with RAC</li>\r\n</ul>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To avoid incorrect results or ORA-12096/ORA-00942 errors during online reorganizations in the RAC environment, you must take into account SAP Note 1336810.</p>\r\n<ol>17. Is it possible that performance deteriorates after an online reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For various reasons, performance may deteriorate after an online reorganization (for example, target segments in a slower disk area, changed CBO decisions due to new CBO statistics).</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;The deterioration in performance may also be caused by the use of Oracle Parallel Data Manipulation Language (DML) due to which the sequence of the data records may be mixed. This has negative effects on the clustering factors of indexes. If you use Oracle Parallel Execution (BRSPACE parameter &quot;-e &lt;degree&gt;&quot;), you can deactivate Parallel DML if required by using the additional BRSPACE parameter &quot;-SPM&quot;. As a result, the system uses only the more harmless parallel processing variants Parallel Query and Parallel DDL. In BRSPACE 7.20 (27) and later, Parallel DML is no longer activated in the default settings, which means there is no longer any deterioration with clustering. To reactivate Parallel DML to increase reorganization performance, you can set the BRSPACE parameter &quot;-FPM&quot;. Note that although Parallel DML can speed up the runtimes of the reorganization, in some circumstances it can also harm access performance during regular operations after reorganization. Therefore, before you use &quot;-FPM&quot;, you should always test whether the associated benefit is greater than the potential damage.</p>\r\n<ol>18. Where can I find further information about reorganization?</ol>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP Note 646681 contains information about reorganizing objects with BRSPACE.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAP Note 741478 contains information about how materialized views can be used in online reorganizations and what problems can occur when you do this.</p>\r\n<p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Also refer to the relevant SAP online documentation.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DB-ORA (Oracle)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D030484)"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000541538/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000541538/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000541538/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "910389", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Segment Shrinking", "RefUrl": "/notes/910389"}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477"}, {"RefNumber": "85558", "RefComponent": "BC-DB-ORA", "RefTitle": "Tables with data sorted by index", "RefUrl": "/notes/85558"}, {"RefNumber": "84348", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle deadlocks, ORA-00060", "RefUrl": "/notes/84348"}, {"RefNumber": "832343", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Clustering factor", "RefUrl": "/notes/832343"}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687"}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554"}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113"}, {"RefNumber": "787533", "RefComponent": "BC-DB-ORA", "RefTitle": "Bad performance/ system lockup due to enqueue waits", "RefUrl": "/notes/787533"}, {"RefNumber": "778784", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/778784"}, {"RefNumber": "771191", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Copying BW objects to new tablespaces", "RefUrl": "/notes/771191"}, {"RefNumber": "741478", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Materialized views", "RefUrl": "/notes/741478"}, {"RefNumber": "737810", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-00069/ora-08116 with DDL operation for table or index", "RefUrl": "/notes/737810"}, {"RefNumber": "722188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle partitioning", "RefUrl": "/notes/722188"}, {"RefNumber": "666061", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database objects, segments, and extents", "RefUrl": "/notes/666061"}, {"RefNumber": "659946", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Temporary tablespaces", "RefUrl": "/notes/659946"}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697"}, {"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681"}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803"}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188"}, {"RefNumber": "535675", "RefComponent": "BC-DB-ORA", "RefTitle": "EXP can produce dump file with corrupted data", "RefUrl": "/notes/535675"}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "Archiver stuck", "RefUrl": "/notes/391"}, {"RefNumber": "355771", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Explanation of the new tablespace layout", "RefUrl": "/notes/355771"}, {"RefNumber": "354293", "RefComponent": "BC-DB-ORA", "RefTitle": "DBVerify reports corrupt block in freespace area", "RefUrl": "/notes/354293"}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677"}, {"RefNumber": "23237", "RefComponent": "BC-DB-ORA", "RefTitle": "duplicate keys, duplicate rows, duplicate records", "RefUrl": "/notes/23237"}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995"}, {"RefNumber": "1565421", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: dbms_redefinition may flush shared pool", "RefUrl": "/notes/1565421"}, {"RefNumber": "1292525", "RefComponent": "BC-DB-ORA", "RefTitle": "Ora-600 [Kdlm_merge_lobs:1] during table reorganization", "RefUrl": "/notes/1292525"}, {"RefNumber": "1290097", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01792 while running online reorganization of the table", "RefUrl": "/notes/1290097"}, {"RefNumber": "11369", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-1452 during import after export from other system", "RefUrl": "/notes/11369"}, {"RefNumber": "1129347", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_METADATA.GET_DDL, HIDDEN/UNUSED columns and ORA-947", "RefUrl": "/notes/1129347"}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376"}, {"RefNumber": "1016172", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Sorting table records during reorganization", "RefUrl": "/notes/1016172"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2976502", "RefComponent": "BC-DB-ORA", "RefTitle": "How to get back free space in the database (table- and tablespace reorganization)", "RefUrl": "/notes/2976502 "}, {"RefNumber": "2434534", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "ORA-01686 - What to do if number of datafiles in tablespace reached max limit?", "RefUrl": "/notes/2434534 "}, {"RefNumber": "1753120", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Unknown objects mlog$_<table>, rupd$_<table> and <table>#$ due to failing reorganization", "RefUrl": "/notes/1753120 "}, {"RefNumber": "2592310", "RefComponent": "BC-XI-CON-MSG", "RefTitle": "PO/AEX Messaging tables relevant for DB reorg jobs", "RefUrl": "/notes/2592310 "}, {"RefNumber": "1749142", "RefComponent": "BC-CTS-CCO", "RefTitle": "How to remove unused clients including client 001 and 066", "RefUrl": "/notes/1749142 "}, {"RefNumber": "646681", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Reorganization of tables with BRSPACE", "RefUrl": "/notes/646681 "}, {"RefNumber": "619188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle wait events", "RefUrl": "/notes/619188 "}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687 "}, {"RefNumber": "793113", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle I/O configuration", "RefUrl": "/notes/793113 "}, {"RefNumber": "365304", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-ADMIN: Reports for deleting tables", "RefUrl": "/notes/365304 "}, {"RefNumber": "489690", "RefComponent": "BC-CTS-CCO", "RefTitle": "CC-INFO: Copying large production clients", "RefUrl": "/notes/489690 "}, {"RefNumber": "659946", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Temporary tablespaces", "RefUrl": "/notes/659946 "}, {"RefNumber": "332677", "RefComponent": "BC-DB-ORA", "RefTitle": "Rebuilding fragmented indexes", "RefUrl": "/notes/332677 "}, {"RefNumber": "806554", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: I/O-intensive database operations", "RefUrl": "/notes/806554 "}, {"RefNumber": "1016172", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Sorting table records during reorganization", "RefUrl": "/notes/1016172 "}, {"RefNumber": "391", "RefComponent": "BC-DB-ORA", "RefTitle": "Archiver stuck", "RefUrl": "/notes/391 "}, {"RefNumber": "722188", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle partitioning", "RefUrl": "/notes/722188 "}, {"RefNumber": "1565421", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 11g: dbms_redefinition may flush shared pool", "RefUrl": "/notes/1565421 "}, {"RefNumber": "741478", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Materialized views", "RefUrl": "/notes/741478 "}, {"RefNumber": "1292525", "RefComponent": "BC-DB-ORA", "RefTitle": "Ora-600 [Kdlm_merge_lobs:1] during table reorganization", "RefUrl": "/notes/1292525 "}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995 "}, {"RefNumber": "84348", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle deadlocks, ORA-00060", "RefUrl": "/notes/84348 "}, {"RefNumber": "1080376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Enhancements for reorganization and rebuild", "RefUrl": "/notes/1080376 "}, {"RefNumber": "787533", "RefComponent": "BC-DB-ORA", "RefTitle": "Bad performance/ system lockup due to enqueue waits", "RefUrl": "/notes/787533 "}, {"RefNumber": "832343", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Clustering factor", "RefUrl": "/notes/832343 "}, {"RefNumber": "1290097", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-01792 while running online reorganization of the table", "RefUrl": "/notes/1290097 "}, {"RefNumber": "1129347", "RefComponent": "BC-DB-ORA", "RefTitle": "DBMS_METADATA.GET_DDL, HIDDEN/UNUSED columns and ORA-947", "RefUrl": "/notes/1129347 "}, {"RefNumber": "666061", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Database objects, segments, and extents", "RefUrl": "/notes/666061 "}, {"RefNumber": "910389", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle Segment Shrinking", "RefUrl": "/notes/910389 "}, {"RefNumber": "647697", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "BRSPACE - New tool for Oracle database administration", "RefUrl": "/notes/647697 "}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803 "}, {"RefNumber": "771191", "RefComponent": "BW-SYS-DB-ORA", "RefTitle": "Copying BW objects to new tablespaces", "RefUrl": "/notes/771191 "}, {"RefNumber": "354293", "RefComponent": "BC-DB-ORA", "RefTitle": "DBVerify reports corrupt block in freespace area", "RefUrl": "/notes/354293 "}, {"RefNumber": "23237", "RefComponent": "BC-DB-ORA", "RefTitle": "duplicate keys, duplicate rows, duplicate records", "RefUrl": "/notes/23237 "}, {"RefNumber": "11369", "RefComponent": "BC-DB-ORA", "RefTitle": "ORA-1452 during import after export from other system", "RefUrl": "/notes/11369 "}, {"RefNumber": "875477", "RefComponent": "BC-DB-ORA", "RefTitle": "Avoiding long runtimes with BEGIN BACKUP", "RefUrl": "/notes/875477 "}, {"RefNumber": "85558", "RefComponent": "BC-DB-ORA", "RefTitle": "Tables with data sorted by index", "RefUrl": "/notes/85558 "}, {"RefNumber": "355771", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle: Explanation of the new tablespace layout", "RefUrl": "/notes/355771 "}, {"RefNumber": "737810", "RefComponent": "BC-DB-ORA", "RefTitle": "ora-00069/ora-08116 with DDL operation for table or index", "RefUrl": "/notes/737810 "}, {"RefNumber": "535675", "RefComponent": "BC-DB-ORA", "RefTitle": "EXP can produce dump file with corrupted data", "RefUrl": "/notes/535675 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}