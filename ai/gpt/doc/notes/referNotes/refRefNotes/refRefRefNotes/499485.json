{"Request": {"Number": "499485", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 603, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000002339712017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000499485?language=E&token=FD3397A821D78CD95D02BDE60352DD62"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000499485", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000499485/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "499485"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 13}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with high priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.02.2002"}, "SAPComponentKey": {"_label": "Component", "value": "PY-MX"}, "SAPComponentKeyText": {"_label": "Component", "value": "Mexico"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Payroll", "value": "PY", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Mexico", "value": "PY-MX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'PY-MX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "499485 - SAPServe transport because of annual processes"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>Because of problems in the SAPServe transport associated with note 499100, it is necessary to make a new SAPServe transport.<br />Some other fixes were also applied to programs HMXCCRE0 and HMXCDSS0.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>HMXTRTR0<br />HMXCDSS0<br />HMXCCRE0</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Some objects which were changed in a recent note and that were<br />required in sapserv delivered in note 499100 were missing.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p><br />Please follow the next steps :<br />1)&#x00A0;&#x00A0;open an ftp connection to sapserv3.wdf.sap-ag.de<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;(user ftp, pass ftp)<br />2)&#x00A0;&#x00A0;change to directory general/R3server/abap/note.0499100<br />3)&#x00A0;&#x00A0;set binary transfer mode ( type bin )<br />4) download the file which matchs to your system : &#x00A0;&#x00A0;&#x00A0;&#x00A0; MX-0499100-45B-NEW.CAR&#x00A0;&#x00A0; if you run a 45B system, or &#x00A0;&#x00A0;&#x00A0;&#x00A0; MX-0499100-46B-NEW.CAR&#x00A0;&#x00A0; if you run a 46B system, or &#x00A0;&#x00A0;&#x00A0;&#x00A0; MX-0499100-46C-NEW.CAR&#x00A0;&#x00A0; if you run a 46C system<br />In order to install the corrections you have to use the program tp. Note that the CAR file contains both the R* file ( that should go into your local trans/data directory ) and the K* ( that should go into your localtrans/cofiles directory). These files are contained in the directories data and cofiles of the coresponding CAR file. If you prefer, you can extract the files manually with the CAR utility. Please refer to note 13719 for information about using sapserv. &#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Page Note that the correction instructions included in this note refer only to the new corrections made after the first sapserv in note 499100. The recommended action is to simply apply the sapserv as explained<br />above and not to try to apply manually the correction instructions<br />on top the previous sapserv</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Transaction codes", "Value": "CLEAR"}, {"Key": "Transaction codes", "Value": "MOVE"}, {"Key": "Transaction codes", "Value": "CORR"}, {"Key": "Transaction codes", "Value": "FILE"}, {"Key": "Transaction codes", "Value": "MODE"}, {"Key": "Transaction codes", "Value": "COLOR"}, {"Key": "Transaction codes", "Value": "0000"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D023056)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D023056)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000499485/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "500725", "RefComponent": "PY-MX", "RefTitle": "Wrong total amount of wage credit in Temse file", "RefUrl": "/notes/500725"}, {"RefNumber": "499100", "RefComponent": "PY-MX", "RefTitle": "sapserv for corrections of \"procesos anuales\"", "RefUrl": "/notes/499100"}, {"RefNumber": "498848", "RefComponent": "PY-MX", "RefTitle": "Legal change in 26 Form (HMXCCRE0)", "RefUrl": "/notes/498848"}, {"RefNumber": "498354", "RefComponent": "PY-MX", "RefTitle": "Leg. changes in\"Declaración sueldos y salarios\" temse format", "RefUrl": "/notes/498354"}, {"RefNumber": "498157", "RefComponent": "PY-MX", "RefTitle": "Employees subject to risk (HMXTRTR0)", "RefUrl": "/notes/498157"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "500725", "RefComponent": "PY-MX", "RefTitle": "Wrong total amount of wage credit in Temse file", "RefUrl": "/notes/500725 "}, {"RefNumber": "498848", "RefComponent": "PY-MX", "RefTitle": "Legal change in 26 Form (HMXCCRE0)", "RefUrl": "/notes/498848 "}, {"RefNumber": "498354", "RefComponent": "PY-MX", "RefTitle": "Leg. changes in\"Declaración sueldos y salarios\" temse format", "RefUrl": "/notes/498354 "}, {"RefNumber": "498157", "RefComponent": "PY-MX", "RefTitle": "Employees subject to risk (HMXTRTR0)", "RefUrl": "/notes/498157 "}, {"RefNumber": "499100", "RefComponent": "PY-MX", "RefTitle": "sapserv for corrections of \"procesos anuales\"", "RefUrl": "/notes/499100 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_HR", "From": "45B", "To": "45B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46B", "To": "46B", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "46C", "To": "46C", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SAP_HR 45B", "SupportPackage": "SAPKE45B72", "URL": "/supportpackage/SAPKE45B72"}, {"SoftwareComponentVersion": "SAP_HR 46B", "SupportPackage": "SAPKE46B54", "URL": "/supportpackage/SAPKE46B54"}, {"SoftwareComponentVersion": "SAP_HR 46C", "SupportPackage": "SAPKE46C45", "URL": "/supportpackage/SAPKE46C45"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "SAP_HR", "NumberOfCorrin": 3, "URL": "/corrins/0000499485/2"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 3, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 14, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "385817 ", "URL": "/notes/385817 ", "Title": "Declaración anual de credito al salario (version 4.0)", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "394614 ", "URL": "/notes/394614 ", "Title": "Inconsistence between HMXCRET0 and HMXCCRE0 for inganu.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "402466 ", "URL": "/notes/402466 ", "Title": "New report HMXCDSS0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "408191 ", "URL": "/notes/408191 ", "Title": "First correction to the HMXCDSS0 report", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "456540 ", "URL": "/notes/456540 ", "Title": "Employees with \"credito salario\" = 0 shouldn't be reported", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "458751 ", "URL": "/notes/458751 ", "Title": "TAX:\"subs. acreditable\" and \"subs. no acreditable\" in adj.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "486490 ", "URL": "/notes/486490 ", "Title": "Corrections to the report HMXCDSS0 and its temse file format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "490944 ", "URL": "/notes/490944 ", "Title": "Reading infotype 0185 in report HMXCCRE0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "496734 ", "URL": "/notes/496734 ", "Title": "Characters string not defined in HMXCDSS0.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "498354 ", "URL": "/notes/498354 ", "Title": "Leg. changes in\"Declaración sueldos y salarios\" temse format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "45B", "Number": "498848 ", "URL": "/notes/498848 ", "Title": "Legal change in 26 Form (HMXCCRE0)", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "46C", "Number": "368888 ", "URL": "/notes/368888 ", "Title": "Legal Reports for annual tax adjustment prev.year", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "45B", "ValidTo": "470", "Number": "430649 ", "URL": "/notes/430649 ", "Title": "Corrections to report HMXCDSS0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "385817 ", "URL": "/notes/385817 ", "Title": "Declaración anual de credito al salario (version 4.0)", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "391038 ", "URL": "/notes/391038 ", "Title": "Changes for the output and temses in report HMXCCRE0.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "394614 ", "URL": "/notes/394614 ", "Title": "Inconsistence between HMXCRET0 and HMXCCRE0 for inganu.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "402466 ", "URL": "/notes/402466 ", "Title": "New report HMXCDSS0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "408191 ", "URL": "/notes/408191 ", "Title": "First correction to the HMXCDSS0 report", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "456540 ", "URL": "/notes/456540 ", "Title": "Employees with \"credito salario\" = 0 shouldn't be reported", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "458751 ", "URL": "/notes/458751 ", "Title": "TAX:\"subs. acreditable\" and \"subs. no acreditable\" in adj.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "486490 ", "URL": "/notes/486490 ", "Title": "Corrections to the report HMXCDSS0 and its temse file format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "490944 ", "URL": "/notes/490944 ", "Title": "Reading infotype 0185 in report HMXCCRE0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "496734 ", "URL": "/notes/496734 ", "Title": "Characters string not defined in HMXCDSS0.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "498354 ", "URL": "/notes/498354 ", "Title": "Leg. changes in\"Declaración sueldos y salarios\" temse format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46B", "ValidTo": "46B", "Number": "498848 ", "URL": "/notes/498848 ", "Title": "Legal change in 26 Form (HMXCCRE0)", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "385817 ", "URL": "/notes/385817 ", "Title": "Declaración anual de credito al salario (version 4.0)", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "391038 ", "URL": "/notes/391038 ", "Title": "Changes for the output and temses in report HMXCCRE0.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "394614 ", "URL": "/notes/394614 ", "Title": "Inconsistence between HMXCRET0 and HMXCCRE0 for inganu.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "402466 ", "URL": "/notes/402466 ", "Title": "New report HMXCDSS0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "408191 ", "URL": "/notes/408191 ", "Title": "First correction to the HMXCDSS0 report", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "456540 ", "URL": "/notes/456540 ", "Title": "Employees with \"credito salario\" = 0 shouldn't be reported", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "458751 ", "URL": "/notes/458751 ", "Title": "TAX:\"subs. acreditable\" and \"subs. no acreditable\" in adj.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "486490 ", "URL": "/notes/486490 ", "Title": "Corrections to the report HMXCDSS0 and its temse file format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "490944 ", "URL": "/notes/490944 ", "Title": "Reading infotype 0185 in report HMXCCRE0", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "496734 ", "URL": "/notes/496734 ", "Title": "Characters string not defined in HMXCDSS0.", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "498354 ", "URL": "/notes/498354 ", "Title": "Leg. changes in\"Declaración sueldos y salarios\" temse format", "Component": "PY-MX"}, {"SoftwareComponent": "SAP_HR", "ValidFrom": "46C", "ValidTo": "46C", "Number": "498848 ", "URL": "/notes/498848 ", "Title": "Legal change in 26 Form (HMXCCRE0)", "Component": "PY-MX"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}