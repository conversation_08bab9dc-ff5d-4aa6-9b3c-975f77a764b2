{"Request": {"Number": "196113", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 403, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014768362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000196113?language=E&token=1D2AD5E4C696E1DAB14C0F1C07B3AA83"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000196113", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000196113/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "196113"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "17.08.2004"}, "SAPComponentKey": {"_label": "Component", "value": "BC-SRV-BAL"}, "SAPComponentKeyText": {"_label": "Component", "value": "Basis Application Log"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Services/Communication Interfaces", "value": "BC-SRV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Basis Application Log", "value": "BC-SRV-BAL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-SRV-BAL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "196113 - SAPSQL_ARRAY_INSERT_DUPREC, BALHDR"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>SAPSQL_ARRAY_INSERT_DUPREC, table BALHDR<br />Open SQL array insert causes duplicate records in the database.<br /></p> <UL><LI>For Releases &lt; 4.6A:<br />===============================================================<br />&#x00A0;&#x00A0;Symptom:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ABAP/4 runtime error (short dump) in program SAPLSLG0<br />&#x00A0;&#x00A0;Place in the source code:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function module APPL_LOG_WRITE_DB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPLSLG0<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LSLG0U07<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000570 UPDATE BALHDR&#x00A0;&#x00A0;FROM TABLE BALHDR_U.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=====&gt; INSERT BALHDR&#x00A0;&#x00A0;FROM TABLE BALHDR_I.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000590 DELETE BALHDRP FROM TABLE BALHDRP_D.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...</LI></UL> <UL><LI>For Releases 4.6A to 4.6B:<br />===============================================================<br />&#x00A0;&#x00A0;Symptom:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ABAP/4 runtime error (short dump) in program SAPLSBAL_DB<br />&#x00A0;&#x00A0; Or:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update termination in BAL_DB_SAVE_IN_UPDATE_TASK<br />&#x00A0;&#x00A0;Place in the source code:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function module BAL_DB_SAVE_NO_UPDATE_TASK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Program&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;SAPLSBAL_DB<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;LSBAL_DBU07<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000440 IF NOT I_S_DB_TABLES-BALHDR_I IS INITIAL.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=====&gt; INSERT BALHDR FROM TABLE I_S_DB_TABLES-BALHDR_I.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000460 ENDIF.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...</LI></UL> <UL><LI>For Release 4.6C and higher:<br />===============================================================<br />&#x00A0;&#x00A0;Symptom:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;ABAP/4 runtime error (short dump) in SAPLSBAL_DB_INTERNAL<br />&#x00A0;&#x00A0;Or:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Update termination in BAL_DB_INTERNAL_IN_UPDATE_TASK<br />&#x00A0;&#x00A0;Place in the source code:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Function module BAL_DB_INTERNAL_NO_UPDATE_TASK<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Program SAPLSBAL_DB_INTERNAL<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Include LSBAL_DB_INTERNALU02<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000540 IF NOT i_s_db_tables-balhdr_i IS INITIAL.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;=====&gt; INSERT balhdr CLIENT SPECIFIED FROM TABLE<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;i_s_db_tables-balhdr_i.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;000560&#x00A0;&#x00A0;ENDIF.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;...<br /></LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Application log, logs, messages, save, database, BALHDR, BALHDRP, APPL_LOG_WRITE_DB, SAPLSLG0, LSLG0U07, BAL_DB_SAVE, BAL_DB_SAVE_IN_UPDATE_TASK, BAL_DB_INTERNAL_NO_UPDATE_TASK, SAPLSBAL_DB_INTERNAL, LSBAL_DB_INTERNALU02, BAL_DB_INTERNAL_IN_UPDATE_TASK, AL_DB_INTERNAL_NO_UPDATE_TASK, APLSBAL_DB_INTERNAL, SBAL_DB_INTERNALU02, Number ranges, Number range object<br /></p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The BALHDR table belongs to the application log. This is a tool to collect, save and display messages.<br />In this tool, the messages are grouped to logs where each log gets a unique number (in the respective client).<br />When the system saves the log, this number is determined from the APPL_LOG number range object in which the number assigned last was saved.<br />If this last number has an incorrect status, a number that already exists can be assigned, a short dump occurs when you save, as the database already contains an entry of this type.<br /></p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Step 1: Determining the last assigned number<br />===============================================================<br />In the R/3 System, call transaction SE16.<br />Table: BALHDR.<br />Select the 'Table contents' icon (F7)<br />On the selection screen, enter the value 999999999 in the lowest field (maximum score) and select 'Execute' (F8).<br />A list is displayed. Scroll to the very bottom of this list. Make a note of the value in the bottom row in the 'LOGNUMBER' column (176508, for example).<br /><br />Step 2: Correcting the current number level<br />===============================================================<br />In the R/3 System, call transaction SLGN.<br />Choose the Interval -&gt; Change status (Shift F6) menu path.<br />Here you should see the following:<br /><br />&#x00A0;&#x00A0;No From number&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;To number&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Current number<br />&#x00A0;&#x00A0;01 00000000000000000001&#x00A0;&#x00A0;99999999999999999999&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;16936<br /><br />The Current number field usually contains a value that is greater than the one you noted before.<br />However, if the number is less than the number you have noted, enter the noted value ere and add another 1000 to it for good measure.<br />Save the entry.<br /><br />Step 3: Buffering the number range object<br />===============================================================<br />If the dump still occurs, there are obsolete numbers in the buffer memory of your current server.<br />In this case, reset the number range buffers of the application servers:  to do this, start transaction SM56. In the menu, select Number range buffer -&gt; Reset.&#x00A0;&#x00A0;Fill the fields in the dialog box that appears as follows:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Client&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&lt;current client &gt;<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Object&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;APPL_LOG<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Subobject<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Range<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Year<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;Global reset X<br />You can call the RSSLGK00 report to test that no more dumps are triggered and the list of results shows a protocol number that is greater than or equal to the number you have entered.<br /><br />If the dump persists, the problem is caused by something else. The problem cannot be corrected with this note.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "I025657"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000196113/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000196113/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000196113/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410"}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971"}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092"}, {"RefNumber": "737696", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/737696"}, {"RefNumber": "637683", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/637683"}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852"}, {"RefNumber": "587896", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/587896"}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069"}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968"}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "3043357", "RefComponent": "CA-GTF-TS-BRHF", "RefTitle": "Runtime error MESSAGE_TYPE_X in program SAPLEMSG", "RefUrl": "/notes/3043357 "}, {"RefNumber": "2627198", "RefComponent": "BC-SRV-BAL", "RefTitle": "APPLICATION LOG: The system tried to insert a data record, even tho 0", "RefUrl": "/notes/2627198 "}, {"RefNumber": "2518643", "RefComponent": "FIN-FB-SRV", "RefTitle": "Uncaught exception CX_FINB_MSG_RAISED raised during client copy in class CL_FINB_MSG_HANDLING_FACADE", "RefUrl": "/notes/2518643 "}, {"RefNumber": "2093758", "RefComponent": "BC-SRV-BAL", "RefTitle": "Check report for application logs", "RefUrl": "/notes/2093758 "}, {"RefNumber": "826092", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0", "RefUrl": "/notes/826092 "}, {"RefNumber": "1088904", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR3 ABAP", "RefUrl": "/notes/1088904 "}, {"RefNumber": "1156968", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to EHP 4 for SAP ERP 6.0 ABAP", "RefUrl": "/notes/1156968 "}, {"RefNumber": "1292069", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP 6.0 EHP4 SR1 ABAP", "RefUrl": "/notes/1292069 "}, {"RefNumber": "913971", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ERP Central Component 6.0 SR1", "RefUrl": "/notes/913971 "}, {"RefNumber": "961410", "RefComponent": "BC-UPG-RDM", "RefTitle": "Add. info. on upgrading to SAP ECC 6.0 SR2 ABAP", "RefUrl": "/notes/961410 "}, {"RefNumber": "603852", "RefComponent": "BC-UPG-RDM", "RefTitle": "Additional information about upgrading to SAP SCM 4.0", "RefUrl": "/notes/603852 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}