{"Request": {"Number": "480671", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 555, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015147452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000480671?language=E&token=CD4065272EA502AE0EDBA871A3374FCA"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000480671", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000480671/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "480671"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 74}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Documentation error"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "15.04.2014"}, "SAPComponentKey": {"_label": "Component", "value": "BC-I18"}, "SAPComponentKeyText": {"_label": "Component", "value": "Internationalization (I18N)"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Internationalization (I18N)", "value": "BC-I18", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-I18*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "480671 - The Text Language Flag of LANG Fields"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<ul>\r\n<li>In Unicode systems:<br />RFC performs wrong code page conversion between R/3 Unicode and non-Unicode, single or multiple code page (MDMP), systems.</li>\r\n</ul>\r\n<ul>\r\n<li>In Unicode systems:<br />transport with R3trans originating in a non-Unicode system results in garbled text data in the Unicode system.</li>\r\n</ul>\r\n<ul>\r\n<li>In non-Unicode systems:<br />SPUMG, the tool to convert non-Unicode systems to Unicode systems, cannot determine the LANG field which gives the encoding code page of source data.</li>\r\n</ul>\r\n<ul>\r\n<li>In Unicode or non-Unicode systems:<br />applications may rely on the LANG field for correct language dependent processing of data.</li>\r\n</ul>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>RFC Unicode non-Unicode MDMP code page conversion<br />SPUMG Textflag TYPELOAD_NEW_VERSION Languflag<br />RADNTLANG</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3>\r\n<p>LANG fields can have an additional DDIC attribute (the \"text language flag\") beginning with, and including, release 6.20. This Note describes how to add and how to maintain the text language flag.<br />The text language flag denotes the type LANG field of a structure which can be used to determine the language, and hence the encoding, of text data in a structure value (table line, etc).<br /><br />Please understand that the semantics of this DDIC attribute forbid data designs with differently encoded fields within a structure. The mechanisms listed below can not handle such data correctly.<br /><br />Examples:</p>\r\n<ul>\r\n<li>2 LANG fields, 1 flag:<br />structure T002T has LANG fields SPRAS and SPRSL, where SPRSL is the language the name of which in language SPRAS is stored in field SPTXT. Hence in T002T the LANG field SPRAS is flagged as the text language; LANG field SPRSL is not flagged because it indicates the language the name of which can be given in other languages (indicated by field SPRAS).</li>\r\n</ul>\r\n<ul>\r\n<li>1 LANG field, with flag:<br />Table MAKT contains material descriptions in different languages; the type LANG field indicates the language (hence encoding) of the description text.</li>\r\n</ul>\r\n<ul>\r\n<li>1 LANG field, without flag:<br />The type LANG field of table KNA1 designates the correspondence language for this customer/contact; it does not allow to deduce the encoding of text data in a record. Cf Note 680695 for details.</li>\r\n</ul>\r\n<p><br /><br />The following mechanisms require this flag:</p>\r\n<ul>\r\n<li>in Unicode systems:<br />RFC between Unicode and non-Unicode, single code page or multiple code page (MDMP), systems</li>\r\n</ul>\r\n<ul>\r\n<li>in non-Unicode systems:<br />Conversion of a non-Unicode system to a Unicode system (trx SPUMG)</li>\r\n</ul>\r\n<ul>\r\n<li>R3trans, when transporting text data between non-Unicode and Unicode systems.</li>\r\n</ul>\r\n<ul>\r\n<li>Abap statement IMPORT FROM DATABASE: in Unicode systems when importing data exported by an MDMP system, and in non-Unicode systems when importing data exported by a Unicode system.</li>\r\n</ul>\r\n<p><strong>RFC between Unicode and non-Unicode systems:</strong></p>\r\n<p>When RFC converts text data contained in TABLES parameters between Unicode and non-Unicode systems, it converts on the Unicode side from/to the code page in which the text data are encoded on the non-Unicode side.<br />The encoding code page depends on the text language which is taken from LANG fields in the structure. Note 881781 (\"Unicode/non-Unicode RFC language and code page assignment\") describes this dependency.<br /><br />You find tutorial information on RFC in these configurations on the SAP Service Marketplace. This link leads you to the information:<br />&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;www.service.sap.com/unicode@sap<br />-&gt; Unicode Library -&gt; ABAP and Unicode -&gt; SPC251. SPC251 is the tutorial on ABAP RFC programming.<br /><br />When a structure has multiple LANG fields, and one of these fields contains the text language, then this must be flagged manually as the text language (see Solution A).<br />When a structure has 1 LANG field this is implied to be the text language at structure definition time and the text language flag is set; when that LANG field does not indicate the text language, please reset its text language flag manually (cf Solution A).<br />When the RFC engine inspects TABLES parameter structures for text language information it only considers LANG fields where the text language flag is set. This applies to structures with exactly 1 LANG field as well.<br /><br /><br />.INCLUDE and .APPEND:<br />structures which refer to other structures by .INCLUDE or .APPEND do not inherit text language flags present in referred structures. The text language flag must be set at the level of the referring structure again. To modify the text language flag you might proceed as follows:</p>\r\n<ul>\r\n<li>in SE11 select the table or structure, in change mode;</li>\r\n</ul>\r\n<ul>\r\n<li>press \"Expand all\" to expand .INCLUDEs/.APPENDs;</li>\r\n</ul>\r\n<ul>\r\n<li>find the component in question (type: LANG), and double click it (in the Component field);</li>\r\n</ul>\r\n<ul>\r\n<li>you then get a Component Maintenance screen, with the modifiable \"Text Language\" check box at the bottom.</li>\r\n</ul>\r\n<p><br /><br />When the RFC can not determine the text language due to missing LANG fields or text language flags, it uses a transport code page which generally depends on the logon language initiating the RFC; see the online documentation for details.</p>\r\n<p><strong>System Conversion non-Unicode to Unicode:</strong></p>\r\n<p>SPUMG prepares control data for the conversion in the non-Unicode system. In order to determine the encoding of text data in a table it uses LANG fields and their text language flags in the table structure; if no flagged LANG field is found, then a default code page is used.</p>\r\n<p><strong>R3trans transports between non-Unicode and Unicode systems:</strong></p>\r\n<p>R3trans exports the language/code page configuration from a non-Unicode source system, together with the transport data payload; when importing into the Unicode destination system it converts from the code page associated with the LANG field which has an active text flag; otherwise (no LANG field with an active text flag, or no LANG field at all) it converts from the Latin-1 code page.<br />In the opposite direction (Unicode to non-Unicode) R3trans exports from the Unicode source system in Unicode encoding; when importing Unicode transport data into a non-Unicode destination system it converts into the non-Unicode code page associated with the text language per LANG field and text flag; otherwise it converts into the Latin-1 code page or another explicitly specified code page.<br />Cf Notes 638357 and 80727 for details.<br /><br /></p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p><strong>A: Maintaining single text flags:</strong></p>\r\n<ul>\r\n<li>SE11: select the table or structure.</li>\r\n</ul>\r\n<ul>\r\n<li>Display the field list</li>\r\n</ul>\r\n<ul>\r\n<li>expand any structures included by .INCLUDE or .APPEND. See the comment on INCLUDE and APPEND above.</li>\r\n</ul>\r\n<ul>\r\n<li>Select the field you want to maintain, double click this field.</li>\r\n</ul>\r\n<ul>\r\n<li>The field description contains a check box, near the bottom,&#160;&#160;which you set or reset&#160;&#160;as appropriate.<br />This check box has F1 help attached to it.</li>\r\n</ul>\r\n<p><br />Note:<br />When creating a new table or structure with 1 LANG field, the text language flag of this field is set implicitly (starting with Basis Release 6.20 SP19).<br />Please review the semantics of this structure, and reset this flag if this default is not appropriate.</p>\r\n<p><strong>B: Adding the text language flag to Release 6.20:</strong></p>\r\n<p>Report RADNTLANG sets the text language flag on fields of type LANG when there is no other field of this type in the same structure and when the structure is not on a list of exceptions (there are structures with one LANG field where this field does not specify the text language). See the report for the current exception list.<br />The report is meant to reach an initial setting of the text language flag; repeat it with care as it may reset text language flags removed after its last execution.<br />To run the report proceed as follows:</p>\r\n<ul>\r\n<li>Empty table DDNTDONE (in which the report lists the done objects; import of an unclean SP level may undo this effect). You can empty the table via trx SE16: mark all entries and delete them.</li>\r\n</ul>\r\n<ul>\r\n<li>Run report RADNTLANG. In order to avoid aborts due to invalidated ABAP-loads run RADNTLANG when the machine load is low and in \"inactive\" mode. It may be preferrable to run it in background.</li>\r\n<ul>\r\n<li>See also&#160;Note 678221: mark check box parameter \"Activate Inactively\" and leave the other parameters empty. This mode requires an empty table DDXTT.</li>\r\n</ul>\r\n<li>The run time in a 6.20 BW system (installed components: SAP_BASIS, SAP_ABA, SAP_BW, PI_BASIS, FINBASIS, SEM-BW) was 45m.<br />Messages about DDIC-objects M_MTV*, and warnings,&#160;&#160;can be ignored.</li>\r\n</ul>\r\n<p>Notes:</p>\r\n<ul>\r\n<li>Depending on the system load, several short dumps may occur during the execution of RADNTLANG. Please execute RADNTLANG only while your system load is low.</li>\r\n</ul>\r\n<ul>\r\n<li>In releases following 6.20, the Upgrade to that release will set the text language flag for SAP deliverables.<br />DO NOT import into and run this 620 RADNTLANG report in releases younger than 6.20.</li>\r\n</ul>\r\n<ul>\r\n<li>The Unicode Conversion Guide 6.20/6.30 may request execution of RADNTLANG, see there for details.</li>\r\n</ul>\r\n<p><strong>C: RADNTLANG in Releases 640 and newer:</strong></p>\r\n<p>Customers can use RADNTLANG to set the text language flag on customer objects according to above criteria.<br />CAUTION: Run the report only once, or repeat it carefully. Text language flags you removed since last execution of the report, will be set by repeating the report.&#160;&#160;Your changes to text language flags on customer objects will be overwritten.<br />RADNTLANG must not and will not modify SAP objects.<br />NOTE: please expect run times of several minutes, as only customer objects are covered.</p>\r\n<p><strong>D: Obtaining RADNTLANG:</strong></p>\r\n<p>For releases 6.20 and 6.40, the latest version of RADNTLANG that is shipped with support packages comes with SAP_BASIS 6.20 SP 70 and 6.40 SP 28, respectively. To obtain the latest version, apply then the corrections as of note 1801755.</p>\r\n<p>For newer releases, the applicable RADNTLANG version is comprised in the installed SAP_BASIS support package.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-DWB-DIC-AC (ABAP Dictionary Activation and Conversion)"}, {"Key": "Other Components", "Value": "BC-MID-RFC (RFC)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D051027)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (D022370)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "De<PERSON>ch", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000480671/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000480671/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "986236", "RefComponent": "CA-GTF-TS-SMT", "RefTitle": "\"Text language\" flag for field PFLSP in table TJ20", "RefUrl": "/notes/986236"}, {"RefNumber": "963252", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/963252"}, {"RefNumber": "941602", "RefComponent": "CA-GTF-TS-GMA", "RefTitle": "Missing text indicator for AFX*-tables", "RefUrl": "/notes/941602"}, {"RefNumber": "941419", "RefComponent": "CA-GTF-TS-PPM", "RefTitle": "PPF: \"Text language\" indicator not set", "RefUrl": "/notes/941419"}, {"RefNumber": "933710", "RefComponent": "MM-IM-RS-RS", "RefTitle": "RESB: Set flag \"Text language\" for LTXSP field", "RefUrl": "/notes/933710"}, {"RefNumber": "926216", "RefComponent": "CA-GTF-TS-PPO", "RefTitle": "System table entries are not translated", "RefUrl": "/notes/926216"}, {"RefNumber": "925660", "RefComponent": "", "RefTitle": "", "RefUrl": "/notes/925660"}, {"RefNumber": "924162", "RefComponent": "CA-GTF-TS-PPM", "RefTitle": "PPF: Untranslated texts for application types", "RefUrl": "/notes/924162"}, {"RefNumber": "919793", "RefComponent": "ICM-MO", "RefTitle": "Text language flag unchecked for some text tables", "RefUrl": "/notes/919793"}, {"RefNumber": "918955", "RefComponent": "PY-DE-BA", "RefTitle": "LStA/LStB: Unicodeverbesserungen", "RefUrl": "/notes/918955"}, {"RefNumber": "908207", "RefComponent": "BC-SRV-ADR", "RefTitle": "Table T005: Problems with Unicode systems, RFC", "RefUrl": "/notes/908207"}, {"RefNumber": "897591", "RefComponent": "PLM-RM", "RefTitle": "Language Information Flag Missing In Various DDIC Elements", "RefUrl": "/notes/897591"}, {"RefNumber": "889310", "RefComponent": "LO-SRS", "RefTitle": "SRS: \"Text language\" indicator in LANG fields", "RefUrl": "/notes/889310"}, {"RefNumber": "883766", "RefComponent": "LO-MD-MM", "RefTitle": "\"Text Language\" flag is not set in LANG fields", "RefUrl": "/notes/883766"}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781"}, {"RefNumber": "863993", "RefComponent": "BC-CTS-LAN", "RefTitle": "Incorrect text flag in Table TLSY7", "RefUrl": "/notes/863993"}, {"RefNumber": "826140", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer / Unicode: Text flag T800-MASTERLANG", "RefUrl": "/notes/826140"}, {"RefNumber": "814707", "RefComponent": "BC-I18-UNI", "RefTitle": "Troubleshooting for RFC connections Unicode/non-Unicode", "RefUrl": "/notes/814707"}, {"RefNumber": "800791", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: In-place Code Page Conversion ASCII to Unicode", "RefUrl": "/notes/800791"}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991"}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475"}, {"RefNumber": "696543", "RefComponent": "BW-WHM-MTD", "RefTitle": "DW Workbench translation connection - new translation tools", "RefUrl": "/notes/696543"}, {"RefNumber": "694522", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Error message SKTY000 during the translation of SEM-BPS", "RefUrl": "/notes/694522"}, {"RefNumber": "678221", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG program termination with TYPELOAD_NEW_VERSION", "RefUrl": "/notes/678221"}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835"}, {"RefNumber": "662293", "RefComponent": "KM-KW", "RefTitle": "Attribute 'Text Lang.' for tables", "RefUrl": "/notes/662293"}, {"RefNumber": "648058", "RefComponent": "BW-WHM-MTD", "RefTitle": "Unable to translate objects in the Administrator Workbench", "RefUrl": "/notes/648058"}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495"}, {"RefNumber": "644248", "RefComponent": "BC-ABA-LA", "RefTitle": "SAMT task short text corrupted after Unicode migration", "RefUrl": "/notes/644248"}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813"}, {"RefNumber": "595104", "RefComponent": "BC-DB-DBI", "RefTitle": "Runtime error TYPELOAD_NEW_VERSION", "RefUrl": "/notes/595104"}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444"}, {"RefNumber": "494639", "RefComponent": "BC-ABA-LA", "RefTitle": "TYPELOAD_NEW_VERSION short dump after a transport", "RefUrl": "/notes/494639"}, {"RefNumber": "1675680", "RefComponent": "BC-DWB-DIC", "RefTitle": "SE11: Clear the Text Language flag in an exception table", "RefUrl": "/notes/1675680"}, {"RefNumber": "1456097", "RefComponent": "IS-U-DM-MR", "RefTitle": "Structure ISU_DWLD_001_600A - Language checkbox", "RefUrl": "/notes/1456097"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "1331533", "RefComponent": "BC-SEC-AUT-RBM", "RefTitle": "Text table RBAMCTXPROVT cannot be translated", "RefUrl": "/notes/1331533"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1259939", "RefComponent": "BC-DOC-TTL", "RefTitle": "Text table design", "RefUrl": "/notes/1259939"}, {"RefNumber": "1247508", "RefComponent": "FI-SL-SL-T", "RefTitle": "Unicode: Text indicator incorrectly set in tables", "RefUrl": "/notes/1247508"}, {"RefNumber": "1100672", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG as a tool for checking the text language indicator", "RefUrl": "/notes/1100672"}, {"RefNumber": "1087727", "RefComponent": "BC-BMT-OM-CRM", "RefTitle": "DD: Indicator for a Language Field wrongly filled", "RefUrl": "/notes/1087727"}, {"RefNumber": "1051414", "RefComponent": "PM-EQM-EQ", "RefTitle": "Table EQUI, IFLOT: Incorrectly set flag 'Text Language'", "RefUrl": "/notes/1051414"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2757819", "RefComponent": "LO-MD-MM", "RefTitle": "ISPRODUCTDESCRIPTIONWD_D: Missing text language label", "RefUrl": "/notes/2757819 "}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "800791", "RefComponent": "BC-INS-AS4", "RefTitle": "IBM i: In-place Code Page Conversion ASCII to Unicode", "RefUrl": "/notes/800791 "}, {"RefNumber": "814707", "RefComponent": "BC-I18-UNI", "RefTitle": "Troubleshooting for RFC connections Unicode/non-Unicode", "RefUrl": "/notes/814707 "}, {"RefNumber": "765475", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Conversion: Troubleshooting", "RefUrl": "/notes/765475 "}, {"RefNumber": "963252", "RefComponent": "BC-I18", "RefTitle": "Workflow: Text Language Attribute Changes (SAP internal!)", "RefUrl": "/notes/963252 "}, {"RefNumber": "547444", "RefComponent": "BC-I18", "RefTitle": "RFC Enhancement for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/547444 "}, {"RefNumber": "1675680", "RefComponent": "BC-DWB-DIC", "RefTitle": "SE11: Clear the Text Language flag in an exception table", "RefUrl": "/notes/1675680 "}, {"RefNumber": "672835", "RefComponent": "BC-I18", "RefTitle": "Textflags could cause problems during Unicode conversion", "RefUrl": "/notes/672835 "}, {"RefNumber": "1247508", "RefComponent": "FI-SL-SL-T", "RefTitle": "Unicode: Text indicator incorrectly set in tables", "RefUrl": "/notes/1247508 "}, {"RefNumber": "1259939", "RefComponent": "BC-DOC-TTL", "RefTitle": "Text table design", "RefUrl": "/notes/1259939 "}, {"RefNumber": "908207", "RefComponent": "BC-SRV-ADR", "RefTitle": "Table T005: Problems with Unicode systems, RFC", "RefUrl": "/notes/908207 "}, {"RefNumber": "897591", "RefComponent": "PLM-RM", "RefTitle": "Language Information Flag Missing In Various DDIC Elements", "RefUrl": "/notes/897591 "}, {"RefNumber": "647495", "RefComponent": "BC-I18-UNI", "RefTitle": "RFC for Unicode ./. non-Unicode Connections", "RefUrl": "/notes/647495 "}, {"RefNumber": "1456097", "RefComponent": "IS-U-DM-MR", "RefTitle": "Structure ISU_DWLD_001_600A - Language checkbox", "RefUrl": "/notes/1456097 "}, {"RefNumber": "1331533", "RefComponent": "BC-SEC-AUT-RBM", "RefTitle": "Text table RBAMCTXPROVT cannot be translated", "RefUrl": "/notes/1331533 "}, {"RefNumber": "79991", "RefComponent": "BC-I18", "RefTitle": "Multi-Language and Unicode support of SAP applications", "RefUrl": "/notes/79991 "}, {"RefNumber": "678221", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG program termination with TYPELOAD_NEW_VERSION", "RefUrl": "/notes/678221 "}, {"RefNumber": "494639", "RefComponent": "BC-ABA-LA", "RefTitle": "TYPELOAD_NEW_VERSION short dump after a transport", "RefUrl": "/notes/494639 "}, {"RefNumber": "1100672", "RefComponent": "BC-DWB-DIC-AC", "RefTitle": "RADNTLANG as a tool for checking the text language indicator", "RefUrl": "/notes/1100672 "}, {"RefNumber": "1051414", "RefComponent": "PM-EQM-EQ", "RefTitle": "Table EQUI, IFLOT: Incorrectly set flag 'Text Language'", "RefUrl": "/notes/1051414 "}, {"RefNumber": "883766", "RefComponent": "LO-MD-MM", "RefTitle": "\"Text Language\" flag is not set in LANG fields", "RefUrl": "/notes/883766 "}, {"RefNumber": "1087727", "RefComponent": "BC-BMT-OM-CRM", "RefTitle": "DD: Indicator for a Language Field wrongly filled", "RefUrl": "/notes/1087727 "}, {"RefNumber": "881781", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode/non-Unicode RFC language and code page assignment", "RefUrl": "/notes/881781 "}, {"RefNumber": "986236", "RefComponent": "CA-GTF-TS-SMT", "RefTitle": "\"Text language\" flag for field PFLSP in table TJ20", "RefUrl": "/notes/986236 "}, {"RefNumber": "696543", "RefComponent": "BW-WHM-MTD", "RefTitle": "DW Workbench translation connection - new translation tools", "RefUrl": "/notes/696543 "}, {"RefNumber": "662293", "RefComponent": "KM-KW", "RefTitle": "Attribute 'Text Lang.' for tables", "RefUrl": "/notes/662293 "}, {"RefNumber": "941602", "RefComponent": "CA-GTF-TS-GMA", "RefTitle": "Missing text indicator for AFX*-tables", "RefUrl": "/notes/941602 "}, {"RefNumber": "941419", "RefComponent": "CA-GTF-TS-PPM", "RefTitle": "PPF: \"Text language\" indicator not set", "RefUrl": "/notes/941419 "}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "933710", "RefComponent": "MM-IM-RS-RS", "RefTitle": "RESB: Set flag \"Text language\" for LTXSP field", "RefUrl": "/notes/933710 "}, {"RefNumber": "926216", "RefComponent": "CA-GTF-TS-PPO", "RefTitle": "System table entries are not translated", "RefUrl": "/notes/926216 "}, {"RefNumber": "925660", "RefComponent": "BC-I18", "RefTitle": "Basis object last changed by MAU on 2003-8-25", "RefUrl": "/notes/925660 "}, {"RefNumber": "924162", "RefComponent": "CA-GTF-TS-PPM", "RefTitle": "PPF: Untranslated texts for application types", "RefUrl": "/notes/924162 "}, {"RefNumber": "918955", "RefComponent": "PY-DE-BA", "RefTitle": "LStA/LStB: Unicodeverbesserungen", "RefUrl": "/notes/918955 "}, {"RefNumber": "919793", "RefComponent": "ICM-MO", "RefTitle": "Text language flag unchecked for some text tables", "RefUrl": "/notes/919793 "}, {"RefNumber": "889310", "RefComponent": "LO-SRS", "RefTitle": "SRS: \"Text language\" indicator in LANG fields", "RefUrl": "/notes/889310 "}, {"RefNumber": "863993", "RefComponent": "BC-CTS-LAN", "RefTitle": "Incorrect text flag in Table TLSY7", "RefUrl": "/notes/863993 "}, {"RefNumber": "826140", "RefComponent": "FI-SL-IS-A", "RefTitle": "Report Writer / Unicode: Text flag T800-MASTERLANG", "RefUrl": "/notes/826140 "}, {"RefNumber": "643813", "RefComponent": "BW-SYS", "RefTitle": "Composite SAP note - BW Unicode", "RefUrl": "/notes/643813 "}, {"RefNumber": "648058", "RefComponent": "BW-WHM-MTD", "RefTitle": "Unable to translate objects in the Administrator Workbench", "RefUrl": "/notes/648058 "}, {"RefNumber": "694522", "RefComponent": "FIN-SEM-BPS", "RefTitle": "Error message SKTY000 during the translation of SEM-BPS", "RefUrl": "/notes/694522 "}, {"RefNumber": "644248", "RefComponent": "BC-ABA-LA", "RefTitle": "SAMT task short text corrupted after Unicode migration", "RefUrl": "/notes/644248 "}, {"RefNumber": "595104", "RefComponent": "BC-DB-DBI", "RefTitle": "Runtime error TYPELOAD_NEW_VERSION", "RefUrl": "/notes/595104 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "500", "To": "500", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "605", "To": "605", "Subsequent": ""}, {"SoftwareComponent": "EA-RETAIL", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "SAP_HR", "From": "470", "To": "470", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "620", "To": "640", "Subsequent": "X"}, {"SoftwareComponent": "SAP_BASIS", "From": "700", "To": "701", "Subsequent": ""}, {"SoftwareComponent": "SAP_BASIS", "From": "710", "To": "711", "Subsequent": ""}, {"SoftwareComponent": "SCM", "From": "410", "To": "410", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "620", "To": "620", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "640", "To": "640", "Subsequent": ""}, {"SoftwareComponent": "SAP_ABA", "From": "700", "To": "700", "Subsequent": ""}, {"SoftwareComponent": "PI", "From": "2004_1_500", "To": "2004_1_500", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "SCM 410", "SupportPackage": "SAPKY41011", "URL": "/supportpackage/SAPKY41011"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47025", "URL": "/supportpackage/SAPKH47025"}, {"SoftwareComponentVersion": "SAP_APPL 470", "SupportPackage": "SAPKH47028", "URL": "/supportpackage/SAPKH47028"}, {"SoftwareComponentVersion": "SAP_HR 470", "SupportPackage": "SAPKE47073", "URL": "/supportpackage/SAPKE47073"}, {"SoftwareComponentVersion": "SAP_APPL 500", "SupportPackage": "SAPKH50011", "URL": "/supportpackage/SAPKH50011"}, {"SoftwareComponentVersion": "SAP_APPL 600", "SupportPackage": "SAPKH60002", "URL": "/supportpackage/SAPKH60002"}, {"SoftwareComponentVersion": "EA-RETAIL 600", "SupportPackage": "SAPKGPRD02", "URL": "/supportpackage/SAPKGPRD02"}, {"SoftwareComponentVersion": "SAP_APPL 605", "SupportPackage": "SAPKH60501", "URL": "/supportpackage/SAPKH60501"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62066", "URL": "/supportpackage/SAPKB62066"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62059", "URL": "/supportpackage/SAPKB62059"}, {"SoftwareComponentVersion": "SAP_ABA 620", "SupportPackage": "SAPKA62063", "URL": "/supportpackage/SAPKA62063"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62051", "URL": "/supportpackage/SAPKB62051"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62034", "URL": "/supportpackage/SAPKB62034"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62060", "URL": "/supportpackage/SAPKB62060"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62062", "URL": "/supportpackage/SAPKB62062"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62019", "URL": "/supportpackage/SAPKB62019"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62035", "URL": "/supportpackage/SAPKB62035"}, {"SoftwareComponentVersion": "SAP_ABA 620", "SupportPackage": "SAPKA62031", "URL": "/supportpackage/SAPKA62031"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62033", "URL": "/supportpackage/SAPKB62033"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62061", "URL": "/supportpackage/SAPKB62061"}, {"SoftwareComponentVersion": "SAP_BASIS 620", "SupportPackage": "SAPKB62015", "URL": "/supportpackage/SAPKB62015"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64020", "URL": "/supportpackage/SAPKB64020"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64019", "URL": "/supportpackage/SAPKB64019"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64017", "URL": "/supportpackage/SAPKB64017"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64018", "URL": "/supportpackage/SAPKB64018"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64024", "URL": "/supportpackage/SAPKB64024"}, {"SoftwareComponentVersion": "SAP_ABA 640", "SupportPackage": "SAPKA64021", "URL": "/supportpackage/SAPKA64021"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64015", "URL": "/supportpackage/SAPKB64015"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64012", "URL": "/supportpackage/SAPKB64012"}, {"SoftwareComponentVersion": "SAP_BASIS 640", "SupportPackage": "SAPKB64004", "URL": "/supportpackage/SAPKB64004"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70010", "URL": "/supportpackage/SAPKB70010"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70009", "URL": "/supportpackage/SAPKB70009"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70008", "URL": "/supportpackage/SAPKB70008"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70011", "URL": "/supportpackage/SAPKB70011"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70001", "URL": "/supportpackage/SAPKB70001"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70017", "URL": "/supportpackage/SAPKB70017"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70018", "URL": "/supportpackage/SAPKB70018"}, {"SoftwareComponentVersion": "SAP_BASIS 700", "SupportPackage": "SAPKB70019", "URL": "/supportpackage/SAPKB70019"}, {"SoftwareComponentVersion": "SAP_ABA 700", "SupportPackage": "SAPKA70014", "URL": "/supportpackage/SAPKA70014"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70104", "URL": "/supportpackage/SAPKB70104"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70102", "URL": "/supportpackage/SAPKB70102"}, {"SoftwareComponentVersion": "SAP_BASIS 701", "SupportPackage": "SAPKB70103", "URL": "/supportpackage/SAPKB70103"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71008", "URL": "/supportpackage/SAPKB71008"}, {"SoftwareComponentVersion": "SAP_BASIS 710", "SupportPackage": "SAPKB71007", "URL": "/supportpackage/SAPKB71007"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71102", "URL": "/supportpackage/SAPKB71102"}, {"SoftwareComponentVersion": "SAP_BASIS 711", "SupportPackage": "SAPKB71101", "URL": "/supportpackage/SAPKB71101"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}