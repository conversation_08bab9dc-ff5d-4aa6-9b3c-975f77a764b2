{"Request": {"Number": "1109554", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1296, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000006594452017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0001109554?language=E&token=A986B42F5306FA5311CEBE9210A6CB1D"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0001109554", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0001109554/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "1109554"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 6}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Program error"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.12.2007"}, "SAPComponentKey": {"_label": "Component", "value": "XX-PART-ISHMED"}, "SAPComponentKeyText": {"_label": "Component", "value": "Clinical System i.s.h.med"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Miscellaneous", "value": "XX", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Partner solutions", "value": "XX-PART", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Clinical System i.s.h.med", "value": "XX-PART-ISHMED", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'XX-PART-ISHMED*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "1109554 - Planning: Appointment, Movement not Linked with Appointment"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The following incorrect data constellations occur in planning:</p> <UL><LI>If you created a clinical order with a service, but plan the appointment using the function \"Maintain Appointment\" (appointment editor) rather than in the clinical order, the link between the appointment and the appointment template is missing.</LI></UL> <UL><LI>If you create a visit for the same clinical order by means of the clinical order dialog, the link between the appointment and the movement is missing.</LI></UL> <UL><LI>If you create a visit for the same clinical order by means of the appointment editor, the link between the movement and the appointment template is missing.</LI></UL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Clinical Order</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Program error</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>This Note contains the source code corrections for both methods that have caused the incorrect entries.<br />In addition, this Note contains the correction report 'RN1_CORRECT_APCNID_LFDBEW' that remedies any data inconsistencies for each institution and for the selected period.<br /><br />It is essential that you first run the correction report in test mode to check whether and how many appointments and movements require correcting. The report shows all appointments and movements that can be corrected. It also shows appointments that cannot be changed.<br />In test mode, the report does not make changes in the database.<br /><br />If you run the report without a test, it corrects the incorrect appointments and movements as follows:</p> <UL><LI>Appointments</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;For appointments without appointment template, the report copies the relevant APCNID from the table N1VKG into the appointment.<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If a movement does not exist for an appointment, the system copies the relevant LFDNR from the table NBEW into the appointment.</p> <UL><LI>Movements</LI></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;If an appointment template does not exist for a movement, the system copies the relevant APCNID from the table N1VKG into the movement.<br /><br />If appointments cannot be corrected, the report displays but does not update them.<br /><br />Note the large datasets can cause long run times for the report.<br />When there are a great many appointments and movements, we thus recommend to run the correction report in relation to shorter periods, otherwise a runtime error can occur.<br /><br />Before you implement the correction, you must make the following manual changes:</p> <OL>1. Unpack the attached file</OL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HW1109554_600.zip for i.s.h. med/IS-H Version 6.00 or <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;HW1109554_603.zip for i.s.h. med/IS-H Version 6.03. <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Important: You cannot use the SAP Support Portal to download the attached file. Instead, go to the SAP Service Marketplace to download the file (For more information about importing attachments see Notes 480180 and 13719 ). <OL>2. Import the unpacked orders into your system.</OL> <p></p> <b>In addition, you must create a number of text elements as follows:</b><br /> <UL><LI>Call the transaction SE38.</LI></UL> <UL><LI>Enter the program name RN1_CORRECT_APCNID_LFDBEW.</LI></UL> <UL><LI>Select the radio button 'Text Elements' and choose \"Change\".</LI></UL> <UL><LI>Choose&#x00A0;&#x00A0;the tab page 'Text Symbols' and create the following text symbols:</LI></UL> <UL><UL><LI>In the first row, enter the value '001' in the column 'Sym' and the text 'No entries were found. Check your input.'</LI></UL></UL> <UL><UL><LI>In the second row, enter the value '002' in the column 'Sym' and the text 'No values could be found in table NLEI.'</LI></UL></UL> <UL><UL><LI>In the third row, enter the value '003' in the column 'Sym' and the text 'All entries could be changed.'</LI></UL></UL> <UL><UL><LI>In the fourth row, enter the value '004' in the column 'Sym' and the text 'Error while updating movements.'</LI></UL></UL> <UL><UL><LI>In the fifth row, enter the value '005' in the column 'Sym' and the text 'Error while updating appointments'.</LI></UL></UL> <UL><UL><LI>In the sixth row, enter the value '006' in the column 'Sym' and the text 'Some entries could not be changed'.</LI></UL></UL> <UL><UL><LI>In the seventh row, enter the value '007' in the column 'Sym' and the text ' corrected appointments: APCNID'.</LI></UL></UL> <p>&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;&#x00A0;Respect the preceding space in the texts in row 7 to 10.</p> <UL><UL><LI>In the eighth row, enter the value '008' in the column 'Sym' and the text ' corrected appointments: seq. no.'.</LI></UL></UL> <UL><UL><LI>In the ninth row, enter the value '009' in the column 'Sym' and the text ' corrected movements: APCNID'.</LI></UL></UL> <UL><UL><LI>In the tenth row, enter the value '010' in the column 'Sym' and the text ' non-corrected appointments'.</LI></UL></UL> <UL><UL><LI>In the eleventh row, enter the value '011' in the column 'Sym' and the text 'TESTMODUS: All enties could be changed'..</LI></UL></UL> <UL><UL><LI>In the twelfth&#x00A0;&#x00A0;row, enter the value '012' in the column 'Sym' and the text 'TESTMODUS: Some entries could not be changed'.</LI></UL></UL> <p></p> <UL><LI>Choose the tab page \"Selection Texts' and create the following texts for appropriate parameter:</LI></UL> <UL><UL><LI>In the row P_BEG_DT, insert the text 'Start Date'.</LI></UL></UL> <UL><UL><LI>In the row P_EINRI, insert the text 'Institution'.</LI></UL></UL> <UL><UL><LI>In the row P_END_DT, insert the text 'End Date'.</LI></UL></UL> <UL><UL><LI>In the row P_TEST, insert the text 'Test Mode'.</LI></UL></UL> <UL><LI>Save and activate the text elements.</LI></UL> <p></p> <b>Create the GUI title</b><br /> <UL><UL><LI>Call the Object Navigator (SE80).</LI></UL></UL> <UL><LI>Choose 'Program' and enter 'RN1_CORRECT_APCNID_LFDBEW'. Confirm your input by choosing 'Enter'.</LI></UL> <UL><LI>Click the program with the right mouse button and create the GUI title ('Create -&gt; GUI Title').</LI></UL> <UL><LI>Enter the title code TITLE_0100 and the title 'Appts and Mvmts to Be Changed'.</LI></UL> <UL><LI>Save and activate the GUI title.</LI></UL> <p></p> <b>Create the GUI status</b><br /> <UL><LI>Click the program with the right mouse button and create the GUI status ('Create -&gt; GUI Status').</LI></UL> <UL><LI>Enter the status STATUS_0100 and the short text 'Status 0100'.</LI></UL> <UL><LI>Important: The radio button for the dialog text must be set, and continue by choosing 'Enter'.</LI></UL> <UL><LI>Expand the 'Function Keys' node by clicking the hotspot with '+'.</LI></UL> <UL><LI>Consider only the entries for the standard toolbar.</LI></UL> <UL><LI>Enter the function code for the following items:</LI></UL> <UL><UL><LI>3rd character from left (\"Back\" pushbutton): Function code 'BACK'.</LI></UL></UL> <UL><UL><LI>4th character from left (\"Exit\" pushbuton): Function code 'EXIT'.</LI></UL></UL> <UL><UL><LI>5th character from left (\"Cancel\" pushbutton): Function code 'CANCEL'.</LI></UL></UL> <UL><LI>Save and activate the GUI status.</LI></UL> <p><br />See the source code corrections.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "XX-PART-ISHMED-ORD (Service Management i.s.h.med)"}, {"Key": "Responsible                                                                                         ", "Value": "C5094209"}, {"Key": "Processor                                                                                           ", "Value": "C5001995"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0001109554/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0001109554/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "HW1109554_600.zip", "FileSize": "5", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000778722007&iv_version=0006&iv_guid=DFC342AB80736B46911349DD186EC824"}, {"FileName": "HW1109554_603.zip", "FileSize": "4", "MimeType": "application/x-zip-compressed", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200000778722007&iv_version=0006&iv_guid=F1CCCFB843CBC44BB132E1DEE15C8BE8"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "IS-H", "From": "600", "To": "600", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "602", "To": "602", "Subsequent": ""}, {"SoftwareComponent": "IS-H", "From": "603", "To": "603", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": [{"SoftwareComponentVersion": "IS-H 600", "SupportPackage": "SAPK-60013INISH", "URL": "/supportpackage/SAPK-60013INISH"}, {"SoftwareComponentVersion": "IS-H 602", "SupportPackage": "SAPK-60202INISH", "URL": "/supportpackage/SAPK-60202INISH"}, {"SoftwareComponentVersion": "IS-H 603", "SupportPackage": "SAPK-60301INISH", "URL": "/supportpackage/SAPK-60301INISH"}]}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": [{"SoftwareComponent": "IS-H", "NumberOfCorrin": 4, "URL": "/corrins/0001109554/6"}]}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 4, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 3, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": [{"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "891427 ", "URL": "/notes/891427 ", "Title": "Clin.Order: Div. <PERSON><PERSON>rs When Planning in Collective Order", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "939208 ", "URL": "/notes/939208 ", "Title": "Planning: Planning Grid - Moving Appointment Blocks", "Component": "XX-PART-ISHMED"}, {"SoftwareComponent": "IS-H", "ValidFrom": "600", "ValidTo": "600", "Number": "1067096 ", "URL": "/notes/1067096 ", "Title": "Planning: Appointment Planning Process - New Business Add-In", "Component": "IS-H"}]}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}