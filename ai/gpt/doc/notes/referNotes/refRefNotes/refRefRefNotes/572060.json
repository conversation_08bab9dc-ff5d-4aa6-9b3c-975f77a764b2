{"Request": {"Number": "572060", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 315, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015322862017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000572060?language=E&token=0E4ACA058205ECE08B77EE1AFAED7936"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000572060", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000572060/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "572060"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 9}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Consulting"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "03.04.2003"}, "SAPComponentKey": {"_label": "Component", "value": "BC-DB-ORA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Oracle"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Basis Components", "value": "BC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Database Interface, Database Platforms", "value": "BC-DB", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Oracle", "value": "BC-DB-ORA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BC-DB-ORA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "572060 - Options in the Oracle database during archiving"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>The space released in the database as a result of the data archiving is not fully available for new data. Due to fragmentation of both data and indexes, the expected improvement in performance following the data archiving does not occur. This may lead to long data archiving deletion runs. Inserting new data in data blocks that are only partially cleared results in a deterioration of performance when you insert new data, as well as when you carry out subsequent operations on this data.</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>Partitioning, PCTUSED, archiving, free space, reorganization, fragmentation</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>The following notes are intended to provide you, the customer, with information on how to optimize the disk space released by the data archiving to improve performance and store new data.<br />When you delete larger datasets, which occurs in particular during the archiving, you must note some special features of Oracle databases. These are associated with the \"space management\" within a database block and the selected archiving criteria. Data archiving generally has two aims:<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;1. Improving performance<br />&#x00A0;&#x00A0;&#x00A0;&#x00A0;2. Ensuring that memory space is released as quickly as possible after archiving is completed within a table, in the tablespace or on the file system.<br />Only by planning in advance can you simultaneously optimize both the level of improvements in performance and the amount of reusable space. Fragmentation of the released space is always a cause of suboptimum results. Therefore, it is important to understand the parameters that affect fragmentation.<br /><br /><STRONG>1. Fragmentation</STRONG><br />To administer the space, the database uses data blocks, which generally contain several data records or index entries for a table. When the data is archived, some of these data records and index entries are deleted. It is always the case that the space released without reorganization can only be reused by new entries in the same table. This is not a problem with data archiving carried out in good time and on a regular basis. Therefore, a reorganization is only necessary if you started the data archiving too late. This is because, during the first archiving, more space may become free in a table than will be required for it in the future in the case of regular archiving.<br />Apart from this effect, note that (for the same table) you can also only reuse without limit those data blocks that were completely emptied. If partially filled blocks remain, this is referred to as fragmentation. The level of fragmentation of the blocks for the data part of the tables is generally low if all data incurred during a specific period is archived simultaneously. Significant fragmentation may occur if a lot of data for a period to be archived remains in the database - either because it does not meet the business prerequisites for an archiving run or because additional selection criteria (for example, for plants, controlling area and so on) were used along with a chronological restriction during the data archiving. Half-filled data blocks are also created with tables that are contained in different archiving objects, for example, in the VBFA table (sales document flow in SD).<br />As well as fragmentation in the data part of the tables, you must also take fragmentation of the indexes into account. In this case, you can always distinguish between chronologically sorted indexes and non-chronologically sorted indexes. In particular, indexes for a more or less chronologically sorted document number are also included in the chronologically sorted indexes. Examples of non-chronologically sorted indexes are indexes about material numbers and business partners. Generally, the level of fragmentation is highest among non-chronologically sorted indexes.<br /><br /><STRONG>2. Effect of fragmentation on performance</STRONG><br />Fragmentation may have a significant influence on system performance. Compared with a database in which all blocks are either completely free or completely filled with data, the data buffer of the database contains less user data. As a result, the buffer quality is not optimal.<br />In this case, data archiving it does not generally lead to a loss of performance when you access existing data. However, the potential to improve performance is not fully exploited.<br />In some cases, the increasing fragmentation of indexes may even cause the performance of the delete programs of the data archiving to suffer.<br />Fragmentation is also important for the insertion of new data. If the system only provides partially emptied blocks for new data, then the number of blocks required for this new data increases. This also increases the chances of data that belongs together being distributed over several blocks. This may also lead to a loss of performance during subsequent read accesses on this data. Therefore, you must also take the performance aspects into account along with the desire to enable the space to be reused as quickly as possible when you administer the freespace.<br /><br /><STRONG>3. Reorganizing the indexes</STRONG><br />After the archiving, it is advisable to reorganize the indexes of the tables affected by the archiving. As of Oracle 8I, you can carry out this task on-line and you can repeat it as often as you wish. The SAPDBA tools also support these reorganization measures. Since you can reorganize the index on-line on Oracle 8i, there is no need to shut down the system or wait for a maintenance window, for example, at the weekend. To avoid increasing fragmentation within the index tablespaces, you should create the indexes in a locally managed tablespace (autoallocate, 64K start extent size) or reorganize the indexes for the first time there. Locally managed tablespaces are fully supported by the SAP System and are the default tablespace type as of SAPDBA 6.2.<br />If you notice a severe loss of performance during a data archiving deletion run, you should use an SQL trace to determine the database operation during which the performance problem occurs. If the access is processed using an index that is actually well suited, it may already be necessary to reorganize it during the deletion run.<br /><br /><STRONG>4. Freespace administration within the data blocks</STRONG><br />Data blocks that have space for the insertion of new data of a table are administered by Oracle in a free list. The data blocks are not completely filled during the insert operations.<br />The PCTFREE parameter determines how much freespace is to remain if a block is taken from the free list. This space is then available for update operations and is used to avoid \"chained rows\". The PCTUSED parameter defines the maximum level that a block must exceed due to the deletion of data before it is available again for insert operations. The default values delivered by SAP are 10% for PCTFREE and 40% for PCTUSED.<br />The Oracle block size in SAP Systems is 8192 bytes. You can save approximately 7,300 bytes in an Oracle initial block (8,192 bytes - 150 bytes administration data - 10% PCTFREE). If, on the other hand, you use a block with a fill level of 40% (3,200 bytes), then only 7,300 bytes minus 3,200 bytes, that is, 4,100 bytes remain for the inclusion of new table entries. In the case of heavy fragmentation, this means that the number of blocks used to save new data may increase by up to 50%. If you cannot use suitable measures to avoid fragmentation, then the mechanism described here may lead to a deterioration in performance when you add new data. You can solve this problem using two strategies. Both strategies require a once-off reorganization of the affected tables.<br /><br /><STRONG>5. Reducing the PCTUSED value of a block</STRONG><br />If you reduce the PCTUSED value from 40% to 20% or 10%, the blocks are subsequently used (under the problematic conditions mentioned above) for insert operations after the archiving runs because the blocks must be further emptied to a considerable extent. In this case, a PCTUSED value of 20% corresponds to approximately 1,600 bytes and a PCTUSED value of 10% of only 800 bytes. Since the changed PCTUSED value is only affects newly allocated extents, it is essential that you reorganize the entire table. Note that for an archiving that is based exclusively on chronological characteristics, the selected PCTUSED value only has a very limited effect because the blocks are completely emptied. The disadvantage of this solution is that, in certain circumstances, it takes considerably longer until blocks from the delete operations of the archiving are available again for insert operations. As a result, tables with a lower PCTUSED value will generally remain somewhat larger. The increase depends on when the filling level falls below the defined value. If PCTUSED is defined too low, these blocks may never be used again in certain circumstances. Therefore, we advise against using a PCTUSED value lower than 10%.<br /><br /><STRONG>6. Using partitioning</STRONG><br />A more interesting alternative is to use Oracle partitioning. Partitioning is especially useful for tables that have a direct or indirect time key. VBRK, VBRP and VBFA are examples of these types of tables.The indirect time key of these tables consists of the VBELN field (or fields associated with this, for example VBELN_V, VBELN_N, and so on). Since the entries in these fields are sequentially incremented with a sequential time, you can use them as a range partitioning key. The idea here is to create a separate partition for a specific area of the VBELN numbers (for example, for a month). In addition, the unique index of the tables is defined as \"Local\", that is, each table partition contains only one specific subarea of the data (ideally, a month).<br />If you now carry out archiving runs, the data is deleted from these partitions only. No other partitions would be affected by the archiving runs. In this case, half-empty blocks do not represent a problem because, due to the partitioning key, no more data arrives in these blocks. Since partitions are dealt with as independent storage objects within Oracle, these partitions can be deleted or summarized very simply after the archiving runs.If, in addition, the partitions are created in separate tablespaces, the memory space can be released directly at file system level.<br />The disadvantage of this solution is that you constantly have to create new partitions because the partitioning key continually changes. However, this is not generally a problem because the number of partitions is manageable and you can create a large number of partitions in advance. First, a reorganization is also required here to transfer the normal table into a partitioned table. In this case, you should also create partitioned tables in separate tablespaces. You should also use locally managed tablespaces that, in this case, can have a unit value (uniform size) of 1M.<br />The methods shown above cannot solve the problems that are caused by an initial archiving that is started too late. In general, you must first reorganize the affected tables. However, Oracle partitioning provides you with an interesting alternative, in particular, if it concerns releasing occupied memory space in the file system because each partition can be created in a separate tablespace.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>Following the discussion above, it is important to consider the following during the data archiving:<br /><br />1. Begin the data archiving as early as possible and execute it regularly.<br />2. Remember that selection criteria that not only limit the data to be archived chronologically lead to fragmentation and you should try to reduce these criteria to a minimum.<br />3. Note that fragmentation will occur if much of the data within the selection area of the archiving programs is not archivable. Establish the reasons for this and try to reduce them to a minimum.<br />4. Regularly reorganize the indexes of the affected tables after archiving runs. In certain circumstances, it may make sense to carry out this kind of reorganization during the current delete programs if the performance of these programs is poor.<br />5. If you anticipate that fragmentation is unavoidable in some tables, it is useful to influence the freespace administration within Oracle data blocks by changing the PCTUSED and PCTFREE parameters or to use partitioning to enhance the data management options.</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "BC-CCM-ADK (Archive Development Kit)"}, {"Key": "Responsible                                                                                         ", "Value": "D020415"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000572060/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000572060/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000572060/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687"}, {"RefNumber": "766827", "RefComponent": "SCM-APO-MD", "RefTitle": "Composite SAP note: Performance SCM 4.0", "RefUrl": "/notes/766827"}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803"}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393"}, {"RefNumber": "409376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Use of locally managed tablespaces for SAP R/3", "RefUrl": "/notes/409376"}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995"}, {"RefNumber": "105047", "RefComponent": "BC-DB-ORA", "RefTitle": "Support for Oracle functions in the SAP environment", "RefUrl": "/notes/105047"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "821687", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Space utilization and fragmentation in Oracle", "RefUrl": "/notes/821687 "}, {"RefNumber": "766827", "RefComponent": "SCM-APO-MD", "RefTitle": "Composite SAP note: Performance SCM 4.0", "RefUrl": "/notes/766827 "}, {"RefNumber": "214995", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle locally managed tablespaces in the SAP environment", "RefUrl": "/notes/214995 "}, {"RefNumber": "620803", "RefComponent": "BC-DB-ORA", "RefTitle": "Oracle 9i: Automatic Segment Space Management", "RefUrl": "/notes/620803 "}, {"RefNumber": "409376", "RefComponent": "BC-DB-ORA-DBA", "RefTitle": "Use of locally managed tablespaces for SAP R/3", "RefUrl": "/notes/409376 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": []}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}