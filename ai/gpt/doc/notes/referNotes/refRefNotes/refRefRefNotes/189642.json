{"Request": {"Number": "189642", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 940, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000014754352017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000189642?language=E&token=9648375E89B31AAFA4473320D1D40533"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000189642", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000189642/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "189642"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 10}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "Legal change"}, "Priority": {"_label": "Priority", "value": "Correction with medium priority"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "16.04.2000"}, "SAPComponentKey": {"_label": "Component", "value": "IS-H-PA"}, "SAPComponentKeyText": {"_label": "Component", "value": "Patient Accounting"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Industry-Specific Components", "value": "IS", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Hospital", "value": "IS-H", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Patient Accounting", "value": "IS-H-PA", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'IS-H-PA*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "189642 - IS-H:Cont. flat rate per/pro.surch. on basis ICD-10"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><p>What is the current status of the delivery of the assignment rules flat rates per case and procedures surcharges (technical name, TNFPSE) on the basis of the ICD-10?<br />Which special features should be considered?<br />Which activities should be made by the customer after the delivery?</p><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>TNFPSE, flat rate per case, procedures surcharges, ICD-10</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>Consultancy</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><p>An agreement of the assignment rules on the basis of the ICD-10 was made on 12/16/1999 between the umbrella associations of the health insurance companies and the DKG.<br />The agreement time slot was too late to be able to carry out a delivery of the assignment rules on the basis of the conclusive version in 1999. A delivery can only be carried out on the basis of the design version in 1999. The delivery on the basis of the conclusive version is planned with maintenance levels as of the middle of January. This note informs you of the current status.<br />You are supplied with the new assignment rules on the basis of the design version in calendar week 51 with the add-on patch 11 for Release 4.03 and add-on patch 11 for Release 4.02.<br />The rules for the flat rate per case begin with rule number 'X'. The rules for the procedures surcharges begin with rule number 'Y'.<br />Note that all rules are delivered as inactive. Only activate the rules for the respective flat rates per case and procedures surcharges which are also produced in your hospital. With the heavy intake of the combinations on the basis ICD-10, it is advisable for reasons of performance, to only activate the rules that are necessary. This also applies to existing rules on the basis of ICD-9. Deactivate all rules on the basis of ICD-9 as soon as possible, when you are sure that you no longer need any charge proposal for cases with admission dates in 1999.<br />Check and assign the service code for flat rates per case or procedure surcharges assigned in your hospital to the particular rule. Note that several rules are possibly valid for a service code through the scope of the definitions. You can recognize this from the sequential number in the final two digits of the rule number.<br />The table TNFPSE was delivered by SAP with in-house service catalog '01' since this in-house service catalog is valid for most users. Users whose in-house service catalog is not '01', must transfer their valid in-house service catalog to the assignment table with report RNFPSE02.<br />Limit the valid end date of your existing ICD-9 assignment rules to 12/31/1999. The newly delivered rules have the validity period of 01/01/2000 to 12/31/9999. As a basis for accessing the rule table, the admission date of the case is used. A transition rule has not been defined up to now, however this corresponds to the previous procedure for flat rates per case and procedures surcharges changes for the fiscal year change (see 5th change VO).</p></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "D020485"}, {"Key": "Processor                                                                                           ", "Value": ""}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000189642/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000189642/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "211934", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Falsche Entgeltregeln mit ICD-10 Auslieferung", "RefUrl": "/notes/211934"}, {"RefNumber": "170085", "RefComponent": "IS-H", "RefTitle": "IS-H: Support of ICD-10 for January 1, 2000", "RefUrl": "/notes/170085"}, {"RefNumber": "153153", "RefComponent": "IS-H", "RefTitle": "IS-H: Composite note on important errors in 4.03A", "RefUrl": "/notes/153153"}, {"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719"}, {"RefNumber": "119281", "RefComponent": "IS-H", "RefTitle": "IS-H: Current collective note on important errors in 4.02A", "RefUrl": "/notes/119281"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "13719", "RefComponent": "BC-CTS", "RefTitle": "Preliminary transports to customers (note for customers)", "RefUrl": "/notes/13719 "}, {"RefNumber": "119281", "RefComponent": "IS-H", "RefTitle": "IS-H: Current collective note on important errors in 4.02A", "RefUrl": "/notes/119281 "}, {"RefNumber": "153153", "RefComponent": "IS-H", "RefTitle": "IS-H: Composite note on important errors in 4.03A", "RefUrl": "/notes/153153 "}, {"RefNumber": "170085", "RefComponent": "IS-H", "RefTitle": "IS-H: Support of ICD-10 for January 1, 2000", "RefUrl": "/notes/170085 "}, {"RefNumber": "211934", "RefComponent": "IS-H-PA", "RefTitle": "IS-H: Falsche Entgeltregeln mit ICD-10 Auslieferung", "RefUrl": "/notes/211934 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "40B", "To": "40B", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}