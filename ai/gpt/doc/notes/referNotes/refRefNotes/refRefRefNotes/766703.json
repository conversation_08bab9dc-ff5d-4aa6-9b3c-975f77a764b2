{"Request": {"Number": "766703", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 1410, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "D", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000015746362017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0000766703?language=E&token=7FE3EC5829414ACF200CA11EA861F021"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0000766703", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0000766703/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "766703"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 4}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "German"}, "Category": {"_label": "Category", "value": "FAQ"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "10/19/2006"}, "SAPComponentKey": {"_label": "Component", "value": "SD-BIL-IV-PC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Payment Cards"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "Sales and Distribution", "value": "SD", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Billing", "value": "SD-BIL", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Processing Billing Documents", "value": "SD-BIL-IV", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-IV*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Payment Cards", "value": "SD-BIL-IV-PC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'SD-BIL-IV-PC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "766703 - FAQ: Credit card encryption in R/3 systems"}, "LongText": {"_label": "Description", "value": "<div class=\"mono\"><h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3><OL>1. How does the credit card encryption in the R/3 system work?</OL> <OL>2. Which algorithm is used for the encryption?</OL> <OL>3. As of which release or Support Package is the encryption available?</OL> <OL>4. How can I activate the payment card encryption?</OL> <OL>5. Which disadvantages are to be expected?</OL> <OL>6. For which potential attacks does the encryption provide protection and for which attacks it does not?</OL> <OL>7. Visibility of payment card numbers in the SD BAPIs</OL> <OL>8. Archiving</OL> <OL>9. What do I have to take into account when I upgrade from a non-Unicode system to a Unicode system?</OL><h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Reason and Prerequisites\">Reason and Prerequisites</h3><p>.</p><h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3><OL>1. This function encrypts payment card numbers before they are stored in the database and decrypts the card numbers while the document is processed if required (for example for authorization purposes). In transactions, the system displays only a masked number.</OL> <OL>2. You can use SAPCryptolib to encrypt your payment card number. For more information, see Note 662340.</OL> <OL>3. If you use Release 46C, you must import Support Package 46. Kernel 4.6D requires the patch level 1329 (see Note 565111) If you use Release 4.70, you must import Support Package 22.</OL> <OL>4. To activate the payment card encryption, proceed as described in Note 633462.</OL> <OL>5. Disadvantages of credit card encryption may include increased memory consumption, decreased performance, the need to search for payment cards, key loss or manipulation.</OL> <OL><OL>a) If encrypted, each payment card number may consume up to 1 kilobyte of memory space. If you carry out mass transactions with payment cards, take into account that encrypted payment card numbers consume more memory space than unencrypted payment cards.</OL></OL> <OL><OL>b) Since the system must encrypt and decrypt payment card numbers during the processing of sales orders and during the payment run, a loss of performance is unavoidable when you use these transactions.</OL></OL> <OL><OL>c) You cannot perform a range search for sales orders within a certain payment card number range when data is encrypted. You can search for sales orders for an individual payment card number if you are authorized to display the payment card number (authorization object V_VBAK_AAT, activity C2).</OL></OL> <OL><OL>d) As is the case with each strong encryption,&#x00A0;&#x00A0;if you lose the key, all payment card information in the system is irrevocably lost. You cannot retrieve this information, and this would not be useful for security reason. Once a potential attacker obtains the key, the encryption no longer provides&#x00A0;&#x00A0;any protection.</OL></OL> <OL>6. The encryption prevents direct database accesses to the tables that contain the payment card information or direct selects from the R/3 environment (for example, transaction SE16 or SE17) or simple reports.<br />Due to the system design, you cannot prevent the following attacks:</OL> <OL><OL>a) If the attacker has full database access, he can also manipulate the source code delivered by SAP and hence deactivate or bypass the encryption.</OL></OL> <OL><OL>b) If the attacker obtains the authorization to change the source code in a SAP system, or if he is authorized to call the encryption routines required for the authorization within reports or transactions that are part of the system, the encryption does not provide any enhanced protection.</OL></OL> <OL><OL>c) Users with debugging authorization can display individual payment card numbers at certain points of the document processing where the number must be decrypted temporarily for further processing.</OL></OL> <OL>7. The BAPI_SALESORDER_SIMULATE returns a transferred payment card number in unencrypted format even if the encryption is activated. When you save the document, the system encrypts the number in the same way as in the online transaction. If you use BAPISDORDER_GETDETAILEDLIST to display documents, the system displays the payment card number in unencrypted format if you have the authorization for activity 'C4' in the V_VBAK_AAT authorization object. If you do not have this authorization, the system returns a masked number.</OL> <OL><OL>a) If you display a payment card data for an FI document, the system displays the payment card number only in a masked form. If you want to display the unencrypted payment card number, you must have the relevant authorization (authorization object F_BKPF_BUK activity C4).</OL></OL> <OL>8. The system does not archive encrypted payment card data. The system only archives the masked payment card number.</OL> <OL>9. If you upgrade a non-Unicode system to a Unicode system and the credit card encryption is activated in the non-Unicode system, you must decrypt the encrypted card number in your non-Unicode system using the CCARDEC_TRANSFORM_SD report, transfer the card number into the new system and then encrypt it again. Note that in a Unicode system, you cannot decrypt data that was encrypted in a non-Unicode system, even if you use the same PSE (Personal Security Environment).</OL></div>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Other Components", "Value": "FI-BL-PT-FO (payment forms)"}, {"Key": "Responsible                                                                                         ", "Value": "<PERSON> (D029676)"}, {"Key": "Processor                                                                                           ", "Value": "D022024"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "J", "TranslationLanguage": "日本語", "TranslationLabel": "", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766703/J"}, {"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Origin)", "TranslationLabel": "Origin", "TranslationIsHuman": true, "TranslationIsOutdated": false, "URL": "/notes/0000766703/D"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0000766703/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": []}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "894022", "RefComponent": "XX-PROJ-CDP-027", "RefTitle": "NAE: Credit Card Masking", "RefUrl": "/notes/894022"}, {"RefNumber": "813198", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Action 'C4' is missing for the VBAK_AAT authorization object", "RefUrl": "/notes/813198"}, {"RefNumber": "812658", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Credit card information incomplete", "RefUrl": "/notes/812658"}, {"RefNumber": "791178", "RefComponent": "FIN-FSCM-BD-AR", "RefTitle": "Credit card encryption in AR backend", "RefUrl": "/notes/791178"}, {"RefNumber": "662340", "RefComponent": "BC-SEC-SSF", "RefTitle": "SSF Encryption Using the SAPCryptolib", "RefUrl": "/notes/662340"}, {"RefNumber": "633462", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Encrypting credit card data", "RefUrl": "/notes/633462"}, {"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517"}, {"RefNumber": "1034482", "RefComponent": "CRM-BF-PC", "RefTitle": "FAQ: Credit card encryption in CRM", "RefUrl": "/notes/1034482"}, {"RefNumber": "1029819", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Encryption of payment cards in SD and customer master", "RefUrl": "/notes/1029819"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "1319517", "RefComponent": "BC-I18-UNI", "RefTitle": "Unicode Collection Note", "RefUrl": "/notes/1319517 "}, {"RefNumber": "592393", "RefComponent": "BC-DB-ORA", "RefTitle": "FAQ: Oracle", "RefUrl": "/notes/592393 "}, {"RefNumber": "791178", "RefComponent": "FIN-FSCM-BD-AR", "RefTitle": "Credit card encryption in AR backend", "RefUrl": "/notes/791178 "}, {"RefNumber": "1034482", "RefComponent": "CRM-BF-PC", "RefTitle": "FAQ: Credit card encryption in CRM", "RefUrl": "/notes/1034482 "}, {"RefNumber": "662340", "RefComponent": "BC-SEC-SSF", "RefTitle": "SSF Encryption Using the SAPCryptolib", "RefUrl": "/notes/662340 "}, {"RefNumber": "812658", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Credit card information incomplete", "RefUrl": "/notes/812658 "}, {"RefNumber": "1029819", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Encryption of payment cards in SD and customer master", "RefUrl": "/notes/1029819 "}, {"RefNumber": "894022", "RefComponent": "XX-PROJ-CDP-027", "RefTitle": "NAE: Credit Card Masking", "RefUrl": "/notes/894022 "}, {"RefNumber": "914603", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "FAQ/Collective note for payment cards (Consulting/modif)", "RefUrl": "/notes/914603 "}, {"RefNumber": "813198", "RefComponent": "SD-BIL-IV-PC", "RefTitle": "Action 'C4' is missing for the VBAK_AAT authorization object", "RefUrl": "/notes/813198 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "SAP_APPL", "From": "46C", "To": "46C", "Subsequent": ""}, {"SoftwareComponent": "SAP_APPL", "From": "470", "To": "470", "Subsequent": "X"}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}