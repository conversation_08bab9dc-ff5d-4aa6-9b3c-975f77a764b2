{"Request": {"Number": "2407087", "LanguageSource": "E", "LanguageTarget": "E"}, "Response": {"_elapsedTime": 481, "Error": {}, "SAPNote": {"_type": "00200720410000000382", "_requestedLanguage": "E", "_masterLanguage": "E", "_loadedLanguage": "E", "_machineTranslationLanguage": "", "Actions": {"_label": "Actions", "Favorite": {"_labelWhenFalse": "<PERSON> as Favorite", "_labelWhenTrue": "Unmark as Favorite", "_type": "EVENT", "value": false, "_visible": true}, "Confirm": {"_label": "Confirm", "_type": "EVENT", "_visible": false}, "NotRelevant": {"_label": "Not Relevant", "_type": "EVENT", "_visible": false}, "Translation": {"_label": "", "_type": "EVENT", "_visible": false}, "Download": {"_label": "Download for SNOTE", "_type": "URL", "_visible": true, "url": "https://notesdownloads.sap.com/note/0040000018461572017"}, "Print": {"_label": "PDF Version", "_type": "URL", "_visible": true, "url": "https://userapps.support.sap.com/sap/support/sfm/notes/print/0002407087?language=E&token=83E71021EDD188ED63F1D7CDF69BB2C6"}, "ShareByEmail": {"_label": "Share by Email", "_type": "EVENT", "_visible": true}, "OpenNewWindow": {"_label": "Open in New Window", "_type": "EVENT", "_visible": true}, "Edit": {"_label": "Edit", "_type": "URL", "url": "https://i7p.wdf.sap.corp/sap/support/notes/edit/0002407087", "_visible": true}, "CompareVersions": {"_label": "Show Changes", "_type": "URL", "_visible": true, "url": "/notesLatestChanges/0002407087/E/diff"}, "Rating": {"_label": "Rate this document", "_type": "EVENT", "_visible": false}}, "Header": {"_label": "Header Data", "Number": {"_label": "SAP Note/KBA", "value": "2407087"}, "Type": {"_label": "Type", "value": "SAP Note"}, "Version": {"_label": "Version", "value": 5}, "Recency": {"_label": "Recency", "key": "UPDATED", "value": "Updated"}, "Language": {"_label": "Language", "value": "English"}, "MasterLanguage": {"_label": "Master Language", "value": "English"}, "Category": {"_label": "Category", "value": "Upgrade information"}, "Priority": {"_label": "Priority", "value": "Recommendations / Additional Info"}, "Status": {"_label": "Release Status", "value": "Released for Customer"}, "ReleasedOn": {"_label": "Released On", "value": "28.03.2018"}, "SAPComponentKey": {"_label": "Component", "value": "BW-WHM-DOC"}, "SAPComponentKeyText": {"_label": "Component", "value": "Documentation"}, "SAPComponentPath": [{"_label": "Services & Support", "value": "servicessupport", "_url": "/servicessupport"}, {"_label": "KBAs & Notes", "value": "knowledge", "_url": "/servicessupport/knowledge"}, {"_label": "SAP Business Warehouse", "value": "BW", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Data Warehouse Management", "value": "BW-WHM", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}, {"_label": "Documentation", "value": "BW-WHM-DOC", "_url": "/mynotes?tab=Search&sortBy=Relevance&filters=themk%25253Aeq~'BW-WHM-DOC*'%25252BreleaseStatus%25253Aeq~'CustomerRelease'%25252BsecurityPatchDay%25253Aeq~'NotRestricted'%25252BfuzzyThreshold%25253Aeq~'0.9'&flag=mynotes "}]}, "Title": {"_label": "Title", "value": "2407087 - SAPBWNews SAP BW/4HANA 1.0 SP 03"}, "LongText": {"_label": "Description", "value": "<h3 data-toc-skip class=\"section\" id=\"Symptom\">Symptom</h3>\r\n<p>This note deals with ABAP Support Package&#160;03 for SAP BW/4HANA 1.0 SP 03</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Other Terms\">Other Terms</h3>\r\n<p>SAPBWNEWS, Support Packages for SAP BW/4HANA 1.0 SP 03,&#160;Patches</p>\r\n<h3 data-toc-skip class=\"section\" id=\"Solution\">Solution</h3>\r\n<p>This note contains the SAPBWNews for ABAP Support Package&#160;03 of SAP BW/4HANA 1.0 SP 03.<br />It provides a list of all notes describing the corrections or enhancements in Support Package 03. This note will be updated when other notes are added.<br /><br /><strong>Factors you must take into account when you import the Support Package:</strong></p>\r\n<ul>\r\n<li>SAP strongly recommends to apply the latest available SAP HANA revision.</li>\r\n<li>\r\n<p><strong>Before</strong> you import the Note please read note <a target=\"_blank\" href=\"/notes/2248091\">2248091 - Change to reimplementation handling </a></p>\r\n</li>\r\n<li>\r\n<p>Support for some application server platforms will be discontinued with the next release of SAP BW/4HANA. See SAP Note <a target=\"_blank\" href=\"/notes/2620910\">2620910</a> for recommended application server platforms.</p>\r\n</li>\r\n</ul>\r\n<p><strong>Issues that may occur after you import the Support Package:</strong></p>\r\n<ul>\r\n<li>For further information on fixes please see the referenced notes</li>\r\n</ul>\r\n<p><strong>Errors corrected /Important Enhancements delivered with this Support Package:</strong></p>\r\n<ul>\r\n<li>\r\n<p><span style=\"font-family: Calibri; color: #1f497d;\">&#160;</span>For full list please see <a target=\"_blank\" href=\"https://launchpad.support.sap.com/#/supportpackage/SAPK-10003INDW4CORE\">https://launchpad.support.sap.com/#/supportpackage/SAPK-10003INDW4CORE</a></p>\r\n</li>\r\n</ul>\r\n<p>&#160;</p>"}, "Attributes": {"_label": "Attributes", "_columnNames": {"Key": "Key", "Value": "Value"}, "Items": [{"Key": "Responsible                                                                                         ", "Value": "<PERSON><PERSON> (D031867)"}, {"Key": "Processor                                                                                           ", "Value": "<PERSON><PERSON> (D031867)"}]}, "Translations": {"_label": "Available Languages", "Items": [{"TranslationLanguageCode": "D", "TranslationLanguage": "<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/D"}, {"TranslationLanguageCode": "J", "TranslationLanguage": "日本語 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/J"}, {"TranslationLanguageCode": "P", "TranslationLanguage": "<PERSON>ug<PERSON><PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/P"}, {"TranslationLanguageCode": "S", "TranslationLanguage": "<PERSON>spa<PERSON><PERSON> (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/S"}, {"TranslationLanguageCode": "F", "TranslationLanguage": "Français (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/F"}, {"TranslationLanguageCode": "I", "TranslationLanguage": "Italiano (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/I"}, {"TranslationLanguageCode": "R", "TranslationLanguage": "Русский (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/R"}, {"TranslationLanguageCode": "1", "TranslationLanguage": "中文 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/1"}, {"TranslationLanguageCode": "3", "TranslationLanguage": "한국어 (Machine Translation)", "TranslationLabel": "Machine Translation", "TranslationIsHuman": false, "TranslationIsOutdated": false, "URL": "/notes/0002407087/3"}]}, "Attachments": {"_label": "Attachments", "_columnNames": {"FileName": "File Name", "FileSize": "File Size", "MimeType": "Mime Type", "URL": "Url"}, "Items": [{"FileName": "SAP_BW4HANA_100_SP_03_Release_Notes.xlsx", "FileSize": "24", "MimeType": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "URL": "https://userapps.support.sap.com/sap/support/sapnotes/public/services/attachment.htm?iv_key=012006153200001635942016&iv_version=0005&iv_guid=6CAE8B3EA6731ED794AC393A69E6A0CB"}]}, "References": {"_label": "References", "RefTo": {"_label": "This document refers to", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2620910", "RefComponent": "BC-OP-PLNX", "RefTitle": "SAP S/4HANA 1511, 1610, 1709, 1809 and SAP BW/4HANA 1.0, 2.0: Recommended and released Application Server Platforms", "RefUrl": "/notes/2620910"}, {"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382"}, {"RefNumber": "2248091", "RefComponent": "BC-UPG-NA", "RefTitle": "Change to reimplementation handling", "RefUrl": "/notes/2248091"}]}, "RefBy": {"_label": "This document is referenced by", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefComponent": "Component", "RefTitle": "Title", "RefUrl": "Url"}, "Items": [{"RefNumber": "2347382", "RefComponent": "BW-B4H-LM", "RefTitle": "SAP BW/4HANA – General Information (Installation, SAP HANA, security corrections…)", "RefUrl": "/notes/2347382 "}, {"RefNumber": "2408215", "RefComponent": "BW-BEX-OT-OLAP-CUR", "RefTitle": "Error occurred during currency conversion () in \"explain query\"", "RefUrl": "/notes/2408215 "}, {"RefNumber": "2434029", "RefComponent": "BW-BEX-OT-OLAP-VAR", "RefTitle": "Hierarchy node variable filled incorrectly", "RefUrl": "/notes/2434029 "}, {"RefNumber": "2428701", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Performance optimization in query", "RefUrl": "/notes/2428701 "}, {"RefNumber": "2433785", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer: Safety belt raises false message even though the total number of cells is less than the limit set.", "RefUrl": "/notes/2433785 "}, {"RefNumber": "2433463", "RefComponent": "BW-PLA-IP-PMD", "RefTitle": "Editing of key figure values in the Planning Modeler", "RefUrl": "/notes/2433463 "}, {"RefNumber": "2432852", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Error for creation from template", "RefUrl": "/notes/2432852 "}, {"RefNumber": "2426576", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "System error in Program CL_RSR_RRI2_FAGGR and Form _CHANGE_AGGRQ-03-", "RefUrl": "/notes/2426576 "}, {"RefNumber": "2431673", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "RSSDA_CREATE_TABLE_STAT shows error: statistics cannot be found (HANA 1)", "RefUrl": "/notes/2431673 "}, {"RefNumber": "2431513", "RefComponent": "BW-BEX-ET-WB-7X", "RefTitle": "Analyzer:  The properties \"Scaling Factor\", \"Decimal Places\" and \"Highlight\" are not saved when the RS_FRONTEND_INIT parameter \"ANA_SERIALIZE_PARTS\" is set for locally created formulas.", "RefUrl": "/notes/2431513 "}, {"RefNumber": "2430084", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Time dimension check incorrectly reports inconsistency", "RefUrl": "/notes/2430084 "}, {"RefNumber": "2429895", "RefComponent": "BW-BEX-OT-CCH", "RefTitle": "Too long SQL statements when deleting cache entries", "RefUrl": "/notes/2429895 "}, {"RefNumber": "2430248", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "empty cells instead of empty result set", "RefUrl": "/notes/2430248 "}, {"RefNumber": "2429287", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BOC: transient Query ATtributes", "RefUrl": "/notes/2429287 "}, {"RefNumber": "2429247", "RefComponent": "BW-BEX-OT-BICS-INA", "RefTitle": "BICS InA: Complex filters with character nodes", "RefUrl": "/notes/2429247 "}, {"RefNumber": "2427952", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "SEGR: Error after change of data type", "RefUrl": "/notes/2427952 "}, {"RefNumber": "2427824", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "adso: Adjustment of update API for transfer", "RefUrl": "/notes/2427824 "}, {"RefNumber": "2427151", "RefComponent": "BW-WHM-DST-DTP", "RefTitle": "Deletion of detached DTPs", "RefUrl": "/notes/2427151 "}, {"RefNumber": "2427863", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Termination with the error 'System error in program CL_RSMD_RS_SPECIAL and form _GET_SID-01'", "RefUrl": "/notes/2427863 "}, {"RefNumber": "2427256", "RefComponent": "BW-BEX-OT-DBIF", "RefTitle": "Dump CL_GUI_CFW in program RS2HANA_ASSIGN_PRIVILEGES", "RefUrl": "/notes/2427256 "}, {"RefNumber": "2426921", "RefComponent": "BW-BEX-ET-QDEF", "RefTitle": "Uncaught exception INHERITED_ERROR during query check/generate", "RefUrl": "/notes/2426921 "}, {"RefNumber": "2426063", "RefComponent": "BW-BEX-OT-OLAP-AUT", "RefTitle": "Display of unauthorized key figures", "RefUrl": "/notes/2426063 "}, {"RefNumber": "2425847", "RefComponent": "BW-BEX-OT-OLAP-HIER", "RefTitle": "Hierarchy attribute \"Do not display leaves for inner-nodes in the Query\" does not work correctly", "RefUrl": "/notes/2425847 "}, {"RefNumber": "2425579", "RefComponent": "BW-WHM-DST-TRF", "RefTitle": "Transformationen werden nicht korrekt gesperrt", "RefUrl": "/notes/2425579 "}, {"RefNumber": "2425375", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "Exception in transaction RAAOE/RAAOP", "RefUrl": "/notes/2425375 "}, {"RefNumber": "2418941", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "RSDDSTAT_OLAP not written for some users", "RefUrl": "/notes/2418941 "}, {"RefNumber": "2418359", "RefComponent": "BI-RA-AO-XLA", "RefTitle": "AO: Check S_RS_FOLD authorization for display of InfoAreas in Open Dialog", "RefUrl": "/notes/2418359 "}, {"RefNumber": "2421735", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Archiving via Process Chain archives no data for advanced DSOs ( corporate memory )", "RefUrl": "/notes/2421735 "}, {"RefNumber": "2399597", "RefComponent": "BW-BEX-OT-F4", "RefTitle": "Input help for CompositeProvider does not take filter into account", "RefUrl": "/notes/2399597 "}, {"RefNumber": "2404659", "RefComponent": "BW-BEX-OT-MDX", "RefTitle": "Long runtime when descendants function is used", "RefUrl": "/notes/2404659 "}, {"RefNumber": "2416319", "RefComponent": "BW-WHM-DBA-ADSO", "RefTitle": "Advanced DSO: Missing buffer invalidation results in runtime error ASSERTION_FAILED in class CL_RSDSO_DATA_TASK", "RefUrl": "/notes/2416319 "}, {"RefNumber": "2397156", "RefComponent": "BW-BEX-OT-OLAP", "RefTitle": "Exception cells do not override correctly when same CKF/RKF from the structure are also used in Cells", "RefUrl": "/notes/2397156 "}, {"RefNumber": "2408573", "RefComponent": "BW-WHM-DST", "RefTitle": "Object not found exception missing in Hadoop client", "RefUrl": "/notes/2408573 "}, {"RefNumber": "2403301", "RefComponent": "BW-WHM-DST-ARC", "RefTitle": "Deletion of Near-line Requests fails with HDFS Error FileNotFoundException", "RefUrl": "/notes/2403301 "}]}}, "Validity": {"_label": "Software Components", "_columnNames": {"SoftwareComponent": "Software Component", "From": "From", "To": "To", "Subsequent": "And subsequent"}, "Items": [{"SoftwareComponent": "DW4CORE", "From": "100", "To": "100", "Subsequent": ""}]}, "SupportPackage": {"_label": "Support Package", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Support Package", "URL": "Url"}, "Items": []}, "SupportPackagePatch": {"_label": "Support Package Patches", "_columnNames": {"SoftwareComponentVersion": "Software Component Version", "SupportPackage": "Patch Level", "SupportPackagePatch": "Patch Level", "URL": "Url"}, "Items": []}, "CorrectionInstructions": {"_label": "Correction Instructions", "_columnNames": {"SoftwareComponent": "Software Component", "NumberOfCorrin": "Number of Correction Instructions", "URL": "Url"}, "Items": []}, "ManualActions": {"_label": "Manual Activities", "value": ""}, "CorrectionsInfo": {"_label": "Information about Correction Instructions", "Corrections": {"_label": "Correction", "value": 0, "state": "None"}, "ManualActivities": {"_label": "Manual Activities", "value": 0, "state": "Success"}, "Prerequisites": {"_label": "Prerequisites", "value": 0, "state": "None"}}, "Preconditions": {"_label": "Prerequisites", "_columnNames": {"SoftwareComponent": "Software Component", "ValidFrom": "From", "ValidTo": "To", "Number": "SAP Note/KBA", "URL": "Url", "Title": "Title", "Component": "Component"}, "Items": []}, "Rating": {"_label": "Rate this document", "RatingHelpful": {"_label": "Rated Helpful", "Helpful-Yes": "0 people"}, "RatingQuality": {"_label": "Quality Rating"}, "Quality-AVG": 0.0, "Quality-Votes": 0, "RatingQualityDetails": {"Stars-1": 0, "Stars-2": 0, "Stars-3": 0, "Stars-4": 0, "Stars-5": 0}, "RatingQuestions": []}, "SideEffects": {"_label": "Side Effects", "SideEffectsCausing": {"_label": "This document solves side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}, "SideEffectsSolving": {"_label": "This document is causing side effects", "_columnNames": {"RefNumber": "SAP Note/KBA", "RefTitle": "Title", "RefUrl": "Url"}, "Items": []}}}}}