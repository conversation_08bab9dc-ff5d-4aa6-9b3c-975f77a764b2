<h1 class="title topictitle1">Feature Scope Description</h1>
<div class="body conbody">
    <p class="shortdesc"></p>\n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_agr_cn4_5pb">
        <section class="section"
                 type="SAP Readiness Check for SAP S/4HANA">
            <h2 class="section_title">SAP Readiness Check
                for SAP S/4HANA</h2>\n\n<p class="p">
            With the SAP Readiness Check tool for
            SAP S/4HANA, SAP provides an overview of
            the\nmost important aspects for an SAP
            ERP 6.0 system conversion to SAP
            S/4HANA.\nConverting a system to SAP
            S/4HANA requires a migration to SAP HANA
            (if not\ncurrently running on SAP HANA),
            and the installation of new simplified
            code and\nadaptions.</p>\n<p class="p">
            SAP S/4HANA is SAP’s next-generation
            business suite. It is not the legal
            successor of\nany SAP Business Suite
            product. SAP S/4HANA is a product built
            on the in-memory\nplatform SAP HANA. To
            build SAP S/4HANA, SAP has reimagined
            solutions for modern\nbusiness processes
            in an increasingly mobile and digitized
            world. SAP S/4HANA\ndelivers
            simplifications and innovations,
            deliverable on one data structure
            and\narchitecture moving forward.</p>\n
            <p class="p">A comprehensive overview of the
                simplifications for SAP S/4HANA,
                compared to SAP\nBusiness Suite
                products, are captured in the <a
                        class="xref"
                        href="https://help.sap.com/viewer/p/SAP_S4HANA_ON-PREMISE"
                        target="_blank" rel="noopener"
                        alt="https://help.sap.com/viewer/p/SAP_S4HANA_ON-PREMISE"
                        title="https://help.sap.com/viewer/p/SAP_S4HANA_ON-PREMISE">\nSimplification
                    List for SAP S/4HANA</a>. For a
                given customer, only a limited\nnumber
                of simplification items from this
                extensive list are applicable.</p>\n<p
                class="p">To provide customers with an
            overview of the implications when
            converting a specific\nSAP ERP 6.0 to
            SAP S/4HANA, SAP offers SAP Readiness
            Check for SAP S/4HANA.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_zcz_gn4_5pb">
        <section class="section"
                 type="SAP Readiness Check for SAP S/4HANA Upgrades">
            <h2 class="section_title">SAP Readiness Check
                for SAP S/4HANA Upgrades</h2>\n\n<p
                class="p">With the SAP Readiness Check
            tool for SAP S/4HANA upgrades, SAP
            provides an overview\nof the most
            important aspects of a version upgrade
            for an SAP S/4HANA system.</p>\n<p
                class="p">SAP S/4HANA is SAP’s
            next-generation business suite built on
            the in-memory platform\nSAP HANA. To
            build SAP S/4HANA, SAP has reimagined
            solutions for modern business\nprocesses
            in an increasingly mobile and
            digitalized world. SAP S/4HANA
            delivers\nsimplifications and
            innovations, deliverable on one data
            structure and architecture\nmoving
            forward. An SAP S/4HANA version upgrade
            delivers incremental functionality\nand
            simplifications, which may affect
            business processes used by customers.
        </p>\n<p class="p">A comprehensive overview of
            the incremental simplifications for the
            target version of\nSAP S/4HANA, compared
            to the current version, is available in
            the <em class="ph i">Simplification\nList
                for SAP S/4HANA</em> on the <a
                    class="xref"
                    href="https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/en-US?task=implement_task"
                    target="_blank" rel="noopener"
                    alt="https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/en-US?task=implement_task"
                    title="https://help.sap.com/viewer/product/SAP_S4HANA_ON-PREMISE/latest/en-US?task=implement_task"><em
                    class="ph i">Implement</em>
                tab of the SAP S/4HANA product
                page</a> for\nthe target version
            on SAP Help Portal. For a given
            customer, only a limited number\nof
            simplification items from this extensive
            list apply.</p>\n<p class="p">To provide
            customers with an overview of the
            implications of upgrading to a
            later\nrelease of SAP S/4HANA, SAP
            offers SAP Readiness Check for SAP
            S/4HANA upgrades.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_dgp_tdg_yrb">
        <section class="section"
                 type="SAP Readiness Check for SAP BW/4HANA">
            <h2 class="section_title">SAP Readiness Check
                for SAP BW/4HANA</h2>\n\n<p class="p">
            With the SAP Readiness Check tool for
            SAP BW/4HANA, SAP provides an overview
            of the most important aspects for
            converting an SAP BW\nsystem based on
            SAP NetWeaver 7.x to SAP BW/4HANA.
            Converting a system to SAP BW/4HANA
            requires a migration to SAP HANA (if it
            is not\ncurrently running on SAP HANA)
            and the installation of new simplified
            code and adaptations.</p>\n<p class="p">
            SAP BW/4HANA is SAP’s next-generation
            business warehouse solution. It is not
            the legal\nsuccessor of any SAP
            NetWeaver product. SAP BW/4HANA is a
            product built on the\nin-memory platform
            SAP HANA. To build SAP BW/4HANA, SAP has
            reimagined solutions for\nmodern
            business warehousing in an increasingly
            mobile and digitized world.
            SAP\nBW/4HANA provides simplifications
            and innovations, deliverable on one data
            structure\nand architecture moving
            forward.</p>\n<p class="p">A
            comprehensive overview of the
            simplifications for SAP BW/4HANA,
            compared to SAP BW systems\nbased on SAP
            NetWeaver products, are captured in the
            simplification list linked in\nSAP Note
            <a href="/docs/link-disclaimer?site=https://me.sap.com/notes/2421930"
               target="_blank"
               rel="noopener">2421930 <img
                    src="themes/sap-light/img/sap_link.png"
                    class="link-sap"
                    alt="Information published on SAP site"
                    title="Information published on SAP site"
                    border="0"></a>
            (Simplification List for SAP BW/4HANA).
            For a\ngiven customer, only a limited
            number of simplification items from this
            extensive\nlist are applicable.</p>\n<p
                class="p">To provide customers with an
            overview of the implications when
            converting a specific SAP BW 7.x system
            to SAP BW/4HANA, SAP offers
            SAP\nReadiness Check for SAP BW/4HANA.
        </p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_z4s_x42_htb">
        <section class="section"
                 type="SAP Readiness Check for SAP Customer Experience Solutions">
            <h2 class="section_title">SAP Readiness Check
                for SAP Customer Experience Solutions
            </h2>\n\n<p class="p">With the SAP Readiness
            Check tool for SAP Customer Experience
            solutions, SAP provides an overview of
            the most important aspects
            for\nconverting an SAP CRM system to the
            latest SAP Customer Experience
            solutions.</p>\n<p class="p">To provide
            customers with an overview of the
            implications when converting a specific
            SAP CRM system to SAP Customer
            Experience\nsolutions, SAP offers SAP
            Readiness Check for SAP Customer
            Experience Solutions.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_lzc_njd_jtb">
        <section class="section"
                 type="SAP Readiness Check for SAP ERP Usage and Data Profiling">
            <h2 class="section_title">SAP Readiness Check
                for SAP ERP Usage and Data Profiling
            </h2>\n\n<p class="p">With the SAP Readiness
            Check tool for SAP ERP Usage and Data
            Profiling, SAP provides an overview of
            the current status of an SAP ERP\n6.0
            system from its functional perspective
            to support SAP S/4HANA new
            implementation scenarios or selective
            data transition\nprojects.</p>\n<p
                class="p">To provide customers with a
            detailed summary of the configuration,
            usage, and data volume footprint of an
            existing SAP ERP 6.0 system,\nSAP offers
            SAP Readiness Check for SAP ERP Usage
            and Data Profiling.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_jjx_pyv_p5b">
        <section class="section"
                 type="SAP Readiness Check for SAP SuccessFactors Solutions">
            <h2 class="section_title">SAP Readiness Check
                for SAP SuccessFactors Solutions</h2>
            \n\n<p class="p">With SAP Readiness Check for
            SAP SuccessFactors solutions, SAP
            provides a self-service tool to analyze
            the existing SAP ERP HCM\nimplementation
            in preparation for a transition to SAP
            SuccessFactors. </p>\n<p class="p">SAP
            Readiness Check for SAP SuccessFactors
            solutions allows subject matter experts
            to have the required level of insight
            into key topic\nareas (such as
            implemented functionality, degree of
            customization, number of interfaces, and
            existing data footprint) necessary
            to\nproperly assess the complexity of an
            existing SAP ERP HCM production
            environment.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_oqq_fx1_bxb">
        <section class="section"
                 type="SAP Readiness Check for SAP Cloud ALM">
            <h2 class="section_title">SAP Readiness Check
                for SAP Cloud ALM</h2>\n\n<p class="p">
            With the SAP Readiness Check tool for
            SAP Cloud ALM, SAP provides an overview
            of the\ncurrent usage of application
            lifecycle management capabilities in an
            SAP Solution\nManager system. The tool
            assesses the scope and availability of
            these capabilities\nin SAP Cloud ALM or
            other SAP solutions outside of SAP
            Solution Manager.</p>\n
        </section>
    </div>
    \n
    <div class="section"
         id="loiob47054d8bb6a47738b874cefc0b9f561__section_og4_jwt_1yb">
        <section class="section"
                 type="SAP Readiness Check for SAP Datasphere, SAP BW Bridge">
            <h2 class="section_title">SAP Readiness Check
                for SAP Datasphere, SAP BW Bridge</h2>
            \n\n<p class="p">With the SAP Readiness Check
            tool for SAP Datasphere, SAP BW bridge,
            SAP provides an\noverview of the most
            important aspects for a conversion of an
            SAP BW 7.x or SAP\nBW/4HANA system to
            SAP Datasphere, SAP BW bridge.</p>\n<p
                class="p">A comprehensive overview of
            the simplifications for SAP Datasphere,
            SAP BW bridge,\ncompared to SAP BW
            systems based on SAP NetWeaver products
            or SAP BW/4HANA, are\ncaptured in SAP
            Note <a href="/docs/link-disclaimer?site=https://me.sap.com/notes/3154420"
                    target="_blank"
                    rel="noopener">3154420 <img
                    src="themes/sap-light/img/sap_link.png"
                    class="link-sap"
                    alt="Information published on SAP site"
                    title="Information published on SAP site"
                    border="0"></a>
            (Simplification List for
            SAP\nDatasphere, SAP BW Bridge). For a
            given customer, only a limited number
            of\nsimplification items from this
            extensive list are applicable.</p>\n<p
                class="p">To provide customers with an
            overview of the implications when
            converting a specific\nSAP BW 7.x or SAP
            BW/4HANA system to SAP Datasphere BW
            bridge, SAP offers SAP\nReadiness Check
            for SAP Datasphere, SAP BW bridge.</p>\n
        </section>
    </div>
    \n
</div>
<div class="related-links"></div>