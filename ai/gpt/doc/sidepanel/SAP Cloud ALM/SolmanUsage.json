{"title": "Learn More About Usage in SAP Solution Manager", "next": "<p>\n          Review the results of the scanned capability usage analysis by choosing a scenario in the list on the left.<br/><br/>\n          To explore your usage results, you can proceed as follows:\n          <ul>\n            <li>Find a certain capability or reduce the number of scenarios shown by using the filter function above the list.</li>\n            <li>Choose a scenario to see the usage status of the corresponding capabilities.</li>\n            <li>See all capabilities that have been identified as used. By default, the value <span style='font-style:italic'>YES</span> is active for all used capabilities. You can, however, override this setting as you require by choosing on the toggle button. All capabilities with the <span style='font-style:italic'>Relevant</span> toggle button set to <span style='font-style:italic'>YES</span> will be included in the <span style='font-style:italic'>Availability of Relevant Capabilities</span> check.</li>\n            <li>Reset the <span style='font-style:italic'>Relevant</span> toggle buttons to the default values based on the usage of capabilities identified by SAP Readiness Check by choosing the <span style='font-style:italic'>Reset</span> icon in the top-right corner of the screen.</li>\n          </ul>\n        </p>", "info": "<p>\n          The data shown in this section allows you to see the scope of application lifecycle management capabilities used in your SAP Solution Manager system.<br/><br/>\n          In the panel on the left, a list of scenarios is organized by area (implementation, operations, service and administration). The number of used capabilities with respect to the total number of capabilities in each scenario is displayed below the scenario name.<br/><br/>\n          The criteria for determining the usage of each capability appear by choosing a capability card on the right side. In addition, for those capabilities with an evaluation of the types of connected managed systems, a table appears with an aggregated list of product types assigned to the identified managed systems.<br/>\n        </p>", "disclaim": "<p>\n          The <span style='font-style:italic'>Relevant</span> toggle button lets you freely choose the capabilities to include in the <span style='font-style:italic'>Availability of Relevant Capabilities</span> check (availability analysis of equivalent capabilities in SAP Cloud ALM or other SAP solutions). This helps you define the scope of your planned SAP Cloud ALM transition.<br/>\n          By default, all used capabilities have the <span style='font-style:italic'>Relevant</span> toggle button value set to <span style='font-style:italic'>YES</span>.\n        </p>"}