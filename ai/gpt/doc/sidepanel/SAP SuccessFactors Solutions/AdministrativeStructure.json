{"next": "\n\t\t\t\t<p>Review and analyze the employee key figures from your SAP ERP HCM system to evaluate and plan the scope of your transition project.</p>\n\t\t\t\t<p>For assistance with analyzing the data, please contact SAP or your preferred implementation partner.</p>", "info": "\n\t\t\t\t<p>When transitioning from SAP ERP HCM to SAP SuccessFactors, there is the need to migrate existing employee data from one or more legacy systems into the Employee Central environment. This can involve several different scenarios depending on various types and statuses of the employees that are migrated. Some of these data migration scenarios might include rehired employees, terminated employees, active global assignments, active leave of absences, and so on. This will require different strategies as well as varying levels of data that need to be included to ensure successful migration to Employee Central. Successful data migration is also affected by critical project decisions like history of data, multiple module deployment, multiple legacy systems, phased implementation versus big-bang approach, among other functional and technical considerations.</p>\n\t\t\t\t<p>This card provides an overview of the administrative structure and how employees are assigned to this structure and contract elements in the analyzed system.</p>", "disclaim": "\n\t\t\t\t<p>We recommend reviewing the check results before using them, within your organization, by experts from SAP, and/or by your preferred implementation partner. Additional guidance can be found in the <a href='https://community.successfactors.com/t5/Implementation-Design-Principles/ct-p/ImplementationDesignPrinciples'>Implementation Design Principles for SAP SuccessFactors Solutions</a>. Implementation Design Principles (IDP) are documents that complement existing implementation handbooks by addressing real-life implementation challenges as well as frequently asked questions.</p>\n\t\t\t\t<p>The data collected for the current check is client dependent. If your production system contains multiple clients, you will need to create separate analyses to view the findings of this check.</p>", "title": "Learn More About Administrative Structure"}