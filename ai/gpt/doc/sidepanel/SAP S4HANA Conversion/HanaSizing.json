{"title": "Learn More About SAP S/4HANA Sizing", "next": "\n          <p>\n            Confirm the result of the sizing by comparing the estimated record counts to the record counts in your system. We recommend making this comparison for the top 5 biggest tables. Pay particular attention to tables that are cluster tables in SAP ERP but converted to transparent tables in SAP S/4HANA (that is CDCLS/CDPOS) as their size estimations involve more steps and can be inaccurate.<br/><br/>\n            To add an estimation for new functionalities in your sizing simulation, you can calculate the respective values by using the <a rel='noopener noreferrer' href='https://www.sap.com/about/benchmark/sizing.quick-sizer.html#quick-sizer'>Quick Sizer</a> tool.<br/><br/>\n            To estimate the impact of data volume management activities on the target system size, use the corresponding simulation feature in this section. We recommend reducing the data volume in your SAP ERP system prior to your conversion. For example, you can perform housekeeping and archiving activities for your top database tables. This can help you reduce the required target hardware size and the memory size that will be consumed in SAP S/4HANA. This might also shorten the conversion downtime. Ensure that you can fulfill all legal reporting requirements before you archive the data. Note that data archived in SAP ERP can still be displayed in SAP S/4HANA.<br/><br/>\n            To estimate the future database growth, we recommend running the sizing report at regular intervals (for example, once a month). The difference in the result can be used as an estimation of the growth.<br/><br/>\n            \n            For more information about sizing, explore the following:\n            <ul>\n              <li>SAP Note <a rel='noopener noreferrer' href='https://me.sap.com/notes/1872170'>1872170</a> (how to interpret the displayed results; in particular, see the FAQ document that is available in the <cite>Attachments</cite> section)</li>\n              <li>Introduction to <a rel='noopener noreferrer' href='https://www.sap.com/about/benchmark/sizing.html'>SAP sizing</a></li>\n            </ul>\n            For more information about data volume management, take the following steps:\n            <ul>\n              <li>See SAP Knowledge Base Article <a rel='noopener noreferrer' href='https://me.sap.com/notes/2818267'>2818267</a> (data volume management during migration to SAP S/4HANA).</li>\n              <li>See <a rel='noopener noreferrer' href='https://help.sap.com/doc/195dd7408c7447388c1bb9e54a5f6a31/LATEST/en-US/DMV_CONV.pdf'>Data Volume Management (DVM) During an SAP S/4HANA Conversion Project</a>.</li>\n              <li>See SAP Knowledge Base Article <a rel='noopener noreferrer' href='https://me.sap.com/notes/2388483'>2388483</a> (how-to instructions for data management for technical tables).</li>\n              <li>\n                SAP Enterprise Support customers can:\n                <ul>\n                  <li>Request a <a rel='noopener noreferrer' href='https://support.sap.com/content/dam/support/en_us/library/ssp/offerings-and-programs/sap-enterprise-support/enterprise-support-academy/continuous-quality-check-improvement-services/cqcdvm.pdf'>CQC Data Volume Management service</a>.</li>\n                  <li>Sign up to explore the <a rel='noopener noreferrer' href='https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/value-maps.html'>Data Volume Management value map</a>.</li>\n                  <li>Attend the <a rel='noopener noreferrer' href='https://support.sap.com/en/offerings-programs/enterprise-support/enterprise-support-academy/expert-guided-implementation.html'>Expert Guided Implementation</a> (EGI) session about DVM (SUP_EDE_0250_1312).</li>\n                </ul>\n              </li>\n              <li>Contact your SAP account executive.</li>\n            </ul>\n          </p>\n        ", "info": "\n          <p>\n            To calculate the right target SAP S/4HANA system size, you need to take several influencing factors into account. On the one hand, this includes the initial target SAP S/4HANA size calculated from your current SAP ERP database size. On the other hand, this includes future database growth, possible data volume reduction in your SAP ERP system, and potential new functionalities that need to be added manually.<br/><br/>\n            This section enables you to run a sizing simulation and provides information about your sizing values and data volume management potential.<br/><br/>\n            On the <cite>SAP S/4HANA Sizing Simulation</cite> tab, the initial target SAP S/4HANA size is provided. It is based on the ABAP on SAP HANA sizing report for SAP Business Suite powered by SAP HANA and SAP S/4HANA, which calculates the initial memory requirements for the SAP HANA database based on current data volumes. These requirements were derived from the size of the tables and the compression rate in the source system. With the identified initial target SAP S/4HANA size, you can perform a target system sizing simulation by manually adding additional factors (future database growth, data volume reduction in your SAP ERP system, as well as additional growth arising from new functionalities, mergers, and acquisitions).<br/><br/>\n            On the <cite>SAP S/4HANA Sizing</cite> tab, your estimated initial target system size is displayed in more detail, providing more information about the estimated memory and disk size requirements by category. In addition, you can see the top 20 database tables of your source system.<br/><br/>\n            On the <cite>Data Volume Management</cite> tab, the size of your archiving potential in GiB and percentage measurements by document type are provided, based on the top database tables in the past 12 and 24 months. In the bar chart view, the chart on the left shows how many gigabytes can be archived for each document type in relation to its total size in your source system. The chart on the right shows the corresponding archiving potential as a percentage for each document type. The bar charts show up to ten document types. To see the analyzed objects, switch to the table view.\n          </p>\n        ", "disclaim": "\n          <p>\n            SAP SE or an SAP affiliate company cannot be held liable for the correctness of the target SAP S/4HANA sizing created in this tool. Please ensure that you validate any sizing with either SAP or your (hardware) integration partner before proceeding with the hardware purchase.<br/>\n            The initial target SAP S/4HANA size does not consider additional data growth caused by additional rollouts, deployments of new functions, or business growth before or after the conversion to SAP S/4HANA. This also includes year-on-year growth, new standard functionalities (SAP Fiori usage and SAP S/4HANA embedded analytics), and new custom functionalities. The initial target size assumes that all objects are loaded to memory at the same time. This might not be the case in reality. To get an idea of the desired target SAP S/4HANA size, these factors need to be added on top by using the simulation feature.<br/><br/>\n            The data collected for the SAP S/4HANA sizing check is client independent.<br/>\n            The data collected for the data volume management check is client dependent. If your production system contains multiple clients, you will need to create separate analyses to view the findings of this check.\n          </p>\n        "}